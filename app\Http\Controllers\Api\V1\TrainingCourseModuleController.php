<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\TrainingCourse;
use App\Models\TrainingCourseModules;
use App\Models\TrainingCourseProgress;
use App\Repositories\TrainingCourseRepository;
use App\Http\Resources\V1\TrainingCourseModulesResource;
use App\Http\Resources\V1\TrainingCourseModuleDetailResource;
use App\Models\TrainingCourseModuleProgress;

class TrainingCourseModuleController extends Controller {

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    private $model;

    public function __construct() {
        $this->model = new TrainingCourseModules();
    }

    /**
     * List Training Course Module - Submodule.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($id) {
        try {
            $module = $this->model->find($id);
            if($module){
                $checkTrainingCourse = checkTrainingCourse($module,'module');
                if($checkTrainingCourse == 1 && auth()->user()->assessor_role == 'No'){
                    return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
                if($checkTrainingCourse == 2){
                    return response()->json(setErrorResponse(__('user.TrainingCourseModule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                if($module->status != 'Active'){
                    return response()->json(setErrorResponse(__('user.TrainingCourseModule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }

                $trainingCourseDetail = TrainingCourse::where('id',$module->training_course_id)->first();
                if(!empty($trainingCourseDetail)){
                    if($trainingCourseDetail->single_module_course == 1){
                        TrainingCourse::addDefaultCourseProgress($module->training_course_id);
                    }
                }

                if($module->total_submodules == 0){
                    $progressData['training_course_id'] = $module->training_course_id;
                    $progressData['module_id'] = $id;
                    $progressData['user_id'] = auth()->user()->id;
                    if($module->moduleProgress){
                        $module->moduleProgress->update([
                            'module_progress'=> 100,
                        ]);
                    }
                    (new TrainingCourseProgress)->calculateTrainingCourseProgress($progressData);
                    // Update New flag
                    (new TrainingCourseModuleProgress)->updateIsNew($progressData);
                    (new TrainingCourseProgress)->updateIsNew($progressData);
                }
                return (new TrainingCourseModulesResource($module));
            }else{
                return response()->json(setErrorResponse(__('user.TrainingCourseModule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Training Course Module Description.
     *
     * @return \Illuminate\Http\Response
     */
    public function show($id) {
        try {
            $module = $this->model->find($id);
            return ($module) ?
                    (new TrainingCourseModuleDetailResource($module)) :
                    response()->json(setErrorResponse(__('user.TrainingCourseModule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
