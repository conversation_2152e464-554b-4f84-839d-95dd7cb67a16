<?php

namespace App\Http\Controllers\Operator\v1;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\Product;
use App\Models\Industry;
use App\Models\ProductType;
use App\Repositories\Operator\v1\ProductRepository;
use App\Http\Requests\Operator\v1\ProductRequest;
use App\Http\Requests\Operator\v1\CommonListingRequest;
use App\Http\Requests\Operator\v1\CommonBulkStatusChangeRequest;
use App\Http\Requests\Operator\v1\AssignResourcesToProductRequest;
use App\Http\Requests\Operator\v1\MarkProductAsFeaturedRequest;
use App\Http\Resources\Operator\v1\ProductDetailResource;
use App\Http\Resources\Operator\v1\ProductListingResource;
use App\Http\Resources\Operator\v1\AssignProductListingResource;

class ProductController extends Controller {

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    protected $model;
    protected $repository;

    public function __construct() {
        $this->model = new Product();
        $this->repository = new ProductRepository($this->model);
    }

    /**
     * @OA\Get(
     *     path="/operator/products/getProductTypes",
     *     tags={"Operator - Product Management"},
     *     summary="Get product types",
     *     description="Get product types",
     *     operationId="getProductTypes",
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getProductTypes() {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $productTypes = ProductType::active()->where('master_user_id', $operatorId)->get(['id', 'name'])->toArray();
            $message = !empty($productTypes) ? __('operator.productType.listFound') : __('operator.productType.listNotFound');
            return response()->json(setResponse($productTypes, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/products/getIndustries",
     *     tags={"Operator - Product Management"},
     *     summary="Get industries",
     *     description="Get industries",
     *     operationId="getIndustries",
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getIndustries() {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $industries = Industry::active()->whereNull('master_user_id')->orwhere('master_user_id', $operatorId)->get(['id', 'name'])->toArray();
            $message = !empty($industries) ? __('operator.products.industryListFound') : __('operator.products.industryListNotFound');
            return response()->json(setResponse($industries, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/products/getDisplayOrder",
     *     tags={"Operator - Product Management"},
     *     summary="Get display order",
     *     description="Get display order",
     *     operationId="getDisplayOrder",
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getDisplayOrder() {
        try {
            $products = [];
            $defaultOrder = [
                'id' => 0,
                'title' => 'New Product',
                'display_order' => 1,
                'display_order_no' => 1,
                'primary_image' => url('/images/default.png'),
            ];

            $existingProducts = Product::select('id', 'title', 'display_order', 'primary_image')->operator()->orderBy('display_order')->get();

            foreach ($existingProducts as $product) {
                $products[] = [
                    'id' => $product->id,
                    'title' => $product->title,
                    'display_order_no' => $product->display_order,
                    'display_order' => $product->id,
                    'primary_image' => $product->primary_image_url
                ];
            }

            if (count($products) > 0) {
                $lastRecord = end($products);
                $defaultOrder['display_order'] = $lastRecord['display_order'] + 1;
                $defaultOrder['display_order_no'] = $lastRecord['display_order_no'] + 1;
            }
            return array_merge($products, [$defaultOrder]);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/products/getAssignProductsListing",
     *     tags={"Operator - Product Management"},
     *     summary="Listing of products assign to Resources",
     *     description="Listing of products assign to Resources",
     *     operationId="getAssignProductsListing",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 example={"per_page": 10, "page": 1, "search_key": "", "sort_by": "name", "order_by": "asc"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getAssignProductsListing(CommonListingRequest $request) {
        try {
            $products = $this->repository->getListing($request->all());
            return AssignProductListingResource::collection($products->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/products/getListing",
     *     tags={"Operator - Product Management"},
     *     summary="List products",
     *     description="List products",
     *     operationId="getListing",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="isExport",
     *                     description="for export csv",
     *                     type="integer"
     *                 ),
     *                 example={"per_page": 10, "page": 1, "search_key": "", "sort_by": "name", "order_by": "asc", "filters": {"title": "", "product_type": "", "status": ""}, "isExport": 0}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getListing(CommonListingRequest $request) {
        try {
            $products = $this->repository->getListing($request->all());
            if ($request->isExport) {
                return $this->repository->exportCsv($products->get());
            }
            return ProductListingResource::collection($products->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/products",
     *     tags={"Operator - Product Management"},
     *     summary="Store product",
     *     description="Store product",
     *     operationId="store",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="title",
     *                     description="Title",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="description",
     *                     description="Description",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="item_code",
     *                     description="Item Code",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="industry_id",
     *                     description="Industry ID",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="product_type_id",
     *                     description="Product Type ID",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="display_order",
     *                     description="Display Order",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="primary_image",
     *                     description="Primary Image",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="images",
     *                     description="Images",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="tags",
     *                     description="Tags",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 @OA\Property(
     *                     property="resources",
     *                     description="Resource IDs",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 example={"title": "Box Connection 20", "description": "Description of Product", "item_code": "PDT12345", "industry_id": 1, "product_type_id": 1, "display_order": 1, "primary_image": "filename.jpg", "images": {"image1.jpg", "image2.jpg"}, "tags": {"tag1", "tag2"}, "status": "Active", "resources": {1, 2}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function store(ProductRequest $request) {
        try {
            $product = $this->repository->create($request->all());
            return (new ProductDetailResource($product));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/products/assignResources",
     *     tags={"Operator - Product Management"},
     *     summary="Assign Resources to Product",
     *     description="Assign Resources to Product",
     *     operationId="assignResources",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="product_id",
     *                     description="Product Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="resources",
     *                     description="Resource IDs",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 example={"product_id": 1, "resources": {1, 2}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function assignResources(AssignResourcesToProductRequest $request) {
        try {
            $product = $this->model->find($request->product_id);
            if ($product) {
                // Sync Resources
                $product->resources()->sync($request->resources);
                return response()->json(setResponse([], ['message' => __('operator.products.created')]))->setStatusCode(Response::HTTP_CREATED);
            }
            return response()->json(setErrorResponse(__('operator.products.not-found')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/products/{id}",
     *     tags={"Operator - Product Management"},
     *     summary="Get product",
     *     description="Get product",
     *     operationId="show",
     *     @OA\Parameter(
     *         description="id",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function show($id) {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $product = $this->model->find($id);
            $productList = $this->model->where('master_user_id', $operatorId)->pluck('id')->toArray();
            if (in_array($id, $productList)) {
                return ($product) ?
                        (new ProductDetailResource($product)) :
                        response()->json(setErrorResponse(__('operator.products.not-found')))->setStatusCode(Response::HTTP_NOT_FOUND);
            } else {
                return response()->json(setErrorResponse(__('You cannot access this record')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Put(
     *     path="/operator/products/{id}",
     *     tags={"Operator - Product Management"},
     *     summary="Update product",
     *     description="Update product",
     *     operationId="update",
     *     @OA\Parameter(
     *         description="Id of product to update",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="title",
     *                     description="Title",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="description",
     *                     description="Description",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="item_code",
     *                     description="Item Code",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="industry_id",
     *                     description="Industry ID",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="product_type_id",
     *                     description="Product Type ID",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="display_order",
     *                     description="Display Order",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="primary_image",
     *                     description="Primary Image",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="images",
     *                     description="Images",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="tags",
     *                     description="Tags",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 @OA\Property(
     *                     property="resources",
     *                     description="Resource IDs",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 example={"title": "Box Connection 20", "description": "Description of Product", "item_code": "PDT12345", "industry_id": 1, "product_type_id": 1, "display_order": 1, "primary_image": "filename.jpg", "images": {"image1.jpg", "image2.jpg"}, "tags": {"tag1", "tag2"}, "status": "Active", "resources": {1, 2}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function update(ProductRequest $request, $id) {
        try {
            $product = $this->model->find($id);
            if ($product) {
                $this->repository->update($request->all(), $product);
                return response()->json(setResponse([], ['message' => __('operator.products.updated')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setErrorResponse(__('operator.products.not-found')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/products/delete",
     *     tags={"Operator - Product Management"},
     *     summary="Delete products",
     *     description="Delete products",
     *     operationId="destroy",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="ids",
     *                     description="Ids of Products",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 example={"ids": {1, 2}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function destroy(Request $request) {
        try {
            $ids = is_array($request->ids) ? $request->ids : [];
            foreach ($ids as $id) {
                $product = Product::find($id);
                $this->model->destroy($id);
                $allNextProducts = Product::whereMasterUserId($product->master_user_id)->where('display_order', '>', $product->display_order)->orderBy('display_order')->get();
                if (!$allNextProducts->isEmpty()) {
                    foreach ($allNextProducts as $key => $productData) {
                        $productData->display_order = (!is_null($productData->display_order) ? ($productData->display_order - 1) : 0);
                        $productData->save();
                    }
                }
            }
            return response()->json(setResponse([], ['message' => __('operator.products.deleted')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/products/changeStatus",
     *     tags={"Operator - Product Management"},
     *     summary="Change product(s) status",
     *     description="Change product(s) status",
     *     operationId="changeStatus",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="ids",
     *                     description="ids",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={"ids": {"1","2"}, "status": "Active"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function changeStatus(CommonBulkStatusChangeRequest $request) {
        try {
            $this->model->whereIn('id', $request->ids)->update(['status' => $request->status]);
            if ($request->status == 'Active') {
                foreach ($request->ids as $id) {
                    $product = Product::find($id);
                    if ($product->status == 'Active' && $product->notify_user == 1) {
                        Product::SendProductNotifications($product);
                    }
                }
            }
            return response()->json(setResponse([], ['message' => __('operator.products.status-changed')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/products/featuredProduct",
     *     tags={"Operator - Product Management"},
     *     summary="Mark product as featured",
     *     description="Mark product as featured",
     *     operationId="featuredProduct",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="id",
     *                     description="ID",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="is_featured",
     *                     description="Is Featured Product",
     *                     type="integer",
     *                     enum={"0", "1"}
     *                 ),
     *                 example={"id": 1, "is_featured": 1}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function featuredProduct(MarkProductAsFeaturedRequest $request) {
        try {
            $product = $this->model->find($request->id);
            $product->update([
                'is_featured' => $request->is_featured
            ]);
            return response()->json(setResponse([], ['message' => __('operator.products.updated')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

}
