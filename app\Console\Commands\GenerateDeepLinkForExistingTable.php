<?php

namespace App\Console\Commands;

use App\Models\TrainingCourseSubModuleHappyUnhappy;
use DBTableNames;
use Exception;
use Illuminate\Console\Command;
use App\Models\TrainingCourseSubmoduleDetails;
use App\Models\TrainingCourse;
use App\Services\DeeplinkService;

class GenerateDeepLinkForExistingTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create:deeplink';

    private DeeplinkService $deeplinkService;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate/Create deep link for existing tables in the system';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(DeeplinkService $deeplinkService)
    {
        parent::__construct();
        $this->deeplinkService = $deeplinkService;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $tables = [
            DBTableNames::TRAINING_COURSE_SUBMODULE_HAPPY_UNHAPPY,
            DBTableNames::TRAINING_COURSE_SUBMODULE_VIDEO_GUIDE,
            DBTableNames::TRAINING_COURSE_SUBMODULE_IMAGE_GALLERY,
            DBTableNames::TRAINING_COURSE_SUBMODULE_PRODUCT_LIST,
            DBTableNames::TRAINING_COURSE_SUBMODULE_DETAILS,
            DBTableNames::TRAINING_COURSE,
            DBTableNames::RESOURCES,
            DBTableNames::PRODUCTS
        ];

        try {
            foreach ($tables as $table) {
                $res = \DB::table($table)
                    ->where(function($query) {
                        $query->where('share_url', '')->orWhereNull('share_url');
                    })
                    ->whereNull('deleted_at')
                    ->get();
                $cnt = $res->count();
                for ($i = 0; $i < $cnt; $i++) {
                    $share_url = "";
                    $id = isset($res[$i]->id) && $res[$i]->id != null ? $res[$i]->id : null;
                    $submodule_id = isset($res[$i]->submodule_id) && $res[$i]->submodule_id != null ? $res[$i]->submodule_id : null;
                    switch ($table) {
                        case DBTableNames::TRAINING_COURSE_SUBMODULE_HAPPY_UNHAPPY:
			    $subModule = TrainingCourseSubmoduleDetails::find($submodule_id);
			    $operatorId = ($subModule->trainingCourse->master_user_id ?? null);
                            // $share_url = generateFirebaseDeepLink(url(route('trainingCourseSubmodule.happyUnhappyList', ['id' => $submodule_id])),$operatorId) . '?type=happyUnhappyList&id=' . $submodule_id;
                            $targetUrl = url(route('trainingCourseSubmodule.happyUnhappyList', ['id' => $submodule_id])). '?type=happyUnhappyList&id=' . $submodule_id;
                    
                            $deeplinkUrl = $this->deeplinkService->generateDeeplink([
                                'target_url' => $targetUrl,
                                'operator_id' =>$operatorId,
                                'type' => 'training_course',
                                'entity_id' => $submodule_id,
                                'entity_type' => 'TrainingCourse'
                            ]);
                            $share_url = $deeplinkUrl;
                            break;
                        case DBTableNames::TRAINING_COURSE_SUBMODULE_VIDEO_GUIDE:
			    $subModule = TrainingCourseSubmoduleDetails::find($submodule_id);
			    $operatorId = ($subModule->trainingCourse->master_user_id ?? null);
			    // $share_url = generateFirebaseDeepLink(url(route('trainingCourseSubmodule.videoGuideList', ['id' => $submodule_id])),$operatorId) . '?type=videoGuideList&id=' . $submodule_id;
                $targetUrl = url(route('trainingCourseSubmodule.videoGuideList', ['id' => $submodule_id])) . '?type=videoGuideList&id=' . $submodule_id;
                $deeplinkUrl = $this->deeplinkService->generateDeeplink([
                    'target_url' => $targetUrl,
                    'operator_id' => $operatorId,
                    'type' => 'training_course',
                    'entity_id' => $submodule_id,
                    'entity_type' => 'TrainingCourse'
                ]);
                $share_url = $deeplinkUrl;
                            break;
                        case DBTableNames::TRAINING_COURSE_SUBMODULE_IMAGE_GALLERY:
			    $subModule = TrainingCourseSubmoduleDetails::find($submodule_id);
			    $operatorId = ($subModule->trainingCourse->master_user_id ?? null);
			    // $share_url = generateFirebaseDeepLink(url(route('trainingCourseSubmodule.imageGalleryList', ['id' => $submodule_id])),$operatorId) . '?type=imageGalleryList&id=' . $submodule_id;
                            $targetUrl = url(route('trainingCourseSubmodule.imageGalleryList', ['id' => $submodule_id])) . '?type=imageGalleryList&id=' . $submodule_id;
                            $deeplinkUrl = $this->deeplinkService->generateDeeplink([
                                'target_url' => $targetUrl,
                                'operator_id' => $operatorId,
                                'type' => 'training_course',
                                'entity_id' => $submodule_id,
                                'entity_type' => 'TrainingCourse'
                            ]);
                            $share_url = $deeplinkUrl;
                            break;
                        case DBTableNames::TRAINING_COURSE_SUBMODULE_PRODUCT_LIST:
			    $subModule = TrainingCourseSubmoduleDetails::find($submodule_id);
			    $operatorId = ($subModule->trainingCourse->master_user_id ?? null);
			    // $share_url = generateFirebaseDeepLink(url(route('trainingCourseSubmodule.productList', ['id' => $submodule_id])),$operatorId) . '?type=productList&id=' . $submodule_id;
                            $targetUrl = url(route('trainingCourseSubmodule.productList', ['id' => $submodule_id])) . '?type=productList&id=' . $submodule_id;
                            $deeplinkUrl = $this->deeplinkService->generateDeeplink([
                                'target_url' => $targetUrl,
                                'operator_id' => $operatorId,
                                'type' => 'training_course',
                                'entity_id' => $submodule_id,
                                'entity_type' => 'TrainingCourse'
                            ]);
                            $share_url = $deeplinkUrl;
                            break;
                        case DBTableNames::TRAINING_COURSE_SUBMODULE_DETAILS:
			    $trainingCourse = TrainingCourse::find($res[$i]->training_course_id);
			    $operatorId = ($trainingCourse->master_user_id ?? null);
                            // $share_url = generateFirebaseDeepLink(url(route('trainingCourseSubmodule.getUploadVideo', ['id' => $id])),$operatorId) . '?type=getUploadVideo&id=' . $id;
                            $targetUrl = url(route('trainingCourseSubmodule.getUploadVideo', ['id' => $id])) . '?type=getUploadVideo&id=' . $id;
                            $deeplinkUrl = $this->deeplinkService->generateDeeplink([
                                'target_url' => $targetUrl,
                                'operator_id' => $operatorId,
                                'type' => 'training_course',
                                'entity_id' => $id,
                                'entity_type' => 'TrainingCourse'
                            ]);
                            $share_url = $deeplinkUrl;
                            break;
                        case DBTableNames::TRAINING_COURSE:
			    $operatorId = ($res[$i]->master_user_id ?? null);
                            // $share_url = generateFirebaseDeepLink(url(route('trainingCourse.show', ['id' => $id])),$operatorId) . '?type=trainingCourse&id=' . $id;
                            $targetUrl = url(route('trainingCourse.show', ['id' => $id])) . '?type=trainingCourse&id=' . $id;
                            $deeplinkUrl = $this->deeplinkService->generateDeeplink([
                                'target_url' => $targetUrl,
                                'operator_id' => $operatorId,
                                'type' => 'training_course',
                                'entity_id' => $id,
                                'entity_type' => 'TrainingCourse'
                            ]);
                            $share_url = $deeplinkUrl;
                            break;
                        case DBTableNames::RESOURCES:
			    $operatorId = ($res[$i]->master_user_id ?? null);
                            // $share_url = generateFirebaseDeepLink(url(route('resources.show', ['id' => $id])),$operatorId) . '?type=resources&id=' . $id;
                            $targetUrl = url(route('resources.show', ['id' => $id])) . '?type=resources&id=' . $id;
                            $deeplinkUrl = $this->deeplinkService->generateDeeplink([
                                'target_url' => $targetUrl,
                                'operator_id' => $operatorId,
                                'type' => 'resources',
                                'entity_id' => $id,
                                'entity_type' => 'Resources'
                            ]);
                            $share_url = $deeplinkUrl;
                            break;
                        case DBTableNames::PRODUCTS:
			    $operatorId = ($res[$i]->master_user_id ?? null);
                            // $share_url = generateFirebaseDeepLink(url(route('products.show', ['id' => $id])),$operatorId) . '?type=products&id=' . $id;
                            $targetUrl = url(route('products.show', ['id' => $id])) . '?type=products&id=' . $id;
                            $deeplinkUrl = $this->deeplinkService->generateDeeplink([
                                'target_url' => $targetUrl,
                                'operator_id' => $operatorId,
                                'type' => 'products',
                                'entity_id' => $id,
                                'entity_type' => 'Products'
                            ]);
                            $share_url = $deeplinkUrl;
                            break;
                        default:
                            break;
                    }
                    if ($share_url !== "") {
                        if ($submodule_id != null) {
                            \DB::table($table)->where('submodule_id', $submodule_id)->update(['share_url' => $share_url]);
                        }else if ($id != null) {
                            \DB::table($table)->where('id', $id)->update(['share_url' => $share_url]);
                        }
                    }
                }
            }
        } catch (Exception $e) {
            $this->info($e->getMessage());
        }
    }
}
