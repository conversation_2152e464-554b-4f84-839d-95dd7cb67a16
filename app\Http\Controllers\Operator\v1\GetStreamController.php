<?php

namespace App\Http\Controllers\Operator\v1;

use DB;
use App\Models\User;
use App\Models\MasterUser;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Services\GetStreamService;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;


class GetStreamController extends Controller
{
    public function CreateGetStreamUser(Request $request)
    {
        try {
            app(GetStreamService::class)->AddUserOnGetStream($request->query('userId'));
            return response()->json(setResponse([], ['message' =>'User Added']))->setStatusCode(Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
