<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Custom Deeplinks Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the custom deeplinks system
    | that replaces Firebase Dynamic Links.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Default Domain
    |--------------------------------------------------------------------------
    |
    | The default domain to use for generating deeplinks when no operator-specific
    | domain is configured.
    |
    */
    'domain' => env('CUSTOM_DEEPLINK_DOMAIN', env('APP_URL')),

    /*
    |--------------------------------------------------------------------------
    | Default Fallback URL
    |--------------------------------------------------------------------------
    |
    | The default URL to redirect to when a deeplink is not found, expired,
    | or when the mobile app is not installed.
    |
    */
    'default_fallback' => env('CUSTOM_DEEPLINK_DEFAULT_FALLBACK', 'https://skillsbase.io'),

    /*
    |--------------------------------------------------------------------------
    | Short Code Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for short code generation including length and character set.
    |
    */
    'code_length' => env('CUSTOM_DEEPLINK_CODE_LENGTH', 8),
    
    /*
    |--------------------------------------------------------------------------
    | Analytics Configuration
    |--------------------------------------------------------------------------
    |
    | Enable or disable analytics tracking for deeplink clicks.
    |
    */
    'analytics_enabled' => env('CUSTOM_DEEPLINK_ANALYTICS_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | Cache Configuration
    |--------------------------------------------------------------------------
    |
    | Cache TTL for deeplink resolution to improve performance.
    |
    */
    'cache_ttl' => env('CUSTOM_DEEPLINK_CACHE_TTL', 3600), // 1 hour

    /*
    |--------------------------------------------------------------------------
    | Mobile App Schemes
    |--------------------------------------------------------------------------
    |
    | Configuration for mobile app URL schemes and fallback app store URLs.
    |
    */
    'mobile_schemes' => [
        'ios' => [
            'default_scheme' => 'skillsbase',
            'fallback_store' => 'https://apps.apple.com/app/skillsbase',
        ],
        'android' => [
            'default_scheme' => 'skillsbase',
            'fallback_store' => 'https://play.google.com/store/apps/details?id=com.skillsbase.app',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    |
    | Security settings for deeplink validation and protection.
    |
    */
    'security' => [
        'allowed_domains' => [
            // Add allowed domains for target URLs to prevent open redirects
            parse_url(env('APP_URL'), PHP_URL_HOST),
            'skillsbase.io',
            'skillsbase.com',
        ],
        'validate_target_urls' => env('CUSTOM_DEEPLINK_VALIDATE_URLS', true),
        'max_redirects_per_ip' => env('CUSTOM_DEEPLINK_MAX_REDIRECTS_PER_IP', 100),
        'rate_limit_window' => env('CUSTOM_DEEPLINK_RATE_LIMIT_WINDOW', 3600), // 1 hour
    ],

    /*
    |--------------------------------------------------------------------------
    | Feature Flags
    |--------------------------------------------------------------------------
    |
    | Feature flags to control rollout and testing of the custom deeplinks system.
    |
    */
    'features' => [
        'enabled' => env('CUSTOM_DEEPLINKS_ENABLED', false),
        'migration_mode' => env('CUSTOM_DEEPLINKS_MIGRATION_MODE', false),
        'analytics_detailed' => env('CUSTOM_DEEPLINKS_DETAILED_ANALYTICS', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Cleanup Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for automatic cleanup of expired and unused deeplinks.
    |
    */
    'cleanup' => [
        'auto_cleanup_enabled' => env('CUSTOM_DEEPLINKS_AUTO_CLEANUP', true),
        'cleanup_after_days' => env('CUSTOM_DEEPLINKS_CLEANUP_AFTER_DAYS', 365),
        'batch_size' => env('CUSTOM_DEEPLINKS_CLEANUP_BATCH_SIZE', 1000),
    ],
];
