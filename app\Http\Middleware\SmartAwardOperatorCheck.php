<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Contracts\Auth\Factory as Auth;
use Illuminate\Support\Facades\Auth as Auth1;
use Illuminate\Http\Response;
use  App\Models\User;
use  App\Models\MasterUser;
use Carbon\Carbon;

class SmartAwardOperatorCheck
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */

    public function handle($request, Closure $next)
    {
        if($request->hasHeader('api-key')){
            $operatorId = config('constants.smart_award_operator_id');
            $operatorApiKey = MasterUser::where('id',$operatorId)->first();
            $requestApiKey = $request->header('api-key');
            if($requestApiKey == $operatorApiKey->external_api_key){
                $response = $next($request);
            }else{
                return response()->json(setErrorResponse('Unauthorize Access!', Response::HTTP_UNAUTHORIZED))
                            ->setStatusCode(Response::HTTP_UNAUTHORIZED);
            }  
        }else{
            return response()->json(setErrorResponse('Unauthorize Access!', Response::HTTP_UNAUTHORIZED))
                            ->setStatusCode(Response::HTTP_UNAUTHORIZED);
        }
        return $response;        
    }
}
