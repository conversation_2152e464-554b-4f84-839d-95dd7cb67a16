<?php

namespace App\Http\Controllers\Operator\v1;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\MasterUser;
use App\Models\WebNotifications;
use App\Models\MasterUserInviteLinks;
use App\Models\AssignUserInviteLinks;
use App\Http\Resources\Operator\v1\AdminProfileResource;
use App\Http\Resources\Operator\v1\MasterUserInviteLinkResource;
use App\Http\Resources\Operator\v1\AssignUserInviteLinkResource;
use App\Http\Requests\Operator\v1\ChangePasswordRequest;
use App\Http\Requests\Operator\v1\AdminProfileRequest;
use App\Http\Requests\Operator\v1\InviteLinksDeleteRequest;
use App\Http\Requests\Admin\v1\CommonBulkStatusChangeRequest;
use App\Repositories\Operator\v1\MasterUserRepository;
use App\Repositories\Operator\v1\MasterUserInviteLinksRepository;
use App\Repositories\Operator\v1\AssignUserInviteLinksRepository;

class UserController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    protected $model;

    protected $user_repository;

    public function __construct() {
        $this->model = new MasterUser();
        $this->user_repository = new MasterUserRepository($this->model);
        $this->masterUserInviteLinkModel = new MasterUserInviteLinks();
        $this->masterUserInviteLinksRepository = new MasterUserInviteLinksRepository($this->masterUserInviteLinkModel);
        $this->assignUserInviteLinkModel = new AssignUserInviteLinks();
        $this->assignUserInviteLinksRepository = new AssignUserInviteLinksRepository($this->assignUserInviteLinkModel);
    }

    /**
     * @OA\Put(
     *     path="/operator/changePassword",
     *     tags={"Operator - User"},
     *     summary="Master User change password process",
     *     description="Master User change password process",
     *     operationId="changePassword",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="old_password",
     *                     description="Old Password",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="password",
     *                     description="Password",
     *                     type="string"
     *                 ),
     *                  @OA\Property(
     *                     property="password_confirmation",
     *                     description="Confirm Password",
     *                     type="string"
     *                 ),
     *                 example={"old_password": "Test@123", "password": "Indianic@123", "password_confirmation": "Indianic@123"}
     *              )
     *          )
     *      ),
     *     security={
     *         {"Operator": {}}
     *     },
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     * )
     */
    public function changePassword(ChangePasswordRequest $request) {
        try {
            Auth::guard('operator')->user()->update(['password' => bcrypt($request->password)]);
            // Add Web notification
            $data['master_user_id'] =  Auth::guard('operator')->user()->id;
            $data['type'] = 'Password';
            WebNotifications::storeNotification($data);
            return response()->json(setResponse([], ['message' => __('operator.UserController.resetPassword')]))
                            ->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/myAccount",
     *     tags={"Operator - User"},
     *     summary="Edit Account",
     *     description="Edit Account",
     *     operationId="myAccount",
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function myAccount() {
        try {
            $admin = auth()->guard('operator')->user();
            return response()->json(setResponse(new AdminProfileResource($admin)))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Put(
     *     path="/operator/myAccount",
     *     tags={"Operator - User"},
     *     summary="Update Account",
     *     description="Update Account",
     *     operationId="updateMyAccount",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="company_name",
     *                     description="Company Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="contact_no",
     *                     description="Contact No",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="address",
     *                     description="Address",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="latitude",
     *                     description="Latitude",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="longitude",
     *                     description="Longitude",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="website",
     *                     description="Website",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="enable_manager_email",
     *                     description="Enable Manager Email",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="enable_unique_id",
     *                     description="Enable Unique Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="unique_id_name",
     *                     description="Unique Id Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="enable_custom_contact_info",
     *                     description="Enable Custom Contact Info",
     *                     type="integer"
     *                 ),
     *                 example={"company_name": "Rogahn PLC", "contact_no": "+44 7700 900077", "address": "St Hermistone EH16 6UF", "latitude": "44.463005", "longitude": "110.366479", "website": "https://www.skillsbase.com", "enable_manager_email": 0, "enable_unique_id": 1, "unique_id_name": "Team ID", "enable_custom_contact_info": 1}
     *              )
     *          )
     *      ),
     *     security={
     *         {"Operator": {}}
     *     },
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     * )
     */
    public function updateMyAccount(AdminProfileRequest $request) {
        try {
            if(isset($this->form_check) && $this->form_check == 0){
                $data = $request->only(['enable_manager_email', 'enable_unique_id', 'enable_custom_contact_info']);
            }else{
                $data = $request->only(['company_name', 'contact_no', 'alternate_email', 'address', 'latitude', 'longitude', 'website', 'enable_manager_email', 'enable_unique_id', 'unique_id_name', 'enable_custom_contact_info']);
            }
            Auth::guard('operator')->user()->update($data);
            return response()->json(setResponse([], ['message' => __('operator.UserController.profileUpdate')]))
                            ->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
    * @OA\Get(
    *     path="/operator/getInviteLink",
    *     tags={"Operator - My Account"},
    *     summary="Get Invite Link",
    *     description="Get Invite Link",
    *     operationId="getInviteLink",
    *     @OA\Response(response=200, description="OK"),
    *     @OA\Response(response=201, description="Created successfully!"),
    *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
    *     @OA\Response(response=401, description="Unauthorize Access!"),
    *     @OA\Response(response=404, description="Not Found."),
    *     @OA\Response(response=500, description="Something went wrong!"),
    *     security={
    *         {"Operator": {}}
    *     },
    * )
    */
    public function getInviteLink() {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $getInviteLink = MasterUserInviteLinks::where('master_user_id',$operatorId)->orderBy('created_at','desc')->first();
            return ($getInviteLink) ?
            response()->json(setResponse(new MasterUserInviteLinkResource($getInviteLink),['message' => __('operator.UserController.linkFound')]))->setStatusCode(Response::HTTP_OK) :
                response()->json(setResponse(['invite_link'=>''],['message' => __('operator.UserController.linkFound')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/generateInviteLink",
     *     tags={"Operator - My Account"},
     *     summary="Generate Invite Link",
     *     description="Generate Invite Link",
     *     operationId="generateInviteLink",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function generateInviteLink(Request $request) {
        try {
            $request->merge(['shortLink' => $request->dynamicLinkInfo['link']]);
            $data = $request->all();
            $generateLink = $this->masterUserInviteLinksRepository->generateInviteLink($data);
            return response()->json(setResponse(new MasterUserInviteLinkResource($generateLink), ['message' => __('operator.UserController.linkGenerated')]))
                        ->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/getAllInviteLinks",
     *     tags={"Operator - My Account"},
     *     summary="Get All Invite Links",
     *     description="Get All Invite Links",
     *     operationId="getAllInviteLinks",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="endDate",
     *                     description="End Date",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="startDate",
     *                     description="Start Date",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 example={"page": 1,"per_page": 100,"search_key": "2022-11-23","sort_by": "created_at","order_by": "asc"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getAllInviteLinks(Request $request) {
        try {
            $getAllInviteLinks = $this->masterUserInviteLinksRepository->getListing($request->all());
            return MasterUserInviteLinkResource::collection($getAllInviteLinks->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/getAllAssignedUsers",
     *     tags={"Operator - My Account"},
     *     summary="Get All Assigned User",
     *     description="Get All Assigned User",
     *     operationId="getAllAssignedUsers",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 example={"page": 1,"per_page": 100,"search_key": "","sort_by": "last_logged_in_at","order_by": "desc","filters": {"user_id" : "","name": "","email" : ""}, "link_id":1, "isExport": 0, "exportFields": {"id", "name","email", "created_at", "status"}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getAllAssignedUsers(Request $request) {
        try {
            $getAllAssignedUsers = $this->assignUserInviteLinksRepository->getAssignedUsersListing($request->all());
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $linkList =  MasterUserInviteLinks::where('master_user_id', $operatorId)->pluck('id')->toArray();
            if(in_array($request->link_id, $linkList)){

                if ($request->isExport) {
                    return $this->assignUserInviteLinksRepository->exportCsv($getAllAssignedUsers->get(), $request->exportFields);
                }
                return AssignUserInviteLinkResource::collection($getAllAssignedUsers->paginate($request->per_page));
            }else{
                return response()->json(setErrorResponse(__('You cannot access this record')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/deleteInviteLinks",
     *     tags={"Operator - My Account"},
     *     summary="Delete invite links",
     *     description="Delete invite links",
     *     operationId="deleteInviteLinks",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="ids",
     *                     description="Ids of invite links",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 example={"ids": {4,6}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function deleteInviteLinks(InviteLinksDeleteRequest $request) {
        try {
            $ids = is_array($request->ids)? $request->ids: [];
            foreach ($ids as $id) {
                $linkDetail = $this->masterUserInviteLinkModel->withCount('assigned_user')->where('id',$id)->first();
                if($linkDetail->assigned_user_count > 0){
                    return response()->json(setErrorResponse(__('operator.UserController.notDeleteLink')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            }
            $this->masterUserInviteLinkModel->destroy($ids);
            return response()->json(setResponse([], ['message' => __('operator.UserController.deleteLinks')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/changeLinkStatus",
     *     tags={"Operator - My Account"},
     *     summary="Change Link Status",
     *     description="Change Link Status",
     *     operationId="changeLinkStatus",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="ids",
     *                     description="ids",
     *                     type="array",
     *                     @OA\Items()
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={"ids": {"1","2"}, "status": "Active"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Updated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function changeLinkStatus(CommonBulkStatusChangeRequest $request) {
        try {
            $this->masterUserInviteLinkModel->whereIn('id', $request->ids)->update(['status' => $request->status]);
            return response()->json(setResponse([], ['message' => __('operator.UserController.updateStatus')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
