<?php
/**
     * @OA\Post(
     *     path="/api/verifyLoginEmailLinkToken",
     *     tags={"Mobile - Auth"},
     *     summary="Verify login with Email link token",
     *     description="Verify login with Email link token",
     *     operationId="verifyLoginEmailLinkToken",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="email",
     *                     description="Email",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="email_token",
     *                     description="Email token",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="device_type",
     *                     description="Device type: Iphone,Android",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="device_token",
     *                     description="Device Token",
     *                     type="string"
     *                 ),
     *                 example={"email": "<EMAIL>", "email_token": "fa37d29c6a6356c6a7e512b7ac51917574b1493ca", "device_type": "Android", "device_token": "pfe9dgXujGVqDVypYOZb2b6TMrGWBP"}
     *              )
     *          )
     *      ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     * )
     */