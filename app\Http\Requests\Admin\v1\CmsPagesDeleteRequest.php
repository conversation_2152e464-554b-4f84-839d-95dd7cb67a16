<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;

class CmsPagesDeleteRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {
        return [
            'ids' => 'required|array',
             'ids.*' => 'required|int'
        ];
    }

    public function messages() : array
    {
        return [
            'ids.required' => __('Page id is required.'),
            'ids.*' => __('Page id must be integer.'),
        ];
    }
}
