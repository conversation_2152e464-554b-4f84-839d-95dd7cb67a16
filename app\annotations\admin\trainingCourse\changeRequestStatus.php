<?php
/**
     * @OA\Post(
     *     path="/admin/trainingCourse/changeRequestStatus",
     *     tags={"Admin - Training Course"},
     *     summary="Change Training Course(s) Request status",
     *     description="Change Training Course(s) Request status",
     *     operationId="changeRequestStatus",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="ids",
     *                     description="ids",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Pending", "Completed", "Rejected", "Inprogress"}
     *                 ),
     *                 example={"ids": {"1","2"}, "status": "Pending"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */