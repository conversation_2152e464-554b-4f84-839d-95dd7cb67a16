<?php

namespace App\Http\Controllers\Admin\v1;

use Illuminate\Http\Response;
use App\Models\Resources;
use App\Repositories\Admin\v1\ResourceRepository;
use App\Http\Requests\Admin\v1\CommonListingRequest;
use App\Http\Requests\Admin\v1\CommonBulkStatusChangeRequest;
use App\Http\Resources\Admin\v1\ResourceListingResource;

class ResourceController extends Controller
{
    protected $model;   
    protected $repository; 
    
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->model = new Resources();
        $this->repository = new ResourceRepository($this->model);
    }
    
    /**
     * List resources
     *
     * @return \Illuminate\Http\Response
     */
    public function getListing(CommonListingRequest $request) {
        try{
            $resources = $this->repository->getListing($request->all());
            if ($request->isExport) {
                return $this->repository->exportCsv($resources->get(), $request->exportFields);
            }
            return ResourceListingResource::collection($resources->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Change resource(s) status
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function changeStatus(CommonBulkStatusChangeRequest $request) {
        try{
            $this->model->whereIn('id', $request->ids)->update(['status' => $request->status]);
            if($request->status == 'Active') {
                foreach($request->ids as $id) {
                    $resource = Resources::find($id);
                    Resources::SendResourceNotifications($resource);
                }
            }
            return response()->json(setResponse([], ['message' => __('admin.resources.status-changed')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

}
