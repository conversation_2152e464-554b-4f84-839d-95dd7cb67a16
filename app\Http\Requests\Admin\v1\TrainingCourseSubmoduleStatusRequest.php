<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;

class TrainingCourseSubmoduleStatusRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array
    {
        return [
            'id' => 'required|numeric|exists:'.\DBTableNames::TRAINING_COURSE_SUBMODULE_DETAILS.',id',
            'status' => 'required|in:Active,Inactive',
        ];
    }
    
    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'id.required' => __('Sub module id is required.'),
            'id.numeric' => __('Sub module id is invalid.'),
            'status.required' => __('Status is required.'),
        ];
    }
    
}
