<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTrainingCourseSubmoduleSelfAssessmentAnswersAttemptsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('training_course_submodule_self_assessment_answers_attempts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('training_course_id');
            $table->unsignedBigInteger('module_id');
            $table->unsignedBigInteger('submodule_id');
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('assessor_id');
            $table->tinyInteger('assessed_by_assessor')->default(0);
            $table->unsignedBigInteger('question_id')->nullable();
            $table->text('question_name')->nullable();
            $table->enum('question_type', [
                'text', 'rating', 'single', 'multiple', 'calculated_single', 'location',
                'image', 'video', 'document', 'cableMatrix', 'locationSelection'
            ])->nullable();
            $table->json('question_options')->nullable()->comment('Options available when attempted assessment');
            $table->integer('total_questions')->nullable()->comment('total Q when attempted assessment');
            $table->longText('option_id')->nullable();
            $table->longText('answer')->nullable();
            $table->tinyInteger('rating')->nullable();
            $table->string('latitude')->nullable();
            $table->string('longitude')->nullable();
            $table->longText('media_data')->nullable();
            $table->boolean('is_new')->default(0)->comment('0=No, 1=Yes');
            $table->unsignedBigInteger('master_user_id');
            $table->string('type')->default('SA');
            $table->string('attempt');
            $table->dateTime('attempt_date')->nullable();
            $table->timestamps();

            $table->foreign('training_course_id', 'tcs_saaa_training_course_id')->references('id')->on(DBTableNames::TRAINING_COURSE)->onDelete('cascade');
            $table->foreign('module_id', 'tcs_saaa_module_id')->references('id')->on(DBTableNames::TRAINING_COURSE_MODULES)->onDelete('cascade');
            $table->foreign('submodule_id', 'tcs_saaa_submodule_id')->references('id')->on(DBTableNames::TRAINING_COURSE_SUBMODULE_DETAILS)->onDelete('cascade');
            $table->foreign('user_id', 'tcs_saaa_user_id')->references('id')->on(DBTableNames::USERS)->onDelete('cascade');
            $table->foreign('assessor_id', 'tcs_saaa_assessor_id')->references('id')->on(DBTableNames::USERS)->onDelete('cascade');
            $table->foreign('question_id', 'tcs_saaa_question_id')->references('id')->on(DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_QUESTIONS)->onDelete('cascade');
            $table->foreign('master_user_id', 'tcs_saaa_master_user_fk')->references('id')->on(DBTableNames::MASTER_USERS)->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('training_course_submodule_self_assessment_answers_attempts');
    }
}
