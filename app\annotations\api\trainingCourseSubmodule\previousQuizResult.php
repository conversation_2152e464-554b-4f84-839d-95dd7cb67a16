<?php
/**
     * @OA\Post(
     *     path="/api/trainingCourseSubmodule/previousQuizResult",
     *     tags={"Mobile - Training Course Submodule"},
     *     summary="Get Previous Quiz Result",
     *     description="Get Previous Quiz Result",
     *     operationId="previousQuizResult",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="submodule_id",
     *                     description="Submodule Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="send_email",
     *                     description="Send Email",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="send_to",
     *                     description="Send To Email",
     *                     type="string"
     *                 ),
     *                 example={"submodule_id": 315, "send_email": 1, "send_to": "<EMAIL>"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */