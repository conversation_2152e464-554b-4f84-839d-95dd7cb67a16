<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddRemoveProgressCalculationColumnInTrainingCourseSubmoduleDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('training_course_submodule_details', 'remove_progress_calculation')) {
            Schema::table('training_course_submodule_details', function (Blueprint $table) {
                $table->boolean('remove_progress_calculation')->default(0);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('training_course_submodule_details', function (Blueprint $table) {
            $table->dropColumn('remove_progress_calculation');
        });
    }
}
