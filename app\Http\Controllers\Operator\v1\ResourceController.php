<?php

namespace App\Http\Controllers\Operator\v1;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\Resources;
use App\Repositories\Operator\v1\ResourceRepository;
use App\Http\Requests\Operator\v1\ResourcesRequest;
use App\Http\Requests\Operator\v1\CommonListingRequest;
use App\Http\Requests\Operator\v1\CommonBulkStatusChangeRequest;
use App\Http\Resources\Operator\v1\ResourceDetailResource;
use App\Http\Resources\Operator\v1\ResourceListingResource;
use App\Http\Resources\Operator\v1\AssignResourceListingResource;
use App\Services\DeeplinkService;

class ResourceController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    protected $model;
    
    protected $repository; 

    public function __construct() {
        $this->deeplinkService =new DeeplinkService();
        $this->model = new Resources();
        $this->repository = new ResourceRepository($this->model);
    }
    
    /**
     * @OA\Post(
     *     path="/operator/resources/getAssignResourceListing",
     *     tags={"Operator - Resources Management"},
     *     summary="Listing of resources assign for Product/News",
     *     description="Listing of resources assign for Product/News",
     *     operationId="getAssignResourceListing",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 example={"per_page": 10, "page": 1, "search_key": "", "sort_by": "name", "order_by": "asc"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getAssignResourceListing(CommonListingRequest $request) {
        try {
            $resources = $this->repository->getListing($request->all());
            return AssignResourceListingResource::collection($resources->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * @OA\Post(
     *     path="/operator/resources/getListing",
     *     tags={"Operator - Resources Management"},
     *     summary="List resources",
     *     description="List resources",
     *     operationId="getListing",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 example={"per_page": 10, "page": 1, "search_key": "", "sort_by": "name", "order_by": "asc", "filters": {"name": "", "id": "", "resource_type": "", "status": ""}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getListing(CommonListingRequest $request) {
        try {
            $resources = $this->repository->getListing($request->all());
            return ResourceListingResource::collection($resources->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * @OA\Post(
     *     path="/operator/resources",
     *     tags={"Operator - Resources Management"},
     *     summary="Store resource",
     *     description="Store resource",
     *     operationId="store",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="description",
     *                     description="Description",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="resource_type",
     *                     description="Resource Type",
     *                     type="string",
     *                     enum={"image", "pdf", "youtube", "vimeo", "source"}
     *                 ),
     *                 @OA\Property(
     *                     property="resource_id",
     *                     description="Resource ID",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="tags",
     *                     description="Tags",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 @OA\Property(
     *                     property="products",
     *                     description="Product IDs",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="news",
     *                     description="News IDs",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 example={"name": "Resource Name", "description": "Description of Resource", "resource_type": "image", "resource_id": "filename.jpg", "tags": {"tag1", "tag2"}, "status": "Active", "products": {1, 2}, "news": {1, 2}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function store(ResourcesRequest $request) {   
        try {
            $data = $request->all();
            $data['master_user_id'] = getOperatorId();
            $data['tags'] = (!empty($request->tags) ? implode(',', $request->tags) : '');
            $resource = $this->model->create($data);
            
            if($resource->status == 'Active' && $resource->notify_user == 1){
                Resources::SendResourceNotifications($resource);
            }
            // Generate QR Code of Training Course
            $actualURL = url(route('resources.show', ['id' => $resource->id])) . '?type=resources&id=' . $resource->id;
            // $dynamicUrl = generateFirebaseDeepLink($actualURL, $data['master_user_id']) . '?type=resources&id=' . $resource->id;
            $deeplinkUrl =$this->deeplinkService->generateDeeplink([
                                    'target_url' => $actualURL,
                                    'operator_id' => $data['master_user_id'],
                                    'type' => 'resources',
                                    'entity_id' => $resource->id,
                                    'entity_type' => 'Resources'
                                ]);
            $dynamicUrl=$deeplinkUrl;
            $qrCode = generateQRCode($dynamicUrl);
            $file = storage_path('/qrcodes/') . $qrCode;
            \Storage::disk('s3')->put(getResourcePath() . $qrCode, file_get_contents($file));
            $resource->update([
                'qr_code' => $qrCode
            ]);
            
            // Sync Products and News
            $resource->news()->sync($data['news']);
            $resource->products()->sync($data['products']);
            return response()->json(setResponse([], ['message' => __('operator.resources.created')]))->setStatusCode(Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * @OA\Get(
     *     path="/operator/resources/{id}",
     *     tags={"Operator - Resources Management"},
     *     summary="Get resource",
     *     description="Get resource",
     *     operationId="show",
     *     @OA\Parameter(
     *         description="id",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function show($id) {
        try {
            $resource = $this->model->find($id);
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $resourceList = $this->model->where('master_user_id', $operatorId)->pluck('id')->toArray();
            if(in_array($id, $resourceList)){
                return ($resource) ?
                        (new ResourceDetailResource($resource)) :
                    response()->json(setErrorResponse(__('operator.resources.not-found')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }else{
                return response()->json(setErrorResponse(__('You cannot access this record')))->setStatusCode(Response::HTTP_NOT_FOUND); 
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * @OA\Put(
     *     path="/operator/resources/{id}",
     *     tags={"Operator - Resources Management"},
     *     summary="Update resource",
     *     description="Update resource",
     *     operationId="update",
     *     @OA\Parameter(
     *         description="Id of resource to update",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="description",
     *                     description="Description",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="resource_type",
     *                     description="Resource Type",
     *                     type="string",
     *                     enum={"image", "pdf", "youtube", "vimeo", "source"}
     *                 ),
     *                 @OA\Property(
     *                     property="resource_id",
     *                     description="Resource ID",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="tags",
     *                     description="Tags",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 @OA\Property(
     *                     property="products",
     *                     description="Product IDs",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="news",
     *                     description="News IDs",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 example={"name": "Resource Name", "description": "Description of Resource", "resource_type": "image", "resource_id": "filename.jpg", "tags": {"tag1", "tag2"}, "status": "Active", "products": {1, 2}, "news": {1, 2}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function update(ResourcesRequest $request, $id) {   
        try {
            $resource = $this->model->find($id);
            if ($resource) {
                $data = $request->all();
                $data['tags'] = (!empty($request->tags) ? implode(',', $request->tags) : '');
                
                if ($resource->resource_id != $data['resource_id']) {
                    if (in_array($resource->resource_type, ['image', 'pdf', 'source'])) { // Remove old file
                        deleteFileFromS3(getResourcePath() . $resource->resource_id);
                        $data['size'] = null;
                    }
                    if (in_array($data['resource_type'], ['image', 'pdf', 'source'])) { // Move uploaded file and get file size
                        moveFileToResource($data['resource_id']);
                        $data['size'] = \Storage::disk('s3')->size(getResourcePath() . $data['resource_id']) / 1000;
                    }
                }
                unset($data['qr_code']);
                $resource->update($data);
                
                // Sync Products and News
                $resource->news()->sync($data['news']);
                $resource->products()->sync($data['products']);
                
                return response()->json(setResponse([], ['message' => __('operator.resources.updated')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setErrorResponse(__('operator.resources.not-found')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * @OA\Post(
     *     path="/operator/resources/delete",
     *     tags={"Operator - Resources Management"},
     *     summary="Delete resources",
     *     description="Delete resources",
     *     operationId="destroy",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="ids",
     *                     description="Ids of Resources",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 example={"ids": {1, 2}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function destroy(Request $request) {
        try {
            $ids = is_array($request->ids)? $request->ids: [];
            $this->model->destroy($ids);
            return response()->json(setResponse([], ['message' => __('operator.resources.deleted')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * @OA\Post(
     *     path="/operator/resources/changeStatus",
     *     tags={"Operator - Resources Management"},
     *     summary="Change resource(s) status",
     *     description="Change resource(s) status",
     *     operationId="changeStatus",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="ids",
     *                     description="ids",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={"ids": {"3","4"}, "status": "Active"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function changeStatus(CommonBulkStatusChangeRequest $request) {
        try {
            $this->model->whereIn('id', $request->ids)->update(['status' => $request->status]);
            if($request->status == 'Active'){
                foreach($request->ids as $id){
                    $resource = Resources::find($id);
                    if($resource->status == 'Active' && $resource->notify_user == 1){
                        Resources::SendResourceNotifications($resource);
                    }
                }
            }
            return response()->json(setResponse([], ['message' => __('operator.resources.status-changed')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

}
