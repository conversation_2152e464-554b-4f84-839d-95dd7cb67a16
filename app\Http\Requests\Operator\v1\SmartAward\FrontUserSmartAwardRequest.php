<?php

namespace App\Http\Requests\Operator\v1\SmartAward;

use App\Http\Requests\CustomFormRequest;
use App\Models\MasterUser;
use App\Models\UserRelation;
use App\Models\User;
class FrontUserSmartAwardRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {
        \Validator::extend('email_exists',
            function($attribute, $value, $parameters)
            {
                $operatorId = 64;

                $existUser = UserRelation::whereMasterUserId($operatorId)->get()->pluck('user_id')->toArray();

                $userExist = User::whereEmail($value)->first();
                if($userExist){
                    return !in_array($userExist->id, $existUser) ? true : false;
                }else{
                    return true;
                }
            }
        );

        return [
            "name" => 'required',
            "email" => 'required|email_exists:'.$this->id,
            "password" => 'sometimes|min:8|max:20|regex:/(?=.*([A-Z]))(?=.*([a-z]))(?=.*([~`\!@#\$%\^&\*\(\)_\{\}\[\]]))/',
            "disableMailSend" => 'boolean',
        ];
    }

    public function messages() : array
    {
        return [
            'name.required' => __('The name field is required.'),
            'email.email_exists' => __("Email already exists"),
            'email.required' => __('Email is required.'),
            'email.email' => __('Invalid email.'),
            'password.required_if' => __('Please enter password.'),
            'password.min' => __('Password must be at least 8 characters.'),
            'password.max' => __('Password may not be greater than 20 characters.'),
            'password.regex' => __('Password must have atleast one capital, small and special character.'),
        ];
    }
}
