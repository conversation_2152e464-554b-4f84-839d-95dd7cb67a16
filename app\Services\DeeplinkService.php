<?php

namespace App\Services;

use App\Models\CustomDeeplink;
use App\Models\whiteLabelSetting;
use App\Models\MasterUser;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class DeeplinkService
{
    // Character set excluding confusing characters (0, O, I, l)
    private const CHARSET = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789';
    private const CODE_LENGTH = 8;
    private const MAX_GENERATION_ATTEMPTS = 10;

    /**
     * Generate a new custom deeplink.
     */
    public function generateDeeplink(array $params): string
    {
        $shortCode = $this->generateShortCode();
        
        $operatorConfig = $this->getOperatorConfiguration($params['operator_id'] ?? null);
        
        $deeplink = CustomDeeplink::create([
            'short_code' => $shortCode,
            'operator_id' => $params['operator_id'] ?? null,
            'target_url' => $params['target_url'],
            'deeplink_type' => $params['type'] ?? 'general',
            'entity_id' => $params['entity_id'] ?? null,
            'entity_type' => $params['entity_type'] ?? null,
            'ios_package_name' => $operatorConfig['ios_package_name'],
            'android_package_name' => $operatorConfig['android_package_name'],
            'ios_app_store_id' => $operatorConfig['ios_app_store_id'] ?? '1555697368', // Default SkillsBase App Store ID
            'fallback_url' => $params['fallback_url'] ?? $operatorConfig['fallback_url'],
            'expires_at' => $params['expires_at'] ?? null,
            'metadata' => $params['metadata'] ?? null,
            'universal_link' => $params['fallback_link'] ?? 'skillsbaselink',
        ]);

        return $this->buildDeeplinkUrl($shortCode, $params['operator_id'] ?? null);
    }

    /**
     * Resolve a deeplink by short code.
     */
    public function resolveDeeplink(string $shortCode): ?CustomDeeplink
    {
        return Cache::remember(
            "deeplink:{$shortCode}",
            config('deeplinks.cache_ttl', 3600),
            function () use ($shortCode) {
                return CustomDeeplink::where('short_code', $shortCode)
                    ->active()
                    ->first();
            }
        );
    }

    /**
     * Track a click on a deeplink.
     */
    public function trackClick(string $shortCode, array $metadata = []): void
    {
        $deeplink = CustomDeeplink::where('short_code', $shortCode)->first();
        
        if ($deeplink) {
            $deeplink->incrementClickCount($metadata);
            
            // Clear cache after tracking
            Cache::forget("deeplink:{$shortCode}");
            
            Log::info('Deeplink clicked', [
                'short_code' => $shortCode,
                'operator_id' => $deeplink->operator_id,
                'type' => $deeplink->deeplink_type,
                'metadata' => $metadata
            ]);
        }
    }

    /**
     * Generate a unique short code.
     */
    public function generateShortCode(): string
    {
        $attempts = 0;
        
        do {
            $code = '';
            for ($i = 0; $i < self::CODE_LENGTH; $i++) {
                $code .= self::CHARSET[random_int(0, strlen(self::CHARSET) - 1)];
            }
            $attempts++;
            
            if ($attempts >= self::MAX_GENERATION_ATTEMPTS) {
                throw new \RuntimeException('Unable to generate unique short code after ' . self::MAX_GENERATION_ATTEMPTS . ' attempts');
            }
            
        } while (CustomDeeplink::where('short_code', $code)->exists());

        return $code;
    }

    /**
     * Validate if a short code is valid format.
     */
    public function isValidShortCode(string $code): bool
    {
        if (strlen($code) !== self::CODE_LENGTH) {
            return false;
        }
        
        for ($i = 0; $i < strlen($code); $i++) {
            if (strpos(self::CHARSET, $code[$i]) === false) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Get operator-specific configuration.
     */
    private function getOperatorConfiguration(?int $operatorId): array
    {
        if (!$operatorId) {
            return $this->getDefaultConfiguration();
        }

        $operator = MasterUser::with('whiteLabelSettings')->find($operatorId);
        
        if (!$operator || !$operator->whiteLabelSettings) {
            return $this->getDefaultConfiguration();
        }

        $settings = $operator->whiteLabelSettings;
        
        return [
            'ios_package_name' => $settings->ios_package_name,
            'android_package_name' => $settings->android_package_name,
            'ios_app_store_id' => $settings->ios_app_store_id,
            'fallback_url' => $settings->fallback_link ?? $this->getDefaultFallback(),
            'domain' => $settings->custom_deeplink_domain ?? config('app.url'),
        ];
    }

    /**
     * Get default configuration.
     */
    private function getDefaultConfiguration(): array
    {
        return [
            'ios_package_name' => env('IOS_BUNDLE_ID'),
            'android_package_name' => env('ANDROID_PACKAGE_NAME'),
            'ios_app_store_id' => null,
            'fallback_url' => $this->getDefaultFallback(),
            'domain' => config('app.url'),
        ];
    }

    /**
     * Get default fallback URL.
     */
    private function getDefaultFallback(): string
    {
        return config('deeplinks.default_fallback', 'https://skillsbase.io');
    }

    /**
     * Build the full deeplink URL.
     */
    private function buildDeeplinkUrl(string $shortCode, ?int $operatorId = null): string
    {
        $domain = config('app.url');
        
        if ($operatorId) {
            $operator = MasterUser::with('whiteLabelSettings')->find($operatorId);
            if ($operator && $operator->whiteLabelSettings && $operator->whiteLabelSettings->custom_deeplink_domain) {
                $domain = $operator->whiteLabelSettings->custom_deeplink_domain;
            }
        }

        return rtrim($domain, '/') . '/v1/dl/' . $shortCode;
    }

    /**
     * Bulk generate deeplinks for existing content.
     */
    public function bulkGenerateDeeplinks(string $entityType, array $entityIds, ?int $operatorId = null): array
    {
        $results = [];
        
        foreach ($entityIds as $entityId) {
            try {
                $targetUrl = $this->buildTargetUrl($entityType, $entityId);
                
                $deeplink = $this->generateDeeplink([
                    'target_url' => $targetUrl,
                    'operator_id' => $operatorId,
                    'type' => $entityType,
                    'entity_id' => $entityId,
                    'entity_type' => $this->getEntityModelName($entityType),
                ]);
                
                $results[$entityId] = $deeplink;
                
            } catch (\Exception $e) {
                Log::error('Failed to generate deeplink', [
                    'entity_type' => $entityType,
                    'entity_id' => $entityId,
                    'operator_id' => $operatorId,
                    'error' => $e->getMessage()
                ]);
                
                $results[$entityId] = null;
            }
        }
        
        return $results;
    }

    /**
     * Build target URL for entity type.
     */
    private function buildTargetUrl(string $entityType, int $entityId): string
    {
        $routes = [
            'training_course' => 'trainingCourse.show',
            'product' => 'products.show',
            'resource' => 'resources.show',
            'training_course_submodule' => 'trainingCourseSubmodule.getUploadVideo',
        ];

        $route = $routes[$entityType] ?? null;
        
        if (!$route) {
            throw new \InvalidArgumentException("Unknown entity type: {$entityType}");
        }

        return url(route($route, ['id' => $entityId]));
    }

    /**
     * Get model name for entity type.
     */
    private function getEntityModelName(string $entityType): string
    {
        $models = [
            'training_course' => 'TrainingCourse',
            'product' => 'Product',
            'resource' => 'Resource',
            'training_course_submodule' => 'TrainingCourseSubmoduleDetails',
        ];

        return $models[$entityType] ?? ucfirst($entityType);
    }
}
