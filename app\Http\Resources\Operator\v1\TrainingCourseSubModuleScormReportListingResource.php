<?php

namespace App\Http\Resources\Operator\v1;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\TrainingCourseSubmoduleDetails;

class TrainingCourseSubModuleScormReportListingResource extends JsonResource
{

    public function toArray($request)
    {
        $totalTime = null;
        if($this->total_time != ''){
            $timeInSeconds = $this->total_time;
            $secs = $timeInSeconds % 60;
            $hrs = $timeInSeconds / 60;
            $mins = $hrs % 60;
            $hrs = $hrs / 60;
            $totalTime = ((int)$hrs == 0) ? (int)$mins . " minutes, " . (int)$secs . " seconds" : (int)$hrs . " hours, " . (int)$mins . " minutes," . (int)$secs . " seconds"; 
        }

        return [
            "id" => $this->id,
            "name" => $this->name ?? null,
            "training_course_name" => $this->training_course_name ?? null,
            "module_name" => $this->module_name ?? null,
            "submodule_name" => $this->submodule_name ?? null,
            "completion_status" => $this->completion_status ?? null,
            "success_status" => $this->success_status ?? null,
            "total_time" => $totalTime,
            "updated_at" => date_format($this->updated_at, 'Y-m-d H:i:s')
        ];
    }
}
