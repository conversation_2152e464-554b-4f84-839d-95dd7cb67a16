<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;

class TrainingCourseRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array
    {
        return [
            'title' => 'required',
            'primary_image' => 'required',
            'display_order' => 'required|numeric',
            'keywords' => 'required|array|min:1',
            'status' => 'required|in:Active,Inactive',
            //'publish_now' => 'required|in:true,false',
            'schedule_at' => 'required_if:publish_now,false'
            //'schedule_at' => 'required_if:publish_now,false|date_format:Y-m-d H:i:s'
        ];
    }
    
    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'title.required' => __('Title is required.'),
            'primary_image.required' => __('Primary image is required.'),
            'display_order.required' => __('Display order is required.'),
            'display_order.numeric' => __('Display order is invalid.'),
            'keywords.required' => __('Keywords are required.'),
            'keywords.array' => __('Keywords are invalid.'),
            'status.required' => __('Status is required.'),
            //'publish_now.required' => __('Please select course publish time.'),
            'schedule_at.required_if' => __('Schedule time is required.'),
            //'schedule_at.date_format' => __('Schedule datetime format is invalid.')
        ];
    }
    
}
