<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddLocationMatrixSelectionToPracticalAssessmentAnswers extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('training_course_submodule_practical_assessment_answers', function (Blueprint $table) {
            $table->json('location_matrix_selection')->nullable()->comment('Store location matrix selection data')->after('cable_matrix_selection');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('training_course_submodule_practical_assessment_answers', function (Blueprint $table) {
            $table->dropColumn('location_matrix_selection');
        });
    }
}
