<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;
use DBTableNames;

class TeamMemberRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array
    {
        return [
            'name' => 'required|max:150',
            'email' => 'required|email|max:255|unique:' . DBTableNames::MASTER_USERS . ',email,' . request()->segment(4) . ',id,deleted_at,NULL',
            'role_id' => 'required|exists:' . DBTableNames::ROLES . ',id',
            'status' => 'required|in:Active,Inactive',
        ];
    }
    
    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'name.required' => __('Please enter name.'),
            'name.max' => __('Name may not be greater than 150 characters.'),
            'email.required' => __('Please enter email.'),
            'email.email' => __('Email must be a valid email address (E.g.: <EMAIL> ).'),
            'email.max' => __('Email may not be greater than 255 characters.'),
            'email.unique' => __('Email has already been taken.'),
            'role_id.required' => __('Please select role.'),
            'role_id.exists' => __('Invalid role selected.'),
            'status.required' => __('Please select status.'),
            'status.in' => __('Invalid status.'),
        ];
    }
    
}
