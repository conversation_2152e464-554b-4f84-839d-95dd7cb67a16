<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;

class PushConfigurationRequest extends CustomFormRequest {

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array {
	$request = $this;
	$rules = [
	    'operator_id' => 'required|int',
	    'is_push_notification_on' => 'required|boolean'
	];
	if (isset($request->is_push_notification_on) && $request->is_push_notification_on) {
	    $rules = array_merge($rules, [
		'push_certificate' => 'file',
		'ios_passphrase' => 'required',
		'apns_topic' => 'required',
//		'push_url' => 'required',
		'android_server_key' => 'required',
	    ]);
	}
	return $rules;
    }

    public function messages(): array {
	return [
	    'operator_id.required' => __('Operator Id is required.'),
	    'is_push_notification_on.required' => __('Push Notification Feature setting is required.'),
	    'push_certificate.required' => __('Push Certificate is required.'),
	    'ios_passphrase.required' => __('Ios Passphrase is required.'),
	    'apns_topic.required' => __('Apns Topic is required.'),
	    'push_url.required' => __('Push Url is required.'),
	    'android_server_key.required' => __('Android Server Key is required.'),
	];
    }

}
