<?php
/**
     * @OA\Post(
     *     path="/admin/userGroups/getUserGroupList",
     *     tags={"Admin - User Groups"},
     *     summary="Get All User Group List ",
     *     description="Get All User Group List ",
     *     operationId="getUserGroupList",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="User Group Name",
     *                     type="string"
     *                 ),
    *                 @OA\Property(
     *                     property="status",
     *                     description="User Group Status",
     *                     type="string",
                           enum={"Active", "Inactive"}
     *                 ),
     *                 example={
                        "per_page": 10,
                        "page": 1,
                        "search_key": "My Group",
                        "filter": {
                            "name": "my group 1",
                            "manager_email": "<EMAIL>",
                            "unique_id": "UID456",
                            "status": "Active"
                        },
                        "sort_by": "name",
                        "order_by": "asc",
                        "isExport": 0
                    }
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="User Group list genetated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */ 