<?php

namespace App\Models;
use App\Models\User;
use App\Models\MasterUser;
use App\Models\TrainingCourse;
use App\Models\TrainingCourseModules;
use Illuminate\Database\Eloquent\Model;
use App\Models\TrainingCourseSubmoduleDetails;
use App\Models\TrainingCourseSubModulePracticalAssessmentMedia;
use App\Models\TrainingCourseSubModulePracticalAssessmentQuestion;

class TrainingCourseSubModuleSelfAssessmentAnswersAttempt extends Model
{
    protected $table = 'training_course_submodule_self_assessment_answers_attempts';

    protected $fillable = [
        'training_course_id', 'module_id', 'submodule_id', 'master_user_id', 'user_id', 'assessor_id', 'question_id', 'answer', 'is_new','option_id','latitude','longitude','media_data','rating','assessed_by_assessor','type','attempt','attempt_date','question_name','question_type','question_options','total_questions'
    ];

     // Relations
     public function trainingCourse() {
        return $this->belongsTo(TrainingCourse::class, 'training_course_id', 'id');
    }
    
    public function module() {
        return $this->belongsTo(TrainingCourseModules::class, 'module_id', 'id');
    }
    
    public function subModule() {
        return $this->belongsTo(TrainingCourseSubmoduleDetails::class, 'submodule_id', 'id');
    }
    
    public function masterUser() {
        return $this->belongsTo(MasterUser::class, 'master_user_id', 'id');
    }
    
    public function user() {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function assessor() {
        return $this->belongsTo(User::class, 'assessor_id', 'id');
    }

    public function question() {
        return $this->belongsTo(TrainingCourseSubModulePracticalAssessmentQuestion::class, 'question_id', 'id');
    }

    public function question_with_trash() {
        return $this->belongsTo(TrainingCourseSubModulePracticalAssessmentQuestion::class, 'question_id', 'id')->withTrashed();
    }

    public function question_data() {
        return $this->belongsTo(TrainingCourseSubModulePracticalAssessmentQuestion::class, 'question_id', 'id')->whereNull('category_id')->withTrashed();
    }

    public function media() {
        return $this->hasMany(TrainingCourseSubModulePracticalAssessmentMedia::class, 'answer_id', 'id');
    }

    public function mediaExtra() {
        return $this->hasMany(TrainingCourseSubModulePracticalAssessmentMedia::class, 'answer_id', 'id');
    }
}
