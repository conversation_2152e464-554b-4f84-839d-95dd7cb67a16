<?php

namespace App\Console\Commands;

use App\Jobs\FlushRecordJob;
use App\Models\TrainingCourseModuleProgress;
use App\Models\TrainingCourseProgress;
use App\Models\TrainingCourseSubmoduleDetails;
use App\Models\TrainingCourseSubmoduleProgress;
use App\Models\TrainingCourseSubModuleQuiz;
use App\Models\TrainingCourseSubModuleQuizResults;
use DB;
use Illuminate\Console\Command;

class QuizAutoResetOnFailCron extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'quiz:aut-reset-duration-on-fail';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Quiz auto reset duration on fail';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $trainingCourseSubModuleQuiz = TrainingCourseSubModuleQuiz::where('auto_reset_duration_on_fail', '>', 0)->pluck('submodule_id');
        $TrainingCourseSubModuleQuizResults = TrainingCourseSubModuleQuizResults::select('id', 'user_id', 'submodule_id', 'is_pass', 'master_user_id')->whereIn('submodule_id', $trainingCourseSubModuleQuiz)->groupBy('user_id', 'submodule_id')->where('is_pass', 0)->orderBy('id', 'DESC')->get();

        foreach ($TrainingCourseSubModuleQuizResults as $value) {
            $Result = TrainingCourseSubModuleQuizResults::where(['submodule_id' => $value->submodule_id, 'user_id' => $value->user_id])->where('is_pass', 1)->first();
            if (empty($Result)) {
                $quizResult[] = $value;
            }
        }
        if (isset($quizResult) && !empty($quizResult)) {
            foreach ($quizResult as $newValue) {
                $resetTime = TrainingCourseSubModuleQuiz::where('submodule_id', $newValue->submodule_id)->value('auto_reset_duration_on_fail');
                $maxAttemtpsOnFail = TrainingCourseSubModuleQuiz::where('submodule_id', $newValue->submodule_id)->value('max_attempts_before_fail');
                $lastTime = TrainingCourseSubModuleQuizResults::where(['submodule_id' => $newValue->submodule_id, 'user_id' => $newValue->user_id])->orderBy('id', 'DESC')->value('created_at');
                $CurrentUtcDate = date('Y-m-d H:i:s');
                $startTime = date("Y-m-d H:i:s", strtotime('-1 minutes', strtotime($CurrentUtcDate)));
                $endTime = date("Y-m-d H:i:s", strtotime('+1 minutes', strtotime($CurrentUtcDate)));
                $actualResetTime = date("Y-m-d H:i:s", strtotime('+' . $resetTime . ' minutes', strtotime($lastTime)));
                if ($actualResetTime >= $startTime && $actualResetTime <= $endTime) {
                    $count = TrainingCourseSubModuleQuizResults::where(['submodule_id' => $newValue->submodule_id, 'user_id' => $newValue->user_id])->count();
                    if ($count >= $maxAttemtpsOnFail) {
                        //Then Flush Data For This User $newValue->user_id For This Module $newValue->submodule_id
                        $data = [
                            'user_id' => $newValue->user_id,
                            'id' => $newValue->submodule_id,
                            'type' => 'submodule',
                        ];

                        $operatorId = $newValue->master_user_id;
                        DB::transaction(function () use ($operatorId, $data) {

                            $subModuleId = $data['id'];
                            $trainingCourseId = TrainingCourseSubmoduleDetails::whereId($subModuleId)->pluck('training_course_id')->first();

                            $subModules = TrainingCourseSubmoduleProgress::whereTrainingCourseId($trainingCourseId)->whereUserId($data['user_id'])->whereMasterUserId($operatorId)->where('submodule_id', '>=', $subModuleId)->where('submodule_progress', '!=', 0);
                            $subModuleList = $subModules->get();
                            $moduleIdArr = $subModules->pluck('module_id')->toArray();
                            $moduleIds = array_unique($moduleIdArr);

                            // Update Reset flag
                            TrainingCourseProgress::whereTrainingCourseId($trainingCourseId)->whereUserId($data['user_id'])->whereMasterUserId($operatorId)->update(['is_reset' => 1, 'is_new' => 0]);

                            (new TrainingCourseSubmoduleProgress)->resetProgress($subModuleList);

                            // Module progress
                            foreach ($moduleIds as $module) {
                                $data = ['training_course_id' => $trainingCourseId, 'user_id' => $data['user_id'], 'module_id' => $module];
                                (new TrainingCourseModuleProgress)->calculateModuleProgress($data);
                            }

                            // Training course progress
                            $tData = ['training_course_id' => $trainingCourseId, 'user_id' => $data['user_id']];
                            (new TrainingCourseProgress)->calculateTrainingCourseProgress($tData);
                        });

                        $userLastattempt = TrainingCourseSubModuleQuizResults::where(['submodule_id' => $data['id'], 'user_id' => $data['user_id']])->orderBy('id', 'DESC')->first();
                        $autoReset = 0;
                        if (!empty($userLastattempt)) {
                            if ($userLastattempt->is_pass != 1) {
                                $count = TrainingCourseSubModuleQuizResults::where(['submodule_id' => $data['id'], 'user_id' => $data['user_id']])->count();
                                if ($count < $maxAttemtpsOnFail) {
                                    $autoReset = 0;
                                } else {
                                    $autoReset = ($resetTime > 0 ? $resetTime : 0);
                                }
                            }
                        }
                        $autoResetQuizDuration = ($autoReset) ? $autoReset : 0;
                        TrainingCourseSubModuleQuizResults::where('submodule_id', $data['id'])->where('user_id', $data['user_id'])->delete();
			$headerData = [
			    'guard' => 'api',
			    'user_id' => $newValue->user_id,
			    'extra' => '',
			];
			app('queue')->createPayloadUsing(function () use ($headerData) {
			    return ['headerData' => $headerData];
			});
			dispatch(new \App\Jobs\FlushRecordJob($newValue->user_id, $newValue->submodule_id, $autoResetQuizDuration));
                    }
                }
            }
        }
    }
}
