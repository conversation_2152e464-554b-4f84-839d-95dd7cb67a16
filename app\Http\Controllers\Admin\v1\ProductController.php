<?php

namespace App\Http\Controllers\Admin\v1;

use Illuminate\Http\Response;
use App\Models\Product;
use App\Repositories\Admin\v1\ProductRepository;
use App\Http\Requests\Admin\v1\CommonListingRequest;
use App\Http\Requests\Admin\v1\CommonBulkStatusChangeRequest;
use App\Http\Resources\Admin\v1\ProductListingResource;

class ProductController extends Controller
{
    protected $model;
    protected $repository;
    
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->model = new Product();
        $this->repository = new ProductRepository($this->model);
    }
    
    /**
     * List products
     *
     * @return \Illuminate\Http\Response
     */
    public function getListing(CommonListingRequest $request) {
        try {
            $products = $this->repository->getListing($request->all());
            if ($request->isExport) {
                return $this->repository->exportCsv($products->get(), $request->exportFields);
            }
            return ProductListingResource::collection($products->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * Change product(s) status
     *
     * @return \Illuminate\Http\Response
     */
    public function changeStatus(CommonBulkStatusChangeRequest $request) {
        try {
            $this->model->whereIn('id', $request->ids)->update(['status' => $request->status]);
            if($request->status == 'Active'){
                foreach($request->ids as $id){
                    $product = Product::find($id);
                    Product::SendProductNotifications($product);
                }
            }
            return response()->json(setResponse([], ['message' => __('admin.products.status-changed')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
