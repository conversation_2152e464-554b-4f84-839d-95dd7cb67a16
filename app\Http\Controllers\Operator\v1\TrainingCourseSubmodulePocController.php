<?php

namespace App\Http\Controllers\Operator\v1;

use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Models\HotspotUploaderMedia;
use App\Models\TrainingCourseSubmoduleDetails;
use App\Models\TrainingCourseSubmoduleHotspotUploaderProgress;
use App\Repositories\Operator\v1\TrainingCourseSubModuleHotspotProgressRepository;
use App\Repositories\Operator\v1\TrainingCourseSubModuleHotspotUploaderMediaRepository;
use App\Http\Requests\Operator\v1\TrainingCourseSubmodulePocUserProgressRequest;
use App\Http\Requests\Operator\v1\TrainingCourseSubmodulePocManageCommentRequest;
use App\Http\Requests\Operator\v1\TrainingCourseSubmoduleChangeRegionStatusRequest;
use App\Http\Requests\Operator\v1\TrainingCourseSubmodulePocGeneralCommentRequest;
use App\Http\Requests\Operator\v1\TrainingCourseSubModulePocListingRequest;
use App\Http\Resources\Operator\v1\TrainingCourseJobsUserResponseResource;
use App\Http\Resources\Operator\v1\TrainingCourseJobsResponseResource;
use App\Models\TrainingCourseSubmoduleProgress;
use App\Models\TrainingCourseModuleProgress;
use App\Models\TrainingCourseProgress;
use App\Models\Notifications;
use App\Models\User;

class TrainingCourseSubmodulePocController extends Controller {

    protected $hotspotMediaModel;
    protected $hotspotMediaRepository;
    protected $hotspotUploaderProgressModel;
    protected $hotspotUploaderProgressRepository;

    public function __construct() {
        $this->hotspotMediaModel = new HotspotUploaderMedia();
        $this->hotspotMediaRepository = new TrainingCourseSubModuleHotspotUploaderMediaRepository($this->hotspotMediaModel);
        $this->hotspotUploaderProgressModel = new TrainingCourseSubmoduleHotspotUploaderProgress();
        $this->hotspotUploaderProgressRepository = new TrainingCourseSubModuleHotspotProgressRepository($this->hotspotUploaderProgressModel);
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourseSubmodule/pocResponses/getListing",
     *     tags={"Operator - Training Course Submodule POC Response"},
     *     summary="Get All Hotspot Responses",
     *     description="Get All Hotspot Responses",
     *     operationId="index",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="submodule_id",
     *                     description="Submodule Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 example={"submodule_id": 1, "per_page": 10, "page": 1, "search_key": "", "sort_by": "name", "order_by": "asc", "filters": {"uname": "", "gname": "", "status": ""}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function index(TrainingCourseSubModulePocListingRequest $request) {
        try {
            $data = $this->hotspotUploaderProgressRepository->getListing($request->all());
            return TrainingCourseJobsResponseResource::collection($data->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourseSubmodule/pocResponses/userResponse/getListing",
     *     tags={"Operator - Training Course Submodule POC Response"},
     *     summary="Get All User Responses for Specific Progress",
     *     description="Get All User Responses for Specific Progress",
     *     operationId="userResponsesListing",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="hotspot_uploader_id",
     *                     description="Hotspot Progress Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 example={"hotspot_uploader_id": 1, "per_page": 10, "page": 1, "sort_by": "", "order_by": "asc"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Role Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function userResponsesListing(TrainingCourseSubmodulePocUserProgressRequest $request) {
        try {
            $hotspotData = $this->hotspotMediaRepository->getUserHotspotUploaderListing($request->all());
            $hotspotProgress = $this->hotspotUploaderProgressModel->find($request->hotspot_uploader_id);
            if (!empty($hotspotProgress->user)) {
                return response()->json(setResponse([
                            'user' => $hotspotProgress->user->name,
                            'comment' => $hotspotProgress->comment,
                            'ratings' => $hotspotProgress->ratings,
                            'status' => $hotspotProgress->status,
                            'data' => TrainingCourseJobsUserResponseResource::collection($hotspotData->paginate($request->per_page))
                        ]))->setStatusCode(Response::HTTP_OK);
            } else {
                return response()->json(setResponse([], ['message' => __('operator.jobs.record-not-found')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourseSubmodule/pocResponses/region/comment",
     *     tags={"Operator - Training Course Submodule POC Response"},
     *     summary="Add/Edit Region Comment",
     *     description="Add/Edit Region Comment",
     *     operationId="manageComment",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="id",
     *                     description="Region ID",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="description",
     *                     description="Description",
     *                     type="string"
     *                 ),
     *                 example={"id": 1, "description": "Manager's comment goes here"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Category Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function manageComment(TrainingCourseSubmodulePocManageCommentRequest $request) {
        try {
            $hotspot = $this->hotspotMediaModel->find($request->id);
            if ($hotspot) {
                $hotspot->update(['description' => $request->description]);
                return response()->json(setResponse([], ['message' => __('operator.jobs.region.updated')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setResponse([], ['message' => __('operator.jobs.region.not-found')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourseSubmodule/pocResponses/region/changeStatus",
     *     tags={"Operator - Training Course Submodule POC Response"},
     *     summary="Change Status of  Region Comment",
     *     description="Change Status of Region Comment",
     *     operationId="changeStatus",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="id",
     *                     description="Region ID",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status: Approved/Rejected",
     *                     type="string",
     *                     enum={"Approved", "Rejected"}
     *                 ),
     *                 example={"id": 1, "status": "Approved"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Category Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function changeStatus(TrainingCourseSubmoduleChangeRegionStatusRequest $request) {
        try {
            $hotspot = $this->hotspotMediaModel->find($request->id);
            if ($hotspot) {
                $hotspot->update(['status' => $request->status]);

                // Change the status of progress too
                $hotspotProgress = $this->hotspotUploaderProgressModel->find($hotspot->hotspot_uploader_id);
                $submodule = TrainingCourseSubmoduleDetails::find($hotspotProgress->submodule_id);
                $photoHotspotIds = $submodule->photoHotspots()->pluck('id')->toArray();

                $rejectedHotspotIds = $hotspotProgress
                                ->select(\DBTableNames::TRAINING_COURSE_SUBMODULE_HOTSPOT_UPLOADER_PROGRESS . '.id', 'hum.id', 'hum.status', 'hum.photo_hotspot_id')
                                ->leftJoin(\DBTableNames::HOTSPOT_UPLOADER_MEDIA . ' as hum', function ($query) {
                                    $query->on('hum.hotspot_uploader_id', '=', \DBTableNames::TRAINING_COURSE_SUBMODULE_HOTSPOT_UPLOADER_PROGRESS . '.id');
                                    $query->where('hum.id', '=', \DB::raw('(select hum2.id as id from `hotspot_uploader_media` as hum2 where hum2.hotspot_uploader_id = `hum`.`hotspot_uploader_id` AND hum2.photo_hotspot_id = `hum`.`photo_hotspot_id` order by hum2.id DESC limit 1)'));
                                })->where('hum.status', '!=', 'Pending')->whereIn('hum.photo_hotspot_id', $photoHotspotIds)->pluck('hum.status', 'hum.id')->toArray();
                if (count($rejectedHotspotIds) === count($photoHotspotIds) && in_array('Rejected', $rejectedHotspotIds)) { // Check if any one previous hotspot is Rejected and none other is Pending then mark the progress as Rejected
                    $hotspotProgress->status = 'Rejected';
                    $hotspotProgress->save();
                    $this->statusNotification('Rejected', $hotspotProgress, $submodule);
                }

                $approvedHotspotIds = $hotspotProgress->hotspotUploader()->where('status', 'Approved')->pluck('photo_hotspot_id')->toArray();

                return response()->json(setResponse([], ['message' => __('operator.jobs.region.change-status')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setResponse([], ['message' => __('operator.jobs.region.not-found')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Delete(
     *     path="/operator/trainingCourseSubmodule/pocResponses/region/comment/{id}",
     *     tags={"Operator - Training Course Submodule POC Response"},
     *     summary="Delete Region Comment",
     *     description="Delete Region Comment",
     *     operationId="destroyComment",
     *     @OA\Parameter(
     *         description="Id of region to delete description",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Role Deleted successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function destroyComment($id) {
        try {
            $hotspot = $this->hotspotMediaModel->find($id);
            if ($hotspot) {
                $hotspot->update(['description' => null]);
                return response()->json(setResponse([], ['message' => __('operator.jobs.region.deleted')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setResponse([], ['message' => __('operator.jobs.region.not-found')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourseSubmodule/pocResponses/userResponse",
     *     tags={"Operator - Training Course Submodule POC Response"},
     *     summary="Add General Comment and Ratings",
     *     description="Add General Comment and Ratings",
     *     operationId="userResponse",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="id",
     *                     description="Region ID",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="comment",
     *                     description="Comment",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="ratings",
     *                     description="5",
     *                     type="integer"
     *                 ),
     *                 example={"id": 1, "comment": "General comment goes here", "ratings": 5}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Category Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function userResponse(TrainingCourseSubmodulePocGeneralCommentRequest $request) {
        try {
            $hotspotProgress = $this->hotspotUploaderProgressModel->find($request->id);
            if ($hotspotProgress) {


                $pocImages = $request->data;
                foreach ($pocImages as $poc) {
                    $this->hotspotMediaModel->find($poc['id'])->update(['description' => $poc['description'], 'status' => $poc['status']]);
                }

                $statusArr = $this->hotspotMediaModel->where('hotspot_uploader_id', $request->id)->pluck('status')->toArray();

                $statusVal = array_count_values($statusArr);
                if (isset($statusVal['Approved'])) {
                    if (count($statusArr) == $statusVal['Approved']) {
                        $status = 'Approved';
                    } else {
                        $status = 'Rejected';
                    }
                } else {
                    $status = 'Rejected';
                }

                $hotspotProgress->update([
                    'status' => $status,
                    'comment' => $request->comment,
                    'ratings' => $request->ratings
                ]);

                $submodule = TrainingCourseSubmoduleDetails::find($hotspotProgress->submodule_id);
                //Calculate Submodule Progress
                $submoduleData = TrainingCourseSubmoduleDetails::select('enable_time_spend', 'condition', 'time_spent')->find($hotspotProgress->submodule_id);
                $progressData = TrainingCourseSubmoduleProgress::where(['submodule_id' => $hotspotProgress->submodule_id, 'user_id' => $hotspotProgress->user_id])->first();

                if ($hotspotProgress->status == 'Approved') { // Check if all hotspots are Approved then mark the progress as Approved
                    $this->statusNotification('Approved', $hotspotProgress, $submodule);
                    if ($submoduleData['enable_time_spend'] == 1) {
                        if ($submoduleData['condition'] == 'and') {
                            if ($progressData->time_spent >= $progressData->total_spent) {
                                $progress = 100;
                            } else {
                                $timeSpent = (int) ((100 * $progressData->time_spent) / $progressData->total_spent);
                                $progress = (int) (($timeSpent + 100) / 2);
                            }
                        } else {
                            $progress = 100;
                        }
                    } else {
                        $progress = 100;
                    }
                } else if ($hotspotProgress->status == 'Rejected') { // Rejected
                    $this->statusNotification('Rejected', $hotspotProgress, $submodule);
                    if ($submoduleData['enable_time_spend'] == 1) {
                        $timeSpent = (int) ((100 * $progressData->time_spent) / $progressData->total_spent);
                        if ($submoduleData['condition'] == 'and') {
                            $progress = (int) ($timeSpent / 2);
                        } else {
                            $progress = $timeSpent;
                        }
                    } else {
                        $progress = 0;
                    }
                }

                $progress = (int) ($progress > 100 ? 100 : $progress);
                $progressData->update(['submodule_progress' => $progress, 'visited_child' => $progressData->total_child]);

                // Module progress
                $mData = ['training_course_id' => $progressData->training_course_id, 'module_id' => $progressData->module_id, 'user_id' => $hotspotProgress->user_id];
                (new TrainingCourseModuleProgress)->calculateModuleProgress($mData);

                // Training course progress
                $tData = ['training_course_id' => $progressData->training_course_id, 'user_id' => $hotspotProgress->user_id];
                (new TrainingCourseProgress)->calculateTrainingCourseProgress($tData);

                return response()->json(setResponse([], ['message' => __('operator.jobs.updated')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setResponse([], ['message' => __('operator.jobs.not-found')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function statusNotification($status, $hotspotProgress, $submodule) {
        try {
            // Push Notification
            $registeredUser = User::whereId($hotspotProgress->user_id)->first();
            $mutable = $registeredUser->pocStatusNotificationCheck->is_on ?? 0;

            $pocStatusNotificationJob = (new \App\Jobs\PocStatusNotificationJob($registeredUser, $mutable, $hotspotProgress, auth()->guard('operator')->id(), $status, $submodule))->delay(env('QUEUE_JOB_DELAY_TIME'));
            dispatch($pocStatusNotificationJob);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

}
