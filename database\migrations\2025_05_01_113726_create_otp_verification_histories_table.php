<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOtpVerificationHistoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('otp_verification_histories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('master_users')->onDelete('cascade');
            $table->string('email')->index();
            $table->string('otp')->index();
            $table->timestamp('otp_verification_date')->nullable();
            $table->enum('user_type', ['Operator', 'Admin','User'])->default(null);
            $table->string('action')->nullable();
            $table->enum('status', ['success', 'failed','sent','resend'])->default('failed');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('otp_verification_histories');
    }
}
