<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTypeColumnInTrainingCourseSubmodulePracticalAssessmentAnswersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('training_course_submodule_practical_assessment_answers', function (Blueprint $table) {
            if (!Schema::hasColumn('training_course_submodule_practical_assessment_answers', 'type')) {
                $table->string('type')->default('PA')->after('master_user_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('training_course_submodule_practical_assessment_answers', function (Blueprint $table) {
            if (Schema::hasColumn('training_course_submodule_practical_assessment_answers', 'type')) {
                $table->dropColumn('type');
            }
        });
    }
}
