<?php
/**
     * @OA\Post(
     *     path="/api/updateProfileDetails",
     *     tags={"Mobile - User"},
     *     summary="Update Profile Details",
     *     description="Update Profile Details",
     *     operationId="updateProfileDetails",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="manager_email",
     *                     description="Manager Email",
     *                     type="string"
     *                 ),
     *                  @OA\Property(
     *                     property="unique_id",
     *                     description="Unique Id",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                      description="Photo",
     *                      property="photo",
     *                      type="file",
     *                 ),
     *                 @OA\Property(
     *                     property="device_type",
     *                     description="device_type Iphone,Android",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="device_token",
     *                     description="device_token",
     *                     type="string"
     *                 )
     *              )
     *          )
     *      ),
     *     security={
     *         {"User": {}}
     *     },
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     * )
     */