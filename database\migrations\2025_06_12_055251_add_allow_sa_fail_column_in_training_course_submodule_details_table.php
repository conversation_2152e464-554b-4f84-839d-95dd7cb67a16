<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAllowSaFailColumnInTrainingCourseSubmoduleDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('training_course_submodule_details', function (Blueprint $table) {
            if (!Schema::hasColumn('training_course_submodule_details', 'allow_sa_fail')) {
                $table->tinyInteger('allow_sa_fail')->default(0)->after('allow_sa_override');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('training_course_submodule_details', function (Blueprint $table) {
            if (Schema::hasColumn('training_course_submodule_details', 'allow_sa_fail')) {
                $table->dropColumn('allow_sa_fail');
            }
        });
    }
}
