<?php
/**
     * @OA\Post(
     *     path="/api/products/feedback",
     *     tags={"Mobile - Products"},
     *     summary="Products Feedback",
     *     description="Products Feedback",
     *     operationId="feedback",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 type="object",
     *                  @OA\Property(
     *                      property="product_id",
     *                      description="Product Id",
     *                      type="integer"
     *                  ),
     *                  @OA\Property(
     *                      property="contact_no",
     *                      description="Contact No",
     *                      type="string"
     *                  ),
     *                  @OA\Property(
     *                      property="message",
     *                      description="Message",
     *                      type="string"
     *                  ),
     *                  @OA\Property(
     *                      property="media[]",
     *                      description="Media (Image/PDF/Video)",
     *                      type="array",
     *                      @OA\Items(
     *                          type="file",
     *                          @OA\Property(
     *                              description="Media (Image/PDF/Video)",
     *                              type="file"
     *                          )
     *                      ),
     *                  )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */