<?php

namespace App\Http\Controllers\Operator\v1;

use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\TrainingCourse;
use App\Models\FilterData;
use App\Http\Requests\Operator\v1\MediaRequest;
use App\Http\Requests\Operator\v1\ImageRequest;
use App\Http\Requests\Operator\v1\VimeoMp4UrlRequest;
use Pawlox\VideoThumbnail\Facade\VideoThumbnail;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;
use App\Http\Resources\Operator\v1\FavouriteFilterResource;
use Spatie\PdfToImage\Pdf;

class GlobalController extends Controller
{
    protected $filterData;
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->filterData = new FilterData();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */

    /**
     * @OA\Post(
     *     path="/operator/uploadImage",
     *     tags={"Operator - Global Methods - Media Upload"},
     *     summary="Image upload",
     *     description="Image upload",
     *     operationId="uploadImage",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="type",
     *                     description="types : modules/submodules/courses/users/resources",
     *                     type="string"
     *                 ),
     *                  @OA\Property(
     *                     property="id",
     *                     description="[modules / submodules / courses: Training Course Id, profile: User Id]",
     *                     type="string",
     *                 ),
     *                 @OA\Property(
     *                      description="Image",
     *                      property="image",
     *                      type="file",
     *                 ),
     *                 @OA\Property(
     *                      property="images[]",
     *                      description="Array of Images for Products and News",
     *                      type="array",
     *                      @OA\Items(
     *                          type="file",
     *                          @OA\Property(
     *                              description="Image",
     *                              type="file"
     *                          )
     *                      ),
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */ 
    public function uploadImage(ImageRequest $request)
    {
        try {
            $response = [];
            $requestData = $request->all();
            $dataId = isset($requestData['id']) ? $requestData['id'] : '';
            $fromImage = $requestData['type'];
            $uploadedImageName = '';
            
            if ($request->image) {
                if($dataId != ''){
                    $courseData = TrainingCourse::find($dataId);
                    if ($courseData && $fromImage == 'modules') {

                        $file = \Storage::disk('s3')->put(getTrainingCourseModulePath(), $request->file('image'));
                        $name = explode('/', $file);
                        $uploadedImageName = $name[count($name)-1];
                        $response['image'] = $uploadedImageName;
                        $uploadedImageName = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCourseModulePath(), '/') . '/' . $uploadedImageName;
                        $response['url'] = $uploadedImageName;
                    } elseif($courseData && $fromImage == 'submodules') {
                        $file = \Storage::disk('s3')->put(getTrainingCourseSubmodulePath(), $request->file('image'));
                        $name = explode('/', $file);
                        $uploadedImageName = $name[count($name)-1];
                        $response['image'] = $uploadedImageName;
                        $uploadedImageName = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCourseSubmodulePath(), '/') . '/' . $uploadedImageName;
                        $response['url'] = $uploadedImageName;
                    } elseif($courseData && $fromImage == 'courses') {
                        $file = \Storage::disk('s3')->put(getTrainingCoursePath(), $request->file('image'));
                        $name = explode('/', $file);
                        $uploadedImageName = $name[count($name)-1];
                        $response['image'] = $uploadedImageName;
                        $uploadedImageName = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCoursePath(), '/') . '/' . $uploadedImageName;
                        $response['url'] = $uploadedImageName;
                    } else{
                        return response()->json(setErrorResponse(__('operator.GlobalController.dataNotExist')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                    }
                }else{
                    if ($fromImage == 'modules') {
                        $file = \Storage::disk('s3')->put(getTrainingCourseModulePath(), $request->file('image'));
                        $name = explode('/', $file);
                        $uploadedImageName = $name[count($name)-1];
                        $response['image'] = $uploadedImageName;
                        $uploadedImageName = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCourseModulePath(), '/') . '/' . $uploadedImageName;
                        $response['url'] = $uploadedImageName;
                    } elseif($fromImage == 'submodules') {
                        $file = \Storage::disk('s3')->put(getTrainingCourseSubmodulePath() . '/'. 'submodules', $request->file('image'));
                        $name = explode('/', $file);
                        $uploadedImageName = $name[count($name)-1];
                        $response['image'] = $uploadedImageName;
                        $uploadedImageName = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCourseSubmodulePath(), '/') . '/' . $uploadedImageName;
                        $response['url'] = $uploadedImageName;
                    } elseif ($fromImage == 'courses') {
                        $file = \Storage::disk('s3')->put(getTrainingCoursePath(), $request->file('image'));
                        $name = explode('/', $file);
                        $uploadedImageName = $name[count($name)-1];
                        $response['image'] = $uploadedImageName;
                        $uploadedImageName = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCoursePath(), '/') . '/' . $uploadedImageName;
                        $response['url'] = $uploadedImageName;
                    } elseif ($fromImage == 'resources') {
                        $file = \Storage::disk('s3')->put(getTrainingCoursePath(), $request->file('image'));
                        $name = explode('/', $file);
                        $uploadedImageName = $name[count($name)-1];
                        $response['image'] = $uploadedImageName;
                        $uploadedImageName = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCoursePath(), '/') . '/' . $uploadedImageName;
                        $response['url'] = $uploadedImageName;
                    } elseif ($fromImage == 'users') {
			$file = \Storage::disk('s3')->put(getUsersPath(), $request->file('image'));
			$name = explode('/', $file);
			$uploadedImageName = $name[count($name) - 1];
			$response['image'] = $uploadedImageName;
			$uploadedImageName = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getUsersPath(), '/') . '/' . $uploadedImageName;
			$response['url'] = $uploadedImageName;
		    } elseif ($fromImage == 'product_type') {
			$file = \Storage::disk('s3')->put(moveFileToProductType(), $request->file('image'));
			$name = explode('/', $file);
			$uploadedImageName = $name[count($name) - 1];
			$response['image'] = $uploadedImageName;
			$uploadedImageName = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(moveFileToProductType(), '/') . '/' . $uploadedImageName;
			$response['url'] = $uploadedImageName;
		    }
		}
            } else {
                foreach ($request->images as $image) {
                    $name = basename(\Storage::disk('s3')->put(getTrainingCoursePath(), $image));                    
                    $response[] = [
                        'image' => $name,
                        'url' => 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCoursePath(), '/') . '/' . $name
                    ];
                }
            }
            return response()->json(setResponse($response, ['message' => __('operator.GlobalController.imageUpload')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    /**
     * @OA\Post(
     *     path="/operator/uploadMedia",
     *     tags={"Operator - Global Methods - Media Upload"},
     *     summary="Media Upload",
     *     description="Media Upload",
     *     operationId="uploadMedia",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="type",
     *                     description="types : submodules / resources",
     *                     type="string"
     *                 ),
     *                  @OA\Property(
     *                     property="media_type",
     *                     description="media_types : video / videowiththumb / pdf / pdfwiththumb / document",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                      description="File",
     *                      property="file",
     *                      type="file"
     *                 ),
     *                 example={"type": "submodules", "file": "video.mp4", "media_type": "videowiththumb"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */ 

    public function uploadMedia(MediaRequest $request)
    {   
        $requestData = $request->all();
        try {
            $response = [];
            
            if ($requestData['media_type'] === 'video') {
                $file = \Storage::disk('s3')->put(getTrainingCoursePath(), $request->file('file'));
                $name = explode('/', $file);
                $uploadedImageName = $response['file'] = $name[count($name)-1];
                $response['url'] = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCoursePath(), '/') . '/' . $uploadedImageName;
            } else if ($requestData['media_type'] === 'videowiththumb') {
                $file = \Storage::disk('s3')->put(getTrainingCoursePath(), $request->file('file'));
                $name = explode('/', $file);
                $uploadedImageName = $response['file'] = $name[count($name)-1];
                $response['url'] = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCoursePath(), '/') . '/' . $uploadedImageName;

                $storageUrl = base_path('storage/thumb');
                if (!file_exists($storageUrl)) {
                    mkdir($storageUrl);
                    chmod($storageUrl, 0777);
                }
                $thumb_name = explode('.', $uploadedImageName);
                $thumb_name = $thumb_name[0] . '.jpg';
                $imgSizes = config('constants.videoguide.thumbnail');
                VideoThumbnail::createThumbnail($response['url'], $storageUrl, $thumb_name, 0, $imgSizes['width'], $imgSizes['height']);

                chmod(base_path('storage/thumb/' . $thumb_name), 0777);
                $file = base_path('storage/thumb/' . $thumb_name);

                \Storage::disk('s3')->put(getTrainingCoursePath() . '/' . $thumb_name, file_get_contents($file));
                if (file_exists($file)) {
                    unlink($file);
                }
                $response['thumbnail'] = $thumb_name;
                $response['thumbUrl'] = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCoursePath()) . '/' . $thumb_name;
            } else if ($requestData['media_type'] === 'pdf') {
                $file = \Storage::disk('s3')->put(getTrainingCoursePath(), $request->file('file'));
                $name = explode('/', $file);
                $uploadedImageName = $response['file'] = $name[count($name)-1];
                $response['url'] = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCoursePath(), '/') . '/' . $uploadedImageName;
            } else if ($requestData['media_type'] === 'pdfwiththumb') {
                $storageUrl = base_path('storage/' . $requestData['type']);
                if (!file_exists($storageUrl)) {
                    mkdir($storageUrl);
                    chmod($storageUrl, 0777);
                }
                
                // create image from the stored pdf
                $folderPath = $requestData['type'] . '/';
                $pdfFilePath = \Storage::disk('local')->put($folderPath, $request->file('file'));
                $pdfFileName = basename($pdfFilePath);
                $pdfImageName = Str::before($pdfFileName, '.pdf') . '.png';

                $pdf = new \Spatie\PdfToImage\Pdf(storage_path('app/' . $folderPath . $pdfFileName));
                $pdf->format(\Spatie\PdfToImage\Enums\OutputFormat::Png)->save(storage_path('app/' . $folderPath . $pdfImageName));
                
                // Resize the image to a width of 180 and constrain aspect ratio (auto height)
                $image = Image::make(storage_path('app/' . $folderPath . $pdfImageName));
                $image->resize(180, null, function ($constraint) {
                  $constraint->aspectRatio();
                })->save();

                \Storage::disk('s3')->put(getTrainingCoursePath() . '/' . $pdfFileName, file_get_contents(storage_path('app/' . $folderPath . $pdfFileName)));
                \Storage::disk('s3')->put(getTrainingCoursePath() . '/' . $pdfImageName, file_get_contents(storage_path('app/' . $folderPath . $pdfImageName)));
                unlink(storage_path('app/' . $folderPath . $pdfFileName));
                unlink(storage_path('app/' . $folderPath . $pdfImageName));
                $response['file'] = $pdfFileName;
                $response['url'] = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCoursePath()) . '/' . $pdfFileName;
                $response['thumbnail'] = $pdfImageName;
                $response['thumbUrl'] = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCoursePath()) . '/' . $pdfImageName;
            }else if ($requestData['media_type'] === 'document') {
                $file = \Storage::disk('s3')->put(getTrainingCoursePath(), $request->file('file'));
                $name = explode('/', $file);
                $uploadedImageName = $response['file'] = $name[count($name)-1];
                $response['url'] = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCoursePath(), '/') . '/' . $uploadedImageName;
            }else if ($requestData['media_type'] === 'zip') {
                $file = \Storage::disk('s3')->put(getTrainingCoursePath(), $request->file('file'));
                $name = explode('/', $file);
                $uploadedImageName = $response['file'] = $name[count($name)-1];
                $response['url'] = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCoursePath(), '/') . '/' . $uploadedImageName;
            }
            
            return response()->json(setResponse($response, ['message' => __('operator.GlobalController.fileUpload')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    public function uploadQuestionMedia(MediaRequest $request)
    {
        $requestData = $request->all();
        try {
            $response = [];
            if ($requestData['media_type'] === 'document') {
                $file = \Storage::disk('s3')->put(getTrainingCoursePath(), $request->file('file'));
                $name = explode('/', $file);
                $uploadedImageName = $response['file'] = $name[count($name)-1];
                $response['url'] = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCoursePath(), '/') . '/' . $uploadedImageName;

                // Handle Cable Matrix CSV validation (without question_id during submodule creation)
                if ($request->has('question_type') && $request->question_type === 'cableMatrix') {
                    $cableMatrixService = new \App\Services\CableMatrixService();
                    $csvValidation = $cableMatrixService->validateCsvFile($file);

                    if ($csvValidation['success']) {
                        $response['cable_matrix_validation'] = $csvValidation['data'];
                    } else {
                        // Delete uploaded file if validation fails
                        \Storage::disk('s3')->delete($file);
                        return response()->json(setErrorResponse($csvValidation['message']))->setStatusCode(422);
                    }
                }
            }
            return response()->json(setResponse($response, ['message' => __('operator.GlobalController.fileUpload')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }    

     /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    /**
     * @OA\Post(
     *     path="/operator/getVimeoMp4Url",
     *     tags={"Operator - Global Methods - Media Upload"},
     *     summary="Get Vimeo Mp4 Url",
     *     description="Get Vimeo Mp4 Url",
     *     operationId="getVimeoMp4Url",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="vimeo_id",
     *                     description="Vimeo Id",
     *                     type="string"
     *                 ),
     *                 example={"vimeo_id": "548915948"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */ 
    public function getVimeoMp4Url(VimeoMp4UrlRequest $request)
    {
        try {
            $output = [];
            $mp4Url = '';
            $result = file_get_contents("https://player.vimeo.com/video/".$request->vimeo_id."/config");
            $response = json_decode($result, true);
            $videoUrlList = $response['request']['files']['progressive'];
            if(count($videoUrlList) > 0){
                $mp4Url = $this->getUrl($videoUrlList, '360p');
                if($mp4Url == ''){
                    $mp4Url = $this->getUrl($videoUrlList, '540p');
                }
                if($mp4Url == ''){
                    $mp4Url = $this->getUrl($videoUrlList, '720p');
                }
                if($mp4Url == ''){
                    $mp4Url = $this->getUrl($videoUrlList, '1080p');
                }
            }
            $output['url'] = $mp4Url;
            return response()->json(setResponse($output, ['message' => __('operator.GlobalController.foundMp4Url')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function getUrl($videoUrlList, $quality){
        try {
            foreach($videoUrlList as $video){
                if($video['quality'] == $quality){
                    return  $video['url'];
                }
            }
            return '';
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

         /**
     * @OA\Get(
     *     path="/operator/getTableSettings",
     *     tags={"Operator - Favourite Filter"},
     *     summary="Favourite Filter",
     *     description="Favourite Filter",
     *     operationId="getTableSettings",
     *     @OA\RequestBody(
     *         description="Module Type",
    *         @OA\MediaType(
    *             mediaType="application/json",
    *             @OA\Schema(
    *                 type="object",
    *                 @OA\Property(
    *                      property="module_type",
    *                     description="Module Type",
    *                     type="string"
    *                 ),
    *                 example={"module_type": "JobsList"}
    *             )
    *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getTableSettings(Request $request){
        try {
            $master_user_id = auth()->guard('operator')->user()->id;
            $data = FilterData::select('filter_data', 'save_columns', 'module_type')->where(['master_user_id'=>$master_user_id, 'module_type'=>$request->module_type])->first();
            if(empty($data)){
                $userFilterData = [
                    'master_user_id' => $master_user_id,
                    'filter_data' => '[]',
                    'save_columns' => null,
                    'module_type' => $request->module_type,              
                ];
                $user = $this->filterData->create($userFilterData);
                return new FavouriteFilterResource($user);
            }else{
                return new FavouriteFilterResource($data);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

     /**
     * @OA\Get(
     *     path="/operator/saveFavouriteFilter",
     *     tags={"Operator - Save Favourite Filter"},
     *     summary="Save Favourite Filter",
     *     description="Save Favourite Filter",
     *     operationId="saveFavouriteFilter",
     *     @OA\RequestBody(
     *         description="Module Type",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                  @OA\Property(
    *                      property="module_type",
    *                     description="Module Type",
    *                     type="string"
    *                 ),
     *                @OA\Property(
     *                     property="filter_data[]",
     *                     description="for save filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 example={"module_type": "jobsList","filters": {"status": "Approved"}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function saveFavouriteFilter(Request $request){
        try {
            $master_user_id = auth()->guard('operator')->user()->id;        
            $userData = FilterData::where(['master_user_id'=>$master_user_id,'module_type'=>$request->module_type])->first();        
            FilterData::where(['master_user_id'=>$master_user_id,'module_type'=>$request->module_type])->update(['filter_data' => json_encode($request->filters),'module_type'=>$request->module_type]);
            return response()->json(setResponse([], ['message' => __('operator.job.filterUpdated')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        } 
    }

/**
     * @OA\Post(
     *     path="/operator/saveColumns",
     *     tags={"Operator - Save Columns"},
     *     summary="Save Columns",
     *     description="Save Columns",
     *     operationId="saveColumns",
     *     @OA\RequestBody(
     *         description="Module Type",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                  @OA\Property(
    *                      property="module_type",
    *                     description="Module Type",
    *                     type="string"
    *                 ),
     *                @OA\Property(
     *                     property="column_data[]",
     *                     description="for save columns[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 example={"course_name":"New Demo Course","job_no":"test123","user_name":"test","group_name":"newww","unique_id":"404145","created_at":{"endDate":"2022-06-24","startDate":"2022-06-03"},"submodule_name":"dummy image","cost":"240","job_topic":"T1 Demo","status":"Approved"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function saveColumns(Request $request){
        try {
            $master_user_id = auth()->guard('operator')->user()->id;        
            $userData = FilterData::where(['master_user_id'=>$master_user_id,'module_type'=>$request->module_type])->first();        
            FilterData::where(['master_user_id'=>$master_user_id,'module_type'=>$request->module_type])->update(['save_columns' => json_encode($request->columns_data),'module_type'=>$request->module_type]);
            return response()->json(setResponse([], ['message' => __('operator.job.columnUpdated')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        } 
    }
}
