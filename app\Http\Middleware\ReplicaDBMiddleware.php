<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\DB;

class ReplicaDBMiddleware {

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next) {
	if (env('DB_READ_HOST') != '' && env('DB_READ_HOST') != null) {

//	DB::setDefaultConnection('mysql_read_replica');
	    \Illuminate\Support\Facades\Config::set('database.default', 'mysql_read_replica');
	    DB::reconnect();
//	echo "<pre>";
//	print_r(DB::getDatabaseName());
//	exit;
//	echo "<pre>";
//	print_r("dfdsf");
//	exit;
	}
	$response = $next($request);
	return $response;
    }

}
