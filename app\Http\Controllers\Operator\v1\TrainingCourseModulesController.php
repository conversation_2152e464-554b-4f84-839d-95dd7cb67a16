<?php

namespace App\Http\Controllers\Operator\v1;

use App\Models\MasterUser;
use Illuminate\Http\Request;
use App\Models\Notifications;
use Illuminate\Http\Response;
use App\Models\TrainingCourse;
use Illuminate\Support\Facades\DB;
use App\Models\PrerequisiteModules;
use App\Models\TrainingCourseModules;
use App\Models\TrainingCourseProgress;
use App\Notifications\NotifiableEmail;
use Illuminate\Support\Facades\Notification;
use App\Models\TrainingCourseSubmoduleDetails;
use App\Notifications\MicroLearningModuleSendEmail;
use App\Http\Requests\Operator\v1\SortModulesRequest;
use App\Http\Requests\Operator\v1\AddSubModulesRequest;
use App\Http\Requests\Operator\v1\MicroLearningModuleRequest;
use App\Http\Requests\Operator\v1\sendMicroLearningModuleEmail;
use App\Http\Requests\Operator\v1\TrainingCourseModulesRequest;
use App\Http\Resources\Operator\v1\TrainingCourseModulesResource;
use App\Repositories\Operator\v1\TrainingCourseModulesRepository;
use App\Http\Requests\Operator\v1\TrainingCourseModuleStatusRequest;
use App\Http\Resources\Operator\v1\TrainingCourseModuleListResource;
use App\Http\Requests\Operator\v1\TrainingCourseModuleCopyPasteRequest;


class TrainingCourseModulesController extends Controller {

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    private $model;

    public function __construct() {
        $this->model = new TrainingCourseModulesRepository(new TrainingCourseModules());
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourseModule",
     *     tags={"Operator - Training Course Module"},
     *     summary="Store Training Course Module",
     *     description="Store Training Course Module",
     *     operationId="store",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="description",
     *                     description="Description",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="module_lock",
     *                     description="Module Lock",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="module_complete",
     *                     description="Mmodule Complete",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="duration",
     *                     description="Duration (Month)",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="duration_type",
     *                     description="Duration type",
     *                     type="string",
     *                     enum={"Hours", "Days"}
     *                 ),
     *                 @OA\Property(
     *                     property="unlock_datetime",
     *                     description="Unlock Datetime",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 @OA\Property(
     *                     property="image",
     *                     description="Image",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="notify_users",
     *                     description="notify_users",
     *                     type="string",
     *                 ),
     *                 @OA\Property(
     *                     property="prerequisiteModules",
     *                     description="prerequisiteModules",
     *                     type="string"
     *                 ),
     *                 example={"name": "Theory","training_course_id":1 ,"description": "Sample Description", "module_lock": "1", "module_complete": "0", "duration": 5,"duration_type": "Hours/Days", "unlock_datetime": "2021-04-04 10:10:10", "status": "Active", "image": "89xe1IurQOpHoPd5roa5pYrNYq1sTlXQuNWoZIIA.png", "notify_users": 0, "prerequisiteModules": "[{id: 2,percentage: 100}]"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function store(TrainingCourseModulesRequest $request) {
        try {
            $result = DB::transaction(function () use ($request) {
                        $moduleData = $request;

                        $prerequisiteModules = [];
                        $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;

                        if (isset($moduleData['prerequisiteModules'])) {
                            $prerequisiteModules = $moduleData['prerequisiteModules'];
                            unset($moduleData['prerequisiteModules']);
                        }

                        if ($moduleData['duration'] != '' || $moduleData['duration'] != null) {
                            $data['date_time'] = date('Y-m-d H:i:s');
                            $data['duration'] = $moduleData['duration'];
                            $data['type'] = $moduleData['duration_type'];
                            $moduleData['duration_unlock_datetime'] = addLockDurationToDate($data);
                        } else {
                            $moduleData['duration_unlock_datetime'] = null;
                        }

                        $createdModule = $this->model->create($moduleData);

                        $courseStatus = TrainingCourseSubmoduleDetails::getcourseStatus($createdModule);

                        if ($courseStatus->status == 'Active') {
                            if ($createdModule->status == 'Active' && $createdModule->notify_users == 1) {

                                $userList = TrainingCourse::sendCourseModuleNotification($createdModule->training_course_id);

                                $moduleNotificationJob = (new \App\Jobs\ModuleUserNotificationJob($userList, $createdModule, $operatorId))->delay(env('QUEUE_JOB_DELAY_TIME'));
                                dispatch($moduleNotificationJob);
                            }
                        }

                        if (count($prerequisiteModules) > 0) {
                            foreach ($prerequisiteModules as $key => $value) {
                                $prerequisiteModule = new PrerequisiteModules;
                                $prerequisiteModule->training_course_id = $moduleData['training_course_id'];
                                $prerequisiteModule->module_id = $createdModule->id;
                                $prerequisiteModule->prerequisite_module_id = $value['id'];
                                $prerequisiteModule->percentage = $value['percentage'];
                                $prerequisiteModule->save();
                            }
                        }
                    });
            return response()->json(setResponse([], ['message' => __('operator.TrainingCourseModule.add')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/trainingCourseModule/{id}",
     *     tags={"Operator - Training Course Module"},
     *     summary="Get Course Module Details",
     *     description="Get Course Module Details",
     *     operationId="show",
     *    @OA\Parameter(
     *         description="id",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function show($id) {
        try {
            $courseModuleDetail = new TrainingCourseModulesResource(TrainingCourseModules::with('prerequisiteModules.module')->findOrFail($id));
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $courseList = TrainingCourse::where('master_user_id', $operatorId)->pluck('id')->toArray();
            if (in_array($courseModuleDetail->training_course_id, $courseList)) {
                $message = !empty($courseModuleDetail) ? __('operator.TrainingCourseModule.found') : __('operator.TrainingCourseModule.notFound');
                return response()->json(setResponse($courseModuleDetail, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
            } else {
                return response()->json(setErrorResponse(__('You cannot access this record')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Put(
     *     path="/operator/trainingCourseModule/{id}",
     *     tags={"Operator - Training Course Module"},
     *     summary="Update Training Course Module",
     *     description="Update Training Course Module",
     *     operationId="update",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="description",
     *                     description="Description",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="module_lock",
     *                     description="Module Lock",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="module_complete",
     *                     description="Module Complete",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="duration",
     *                     description="Duration (Month)",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="unlock_datetime",
     *                     description="Unlock Datetime",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 @OA\Property(
     *                     property="image",
     *                     description="Image",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="notify_users",
     *                     description="notify_users",
     *                     type="string",
     *                 ),
     *                 @OA\Property(
     *                     property="prerequisiteModules",
     *                     description="prerequisiteModules",
     *                     type="string"
     *                 ),
     *                 example={"name": "Theory","training_course_id":1 ,"description": "Sample Description", "module_lock": "1", "module_complete": "0", "duration": 5, "unlock_datetime": "2021-04-04 10:10:10", "Status": "Active", "image": "89xe1IurQOpHoPd5roa5pYrNYq1sTlXQuNWoZIIA.png", "notify_users": 0, "prerequisiteModules": "[{id: 2,percentage: 100}]"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function update(TrainingCourseModulesRequest $request, $id) {
        try {
            $result = DB::transaction(function () use ($request, $id) {
                        $moduleData = $request->all();
                        $prerequisiteModules = [];
                        if (isset($moduleData['prerequisiteModules'])) {
                            $prerequisiteModules = $moduleData['prerequisiteModules'];
                            unset($moduleData['prerequisiteModules']);
                        }
                        $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;

                        $moduleDetail = TrainingCourseModules::find($id);
                        $pastStatus = $moduleDetail['status'];
                        $pastNotifyUser = $moduleDetail['notify_users'];
                        $courseStatus = TrainingCourseSubmoduleDetails::getcourseStatus($moduleDetail);

                        $oldImage = $moduleDetail->image;
                        if ($moduleData['module_lock'] == 0) {
                            $moduleData['module_complete'] = null;
                        }

                        if ($moduleData['duration'] != '' || $moduleData['duration'] != null) {
                            $data['date_time'] = date('Y-m-d H:i:s');
                            $data['duration'] = $moduleData['duration'];
                            $data['type'] = $moduleData['duration_type'];
                            $moduleData['duration_unlock_datetime'] = addLockDurationToDate($data);
                        } else {
                            $moduleData['duration_unlock_datetime'] = null;
                        }

                        $moduleDetail->update($moduleData);

                        $checkNotification = Notifications::select('type_id')->where(['type_id' => $moduleDetail->id, 'type' => "Module"])->first();
                        if ($courseStatus->status == 'Active') {
                            // dd($checkNotification->type_id);exit();
                            if (!isset($checkNotification->type_id)) {
                                if ($moduleDetail['notify_users'] == 1 && $moduleDetail['status'] == 'Active') {
                                    $userList = TrainingCourse::sendCourseModuleNotification($moduleDetail['training_course_id']);

                                    $moduleNotificationJob = (new \App\Jobs\ModuleUserNotificationJob($userList, $moduleDetail, $operatorId))->delay(env('QUEUE_JOB_DELAY_TIME'));
                                    dispatch($moduleNotificationJob);
                                }
                            }
                        }

                        if ($oldImage != $moduleData['image']) {
                            \Storage::disk('s3')->copy(getTrainingCourseModulePath() . "/" . $moduleData['image'], getTrainingCourseModulePath($moduleDetail->training_course_id) . "/" . $moduleData['image']);
                            \Storage::disk('s3')->delete(getTrainingCourseModulePath($moduleDetail->training_course_id) . "/" . $oldImage);
                            \Storage::disk('s3')->delete(getTrainingCourseModulePath() . "/" . $moduleData['image']);
                        }
                        if (count($prerequisiteModules) > 0) {
                            PrerequisiteModules::where('module_id', '=', $id)->delete();
                            foreach ($prerequisiteModules as $key => $value) {
                                $prerequisiteModule = new PrerequisiteModules;
                                $prerequisiteModule->training_course_id = $moduleDetail->training_course_id;
                                $prerequisiteModule->module_id = $id;
                                $prerequisiteModule->prerequisite_module_id = $value['id'];
                                $prerequisiteModule->percentage = $value['percentage'];
                                $prerequisiteModule->save();
                            }
                        } else {
                            PrerequisiteModules::where('module_id', '=', $id)->delete();
                        }
                    });
            return response()->json(setResponse([], ['message' => __('operator.TrainingCourseModule.update')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Delete(
     *     path="/operator/trainingCourseModule/{id}",
     *     tags={"Operator - Training Course Module"},
     *     summary="Delete  Training Course Module",
     *     description="Delete  Training Course Module",
     *     operationId="delete",
     *     @OA\Parameter(
     *         description="id",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function destroy($id) {
        try {
            \DB::transaction(function () use ($id) {
                $this->model->delete($id);
            });
            return response()->json(setResponse([], ['message' => __('operator.TrainingCourseModule.delete')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/trainingCourseModule/getAllCourseModules/{courseId}",
     *     tags={"Operator - Training Course Module"},
     *     summary="Get Course All Module Details",
     *     description="Get All Course Module Details",
     *     operationId="getAllCourseModules",
     *     @OA\Parameter(
     *         description="Training Course Id",
     *         name="courseId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getAllCourseModules($courseId) {
        try {
            $allCourseModules = TrainingCourseModules::with('prerequisiteModules.module')->whereTrainingCourseId($courseId)->orderBy('display_order')->get();
            $message = count($allCourseModules) > 0 ? __('operator.TrainingCourseModule.listFound') : __('operator.TrainingCourseModule.listNotFound');
            return response()->json(setResponse(TrainingCourseModulesResource::collection($allCourseModules), ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourseModule/changeStatus",
     *     tags={"Operator - Training Course Module"},
     *     summary="Training Course Module Change Status",
     *     description="Training Course Module Change Status",
     *     operationId="changeStatus",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="id",
     *                     description="id",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={"id": 1, "status": "Active"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function changeStatus(TrainingCourseModuleStatusRequest $request) {
        try {
            if ($request->status === 'Inactive') {
                $prerequisiteModule = PrerequisiteModules::where(['prerequisite_module_id' => $request->id])->first();
                if ($prerequisiteModule) {
                    return response()->json(setErrorResponse(__('operator.TrainingCourseModule.status-not-changed')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            }
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $module = TrainingCourseModules::find($request->id);
            $module->update(['status' => $request->status]);

            $courseStatus = TrainingCourseSubmoduleDetails::getcourseStatus($module);

            if ($courseStatus->status == 'Active') {
                if ($request->status == 'Active' && $module->notify_users == 1) {

                    $userList = TrainingCourse::sendCourseModuleNotification($module->training_course_id);

                    $moduleNotificationJob = (new \App\Jobs\ModuleUserNotificationJob($userList, $module, $operatorId))->delay(env('QUEUE_JOB_DELAY_TIME'));
                    dispatch($moduleNotificationJob);
                }
            }
            $newData=['training_course_id' => $module->training_course_id, 'module_id' => $request->id];
            $getTrainingCourseProgressList = TrainingCourseProgress::query()->where('training_course_id',$newData['training_course_id'])->where('course_progress', '<', 100)->get();
            foreach ($getTrainingCourseProgressList as $trainingCourse) {
            // Training course progress
            $tData = ['training_course_id' => $trainingCourse->training_course_id, 'user_id' => $trainingCourse->user_id];
            (new TrainingCourseProgress)->calculateTrainingCourseProgress($tData);
            }
            return response()->json(setResponse([], ['message' => __('operator.TrainingCourseModule.status')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/trainingCourseModule/duplicateModule/{courseId}/{moduleId}",
     *     tags={"Operator - Training Course Module"},
     *     summary="Duplicate Training Course Module",
     *     description="Duplicate Training Course Module",
     *     operationId="duplicateModule",
     *     @OA\Parameter(
     *         description="Training Course Id",
     *         name="courseId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Parameter(
     *         description="Training Course Module Id",
     *         name="moduleId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function duplicateModule($courseId, $moduleId) {

        try {
            $result = \DB::transaction(function () use ($courseId, $moduleId) {
                        $trainingModule = TrainingCourseModules::whereId($moduleId)->whereTrainingCourseId($courseId)->first();

                        $imageName = '';
                        if (!empty($trainingModule->image)) {
                            $image = explode('.', $trainingModule->image);
                            $imageName = $image[0] . rand() . '.' . $image[1];
                        }

                        $newTrainingModule = $trainingModule->replicate()->fill([
                            'name' => $trainingModule->name . ' - copy',
                            'copy_from' => $moduleId,
                            'image' => $imageName,
                            'status' => 'Inactive'
                        ]);
                        $newTrainingModule->save();
                        if (!empty($trainingModule->image)) {
                            \Storage::disk('s3')->copy(getTrainingCourseModulePath($courseId) . "/" . $trainingModule->image, getTrainingCourseModulePath($courseId) . "/" . $imageName);
                        }
                        // Duplicate Sub modules
                        TrainingCourseSubmoduleDetails::duplicateSubModules($newTrainingModule, $trainingModule, true, '', auth()->guard('operator')->user());
                    });
            return response()->json(setResponse([], ['message' => __('operator.TrainingCourseModule.duplicate')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/trainingCourseModule/getAllCourseModulesList",
     *     tags={"Operator - Training Course Module"},
     *     summary="Get All Training Course Module list",
     *     description="Get All Training Course Module list",
     *     operationId="getAllCourseModulesList",
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getAllCourseModulesList() {
        try {
            $master_user_id = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $trainingCourseList = new TrainingCourseModuleListResource(TrainingCourse::with('modules')->where('master_user_id', $master_user_id)->get());

            $message = !empty($trainingCourseList) ? __('operator.TrainingCourseModule.courseListFound') : __('operator.TrainingCourseModule.courseListNotFound');
            return response()->json(setResponse(TrainingCourseModuleListResource::collection($trainingCourseList), ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourseModule/addCourseModulesReplica",
     *     tags={"Operator - Training Course Module"},
     *     summary="Add Training Course module Replica",
     *     description="Add Training Course module Replica",
     *     operationId="addCourseModulesReplica",
     *         @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="id",
     *                     description="id",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={
      "trainingCourseId":5,
      "courses":{{
      "courseId":1,
      "moduleIds":{
      "0":1,"1":2,"2":3
      }
      },{
      "courseId":2,
      "moduleIds":{
      "0":1,"1":2,"2":3
      }
      },{
      "courseId":3,
      "moduleIds":{
      "0":1,"1":2,"2":3
      }
      }}
      }
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Course Module Replica added Successfully"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function addCourseModulesReplica(Request $request) {
        try {
            $result = \DB::transaction(function () use ($request) {
                        $requestData = $request->all();
                        $trainingCourseId = $requestData['trainingCourseId'];
                        if (!empty($requestData['courses'])) {
                            foreach ($requestData['courses'] as $key => $course) {

                                $courseId = $course['courseId'];
                                if (!empty($course['moduleIds'])) {

                                    foreach ($course['moduleIds'] as $key => $moduleId) {

                                        $trainingModule = TrainingCourseModules::whereId($moduleId)->whereTrainingCourseId($courseId)->first();

                                        if ($trainingModule) {
                                            $imageName = '';
                                            if (!empty($trainingModule->image)) {
                                                $image = explode('.', $trainingModule->image);
                                                $imageName = $image[0] . rand() . '.' . $image[1];
                                            }

                                            $newTrainingModule = $trainingModule->replicate()->fill([
                                                'training_course_id' => $trainingCourseId,
                                                'copy_from' => $moduleId,
                                                'image' => $imageName
                                            ]);

                                            $newTrainingModule->save();

                                            if (!empty($trainingModule->image)) {
                                                \Storage::disk('s3')->copy(getTrainingCourseModulePath($courseId) . "/" . $trainingModule->image, getTrainingCourseModulePath($trainingCourseId) . "/" . $imageName);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    });
            return response()->json(setResponse([], ['message' => __('operator.TrainingCourseModule.replica')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourseModule/copyPasteModule",
     *     tags={"Operator - Training Course Module"},
     *     summary="Copy Past selected module into target course",
     *     description="Copy Past selected module into target course",
     *     operationId="copyPasteModule",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 example={"trainingCourseId":14, "moduleId":31}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Module added in to selected course Successfully"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function copyPasteModule(TrainingCourseModuleCopyPasteRequest $request) {
        try {
            $trainingCourse = TrainingCourse::find($request->trainingCourseId);
            if ($trainingCourse->single_module_course == 1) {
                $trainingCourseModuleCount = TrainingCourseModules::where('training_course_id', $request->trainingCourseId)->whereNull('deleted_at')->count();
                if ($trainingCourseModuleCount == 0) {
                    $copyPasteModuleJob = (new \App\Jobs\CopyPasteModuleJob($request->trainingCourseId, $request->moduleId, auth()->guard('operator')->user()))->delay(3);
                    dispatch($copyPasteModuleJob);
                    return response()->json(setResponse([], ['message' => __('operator.TrainingCourseModule.copyPaste')]))->setStatusCode(Response::HTTP_OK);
                } else {
                    return response()->json(setErrorResponse(__('operator.TrainingCourseModule.singleModuleCourseError')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            } else {
                $copyPasteModuleJob = (new \App\Jobs\CopyPasteModuleJob($request->trainingCourseId, $request->moduleId, auth()->guard('operator')->user()))->delay(3);
                dispatch($copyPasteModuleJob);
                return response()->json(setResponse([], ['message' => __('operator.TrainingCourseModule.copyPaste')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourseModule/addSubModules",
     *     tags={"Operator - Training Course Module"},
     *     summary="Add sub modules from other Training Course",
     *     description="Add sub modules from other Training Course",
     *     operationId="addSubModules",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="moduleId",
     *                     description="Module Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="subModulesId",
     *                     description="SubModule Ids",
     *                     type="array",
     *                     @OA\Items()
     *                 ),
     *                 example={"moduleId": 1, "subModulesId": {1, 5, 10, 15}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function addSubModules(AddSubModulesRequest $request) {
        try {
            if (isset($request->single_submodule) && $request->single_submodule == 1) {
                if (count($request->subModulesId) == 1) {
                    TrainingCourseSubmoduleDetails::where('module_id', $request->moduleId)->delete();
                }
            }
            dispatch(new \App\Jobs\AddSubModulesToTrainingCourseJob($request->moduleId, $request->subModulesId, auth()->guard('operator')->user()));
            return response()->json(setResponse([], ['message' => __('operator.TrainingCourseModule.subModulesAdded')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            dd($e);
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/trainingCourseModule/getDefaultImages",
     *     tags={"Operator - Training Course Default Module"},
     *     summary="Get Default Training Course Module Image",
     *     description="Get All Training Course Module Image",
     *     operationId="getDefaultImages",
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getDefaultImages() {
        try {
            $defaultImage['module_image'] = env('CDN_URL') . 'default/module.png';
            return response()->json(setResponse($defaultImage, ['message' => __('Image found')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourseModule/sortModules",
     *     tags={"Operator - Training Course Module"},
     *     summary="Copy Past selected module into target module",
     *     description="Copy Past selected module into target module",
     *     operationId="sortModules",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 example={"sort_modules": { "module_id": 255, "order": 2 },{ "module_id": 261,"order": 1}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Module added in to selected course Successfully"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function sortModules(SortModulesRequest $request) {
        try {
            $moduleList = $request->sort_modules;
            foreach ($moduleList as $key => $module) {
                TrainingCourseModules::whereId($module['module_id'])->update(['display_order' => $module['order']]);
            }
            return response()->json(setResponse([], ['message' => __('operator.TrainingCourseModule.sortSuccess')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="operator/micro-learning/trainingCourseModule",
     *     tags={"Operator - Micro Learning"},
     *     summary="Store Training Course Module for Micro Learning",
     *     description="Store Training Course Module for Micro Learning",
     *     operationId="storeMicroLearningModule",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="description",
     *                     description="Description",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="module_lock",
     *                     description="Module Lock",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="module_complete",
     *                     description="Mmodule Complete",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="duration",
     *                     description="Duration (Month)",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="duration_type",
     *                     description="Duration type",
     *                     type="string",
     *                     enum={"Hours", "Days"}
     *                 ),
     *                 @OA\Property(
     *                     property="unlock_datetime",
     *                     description="Unlock Datetime",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 @OA\Property(
     *                     property="image",
     *                     description="Image",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="notify_users",
     *                     description="notify_users",
     *                     type="string",
     *                 ),
     *                 @OA\Property(
     *                     property="prerequisiteModules",
     *                     description="prerequisiteModules",
     *                     type="string"
     *                 ),
     *                 example={"name": "Theory","training_course_id":1 ,"description": "Sample Description", "module_lock": "1", "module_complete": "0", "duration": 5,"duration_type": "Hours/Days", "unlock_datetime": "2021-04-04 10:10:10", "status": "Active", "image": "89xe1IurQOpHoPd5roa5pYrNYq1sTlXQuNWoZIIA.png", "notify_users": 0, "prerequisiteModules": "[{id: 2,percentage: 100}]"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function storeMicroLearningModule(MicroLearningModuleRequest $request) {
        try {
            $result = DB::transaction(function () use ($request) {
                        $moduleData = $request;
                        $createdModule = $this->model->storeMicroLearningModule($moduleData);
                    });
            return response()->json(setResponse([], ['message' => __('operator.TrainingCourseModule.add')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="operator/micro-learning/trainingCourseModule/sendEmail",
     *     tags={"Operator - Micro Learning"},
     *     summary="Send email to the requested email address",
     *     description="Send email to the requested email address",
     *     operationId="sendEmail",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="email",
     *                     description="Email",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="courseId",
     *                     description="Course ID",
     *                     type="integer"
     *                 ),
     *                 
     *                 @OA\Property(
     *                     property="notEnoughContent",
     *                     description="Not Enough Content",
     *                     type="string",
     *                 ),
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function sendEmail(sendMicroLearningModuleEmail $request) {
        try {
            $result = DB::transaction(function () use ($request) {
                $data = $request->all(); 
                $operator = MasterUser::find($data['operator_user_id']);
                //Checking for email address if smartawards contains only digits before @
                $smartAwardsMailSendFlag = smartAwardsEmailSendResctrict($operator->email);
                if($smartAwardsMailSendFlag == 1){
                    Notification::send(new NotifiableEmail($operator->email), new MicroLearningModuleSendEmail($data,$operator->name));
                }
            });
            return response()->json(setResponse([], ['message' => __('The micro-learning email has been sent to the requested email address.')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
