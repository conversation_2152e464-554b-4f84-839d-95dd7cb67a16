<?php
/**
     * @OA\Get(
     *     path="/operator/trainingCourseSubmodule/assessment/{submoduleId}/response/{assessorId}/{userId}",
     *     tags={"Operator - Practical Assessment"},
     *     summary="Get Specific Assessment Response",
     *     description="Get Specific Assessment Response",
     *     operationId="viewAssessment",
     *     @OA\Parameter(
     *         description="Training Course Submodule Id",
     *         in="path",
     *         name="submoduleId",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Parameter(
     *         description="Assessor Id",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Parameter(
     *         description="User Id",
     *         in="path",
     *         name="userId",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Role Deleted successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */