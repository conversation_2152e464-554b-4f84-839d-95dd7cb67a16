<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddUniversalLinkColumnInCustomDeeplinksTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('custom_deeplinks', function (Blueprint $table) {
            $table->string('universal_link')->nullable()->after('ios_app_store_id')->comment('Universal link for iOS deeplinks');
            $table->string('android_universal_link')->nullable()->after('android_package_name')->comment('Universal link for Android deeplinks');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('custom_deeplinks', function (Blueprint $table) {
            $table->dropColumn(['universal_link', 'android_universal_link']);
        });
    }
}
