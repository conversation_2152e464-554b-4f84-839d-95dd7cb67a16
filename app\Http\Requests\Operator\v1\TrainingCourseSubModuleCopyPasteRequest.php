<?php

namespace App\Http\Requests\Operator\v1;

use App\Http\Requests\CustomFormRequest;

class TrainingCourseSubModuleCopyPasteRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array
    {
        return [
            'moduleId' => 'required|int|exists:'.\DBTableNames::TRAINING_COURSE_MODULES.',id',
            'subModuleId' => 'required|int|exists:'.\DBTableNames::TRAINING_COURSE_SUBMODULE_DETAILS.',id'
        ];
    }
    
    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'moduleId.required' => __('Training course module id is required.'),
            'subModuleId.required' => __('Training course submodule id is required.'),
            'moduleId.int' => __('Training course module id must be integer.'),
            'subModuleId.int' => __('Training course submodule id must be integer.'),
            'moduleId.exists' => __('Invalid training course module id.'),
            'subModuleId.exists' => __('Invalid training course submodule id.'),
        ];
    }
}
