<?php

namespace App\Http\Requests\Operator\v1;

use App\Http\Requests\CustomFormRequest;
use App\Models\UserGroup;
class UserGroupRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {
        \Validator::extend('name_exists',
            function($attribute, $value, $parameters)
            {
                $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
                if(isset($parameters[0]) && !is_null($parameters[0])){
                    $existGroup = UserGroup::where('name',$value)->whereMasterUserId($operatorId)->where('id','!=', $parameters[0])->first();
                }else{
                    $existGroup = UserGroup::where('name',$value)->whereMasterUserId($operatorId)->first();
                }
                return $existGroup ? false : true;
            }
        );
        return [
            "name" => 'required|name_exists:'.$this->id,
            //"manager_email" => 'sometimes|required|email',
        ];
    }

    public function messages() : array
    {
        return [
            'name.required' => __('The name field is required.'),
            'name.name_exists' => __('The Group name is already exist.'),
            //'manager_email.email' => __('Invalid manager email.'),
        ];
    }
}
