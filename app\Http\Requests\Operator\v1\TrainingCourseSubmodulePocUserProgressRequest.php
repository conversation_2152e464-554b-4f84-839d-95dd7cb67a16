<?php

namespace App\Http\Requests\Operator\v1;

use DBTableNames;
use App\Http\Requests\CustomFormRequest;

class TrainingCourseSubmodulePocUserProgressRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {
        return [
            'hotspot_uploader_id' => 'required|integer|exists:' . DBTableNames::TRAINING_COURSE_SUBMODULE_HOTSPOT_UPLOADER_PROGRESS . ',id',
            'per_page' => 'numeric',
            'page' => 'numeric',
            'order_by' => "in:ASC,DESC,asc,desc"
        ];
    }

    public function messages() : array
    {
        return [
            'hotspot_uploader_id.required' => __('Progress id is required.'),
            'hotspot_uploader_id.integer' => __('Progress id must be an integer.'),
            'hotspot_uploader_id.exists' => __('Progress doesn\'t exists.'),
            'per_page.numeric' => __('Please enter valid per page limit'),
            'page.numeric' => __('Please enter valid page number'),
            'order_by.in' => __('Please enter valid sort order')
        ];
    }
}
