<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAllowOfflineSaSubmissionColumnInTrainingCourseSubmoduleDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('training_course_submodule_details', function (Blueprint $table) {
            if (!Schema::hasColumn('training_course_submodule_details', 'allow_offline_sa_submission')) {
                $table->boolean('allow_offline_sa_submission')->default(0)->after('restrict_gallery');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('training_course_submodule_details', function (Blueprint $table) {
            if (Schema::hasColumn('training_course_submodule_details', 'allow_offline_sa_submission')) {
                $table->dropColumn('allow_offline_sa_submission');
            }
        });
    }
}
