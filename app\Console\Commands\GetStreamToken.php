<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\MasterUser;
use Illuminate\Console\Command;
use App\Services\GetStreamService;

class GetStreamToken extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'token:get-stream';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate Get Stream Token For Users(Chat Integration)';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $operator= MasterUser::select('id','parent_id','name','email','image')->where('user_type','Operator')->whereNull('get_stream_run')->get()->toArray();
        foreach($operator as $operator_value){
            if(!empty($operator_value['parent_id'])){
            $userType=2;
            $UserId=$operator_value['parent_id'];
        }else{
            $userType=3;
            $UserId=$operator_value['id'];
        }
            app(GetStreamService::class)->GenerateGetStreamAllOperatorToken($operator_value['id'],$operator_value['name'],$operator_value['email'],$operator_value['image'],$userType,$UserId);
        }
        $record= User::select('id','name','email','photo')->whereNull('get_stream_run')->get()->toArray();
        foreach($record as $value){
            app(GetStreamService::class)->GenerateGetStreamTokenForAllUser($value['id'],$value['name'],$value['email'],$value['photo']);
        }

    }
}
