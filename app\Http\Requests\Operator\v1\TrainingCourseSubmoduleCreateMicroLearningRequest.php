<?php

namespace App\Http\Requests\Operator\v1;

use DBTableNames;
use App\Http\Requests\CustomFormRequest;
use App\Models\TrainingCourseSubmoduleDetails;
use App\Models\TrainingCourseSubModuleFeedbackQuestion;
use App\Models\TrainingCourseSubModulePracticalAssessmentQuestion;
use App\Models\TrainingCourseModules;

class TrainingCourseSubmoduleCreateMicroLearningRequest extends CustomFormRequest
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {

        $existModule = TrainingCourseModules::where('training_course_id',request()->training_course_id)->where('is_micro_learning_module',1)->first();

        \Validator::extend('module_exists',
            function($attribute, $value, $parameters)
            {
                if(isset($parameters[0]) && !is_null($parameters[0])){
                    $existModule = TrainingCourseModules::where('training_course_id',$parameters[0])->where('is_micro_learning_module',1)->first();
                    if(!empty($existModule)){
                        return true;
                    }else{
                        return false;
                    }
                }else{
                    return false;
                }
            }
        );

        \Validator::extend('name_exists',
            function($attribute, $value, $parameters)
            {
                $existModule = TrainingCourseModules::where('training_course_id',$parameters[1])->where('is_micro_learning_module',1)->first();
                if(!empty($existModule)){
                    if(isset($parameters[0]) && !is_null($parameters[0])){
                        $existModule = TrainingCourseSubmoduleDetails::where('submodule_name',$value)->whereTrainingCourseId($parameters[1])->whereModuleId($existModule->id)->where('id','!=', $parameters[0])->first();
                    }else{
                        $existModule = TrainingCourseSubmoduleDetails::where('submodule_name',$value)->whereTrainingCourseId($parameters[1])->whereModuleId($existModule->id)->first();
                    }
                    return $existModule ? false : true;
                }else{
                    return false;
                }
            }
        );

        \Validator::extend('token_exists',
            function($attribute, $value, $parameters)
            {
                if(isset($parameters[0]) && !is_null($parameters[0])){
                    $existModule = TrainingCourseSubmoduleDetails::where($attribute,$value)->first();
                }
                return $existModule ? false : true;

            }
        );

        if($this->is_unlocked=='false'){
        return [
            "training_course_id" => 'required|integer|exists:'.\DBTableNames::TRAINING_COURSE.',id|module_exists:'.$this->training_course_id,
            "submodule_name" => "required|name_exists:".$this->id.",".$this->training_course_id.",".($existModule ? $existModule->id : 'null'),
            "thumbnail" => "required|image|mimes:jpg,jpeg,png",
            "description" => "required",
            "file" => "required|mimes:html",
            "api_token" => "required|string|token_exists:training_course_submodule_details:".$this->api_token,
            "is_unlocked" => "required",
            "need_to_complete_sid" => 'required|int|exists:'.\DBTableNames::TRAINING_COURSE_SUBMODULE_DETAILS.',id',
            "days_after" => "required",
        ];
    }else{
        return [
            "training_course_id" => 'required|integer|exists:'.\DBTableNames::TRAINING_COURSE.',id|module_exists:'.$this->training_course_id,
            "submodule_name" => "required|name_exists:".$this->id.",".$this->training_course_id.",".($existModule ? $existModule->id : 'null'),
            "thumbnail" => "required|image|mimes:jpg,jpeg,png",
            "description" => "required",
            "file" => "required|mimes:html",
            "api_token" => "required|string|token_exists:training_course_submodule_details:".$this->api_token,
            "is_unlocked" => "required",
        ];
    }
    }

    public function messages() : array
    {
        return [
            "training_course_id.required" => __("The training course ID is required."),
            "training_course_id.integer" => __("The training course ID must be an integer."),
            "training_course_id.exists" => __("The selected training course ID is invalid."),
            "training_course_id.module_exists" => __("The Micro learning module doesn't exists. Please add Micro Learning module first."),
            "submodule_name.required" => __("The submodule name is required."),
            "submodule_name.name_exists" => __("The submodule name already exists for the selected training course and module."),
            "thumbnail.required" => __("The thumbnail is required."),
            "description.required" => __("The description is required."),
            "thumbnail.image" => __("The thumbnail must be an image."),
            "thumbnail.mimes" => __("The thumbnail must be a file of type: jpg, jpeg, png."),
            "file.required" => __("The file is required."),
            "file.mimes" => __("The file must be a file of type: html."),
            "api_token.required" => __("The API token is required."),
            "api_token.string" => __("The API token must be a string."),
            "api_token.token_exists" => __("The provided API token is invalid."),
            "need_to_complete_sid.required_sid" => __("The Need to complete SID is required."),
        ];
    }
}
