<?php

namespace App\Http\Controllers\Operator\v1;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Models\JobProgressMedia;
use App\Models\TrainingCourseSubmoduleJobProgress;
use App\Http\Requests\Operator\v1\TrainingCourseSubModuleJobsListingRequest;
use App\Http\Requests\Operator\v1\TrainingCourseSubmoduleJobProgressUpdateRequest;
use App\Http\Resources\Operator\v1\TrainingCourseJobResponseResource;
use App\Http\Resources\Operator\v1\TrainingCourseJobUserResponseResource;
use App\Repositories\Operator\v1\TrainingCourseSubModuleJobProgressRepository;
use App\Repositories\Operator\v1\TrainingCourseSubModuleJobProgressMediaRepository;
use App\Models\TrainingCourseSubmoduleDetails;
use App\Models\TrainingCourseSubmoduleProgress;
use App\Models\TrainingCourseModuleProgress;
use App\Models\TrainingCourseProgress;
use Illuminate\Support\Facades\Auth;
use App\Models\Notifications;
use App\Http\Requests\Operator\v1\CommonListingRequest;
use DBTableNames;
use App\Http\Resources\CustomCollection;
use DB;

class TrainingCourseSubmoduleJobController extends Controller
{   
    protected $jobsMediaModel;
    protected $jobsMediaRepository;
    protected $jobsProgressModel;
    protected $jobsProgressRepository;
    
    public function __construct() {
        $this->jobsMediaModel = new JobProgressMedia();
        $this->jobsMediaRepository = new TrainingCourseSubModuleJobProgressMediaRepository($this->jobsMediaModel);
        $this->jobsProgressModel = new TrainingCourseSubmoduleJobProgress();
        $this->jobsProgressRepository = new TrainingCourseSubModuleJobProgressRepository($this->jobsProgressModel);
       
    }
    
    /**
     * @OA\Post(
     *     path="/operator/jobs/getListing",
     *     tags={"Operator - Jobs Response"},
     *     summary="Get Jobs Responses",
     *     description="Get Jobs Responses",
     *     operationId="getListing",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="submodule_id",
     *                     description="Submodule Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 example={"submodule_id": 1, "per_page": 10, "page": 1, "search_key": "", "sort_by": "name", "order_by": "asc", "filters": {"uname": "", "gname": "", "status": ""}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getListing(TrainingCourseSubModuleJobsListingRequest $request) {
        try {
            $data = $this->jobsProgressRepository->getListing($request->all());
            return TrainingCourseJobResponseResource::collection($data->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * @OA\Get(
     *     path="/operator/jobs/{id}",
     *     tags={"Operator - Jobs Response"},
     *     summary="Get User Responses for Specific Job",
     *     description="Get User Responses for Specific Job",
     *     operationId="show",
     *     @OA\Parameter(
     *         description="Id of Job",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function show($id) {
        try {
            $jobProgress = $this->jobsProgressModel->find($id);
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $jobList = $this->jobsProgressModel->where('master_user_id', $operatorId)->pluck('id')->toArray();
            if(in_array($id, $jobList)){
                $autoApprove = TrainingCourseSubmoduleDetails::select('auto_approve')->find($jobProgress->submodule_id);
                if($autoApprove['auto_approve'] == 1){
                    $status = "Approved";
                }else{
                    $status = $jobProgress->status;
                } 
                if ($jobProgress) {
                    return response()->json(setResponse([
                        'user' => $jobProgress->user->name,
                        'photo_url' => $jobProgress->user->photo_url,
                        'comments' => $jobProgress->comments,
                        'operator_comments' => $jobProgress->operator_comments,
                        'updated_at' => date_format($jobProgress->updated_at, 'Y-m-d H:i:s'),
                        'status' => $status,
                        'unique_id' => TrainingCourseSubmoduleJobProgress::getUniqueId($jobProgress->master_user_id, $id),
                        'job_no'=> $jobProgress->job_no,
                        'master_user_mail' => TrainingCourseSubmoduleJobProgress::getMasterUserMail($jobProgress->master_user_id, $id),
                        'submodule_name' => $jobProgress->subModule->submodule_name,
                        'cost' => $jobProgress->subModule->cost,
                        'job_topic' => $jobProgress->subModule->job_topic,
                        'location'=> $jobProgress->location,
                        'latitude'=> ($jobProgress->latitude) ? (float)$jobProgress->latitude : null,
                        'longitude'=> ($jobProgress->longitude) ? (float)$jobProgress->longitude : null,
                        'data' => TrainingCourseJobUserResponseResource::collection($jobProgress->jobProgressMedia)
                    ]))
                    ->setStatusCode(Response::HTTP_OK);
                }
                return response()->json(setResponse([], ['message' => __('operator.job.not-found')]))->setStatusCode(Response::HTTP_OK);
            }else{
                return response()->json(setErrorResponse(__('You cannot access this record')))->setStatusCode(Response::HTTP_NOT_FOUND); 
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Put(
     *     path="/operator/jobs/{id}",
     *     tags={"Operator - Jobs Response"},
     *     summary="Update User Response for Specific Job Progress",
     *     description="Update User Response for Specific Job Progress",
     *     operationId="update",
     *     @OA\Parameter(
     *         description="Id of Job Progress",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Approved", "Rejected"}
     *                 ),
     *                 @OA\Property(
     *                     property="operator_comments",
     *                     description="Comment",
     *                     type="string"
     *                 ),
     *                 example={"status": "Approved", "operator_comments": "Manager's comment goes here"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function update(TrainingCourseSubmoduleJobProgressUpdateRequest $request, $id) {
        try {
            $jobProgress = $this->jobsProgressModel->find($id);
            if ($jobProgress) {
                $jobProgress->update([
                    'status' => $request->status,
                    'operator_comments' => $request->operator_comments
                ]);
                
                $user = $jobProgress->user;
                // Push Notification
                $mutable = $user->jobStatusNotificationCheck->is_on ?? 0;
                $jobStatusNotificationJob = (new \App\Jobs\JobStatusNotificationJob($user, $mutable, $jobProgress, $jobProgress->subModule->submodule_type_id, $user->user_relation->master_user_id, $request->status))->delay(env('QUEUE_JOB_DELAY_TIME'));
                dispatch($jobStatusNotificationJob);

                //Calculate Submodule Progress
                $submoduleData = TrainingCourseSubmoduleDetails::select('enable_time_spend', 'condition','time_spent')->find($jobProgress->submodule_id);
                $progressData = TrainingCourseSubmoduleProgress::where(['submodule_id' => $jobProgress->submodule_id, 'user_id' => $jobProgress->user_id])->first();
                
                if($request->status == 'Approved'){
                    if($submoduleData['enable_time_spend'] == 1){
                        if($submoduleData['condition'] == 'and'){
                            if($progressData->time_spent == $progressData->total_spent){
                                $progress = 100;
                            } else {
                                $timeSpent = (int)((100 * $progressData->time_spent) / $progressData->total_spent);
                                $progress = (int)(($timeSpent + 100) / 2);
                            }
                        } else {
                            $progress = 100;
                        }
                    } else {
                        $progress = 100;
                    }
                } else if($request->status == 'Rejected'){
                    if($submoduleData['enable_time_spend'] == 1){
                        $timeSpent = (int)((100 * $progressData->time_spent) / $progressData->total_spent);
                        if($submoduleData['condition'] == 'and'){
                            $progress = (int)($timeSpent / 2);
                        } else {
                            $progress = $timeSpent;
                        }
                    } else {
                        $progress = 0;
                    }
                }

                $progress = (int)($progress > 100 ? 100 : $progress);
                $progressData->update(['submodule_progress' => $progress]);

                // Module progress
                $mData = ['training_course_id' => $jobProgress->training_course_id, 'module_id' => $progressData->module_id, 'user_id' => $jobProgress->user_id];
                (new TrainingCourseModuleProgress)->calculateModuleProgress($mData);

                // Training course progress
                $tData = ['training_course_id' => $jobProgress->training_course_id, 'user_id' => $jobProgress->user_id];
                (new TrainingCourseProgress)->calculateTrainingCourseProgress($tData);

                return response()->json(setResponse([], ['message' => __('operator.job.updated')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setResponse([], ['message' => __('operator.job.not-found')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/jobs/getCourseJobList",
     *     tags={"Operator - Course Jobs"},
     *     summary="Get All Course Job List ",
     *     description="Get All Course Job List ",
     *     operationId="getCourseJobList",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                  @OA\Property(
     *                     property="endDate",
     *                     description="End Date",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="startDate",
     *                     description="Start Date",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="isExport",
     *                     description="for export csv",
     *                     type="integer"
     *                 ),
     *                 example={"per_page": 10, "page": 1, "search_key": "", "sort_by": "course_name", "order_by": "asc", "filters": {"course_name": "", "user_name":"","group_name": "", "created_at": "", "status": "","startDate": "2021-07-01", "endDate": "2021-09-30"}, "isExport" : 0}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="User Group list genetated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */ 
    public function getCourseJobList(CommonListingRequest $request)
    {   
        try {
            $data =  $request->all();  
            $perPage = isset($request->per_page) ? $request->per_page : 10;
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $selectColumns = [
                DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS. '.id',
                DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS. '.comments',
                DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS. '.operator_comments',
                DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS. '.training_course_id',
                DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS. '.status',
                DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS. '.created_at',
                DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS. '.job_no',
                'training_course.title as course_name',
                'training_course.primary_image',
                'groups.name as group_name',
                'users.name as user_name',
                'training_course_submodule_details.submodule_name',
                'training_course_submodule_details.job_topic',
                'training_course_submodule_details.cost',
                'users.email as user_email',
                'user_relations.unique_id',
                'master_users.email'
            ];

            $query = TrainingCourseSubmoduleJobProgress::
                join(DBTableNames::USERS, DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS . '.user_id', '=', 'users.id')
                ->leftJoin(DBTableNames::JOB_PROGRESS_MEDIA, DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS . '.id', '=', 'job_progress_media.job_progress_id')
                ->join(DBTableNames::USER_RELATIONS, function ($join)use($operatorId) {
                    $join->on(DBTableNames::USER_RELATIONS . '.user_id', '=', 'users.id');
                    $join->where(DBTableNames::USER_RELATIONS . '.master_user_id', '=', $operatorId);
                })
                ->leftJoin(DBTableNames::USER_GROUPS, DBTableNames::USER_RELATIONS . '.user_group_id', '=', 'groups.id')
                ->join(DBTableNames::TRAINING_COURSE,DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS.'.training_course_id','=','training_course.id')
                ->join(DBTableNames::TRAINING_COURSE_SUBMODULE_DETAILS,DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS.'.submodule_id','=','training_course_submodule_details.id')
                ->join(DBTableNames::MASTER_USERS, DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS . '.master_user_id', '=', 'master_users.id')
                ->groupBy(DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS.'.id')
                ->select($selectColumns);  

            // Searching
            $search = isset($data['search_key']) ? $data['search_key'] : "";
            $searchFields = ['training_course_submodule_job_progress.status',
            'training_course.title',
            'groups.name', 
            'users.name',
            'training_course_submodule_job_progress.created_at',
            'training_course_submodule_details.submodule_name',
            'training_course_submodule_details.job_topic',
            'training_course_submodule_details.cost',
            ];

            if (!empty($search)) {
                $query = $query->where(function($query) use ($searchFields, $search) {
                            foreach ($searchFields as $key => $field) {
                                $query->orWhere($field, 'LIKE', '%'.$search.'%');
                            }
                        });
            }
            // Show only assigned users if sub-operator has not permission to view all users data
            if (!canViewAllUsersData(auth()->guard('operator')->user()->role_id)) {
                $query = $query->whereIn(DBTableNames::USERS. '.id', getAssignedUsersId());
            }
            if ($request->isExport) {
                if(isset($request->ids) && !empty($request->ids)){
                    $query->whereIn(DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS.'.id', $request->ids);
                }
            }
            $query->where(DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS.'.master_user_id', $operatorId);
            $query->where(DBTableNames::USERS.'.deleted_at',null);
            $query->where(DBTableNames::TRAINING_COURSE_SUBMODULE_DETAILS.'.deleted_at',null);
            $query->where(DBTableNames::TRAINING_COURSE.'.deleted_at',null);
            /* Advance Filters */
            if (isset($data['filters']) && count($data['filters']) > 0) {
                if (isset($data['filters']['unique_id']) && $data['filters']['unique_id'] != "") {
                    $query = $query->where('user_relations.unique_id', 'LIKE', '%' . $data['filters']['unique_id'] . '%');
                }
                if (isset($data['filters']['job_no']) && $data['filters']['job_no'] != "") {
                    $query = $query->where('training_course_submodule_job_progress.job_no', 'LIKE', '%' . $data['filters']['job_no'] . '%');
                }
                if (isset($data['filters']['user_name']) && $data['filters']['user_name'] != "") {
                    $query = $query->where('users.name', 'LIKE', '%' . $data['filters']['user_name'] . '%');
                }
                if (isset($data['filters']['group_name']) && $data['filters']['group_name'] != "") {
                    $query = $query->where('groups.name', 'LIKE', '%' . $data['filters']['group_name'] . '%');
                }
                if (isset($data['filters']['course_name']) && $data['filters']['course_name'] != "") {
                    $query = $query->where('training_course.title', 'LIKE', '%' . $data['filters']['course_name'] . '%');
                }
                if (isset($data['filters']['status']) && $data['filters']['status'] != "") {
                    $query = $query->where('training_course_submodule_job_progress.status', 'LIKE', '%' . $data['filters']['status'] . '%');
                }
                if (isset($data['filters']['submodule_name']) && $data['filters']['submodule_name'] != "") {
                    $query = $query->where('training_course_submodule_details.submodule_name', 'LIKE', '%' . $data['filters']['submodule_name'] . '%');
                }
                if (isset($data['filters']['job_topic']) && $data['filters']['job_topic'] != "") {
                    $query = $query->where('training_course_submodule_details.job_topic', 'LIKE', '%' . $data['filters']['job_topic'] . '%');
                }
                if (isset($data['filters']['created_at']['startDate']) && $data['filters']['created_at']['startDate'] != "") {
                    $query = $query->whereDate('training_course_submodule_job_progress.created_at', '>=' , $data['filters']['created_at']['startDate'] );
                }
                if (isset($data['filters']['created_at']['endDate']) && $data['filters']['created_at']['endDate']) {
                    $query = $query->whereDate('training_course_submodule_job_progress.created_at', '<=' , $data['filters']['created_at']['endDate'] );
                }
                if (isset($data['filters']['cost']) && $data['filters']['cost'] != "") {
                    $query = $query->having('training_course_submodule_details.cost', 'LIKE', '%' . $data['filters']['cost'] . '%');
                }
            }
            
            // Sorting
            $sort = DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS . '.id'; // Default sort by ID
            $sortingKeys = [
                'user_name' => 'user_name',
                'group_name' => 'group_name',
                'created_at' => 'created_at',
                'course_name' => 'course_name',  
                'course_progress' => 'course_progress',
                'status' => 'status',
                'submodule_name' => 'submodule_name',  
                'job_topic' => 'job_topic',
                'cost' => 'cost',
                'unique_id' => 'unique_id',
                'job_no' => 'job_no'
            ];
            if (isset($data['sort_by']) && $data['sort_by'] != "") {
                if (array_key_exists($data['sort_by'], $sortingKeys)) {
                    $sort = $sortingKeys[$data['sort_by']];
                }
            }
            
            // Ordering (Default DESC)
            $order = isset($data['order_by']) ? $data['order_by'] : 'DESC';
            
            // Apply Sorting and Ordering
            if (in_array($sort, $sortingKeys)) {
                $query = $query->orderBy($sort, $order);
            }else{
                $query = $query->orderBy(DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS. '.created_at', 'DESC');
            }
            if ($request->isExport) {
                return $this->jobsMediaRepository->exportCsv(new CustomCollection($query->get(), 'App\Http\Resources\Operator\v1\TrainingCourseJobsResource'), $request->exportFields);
            }else{
                $jobs = $query->paginate($perPage);

            }

            return new CustomCollection($jobs, 'App\Http\Resources\Operator\v1\TrainingCourseJobsResource');
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/jobs/delete",
     *     tags={"Operator - Jobs Management"},
     *     summary="Delete Jobs",
     *     description="Delete Jobs",
     *     operationId="destroy",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="ids",
     *                     description="Ids of Resources",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 example={"ids": {1, 2}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function destroy(Request $request) {
        try {
            $ids = is_array($request->ids)? $request->ids: [];
            $this->jobsProgressModel->destroy($ids);
            return response()->json(setResponse([], ['message' => __('operator.jobs.deleted')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}

