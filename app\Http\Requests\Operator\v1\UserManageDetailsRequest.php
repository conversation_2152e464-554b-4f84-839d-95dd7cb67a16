<?php

namespace App\Http\Requests\Operator\v1;

use App\Http\Requests\CustomFormRequest;

class UserManageDetailsRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {
        return [
            "search_type" => 'required|in:email,unique_id',
            "search_key" => 'required',
        ];
    }

    public function messages() : array
    {
        return [
            'search_type.required' => __('The search type is required.'),
            'search_type.in' => __('Invalid search type.'),
            'search_key.required' => __('Search input required.'),
        ];
    }
}
