<?php

namespace App\Http\Middleware;

use App\Models\MasterUser;
use App\Models\UserRelation;
use Closure;

class SwitchOperator
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // Check if 'bundle-id' header or 'bundle_id' exists in the request
        $headerBundleId = $request->header('bundle-id') ?: $request->bundle_id;
        if ($headerBundleId) {
            // Normalize the bundle ID to lowercase
            $headerBundleId = strtolower($headerBundleId);
            // Get bundle IDs from config and normalize keys to lowercase
            $bundleIds = array_change_key_case(config('constants.bundle_ids'), CASE_LOWER);
            // Check if the provided bundle ID exists in the configured bundle IDs
            if (array_key_exists($headerBundleId, $bundleIds)) {
                $operatorId = $bundleIds[$headerBundleId];
                $operator = MasterUser::find($operatorId);
                $user = auth()->user();
                // Ensure the user is authenticated
                if ($user) {
                    $currentOperator = UserRelation::where('user_id', $user->id)->where('is_current_operator', 1)->value('master_user_id');
                    // Update user relations to set the current operator
                    if ($operatorId !== $currentOperator) {
                    UserRelation::where('user_id', $user->id)->update(['is_current_operator' => 0]);
                        if (!empty($operatorId)) {
                            UserRelation::where('user_id', $user->id)
                                ->where('master_user_id', $operatorId)
                                ->update(['is_current_operator' => 1]);
                        }
                    }
                }
            }
        }
        return $next($request);
    }
}