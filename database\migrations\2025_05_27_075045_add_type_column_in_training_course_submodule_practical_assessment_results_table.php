<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTypeColumnInTrainingCourseSubmodulePracticalAssessmentResultsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('training_course_submodule_practical_assessment_results', function (Blueprint $table) {
            if (!Schema::hasColumn('training_course_submodule_practical_assessment_results', 'type')) {
                $table->string('type')->default('PA')->after('adjust_result');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('training_course_submodule_practical_assessment_results', function (Blueprint $table) {
            if (Schema::hasColumn('training_course_submodule_practical_assessment_results', 'type')) {
                $table->dropColumn('type');
            }
        });
    }
}
