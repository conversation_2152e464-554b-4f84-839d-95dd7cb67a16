<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use App\Models\User;
use Illuminate\Console\Command;
use App\Jobs\CertificateExpireJob;
use Illuminate\Support\Facades\DB;
use App\Models\UserCourseCertificate;


class CertificateExpire extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'certificate:expire';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $yday = date('Y-m-d 00:00:00', strtotime("-30 day"));
        $expiringToday = UserCourseCertificate::whereDate('expiry_date', $yday)->get()->toArray();
        foreach ($expiringToday as $value) {
            dispatch(new CertificateExpireJob($value['user_id'], $value['training_course_id']));
        }
        die;
    }
}