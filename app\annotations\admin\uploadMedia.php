<?php
/**
     * @OA\Post(
     *     path="/admin/uploadMedia",
     *     tags={"Admin - Global Methods - Media Upload"},
     *     summary="Upload Media",
     *     description="Upload Media",
     *     operationId="uploadMedia",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="type",
     *                     description="types : modules / submodules / courses / users",
     *                     type="string"
     *                 ),
     *                  @OA\Property(
     *                     property="media_type",
     *                     description="types : submodules",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="id",
     *                     description="[ modules / submodules / courses: Training Course Id]",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="media_sub_type",
     *                     description="types : videoguide",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                      description="File",
     *                      property="file",
     *                      type="file",
     *                 ),
     *                 example={"type": "submodules","training_course_id":1 ,"file":"video.mp4", "media_type": "submodules"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */ 