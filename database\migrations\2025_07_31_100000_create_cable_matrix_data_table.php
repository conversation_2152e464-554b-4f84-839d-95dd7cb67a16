<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCableMatrixDataTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('cable_matrix_data', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('question_id');
            $table->unsignedBigInteger('master_user_id');
            $table->string('cable_diameter', 191)->nullable()->comment('Primary filter - Cable Diameter (mm)');
            $table->string('cable_type', 191)->nullable()->comment('Secondary filter - Cable Type');
            $table->text('cable_description')->comment('Final selection - Cable Description');
            $table->json('additional_data')->nullable()->comment('Other CSV columns (Index, Kg/m, Type)');
            $table->timestamps();
            $table->softDeletes();
            
            // Foreign keys
            $table->foreign('question_id')->references('id')->on('training_course_submodule_practical_assessment_questions')->onDelete('cascade');
            $table->foreign('master_user_id')->references('id')->on('master_users')->onDelete('cascade');
            
            // Indexes for performance
            $table->index(['question_id', 'cable_diameter', 'cable_type'])->name('idx_cable_matrix_question_filters');
            $table->index(['question_id', 'master_user_id'])->name('idx_cable_matrix_question_user');
            $table->fullText(['cable_diameter', 'cable_type', 'cable_description'])->name('idx_cable_matrix_fulltext');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('cable_matrix_data');
    }
}
