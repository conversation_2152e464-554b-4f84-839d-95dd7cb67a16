<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;

class ChangePasswordRequest extends CustomFormRequest {

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'old_password' => 'required|check_current_password:admin',
            'password' => [
                'required',
                'check_new_password:admin',
                'min:6',
                'max:64',
                'regex:/[a-z]/', // must contain at least one lowercase letter
                'regex:/[A-Z]/', // must contain at least one uppercase letter
                'regex:/[0-9]/', // must contain at least one digit
                'regex:/[-@$!%*#?&]/',
            ],
            'password_confirmation' => 'required|same:password',
        ];
    }
    
    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'old_password.required' => __('Old password is required.'),
            'old_password.check_current_password' => __('Old password is incorrect.'),
            'password.required' => __('Password is required.'),
            'password.check_new_password' => __('New password must not be same as old password.'),
            'password.regex' => __('Password must consist of one lowercase, uppercase, digit and a special character.'),
            'password_confirmation.required' => __('Confirm password is required.'),
            'password_confirmation.same' => __('Password and confirm password must be same.'),
        ];
    }

}
