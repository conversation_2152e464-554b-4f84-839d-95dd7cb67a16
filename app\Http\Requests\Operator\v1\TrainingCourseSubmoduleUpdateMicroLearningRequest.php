<?php

namespace App\Http\Requests\Operator\v1;

use DBTableNames;
use App\Http\Requests\CustomFormRequest;
use App\Models\TrainingCourseSubmoduleDetails;
use App\Models\TrainingCourseSubModuleFeedbackQuestion;
use App\Models\TrainingCourseSubModulePracticalAssessmentQuestion;
use App\Models\TrainingCourseModules;
use Illuminate\Validation\Rule;

class TrainingCourseSubmoduleUpdateMicroLearningRequest extends CustomFormRequest
{
    
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {
        return [
            'id' => [
                'required',
                'numeric',
                Rule::exists(DBTableNames::TRAINING_COURSE_SUBMODULE_DETAILS, 'id')->where(function ($query) {
                    $query->where('submodule_type_id', 18);
                })
            ],
            'submodule_name' => 'sometimes|required',
            'description' => 'sometimes|required',
            'thumbnail' => 'sometimes|required|image|mimes:jpg,jpeg,png',
            'file' => 'sometimes|required|mimes:html',
            
        ]; 
    }

    public function messages() : array
    {
        return [
           
            "submodule_name.required" => __("The submodule name is required."),
            "submodule_name.name_exists" => __("The submodule name already exists for the selected training course and module."),
            "thumbnail.required" => __("The thumbnail is required."),
            "description.required" => __("The description is required."),
            "thumbnail.image" => __("The thumbnail must be an image."),
            "thumbnail.mimes" => __("The thumbnail must be a file of type: jpg, jpeg, png."),
            "file.required" => __("The file is required."),
            "file.mimes" => __("The file must be a file of type: html."),
        ];
    }
}
