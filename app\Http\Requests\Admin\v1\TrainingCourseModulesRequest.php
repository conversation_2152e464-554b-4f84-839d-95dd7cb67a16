<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;

class TrainingCourseModulesRequest extends CustomFormRequest
{
    
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {
        return [
            'name' => 'required',
            "description" => 'required',
            "module_lock" => 'required',
            'image' => 'required',
            'status' => 'required|in:Active,Inactive',
            "submodule_complete" => 'required',
        ];
    }

    public function messages() : array
    {
        return [
            'name.required' => __('The name field is required.'),
            'image.required' => __('The image field is required.'),
            'description.required' => __('The description field is required.'),
            'module_lock.required' => __('The unlock date & time field is required.'),
            'status.required' => __('Status is required.'),
            'submodule_complete.required' => __('The module require field is required.'),
        ];
    }
}
