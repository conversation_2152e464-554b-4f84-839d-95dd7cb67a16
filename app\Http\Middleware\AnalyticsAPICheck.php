<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Contracts\Auth\Factory as Auth;
use Illuminate\Support\Facades\Auth as Auth1;
use Illuminate\Http\Response;
use  App\Models\User;
use  App\Models\MasterUser;
use Carbon\Carbon;
use App\Http\Requests\Operator\v1\AnalyticsAPIRequest;

class AnalyticsAPICheck
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */

    public function handle($request, Closure $next)
    {
        // check has api-key in header
        if($request->hasHeader('api-key')){
            // check operatorID exists in request parameter
            if(!$request->exists('operatorID')){
                // Operator ID is required in request param
                return response()->json(setErrorResponse('Operator ID is required.', Response::HTTP_UNPROCESSABLE_ENTITY))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }else{
                // Check if Operator ID is integer in request param
                if (is_numeric($request->operatorID)){
                    // Operator details based on operator ID in request param & api-key in request header
                    $operatorDetails = MasterUser::where('id',$request->operatorID)->where('analytics_api_key',$request->header('api-key'))->first();
                    // Check Operator details found
                    if($operatorDetails){
                        // Check if requested api-key in request header must be matched with the api-key getting from operator details
                        if($request->header('api-key') == $operatorDetails->analytics_api_key){
                            // Proceed for the next step
                            $response = $next($request);
                        }else{
                            // Unauthorize Access
                            return response()->json(setErrorResponse('Unauthorize Access!', Response::HTTP_UNAUTHORIZED))->setStatusCode(Response::HTTP_UNAUTHORIZED);
                        }
                    }else{
                        // Unauthorize Access
                        return response()->json(setErrorResponse('Unauthorize Access!', Response::HTTP_UNAUTHORIZED))->setStatusCode(Response::HTTP_UNAUTHORIZED);
                    }
                }else{
                    // Validation for Operator ID is integer in request param
                    return response()->json(setErrorResponse('Operator ID must be an integer.', Response::HTTP_UNPROCESSABLE_ENTITY))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            }
        }else{
            // Unauthorize Access
            return response()->json(setErrorResponse('Unauthorize Access!', Response::HTTP_UNAUTHORIZED))->setStatusCode(Response::HTTP_UNAUTHORIZED);
        }
        return $response;
    }
}
