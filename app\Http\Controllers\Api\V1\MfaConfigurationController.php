<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Http\Response;
use App\Models\User;

use App\Http\Requests\V1\enableMFARequest;
use App\Http\Requests\V1\verifyMFACodeRequest;
use App\Http\Requests\V1\disableMFARequest;
use App\Http\Requests\V1\resetMFARequest;

use Aws\CognitoIdentityProvider\CognitoIdentityProviderClient;
use Aws\CognitoIdentityProvider\Exception\CognitoIdentityProviderException;
use Aws\Exception\AwsException;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use App\Http\Resources\V1\MfaDetailResource;
use App\Http\Resources\V1\VerifyMFACodeResource;
use App\Notifications\SendResetMFANotification;

class MfaConfigurationController extends Controller {

    private $client;
    private $UserPoolId;
    private $clientId;
    private $clientSecret;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
		//update policy for IAM user for accessing aws sdk methods
		//sdk for laravel
		//composer require aws/aws-sdk-php
		//"aws/aws-sdk-php": "^3.263",
		//Qr code generation
		//"simplesoftwareio/simple-qrcode": "^4.2"
		//php artisan make:migration add_mfa_fields_to_users_table --table=users
		$this->UserPoolId = env('AWS_COGNITO_USER_POOL_ID');
		$this->clientId = env('AWS_COGNITO_CLIENT_ID');
		$this->clientSecret = '';
		$this->client = new CognitoIdentityProviderClient([
			'region' => env('AWS_COGNITO_REGION'), // replace with your preferred region
			'version' => 'latest', // use the latest API version
			'credentials' => [
			'key' => env('AWS_COGNITO_ACCESS_KEY_ID'),
			'secret' => env('AWS_COGNITO_SECRET_ACCESS_KEY'),
			],
		]);
    }

    private function adminGetUser($data = array()) {
	try {
	    $is_user_exist_on_aws = 0;
	    $user_data = [];
	    if (isset($data['mfa_user_id']) && $data['mfa_user_id'] != '') {
		$user = $this->client->adminGetUser([
		    'UserPoolId' => $this->UserPoolId, // REQUIRED
		    'Username' => $data['mfa_user_id'], // REQUIRED
		]);
		$is_user_exist_on_aws = 1;
		$user_data = $user;
	    }
	} catch (AwsException $e) {
	    //
	}
	return [
	    'is_exist' => $is_user_exist_on_aws,
	    'user_data' => $user_data,
	];
    }

	/**
     * @OA\Post(
     *     path="/api/mfa/enable-mfa",
     *     tags={"Mobile - Enable MFA"},
     *     summary="Enable MFA",
     *     description="Enable MFA",
     *     operationId="enable-mfa",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="email",
     *                     description="Email Address",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="password",
     *                     description="Password",
     *                     type="string"
     *                 ),
     *                 example={"email": "<EMAIL>","password": "Test@123"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */
    public function enableMFA(enableMFARequest $request) {
        try {
			$data = $request->all();
			$email = $data['email'];
			$password = $data['password'];
			$user = User::whereEmail($email)->first();
			if (empty($user)){
				return response()->json(setErrorResponse(__('user.MfaConfigurtion.emailIncorrect')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
			}
			if (auth()->user()->id != $user->id){
				return response()->json(setErrorResponse(__('user.UserController.notFound')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
			}
			if (!Hash::check($password, $user->password)){
				return response()->json(setErrorResponse(__('user.MfaConfigurtion.passwordIncorrect')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
			}
			$awsUser = $this->adminGetUser(['mfa_user_id' => $user->mfa_user_id]);
			if ($awsUser['is_exist'] == 1) {
				$mfa_user_id = $awsUser['user_data']['Username'];
			} else {
				$userCreated = $this->client->adminCreateUser([
					'UserPoolId' => $this->UserPoolId,
					'Username' => $email,
					'TemporaryPassword' => $password,
					'UserAttributes' => [
						[
						'Name' => 'email',
						'Value' => $email,
						],
						[
						'Name' => 'email_verified',
						'Value' => 'true'
						]
					],
					'DesiredDeliveryMediums' => ['EMAIL'],
					'ForceAliasCreation' => false,
					'MessageAction' => 'SUPPRESS',
				]);
				$userUuid = $userCreated->get('User');
				$mfa_user_id = $userUuid['Username'];
				$user->mfa_user_id = $mfa_user_id;
				$user->save();
				$passwordUpdated = $this->client->adminSetUserPassword([
					'UserPoolId' => $this->UserPoolId,
					'Username' => $mfa_user_id,
					'Password' => $password,
					'Permanent' => true
				]);
			}
			if ($user->mfa_qr_code == '' || $user->mfa_qr_code == null) {
				$updateMFAPreference = $this->client->adminSetUserMFAPreference([
					'SoftwareTokenMfaSettings' => [
					'Enabled' => false,
					'PreferredMfa' => false,
					],
					'Username' => $mfa_user_id,
					'UserPoolId' => $this->UserPoolId,
				]);
				$initiateAuth = $this->client->adminInitiateAuth([
					'AuthFlow' => 'ADMIN_NO_SRP_AUTH',
					'ClientId' => $this->clientId,
					'UserPoolId' => $this->UserPoolId,
					'AuthParameters' => [
						'USERNAME' => $mfa_user_id,
						'PASSWORD' => $password,
					]
				]);
				$accessToken = $initiateAuth['AuthenticationResult']['AccessToken'];

				$associateSoftwareToken = $this->client->associateSoftwareToken([
					'AccessToken' => $accessToken,
				]);
				$mfa_user_secret = $associateSoftwareToken->get('SecretCode');
				$user->mfa_user_secret = $mfa_user_secret;

				// Generate new QR code image
                                $header_bundle_id = strtolower($request->header('bundle-id'));
                                $bundle_ids = array_change_key_case(config('constants.bundle_ids'), CASE_LOWER);
                                if (in_array($header_bundle_id, array_keys($bundle_ids))) {
                                    $issuer = $bundle_ids['app_name_' . $bundle_ids[$header_bundle_id]];
                                } else {
                                    $issuer = (env('APP_NAME') ?? "SkillsBase");
                                }
				$qrCodeText = "otpauth://totp/" . $issuer . ":{$user->email}?secret={$mfa_user_secret}&issuer=" . $issuer;
				$qrCode = generateQRCode($qrCodeText, 'png');
				$user->mfa_qr_code = $qrCode;
				$user->save();
				$localPath = storage_path('/qrcodes/') . $qrCode;
				$s3Path = str_replace('{user_id}', $user->id, config('constants.aws.mfa_qrcodes'));
				\Storage::disk('s3')->put($s3Path . '/' . $qrCode, file_get_contents($localPath));
				unlink($localPath);
			}
			$returnData = new \stdClass();
			$returnData->mfa_user_id = $user->mfa_user_id;
			$returnData->mfa_user_secret = $user->mfa_user_secret;
			$returnData->qr_code = $user->mfa_qr_code_url;
			return ($returnData) ?
				(new MfaDetailResource($returnData)) :
				response()->json(setErrorResponse(__('user.TrainingCourse.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
		} catch (AwsException $e) {
			$awsError = getAwsErrorMessage($e);
			return response()->json(setErrorResponse(__($awsError['message'])))->setStatusCode($awsError['code']);
		} catch (\Exception $e) {
			return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
		}
    }

    /**
     * @OA\Post(
     *     path="/api/mfa/verifyMFACode",
     *     tags={"Mobile - Verify MFA Code"},
     *     summary="Verify MFA Code",
     *     description="Verify MFA Code",
     *     operationId="verifyMFACode",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
	 * 				   @OA\Property(
     *                     property="mfa_user_id",
     *                     description="MFA User ID",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="password",
     *                     description="Password",
     *                     type="string"
     *                 ),
	 * 				   @OA\Property(
     *                     property="code",
     *                     description="Code",
     *                     type="string"
     *                 ),
     *                 example={"mfa_user_id": "784a0797-1f8a-4f81-a708-67eb200b34a7","password":"Test@123","code": "356128"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */
    public function verifyMFACode(verifyMFACodeRequest $request) {
		try {
			$data = $request->all();
			$mfa_user_id = $data['mfa_user_id']; // replace with the user's actual username
			$password = $data['password']; // replace with the user's actual password
			$user_pool_id = $this->UserPoolId;
			$client_id = $this->clientId; // replace with your actual client ID
			$client_secret = $this->clientSecret; // replace with your actual client secret
			//$secret_hash = base64_encode(hash('sha256', $username . $client_id . $client_secret, true));
			$user = User::whereMfaUserId($mfa_user_id)->first();
			
			if ($user && auth()->user()->id == $user->id && Hash::check($password, $user->password)) {
				$updateMFAPreference = $this->client->adminSetUserMFAPreference([
					'SoftwareTokenMfaSettings' => [
					'Enabled' => false,
					'PreferredMfa' => false,
					],
					'Username' => $mfa_user_id,
					'UserPoolId' => $user_pool_id,
				]);
				$initiateAuth = $this->client->adminInitiateAuth([
					'AuthFlow' => 'ADMIN_NO_SRP_AUTH',
					'ClientId' => $client_id,
					'UserPoolId' => $user_pool_id,
					'AuthParameters' => [
					'USERNAME' => $mfa_user_id,
					'PASSWORD' => $password,
					],
					//'ClientMetadata' => [
					//'SECRET_HASH' => $secret_hash,
					//],
				]);
				$accessToken = $initiateAuth['AuthenticationResult']['AccessToken'];
				$totpVerify = $this->client->verifySoftwareToken([
					'AccessToken' => $accessToken,
					'FriendlyDeviceName' => 'Google Authenticator app',
					'UserCode' => $data['code'], // REQUIRED
				]);
				$returnData = new \stdClass();
				if ($totpVerify['Status'] === 'SUCCESS') {
					$updateMFAPreference = $this->client->adminSetUserMFAPreference([
					'SMSMfaSettings' => [
						'Enabled' => false,
						'PreferredMfa' => false,
					],
					'SoftwareTokenMfaSettings' => [
						'Enabled' => true,
						'PreferredMfa' => true,
					],
					'Username' => $mfa_user_id,
					'UserPoolId' => $user_pool_id,
					]);
					$returnData->is_valid = 1;
					$user->is_mfa_enable=1;
					$user->save();
					return new VerifyMFACodeResource($returnData);
				} else {
					$returnData->is_valid = 0;
					$user->is_mfa_enable=0;
					$user->save();
					return new VerifyMFACodeResource($returnData);
				}
			} else {
				return response()->json(setErrorResponse(__('admin.UserController.not_found')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
			}
		} catch (AwsException $e) {
			$awsError = getAwsErrorMessage($e);
			return response()->json(setErrorResponse(__($awsError['message'])))->setStatusCode($awsError['code']);
		} catch (Exception $e) {
			return response()->json(setErrorResponse(__('user.MfaConfigurtion.invalid_code')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
		}
    }

    /**
     * @OA\Post(
     *     path="/api/mfa/disable-mfa",
     *     tags={"Mobile - Disable MFA"},
     *     summary="Disable MFA",
     *     description="Disable MFA",
     *     operationId="disable-mfa",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="mfa_user_id",
     *                     description="MFA User ID",
     *                     type="string"
     *                 ),
     *                 example={"mfa_user_id": "28edf54f-27c3-4863-980d-8ac1af076e71"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */
    public function disableMFA(disableMFARequest $request) {
		try {
			$data = $request->all();
			$mfa_user_id = $data['mfa_user_id'];
			$user = User::whereMfaUserId($mfa_user_id)->first();
			if (empty($user)){
			    return response()->json(setErrorResponse(__('admin.UserController.not_found')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
			}
			if (isset($data['password']) && !Hash::check($data['password'], $user->password)){
			    return response()->json(setErrorResponse(__('user.MfaConfigurtion.passwordIncorrect')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
			}
			if ($user && auth()->user()->id == $user->id) {
				$updateMFAPreference = $this->client->adminSetUserMFAPreference([
					'SMSMfaSettings' => [
					'Enabled' => false,
					'PreferredMfa' => false,
					],
					'SoftwareTokenMfaSettings' => [
					'Enabled' => false,
					'PreferredMfa' => false,
					],
					'Username' => $mfa_user_id,
					'UserPoolId' => $this->UserPoolId,
				]);
				$user->is_mfa_enable=0;
				$user->save();
				return response()->json(setResponse(['message' => __('user.MfaConfigurtion.disabled')],['message' => __('user.MfaConfigurtion.disabled')]))->setStatusCode(Response::HTTP_OK);
			} else {
				return response()->json(setErrorResponse(__('admin.UserController.not_found')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
			}
		} catch (AwsException $e) {
			$awsError = getAwsErrorMessage($e);
			return response()->json(setErrorResponse(__($awsError['message'])))->setStatusCode($awsError['code']);
		} catch (\Exception $e) {
			return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
		}
    }

	/**
	 * Common function to change password (Use in change & forgot user password)
	 */
	public function adminSetUserPassword($mfa_user_id, $new_password) {
		try {
			$passwordUpdated = $this->client->adminSetUserPassword([
				'UserPoolId' => $this->UserPoolId,
				'Username' => $mfa_user_id,
				'Password' => $new_password,
				'Permanent' => true
			]);
			return true;
		} catch (AwsException $e) {
			return false;
		} catch (\Exception $e) {
			return false;
		}
	}

	/**
     * @OA\Post(
     *     path="/api/mfa/reset-mfa",
     *     tags={"Mobile - Reset MFA"},
     *     summary="Reset MFA",
     *     description="Reset MFA",
     *     operationId="reset-mfa",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="user_id",
     *                     description="User ID",
     *                     type="string"
     *                 ),
     *                 example={"user_id": 267}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */
	public function resetMFAMail(resetMFARequest $request)
	{
		try {
			$user = User::whereId($request->user_id)->first();
			if ($user && auth()->user()->id == $user->id) {
				if($user->mfa_qr_code != ''){
					//Checking for email address if smartawards contains only digits before @
                    $smartAwardsMailSendFlag = smartAwardsEmailSendResctrict($user->email);
					if($smartAwardsMailSendFlag == 1){
						$user->notify(new SendResetMFANotification($user));
					}
					return response()->json(setResponse([], ['message' =>  __('user.MfaConfigurtion.qrCodeSent')]))->setStatusCode(Response::HTTP_OK);
				}else{
					return response()->json(setErrorResponse(__('user.MfaConfigurtion.qrCodeNotAvailable')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
				}
			} else {
				return response()->json(setErrorResponse(__('admin.UserController.not_found')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
			}
		}catch (\Exception $e) {
			return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
		}
	}
}
