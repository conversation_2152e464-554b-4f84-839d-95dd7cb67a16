<?php
/**
     * @OA\Get(
     *     path="/api/trainingCourseModule/details/{id}",
     *     tags={"Mobile - Training Course Module"},
     *     summary="Training Course Module Description",
     *     description="Training Course Module Description",
     *     operationId="index",
     *     @OA\Parameter(
     *         description="Id of training course module",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */