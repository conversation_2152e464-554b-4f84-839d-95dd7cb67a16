<?php
/**
     * @OA\Get(
     *     path="/api/cmsPages/{slug}",
     *     tags={"Mobile - CmsPages"},
     *     summary="Get Cms page details",
     *     description="Get Cms page details",
     *     operationId="cmsPages",
     *     @OA\Parameter(
     *         description="slug",
     *         name="slug",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     * )
     */