<?php

namespace App\Exceptions;

use Throwable;
use Illuminate\Support\Facades\Log;

class NewRelicErrorHandler
{
    /**
     * Report the exception to New Relic with detailed context.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public static function report(Throwable $exception)
    {
        if (extension_loaded('newrelic')) {
            // Get exception details
            $message = $exception->getMessage();
            $file = $exception->getFile();
            $line = $exception->getLine();
            $trace = $exception->getTraceAsString();
            $class = get_class($exception);
            
            // Add exception details as custom parameters
            \newrelic_add_custom_parameter('error.class', $class);
            \newrelic_add_custom_parameter('error.file', $file);
            \newrelic_add_custom_parameter('error.line', $line);
            \newrelic_add_custom_parameter('error.trace', $trace);
            
            // Notice the error in New Relic
            \newrelic_notice_error($message, $exception);
            
            // For critical errors, you might want to add more context
            if (method_exists($exception, 'getStatusCode') && $exception->getStatusCode() >= 500) {
                \newrelic_add_custom_parameter('error.severity', 'critical');
            } elseif ($exception instanceof \Symfony\Component\HttpKernel\Exception\HttpExceptionInterface) {
                // For Symfony HTTP exceptions
                if ($exception->getStatusCode() >= 500) {
                    \newrelic_add_custom_parameter('error.severity', 'critical');
                }
            }
        }
        
        // Also log to the default logger for redundancy
        Log::error($exception);
    }
}
