<?php

namespace App\Http\Controllers\Admin\v1;

use Illuminate\Http\Response;
use App\Models\ProductType;
use App\Http\Requests\Admin\v1\CommonListingRequest;
use App\Http\Requests\Admin\v1\ProductTypeRequest;
use App\Http\Requests\Admin\v1\ImportCSVFileRequest;
use App\Http\Requests\Admin\v1\CommonBulkStatusChangeRequest;
use App\Http\Resources\Admin\v1\ProductTypeResource;
use App\Repositories\Admin\v1\ProductTypeRepository;

class ProductTypeController extends Controller
{
    private $model;
    private $repository;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->model = new ProductType();
        $this->repository = new ProductTypeRepository($this->model);
    }

    /**
     * Get Product Types List
     *
     * @return \Illuminate\Http\Response
     */
    public function getListing(CommonListingRequest $request) {
        try {
            $productTypes = $this->repository->getListing($request->all());
            if ($request->isExport) {
                return $this->repository->exportCsv($productTypes->where('master_user_id', auth()->guard('admin')->id())->get());
            }
            return ProductTypeResource::collection($productTypes->where('master_user_id', auth()->guard('admin')->id())->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(ProductTypeRequest $request) {
        try {
            $data = $request->all();
            $adminId = auth()->guard('admin')->id();
            $data['master_user_id'] = $adminId;
            $data['added_by'] = 'Admin';
            $this->model->create($data);
            return response()->json(setResponse([], ['message' => __('admin.product-type.created')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Product Type Details
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id) {
        try {
            $productType = $this->model->find($id);
            return ($productType) ?
                    (new ProductTypeResource($productType)) :
                    response()->json(setResponse([], ['message' => __('admin.product-type.not-found')]))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Update Product Types Details
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(ProductTypeRequest $request, $id) {
        try {
            $productType = $this->model->find($id);
            if ($productType) {
                $productType->update(['name' => $request->name, 'status' => $request->status, 'image' => ($request->image??null)]);
                return response()->json(setResponse([], ['message' => __('admin.product-type.updated')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setResponse([], ['message' => __('admin.product-type.not-found')]))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Delete Product Types
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id) {
        try {
            $productType = $this->model->find($id);
            if ($productType) {
                $adminId = auth()->guard('admin')->id();
                if($adminId == $productType->master_user_id){
                    $productType->delete();
                    return response()->json(setResponse([], ['message' => __('admin.product-type.deleted')]))->setStatusCode(Response::HTTP_OK);
                }else{
                    return response()->json(setResponse([], ['message' => __('admin.product-type.not-found')]))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
            }
            return response()->json(setResponse([], ['message' => __('admin.product-type.not-found')]))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * Change Product Type(s) Status
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function changeStatus(CommonBulkStatusChangeRequest $request) {
        try {
            $this->model->whereIn('id', $request->ids)->whereAddedBy('Admin')->update(['status' => $request->status]);
            return response()->json(setResponse([], ['message' => __('admin.product-type.status-updated')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * Import Product Types
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function importProductTypes(ImportCSVFileRequest $request) {
        try {
            $isImported = $this->repository->importProductTypes($request);
            if($isImported) {
                return response()->json(setResponse([], ['message' => __('admin.product-type.import-success')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setErrorResponse(__('admin.product-type.import-error')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
