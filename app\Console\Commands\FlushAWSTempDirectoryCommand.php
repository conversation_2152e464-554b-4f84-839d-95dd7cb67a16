<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class FlushAWSTempDirectoryCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'aws:flush-temp-folder';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command is for delete unused files uploaded on temp directory under training course';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $path = config('constants.aws.training_course') . '/temp/';
        $objects = getS3ClientIterator($path);
        foreach ($objects as $object) {
            $uploadedTime = date_create_from_format('Y-m-d H:i:s', $object['LastModified']->format('Y-m-d H:i:s'), (new \DateTimeZone(env('APP_TIMEZONE', 'UTC'))));
            $currentTime = date_create_from_format('Y-m-d H:i:s', date('Y-m-d H:i:s'), (new \DateTimeZone(env('APP_TIMEZONE', 'UTC'))));
            if ($uploadedTime->diff($currentTime)->days > 0) { // Check if file is there for more than 24 hours
                deleteFileFromS3($object['Key']);
            }
        }
    }
}
