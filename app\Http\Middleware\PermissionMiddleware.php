<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Response;

class PermissionMiddleware {

    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @param string $guard
     * @return mixed
     */
    public function handle($request, Closure $next, $guard = null) {
	$route = $request->route();
	// Check if the route is namedRoute
	if (count($route) > 1 && isset($route[1]['as']) && $route[1]['as'] != '') {
	    $routeName = explode('.', $route[1]['as']);
	    $moduleName = $routeName[1] ?? '';
	    $action = $routeName[2] ?? '';
	    $user = auth()->guard($guard)->user();
	    if (!is_null($user->parent_id)) {
		$permissions = getAccessPermissions($user->role_id, $user->id, false);
		if (count($permissions) > 0) { // Only check for sub-admins and sub-operators
		    if (($action != 'sendUserDetailsEmail' && $action != 'sendIQAUserDetailsEmail' && $action != 'restore') && isset($permissions[$moduleName]) && !isset($permissions[$moduleName][$action])) {
			// If module permission is set and user doesn't have access then send unautorized access response
			return response()
					->json(setErrorResponse("You don't have permission to perform this action. Please contact your Operator."))
					->setStatusCode(Response::HTTP_FORBIDDEN);
		    }
		}
	    }
	}
	return $next($request);
    }

}
