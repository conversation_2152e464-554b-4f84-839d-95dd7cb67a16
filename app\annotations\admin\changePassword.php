<?php
/**
     * @OA\Put(
     *     path="/admin/changePassword",
     *     tags={"Admin - User"},
     *     summary="Master User change password process",
     *     description="Master User change password process",
     *     operationId="changePassword",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="old_password",
     *                     description="Old Password",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="password",
     *                     description="Password",
     *                     type="string"
     *                 ),
     *                  @OA\Property(
     *                     property="password_confirmation",
     *                     description="Confirm Password",
     *                     type="string"
     *                 ),
     *                 example={"old_password": "Test@123", "password": "Indianic@123", "password_confirmation": "Indianic@123"}
     *              )
     *          )
     *      ),
     *     security={
     *         {"Admin": {}}
     *     },
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     * )
     */