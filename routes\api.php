<?php
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Response;

/*
  |--------------------------------------------------------------------------
  | Application API Routes
  |--------------------------------------------------------------------------
 */

$router->get('/', function () use ($router) {
    return $router->app->version();
});

// Custom Deeplinks Routes (outside of API prefix for clean URLs)
$router->get('/dl/{shortCode}', ['uses' => 'V1\DeeplinkController@resolve']);

$router->group(["namespace" => "V1", "prefix" => "api",'middleware' => ['system-logs:api','set-custom-headers:api','switchOperator','override-mail-config:api']], function () use ($router) {

    // Custom Deeplinks API Routes
    $router->group(['prefix' => 'deeplinks'], function () use ($router) {
        $router->get('/health', 'DeeplinkController@health');
        $router->get('/{shortCode}/stats', ['middleware' => ['auth'], 'uses' => 'DeeplinkController@getStats']);
        $router->post('/', ['middleware' => ['auth'], 'uses' => 'DeeplinkController@create']);
    });
    $router->group(['prefix' => 'oauth'], function () use ($router) {
	$router->post('login', 'AuthController@login');
	$router->post('android-notification', 'AuthController@testAndroidNotification');
	$router->post('ios-notification', 'AuthController@testiOSNotification');
	//web routes with recaptcha
	$router->group(['middleware' => ['captchaVerification']], function () use ($router) {
	    $router->post('web/login', 'AuthController@webLogin');
	});
    });
    $router->get('getDomainSuggestions', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'AuthController@getDomainSuggestions', 'as' => 'getDomainSuggestions']);
    $router->group(['middleware' => ['check-maintenance-mode:api']], function () use ($router) {
	$router->group(['prefix' => 'oauth'], function () use ($router) {
	    $router->post('register', 'AuthController@register');
	    //$router->post('login', 'AuthController@login');
	    $router->post('socialLogin', 'AuthController@socialLogin');
	    $router->post('forgotPassword', 'AuthController@forgotPassword');
	    $router->post('resetPassword', 'AuthController@resetPassword');

	    //web routes with recaptcha
	    $router->group(['middleware' => ['captchaVerification']], function () use ($router) {
		//$router->post('web/login', 'AuthController@webLogin');
		$router->post('web/forgotPassword', 'AuthController@webForgotPassword');
		$router->post('web/resetPassword', 'AuthController@webResetPassword');
	    });
	});

	// Safe link
	$router->get('/user-disabled/{email}/{token}', function () {
	    return view('register_verified_email');
	});

	$router->get('/user-disabled/{email}/{token}/{appName}', 'AuthController@verifyUseremailLinkView');

	$router->post('/verifyUseremail', 'AuthController@verifyUseremailLink');

	$router->group(['middleware' => ['override-mail-config:api']], function () use ($router) {
		$router->get('/login_link', function () {
			return view('login_link');
		});

		$router->get('/reset_password', function (Request $request) {
			$Time = strtotime("+15 minutes", $request->date);
			if($Time>strtotime(date("Y-m-d H:i:s"))){
				return view('reset_password');
			}else{
				return view('link_expired');
			}
		});
	});

    /* Fetch and validate user course certificate */
    $router->get('certificate_validate',['uses'=>'UserCourseCertificateController@validateCertificateNumber','as'=>'api.certificate.certificate_validate']);
    $router->group(['prefix' => 'certificate'], function () use ($router) {
        $router->get('/', ['uses'=>'UserCourseCertificateController@showDetail','as'=>'api.certificate.showDetail']);
		$router->get('{id}', ['uses'=>'UserCourseCertificateController@show','as'=>'api.certificate.show']);
		$router->get('validate/{id}',['uses'=>'UserCourseCertificateController@validateCertificate','as'=>'api.certificate.validate']);
	    });

	$router->post('resendVerifyEmailLink', ['uses' => 'AuthController@resendVerifyEmailLink']);
	$router->get('verifyEmail/{key}', ['uses' => 'AuthController@viewVerifyEmail', 'as' => 'viewVerifyEmail']);
	$router->post('verifyEmail/{key}', ['uses' => 'AuthController@verifyUserEmail', 'as' => 'verifyEmail']);
	$router->get('password/reset', ['uses' => 'AuthController@reset']);
	$router->get('generatePassword', ['uses' => 'AuthController@generatePassword']);
	$router->post('password/reset', ['as' => 'password.reset', 'uses' => 'AuthController@resetPassword']);

	$router->post('loginWithEmailLink', ['uses' => 'AuthController@loginWithEmailLink']);
	$router->post('verifyLoginEmailLinkToken', ['uses' => 'AuthController@verifyLoginEmailLinkToken', 'as' => 'verifyLoginEmailLinkToken']);

	// Cms Pages
	$router->get('cmsPages/{slug}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'CmsPagesController@index', 'as' => 'cmsPages']);
	$router->post('oauth/logout', 'AuthController@logout');
	// WebAPP QR CODE
	$router->get('/getQRCode', 'AuthController@getQRCode');

	//GetStream Chat Token
	$router->get('get_stream/GetStreamToken', ['uses' => 'GetStreamController@GetStreamToken']);
	$router->get('get_stream/DeleteGetStreamUSer', ['uses' => 'GetStreamController@DeleteGetStreamUSer']);

	$router->group(['middleware' => ['applog-out','auth', 'activeUserCheck']], function () use ($router) {
	    $router->post('/verifyQRCode', 'AuthController@verifyQRCode');
		$router->post('/extractDeeplink', ['uses' => 'DeeplinkController@extractDeeplink']);

	    $router->post('/search', 'SearchController@index');
	    $router->get('/getProfileDetails', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'UserController@getProfileDetails', 'as' => 'users.getProfileDetails']);
	    $router->post('/updateProfileDetails', 'UserController@updateProfileDetails');

	    $router->group(['prefix' => 'notifications'], function () use ($router) {
		$router->get('/', 'NotificationsController@index');
		$router->post('/', 'NotificationsController@store');
		$router->get('/list', ['middleware' => ['replica.db'], 'uses' => 'NotificationsController@notificationList']);
		$router->post('/markasread', 'NotificationsController@markAsRead');
		$router->delete('/delete', 'NotificationsController@deleteNotifications');
	    });

	    $router->group(['prefix' => 'users'], function () use ($router) {
		//$router->get('/', 'UserController@userList');
		$router->get('/operatorList', ['middleware' => ['replica.db'], 'uses' => 'UserController@operatorList']);
		$router->get('/contactUs', ['uses' => 'UserController@contactUs', 'as' => 'users.contactUs']);
		$router->post('/submitHelp', 'UserController@submitHelp');
		$router->put('/switchOperator', 'UserController@switchOperator');
        $router->get('/get_stream_details/{id}', 'UserController@getGetStreamDetails');
		$router->get('/{userId}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'UserController@getProfileDetails', 'as' => 'users.profile']);
		$router->put('changePassword', 'UserController@changePassword');
		$router->put('/{userId}', 'UserController@updateProfileDetails');
		$router->get('/homeScreen/{id}', ['uses' => 'UserController@homeScreen']);
		$router->post('/getOperatorsFromOuc', 'UserController@getOperatorsFromOuc');
		$router->delete('/assessorUnassign/{id}', 'UserController@assessorUnassign');
	    });

	    //practical assessment
        $router->get('/assessorAssignByEmail', ['uses' => 'UserController@assessorAssignByEmail', 'as' => 'users.assessorAssignByEmail']);
	    $router->get('/assessorAssign', ['uses' => 'UserController@AssessorAssign', 'as' => 'users.AssessorAssign']);
	    $router->get('/getAssessorAssignUser', ['middleware' => ['replica.db'], 'uses' => 'UserController@getAssessorAssignUser']);
	    $router->group(['prefix' => 'practicalAssessment'], function () use ($router) {
		$router->post('/users', 'PracticalAssessmentSubModuleController@addAssessmentUsers');
		$router->get('/users', 'PracticalAssessmentSubModuleController@getAssessmentUsers');
        $router->get('/getUsers/{id}', 'PracticalAssessmentSubModuleController@getUsers');
        $router->get('/getAssessorHistory', 'PracticalAssessmentSubModuleController@getAssessorHistory');
		$router->get('/getAutoSaveSubmoduleDetail', ['middleware' => ['replica.db'], 'uses' => 'PracticalAssessmentSubModuleController@getAutoSaveSubmoduleDetail']);
	    });


		$router->group(['prefix' => 'selfAssessment'], function () use ($router) {
        $router->get('/getUsers/{id}', 'PracticalAssessmentSubModuleController@getUserResult');
	    });

	    //assign user to operator via invite link
	    $router->post('/userassign', 'UserController@assignUserInviteLink');

	    $router->group(['prefix' => 'userWebNotifications'], function () use ($router) {
		$router->get('/notificationCount', ['middleware' => ['replica.db'], 'uses' => 'UserController@notificationCount']);
	    });

	    $router->group(['prefix' => 'news'], function () use ($router) {
		//$router->get('/', 'NewsController@index');
		$router->get('/', ['uses' => 'NewsController@index', 'as' => 'news.list']);
		$router->get('/{id}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'NewsController@show', 'as' => 'news.show']);
	    });

	    $router->group(['prefix' => 'products'], function () use ($router) {
		$router->post('/feedback', 'ProductController@feedback');
		$router->get('/getFilters', ['uses' => 'ProductController@getFilters', 'as' => 'products.getFilters']);
		$router->post('/', ['uses' => 'ProductController@index', 'as' => 'products.list']);
		$router->get('/{id}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'ProductController@show', 'as' => 'products.show']);
		$router->post('getCategories', ['middleware' => ['replica.db'], 'uses' => 'ProductController@getProductTypes', 'as' => 'products.getCategories']);
		$router->post('getProductsByCat/{id}', ['middleware' => ['replica.db'], 'uses' => 'ProductController@getProductsByCategory', 'as' => 'products.getProductsByCat']);
	    });

	    $router->group(['prefix' => 'resources'], function () use ($router) {
		$router->post('/', ['middleware' => ['replica.db'], 'uses' => 'ResourceController@index']);
		$router->get('/{id}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'ResourceController@show', 'as' => 'resources.show']);
	    });

	    $router->group(['prefix' => 'trainingCourses'], function () use ($router) {
		$router->get('/{id}', ['uses' => 'TrainingCourseController@show', 'as' => 'trainingCourse.show']);
		$router->get('/list/{operatorId}', ['middleware' => ['replica.db'], 'uses' => 'TrainingCourseController@index']);
		$router->post('/accreditationList', 'TrainingCourseController@accreditationList');
		$router->post('/resetProgress', 'TrainingCourseController@resetProgress');
		$router->post('/assignUserInviteLink', 'TrainingCourseController@assignUserInviteLink');
	    });

	    $router->group(['prefix' => 'trainingCourseModule'], function () use ($router) {
		$router->get('/list/{id}', 'TrainingCourseModuleController@index');
		$router->get('/details/{id}', 'TrainingCourseModuleController@show');
	    });

	    $router->group(['prefix' => 'trainingCourseSubmodule'], function () use ($router) {
		$router->post('SelfAssessmentSubModuleListing', 'TrainingCourseSubModuleController@SelfAssessmentSubModuleListing');
		// 360 route - web view1
		$router->get('/getThreeSixtyUrl/{id}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'TrainingCourseSubModuleController@getThreeSixtyUrl', 'as' => 'trainingCourseSubmodule.getThreeSixtyUrl']);
		// Title Slide
		$router->get('/getTitleSlide/{id}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'TrainingCourseSubModuleController@getTitleSlide', 'as' => 'trainingCourseSubmodule.getTitleSlide']);
		// Upload Video
		$router->get('/getUploadVideo/{id}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'TrainingCourseSubModuleController@getUploadVideo', 'as' => 'trainingCourseSubmodule.getUploadVideo']);

		// Product List Submodule
		$router->get('/productList/{id}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'TrainingCourseSubModuleController@productList', 'as' => 'trainingCourseSubmodule.productList']);
		$router->get('/productDetail/{id}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'TrainingCourseSubModuleController@productDetail', 'as' => 'trainingCourseSubmodule.productDetail']);
		$router->post('sendEmailProduct', 'TrainingCourseSubModuleController@sendEmailProduct');

		// SCORM submodule
		$router->get('/getScorm/{id}', ['middleware' => ['replica.db'], 'uses' => 'TrainingCourseSubModuleController@getScorm']);
        $router->post('/submitScorm', ['middleware' => ['replica.db'], 'uses' => 'TrainingCourseSubModuleController@submitScorm']);

		// Micro Learning submodule
		$router->get('/getMicroLearning/{id}', ['middleware' => ['replica.db'], 'uses' => 'TrainingCourseSubModuleController@getMicroLearning']);
    
		// Quiz Submodule
		$router->get('/checkQuizStatus/{id}', ['middleware' => ['replica.db'], 'uses' => 'TrainingCourseSubModuleQuizController@checkQuizStatus']);
		$router->get('/getQuiz/{id}', ['middleware' => ['replica.db'], 'uses' => 'TrainingCourseSubModuleQuizController@getQuiz']);
		$router->post('/storeAttemptQuiz', 'TrainingCourseSubModuleQuizController@storeAttemptQuiz');
        $router->post('/storeQuizResult', 'TrainingCourseSubModuleQuizController@storeQuizResult');
		$router->post('/previousQuizResult', 'TrainingCourseSubModuleQuizController@previousQuizResult');
		$router->get('/getQuizResult/{id}', ['middleware' => ['replica.db'], 'uses' => 'TrainingCourseSubModuleQuizController@getQuizResult']);

		// Happy Unhappy Submodule
		$router->get('/happyUnhappyList/{id}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'TrainingCourseSubModuleController@happyUnhappyList', 'as' => 'trainingCourseSubmodule.happyUnhappyList']);
		$router->get('/happyUnhappyDetail/{id}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'TrainingCourseSubModuleController@happyUnhappyDetail', 'as' => 'trainingCourseSubmodule.happyUnhappyDetail']);

		// Video Guide Submodule
		$router->get('/videoGuideList/{id}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'TrainingCourseSubModuleController@videoGuideList', 'as' => 'trainingCourseSubmodule.videoGuideList']);
		$router->get('/videoGuideStepsList/{id}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'TrainingCourseSubModuleController@videoGuideStepsList', 'as' => 'trainingCourseSubmodule.videoGuideStepsList']);
		$router->get('/videoGuideStepDetail/{id}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'TrainingCourseSubModuleController@videoGuideStepDetail', 'as' => 'trainingCourseSubmodule.videoGuideStepDetail']);

		// Image Gallery Submodule
		$router->get('/imageGalleryList/{id}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'TrainingCourseSubModuleController@imageGalleryList', 'as' => 'trainingCourseSubmodule.imageGalleryList']);
		$router->get('/imageGalleryDetail/{id}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'TrainingCourseSubModuleController@imageGalleryDetail', 'as' => 'trainingCourseSubmodule.imageGalleryDetail']);

		// Course Feedback Submodule
		$router->get('/courseFeedbackForm/{id}', ['middleware' => ['replica.db'], 'uses' => 'TrainingCourseSubModuleController@courseFeedbackForm']);
		$router->post('/submitCourseFeedback', 'TrainingCourseSubModuleController@submitCourseFeedback');

		// Confirmation
		$router->get('/getConfirmation/{id}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'TrainingCourseSubModuleController@getConfirmation', 'as' => 'trainingCourseSubmodule.getConfirmation']);

		// Confirmation Boxes
		$router->get('/confirmationBoxesList/{id}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'TrainingCourseSubModuleController@confirmationBoxesList', 'as' => 'trainingCourseSubmodule.confirmationBoxesList']);

		// Image with hotspot
		$router->post('/uploadImageHotspot', 'TrainingCourseSubModuleController@uploadImageHotspot');
		$router->get('/imageHotspotList/{id}', 'TrainingCourseSubModuleController@imageHotspotList');
		$router->get('/imageHotspotList/{id}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'TrainingCourseSubModuleController@imageHotspotList', 'as' => 'trainingCourseSubmodule.imageHotspotList']);

		//Jobs
		$router->get('/jobList/{id}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'TrainingCourseSubModuleController@jobList', 'as' => 'trainingCourseSubmodule.jobList']);
		$router->get('/uploadJobList/{id}', ['middleware' => ['replica.db'], 'uses' => 'TrainingCourseSubModuleController@uploadJobList']);
		$router->post('/uploadJob', 'TrainingCourseSubModuleController@uploadJob');
		$router->get('/uploadJobDetail/{id}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'TrainingCourseSubModuleController@uploadJobDetail', 'as' => 'trainingCourseSubmodule.uploadJobDetail']);
		$router->post('/allUploadJobList', 'TrainingCourseSubModuleController@allUploadJobList');
		$router->get('/jobFilterList', ['uses' => 'TrainingCourseSubModuleController@jobFilterList']);
		$router->post('/courseJobList', 'TrainingCourseSubModuleController@courseJobList');

		// Photo with hotspot
		$router->get('/photoHotspotList/{id}/{attempt}', 'TrainingCourseSubModuleController@photoHotspotList');
		$router->get('/pocList/{id}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'TrainingCourseSubModuleController@pocList', 'as' => 'trainingCourseSubmodule.pocList']);

		// Progress Calculation
		$router->post('/submitProgress', 'TrainingCourseSubModuleController@submitProgress');
		// Document Viewer
		$router->get('/getDocumentViewer/{id}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'TrainingCourseSubModuleController@getDocumentViewer', 'as' => 'trainingCourseSubmodule.getDocumentViewer']);

		// Practical Assessment
		$router->get('/getPracticalAssessment/{id}', ['middleware' => ['replica.db', 'cache.response'], 'uses' => 'TrainingCourseSubModuleController@getPracticalAssessment', 'as' => 'trainingCourseSubmodule.getPracticalAssessment']);
		$router->get('/getAttemptHistory/{id}', 'TrainingCourseSubModuleController@getAttemptHistory');
		$router->post('/submitPracticalAssessment', 'TrainingCourseSubModuleController@submitPracticalAssessment');
		$router->post('/submitSelfAssessment', 'TrainingCourseSubModuleController@submitSelfAssessment');
		$router->post('/uploadMediaPracticalAssessment', 'TrainingCourseSubModuleController@uploadMediaPracticalAssessment');
		$router->get('/deleteMediaPracticalAssessment/{id}', 'TrainingCourseSubModuleController@deleteMediaPracticalAssessment');
		$router->post('/uploadPracticalAssessmentMediaComment', 'TrainingCourseSubModuleController@uploadPracticalAssessmentMediaComment');		
		$router->get('/cableMatrix/{questionId}', ['middleware' => ['replica.db'], 'uses' => 'TrainingCourseSubModuleController@getCableMatrixData']);
		$router->get('/locationMatrix/{questionId}', ['middleware' => ['replica.db'], 'uses' => 'TrainingCourseSubModuleController@getLocationMatrixData']);
	    });

	    $router->group(['prefix' => 'documents'], function () use ($router) {
		$router->post('/upload-image', 'DocumentController@store');
	    });
	    /* User creation on AWS cognito user pool and MFA enable and disable routes */
	    $router->group(['prefix' => 'mfa'], function () use ($router) {
		$router->post('/enable-mfa', 'MfaConfigurationController@enableMFA');
		$router->post('/verify-mfa-code', 'MfaConfigurationController@verifyMFACode');
		$router->post('/disable-mfa', 'MfaConfigurationController@disableMFA');
		$router->post('/reset-mfa', 'MfaConfigurationController@resetMFAMail');
	    });
	});
    });
});
