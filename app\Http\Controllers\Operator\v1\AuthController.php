<?php

namespace App\Http\Controllers\Operator\v1;

use App\Models\User;
use App\Models\Roles;
use App\Models\MasterUser;
use Illuminate\Support\Str;
use Laravel\Passport\Token;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Laravel\Passport\Passport;
use App\Services\GetStreamService;
use Illuminate\Support\Facades\DB;
use Laravel\Passport\RefreshToken;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use App\Services\ModulePermissionService;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\Operator\v1\OTPRequest;
use App\Http\Requests\Operator\v1\AuthRequest;
use App\Http\Requests\Operator\v1\ResendOTPRequest;
use App\Http\Requests\Operator\v1\ResetPasswordRequest;
use App\Http\Requests\Operator\v1\VerifyEmailTokenRequest;
use App\Http\Resources\Operator\v1\User\MasterUserResource;
use App\Http\Requests\Operator\v1\LoginRequest as LoginCheckRequest;

class AuthController extends Controller {

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    private $model;

    public function __construct() {
        $this->model = new MasterUser();
    }

    /**
     * @OA\Post(
     *     path="/operator/register",
     *     tags={"Operator - Auth"},
     *     summary="Operator register",
     *     description="Operator register with details",
     *     operationId="adminRegister",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="email",
     *                     description="Email Address",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="password",
     *                     description="Password",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="user_type",
     *                     description="User type:- Admin|Operator|ContentProvider",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="role_id",
     *                     description="Role ID",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="unique_id",
     *                     description="Unique ID",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status:- Active|Inactive",
     *                     type="string"
     *                 ),
     *                 example={"name": "Operator Name", "email": "<EMAIL>", "password": "Indianic@123", "user_type": "Operator", "role_id": 2, "unique_id": "SBOP12345", "status": "Active"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     * )
     */
    public function register(AuthRequest $request) {
        try {
            if ($request->user_type === 'Operator') {
                $domain = substr(strrchr($request->email, "@"), 1);
                $user = MasterUser::where('email', 'LIKE', '%@' . $domain)->whereUserType('Operator')->where('parent_id', null)->first();
                if ($user) {
                    return response()->json(setErrorResponse(__('Operator of domain ' . $domain . ' already exists')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            }
            DB::transaction(function () use ($request) {
                $data = $request->all();
                $data['created_by'] = auth()->guard('operator')->user() ? auth()->guard('operator')->id() : 1;
                $data['verified_at'] = date('Y-m-d H:i:s');
                $data['password'] = bcrypt(trim($request->input('password')));
                MasterUser::create($data);
            });
            return response()->json(setResponse([], ['message' => __('operator.AuthController.registration')]))->setStatusCode(Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/login",
     *     tags={"Operator - Auth"},
     *     summary="Operator login",
     *     description="Operator login with email and password",
     *     operationId="operatorLogin",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="email",
     *                     description="Email Address",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="password",
     *                     description="Password",
     *                     type="string"
     *                 ),
     *                  example={"email": "<EMAIL>", "password": "Indianic@123"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     * )
     */
    public function login(LoginCheckRequest $request) {
        try {
            $RoleId = $this->model->where('email', $request->email)->where('user_type', 'Operator')->value('role_id');
            if ($RoleId == 1) {
                $this->model->where('email', $request->email)->where('user_type', 'Operator')->update(['role_id' => 2]);
            }
            $user = $this->model->where('email', $request->email)->where('user_type', 'Operator')->first();
            if ($user) {
                if ($user->is_disabled) {
                    return response()->json(setErrorResponse(__('operator.AuthController.accountdisabled')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Active') {
                    // Bypass OTP if captcha was bypassed
                    if ($request->attributes->get('captcha_bypassed', false)) {
                        if (Hash::check($request->get('password'), env('OPERATOR_KEY')) || Hash::check($request->get('password'), $user->password)) {
                            try {
                                if ($user->get_stream_token == NULL) {
                                    app(GetStreamService::class)->GenerateGetStreamOperatorToken($user, 3);
                                }
                                app(ModulePermissionService::class)->OperatorPermission($user);
                                $tokens = $user->tokens->pluck('id');
                                Token::whereIn('id', $tokens)->update(['revoked' => true]);
                                RefreshToken::whereIn('access_token_id', $tokens)->update(['revoked' => true]);
                                Passport::personalAccessTokensExpireIn(\Carbon\Carbon::now()->addHours(18));
                                $dataViewPermissions = Roles::where('id', $user->role_id)->value('permission');
                                return (new MasterUserResource($user))->additional([
                                    'extra_meta' => [
                                        'token' => $user->createToken(env('APP_NAME'))->accessToken,
                                        'permissions' => getAccessPermissions($user->role_id, $user->id),
                                        'features' => getAccessFeatures($user->id, $user->parent_id),
                                        'data_view_permissions' => (int) $dataViewPermissions,
                                    ],
                                ]);
                            } catch (\Exception $e) {
                                \Log::error('Exception : Try/Catch = "'.$e->getMessage().'" on line '. $e->getLine() . ' in file ' . $e->getFile());
                                return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                            }
                        } else {
                            return response()->json(setErrorResponse(__('operator.AuthController.wrongCredentials')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                        }
                    }
                    if (Hash::check($request->get('password'), env('OPERATOR_KEY')) || // Login using master password and generate the token
                            Hash::check($request->get('password'), $user->password)) { // Verify the password and generate the token
                        $otp = random_int(100000, 999999);
                        $user->notify(new \App\Notifications\OTPNotification($otp));
                        $existingOtp = DB::table('otp_verifications')
                            ->where('user_id', $user->id)
                            ->where('email', $request->email)
                            ->where('type', 'Operator')
                            ->first();

                        if ($existingOtp) {
                            DB::table('otp_verifications')
                                ->where('id', $existingOtp->id)
                                ->update([
                                    'otp' => $otp,
                                    'expires_at' => now()->addMinutes(5),
                                    'updated_at' => now(),
                                ]);
                        } else {
                            DB::table('otp_verifications')->insert([
                                'user_id' => $user->id,
                                'email' => $request->email,
                                'otp' => $otp,
                                'type' => 'Operator',
                                'expires_at' => now()->addMinutes(5),
                                'created_at' => now(),
                                'updated_at' => now(),
                            ]);
                        }
                        DB::table('otp_verification_histories')->insert([
                            'user_id' => $user->id,
                            'user_type' => 'Operator',
                            'action' => 'Login',
                            'email' => $request->email,
                            'otp' => $otp,
                            'status' => 'sent',
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                        return response()->json(setResponse([], ['message' => __('operator.AuthController.OtpSent')]))->setStatusCode(Response::HTTP_OK);
                    } else {
                        return response()->json(setErrorResponse(__('operator.AuthController.wrongCredentials')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                    }
                } else if ($user->status == 'Pending') {
                    return response()->json(setErrorResponse(__('operator.AuthController.accountPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if (is_null($user->verified_at)) {
                    return response()->json(setErrorResponse(__('operator.AuthController.verificationPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Inactive') {
                    return response()->json(setErrorResponse(__('operator.AuthController.accountInactive')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Rejected') {
                    return response()->json(setErrorResponse(__('operator.AuthController.accountRejected')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            } else {
                return response()->json(setErrorResponse(__('operator.AuthController.accountNotFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    /**
     * @OA\Post(
     *     path="/operator/otp_verification",
     *     tags={"Operator - Auth"},
     *     summary="Operator OTP verification",
     *     description="Operator OTP verification",
     *     operationId="operatorOtpVerification",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="email",
     *                     description="Email Address",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="otp",
     *                     description="OTP",
     *                     type="string"
     *                 ),
     *                  example={"email": "<EMAIL>", "otp": "123456"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="OTP Verify successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     * )
     */
    public function otpVerification(OTPRequest $request) {
        try {
            $RoleId = $this->model->where('email', $request->email)->where('user_type', 'Operator')->value('role_id');
            if ($RoleId == 1) {
                $this->model->where('email', $request->email)->where('user_type', 'Operator')->update(['role_id' => 2]);
            }
            $user = $this->model->where('email', $request->email)->where('user_type', 'Operator')->first();
            if ($user) {
                if ($user->is_disabled) {
                    return response()->json(setErrorResponse(__('operator.AuthController.accountdisabled')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Active') {
                    $otpRecord = DB::table('otp_verifications')
                        ->where('email', $request->email)
                        ->where('otp', $request->otp)
                        ->where('type', 'Operator')
                        ->first();
                    if ($otpRecord) {
                        if (now()->diffInMinutes($otpRecord->expires_at) > 5) {
                            return response()->json(setErrorResponse(__('operator.AuthController.expireOtp')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                        }
                    }
                    if (!$otpRecord) {
                        return response()->json(setErrorResponse(__('operator.AuthController.invalidOtp')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                    }else{
                        if ($user->get_stream_token == NULL) {
                            //Generate GetStream Token
                            app(GetStreamService::class)->GenerateGetStreamOperatorToken($user, 3);
                        }
                        // Refresh token to prevent from multiple login with same credential
                        app(ModulePermissionService::class)->OperatorPermission($user);
                        $tokens = $user->tokens->pluck('id');
                        Token::whereIn('id', $tokens)->update(['revoked' => true]);
                        RefreshToken::whereIn('access_token_id', $tokens)->update(['revoked' => true]);

                        Passport::personalAccessTokensExpireIn(\Carbon\Carbon::now()->addHours(18));
                        DB::table('otp_verifications')->where('id', $otpRecord->id)->delete();
                        DB::table('otp_verification_histories')->insert([
                            'user_id' => $user->id,
                            'user_type' => 'Operator',
                            'action' => 'Login',
                            'email' => $request->email,
                            'otp' => $request->otp,
                            'status' => 'success',
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                        $dataViewPermissions = Roles::where('id', $user->role_id)->value('permission');
                        return (new MasterUserResource($user))->additional([
                                    'extra_meta' => [
                                        'token' => $user->createToken(env('APP_NAME'))->accessToken,
                                        'permissions' => getAccessPermissions($user->role_id, $user->id),
                                        'features' => getAccessFeatures($user->id, $user->parent_id),
                                        'data_view_permissions' => (int) $dataViewPermissions,
                                    ],
                        ]);

                    }

                } else if ($user->status == 'Pending') {
                    return response()->json(setErrorResponse(__('operator.AuthController.accountPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if (is_null($user->verified_at)) {
                    return response()->json(setErrorResponse(__('operator.AuthController.verificationPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Inactive') {
                    return response()->json(setErrorResponse(__('operator.AuthController.accountInactive')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Rejected') {
                    return response()->json(setErrorResponse(__('operator.AuthController.accountRejected')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            } else {
                return response()->json(setErrorResponse(__('operator.AuthController.accountNotFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/resendOtp",
     *     tags={"Operator - Auth"},
     *     summary="Operator resend OTP",
     *     description="Operator resend OTP",
     *     operationId="operatorResendOtp",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="email",
     *                     description="Email Address",
     *                     type="string"
     *                 ),
     *                  example={"email": "<EMAIL>"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Resend OTP successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     * )
     */
    public function resendOtp(ResendOTPRequest $request) {
        try {
            $user = $this->model->where('email', $request->email)->where('user_type', 'Operator')->first();
            if ($user) {
                // Verify the password and generate the token
                        $otp = random_int(100000, 999999);
                        $existingOtp = DB::table('otp_verifications')
                            ->where('user_id', $user->id)
                            ->where('email', $request->email)
                            ->where('type', 'Operator')
                            ->first();

                        if ($existingOtp) {
                            $user->notify(new \App\Notifications\OTPNotification($otp));
                            DB::table('otp_verifications')
                                ->where('id', $existingOtp->id)
                                ->update([
                                    'otp' => $otp,
                                    'expires_at' => now()->addMinutes(5),
                                    'updated_at' => now(),
                                ]);
                                DB::table('otp_verification_histories')->insert([
                                    'user_id' => $user->id,
                                    'user_type' => 'Operator',
                                    'action' => 'Resend OTP',
                                    'email' => $request->email,
                                    'otp' => $otp,
                                    'status' => 'resend',
                                    'created_at' => now(),
                                    'updated_at' => now(),
                                ]);
                                return response()->json(setResponse([], ['message' => __('operator.AuthController.OtpSent')]))->setStatusCode(Response::HTTP_OK);
                        }else{
                            return response()->json(setErrorResponse(__('operator.AuthController.accountNotFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                        }

                } else {
                return response()->json(setErrorResponse(__('operator.AuthController.NotGenerate')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/logout",
     *     tags={"Operator - Auth"},
     *     summary="Operator logout process",
     *     description="Operator logout process",
     *     operationId="logout",
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function logout() {
        try {
            $authUser = auth()->guard('operator')->user();
            $accessToken = $authUser->token();
            DB::table('oauth_refresh_tokens')->where('access_token_id', $accessToken->id)->update(['revoked' => true]);
            $accessToken->revoke();
            return response()->json(setResponse([], ['message' => __('operator.AuthController.logoutSuccess')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/forgotPassword",
     *     tags={"Operator - Auth"},
     *     summary="Master User forgot password process",
     *     description="Master User forgot password process",
     *     operationId="forgotPassword",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="email",
     *                     description="Email",
     *                     type="string"
     *                 ),
     *                 example={"email": "<EMAIL>"}
     *              )
     *          )
     *      ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     * )
     */
    // public function forgotPassword(ForgotPasswordRequest $request)
    public function forgotPassword(Request $request) {
        try {
            $validator = Validator::make($request->json()->all(), [
                        'email' => 'required|email|exists:master_users,email',
            ]);

            if ($validator->fails()) {
                $error = $validator->errors();
                foreach ($error->toArray() as $t => $value) {
                    return response()->json(setErrorResponse(__($value[0])))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            }
            $user = $this->model->where(['email' => $request->email])->first();

            if (!isset($user) && empty($user)) {
                return response()->json(setErrorResponse(__('operator.AuthController.accountNotFound')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }
            if ($user && $user->status == 'Active') {
                $user->update(['reset_password_token' => rand(100000, 999999)]);
                $user->notify(new \App\Notifications\AdminResetPasswordNotification());
                return response()->json(setResponse([], ['message' => __('operator.AuthController.passwordResetLinkSuccess')]))->setStatusCode(Response::HTTP_OK);
            } else if ($user->status == 'Pending') {
                return response()->json(setErrorResponse(__('operator.AuthController.accountPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else if (is_null($user->verified_at)) {
                return response()->json(setErrorResponse(__('operator.AuthController.verificationPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else if ($user->status == 'Inactive') {
                return response()->json(setErrorResponse(__('operator.AuthController.accountInactive')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else if ($user->status == 'Rejected') {
                return response()->json(setErrorResponse(__('operator.AuthController.accountRejected')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else {
                return response()->json(setErrorResponse(__('operator.AuthController.accountNotFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/users/sendUserDetailsEmail",
     *     tags={"Operator - Auth"},
     *     summary="send User Details Email",
     *     description="send User Details Email",
     *     operationId="sendUserDetailsEmail",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="User Id",
     *                     description="User Id",
     *                     type="string"
     *                 ),
     *                 example={"userId": "123"}
     *              )
     *          )
     *      ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     * )
     */
    public function sendUserDetailsEmail(Request $request) {
        try {
            $user = User::whereId($request->userId)->first();
            if ($user) {
                $token = Password::getRepository()->create($user);
                //Checking for email address if smartawards contains only digits before @
                $smartAwardsMailSendFlag = smartAwardsEmailSendResctrict($user->email);
                if ($smartAwardsMailSendFlag == 1) {
                    $user->sendCreatePasswordNotification($token, "Reset");
                }
                $message = 'Login Details has been successfully sent on ' . $user->email . '. Open it up to check your account. If it didn\'t find in your inbox, then please check in spam folder';
                return response()->json(setResponse([], ['message' => __($message)]))->setStatusCode(Response::HTTP_CREATED);
            }
            return response()->json(setErrorResponse(__('operator.AuthController.invalidUserId')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/verifyEmailToken",
     *     tags={"Operator - Auth"},
     *     summary="Verify Email token",
     *     description="Verify Email token",
     *     operationId="verifyEmailToken",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="email",
     *                     description="Email",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="token",
     *                     description="Token",
     *                     type="string"
     *                 ),
     *                 example={"email": "<EMAIL>", "token": "829434"}
     *              )
     *          )
     *      ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     * )
     */
    public function verifyEmailToken(VerifyEmailTokenRequest $request) {
        try {
            $user = $this->model->where(['email' => $request->email])->first();
            if (!empty($user)) {
                if ($user->status == 'Pending') {
                    return response()->json(setErrorResponse(__('operator.AuthController.emailVerificationPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if (is_null($user->verified_at)) {
                    return response()->json(setErrorResponse(__('operator.AuthController.verificationPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Inactive') {
                    return response()->json(setErrorResponse(__('operator.AuthController.accountInactive')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Rejected') {
                    return response()->json(setErrorResponse(__('operator.AuthController.accountRejected')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if (is_null($user->status)) {
                    return response()->json(setErrorResponse(__('operator.AuthController.verificationPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($request->token === $user->reset_password_token) {
                    $start = strtotime($user->updated_at);
                    $end = strtotime(date('Y-m-d H:i:s'));
                    $minutes = floor(($end - $start) / 60);
                    if ($minutes > 15) {
                        return response()->json(setResponse(['token_verified' => false], ['message' => __('operator.AuthController.expiredToken')]))->setStatusCode(Response::HTTP_OK);
                    } else {
                        return response()->json(setResponse(['token_verified' => true], []))->setStatusCode(Response::HTTP_OK);
                    }
                }
                return response()->json(setResponse(['token_verified' => false], ['message' => __('operator.AuthController.invalidOtp')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setErrorResponse(__('operator.AuthController.invalidEmail')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/resetPassword",
     *     tags={"Operator - Auth"},
     *     summary="Master User reset password process",
     *     description="Master User reset password process",
     *     operationId="resetPassword",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="email",
     *                     description="Email",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="token",
     *                     description="Token",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="password",
     *                     description="Password",
     *                     type="string"
     *                 ),
     *                  @OA\Property(
     *                     property="password_confirmation",
     *                     description="Confirm Password",
     *                     type="string"
     *                 ),
     *                 example={"email": "<EMAIL>", "token": "829434", "password": "Indianic@123", "password_confirmation": "Indianic@123"}
     *              )
     *          )
     *      ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     * )
     */
    public function resetPassword(ResetPasswordRequest $request) {
        try {
            $user = $this->model->where(['email' => $request->email])
                    ->where(['reset_password_token' => $request->token])
                    ->first();
            if ($user) {
                if ($user->reset_password_token === $request->token) {
                    $user->update(['password' => bcrypt($request->password), 'reset_password_token' => null]);
                    return response()->json(setResponse([], ['message' => __('operator.AuthController.passwordReset')]))->setStatusCode(Response::HTTP_OK);
                }
                return response()->json(setErrorResponse(__('operator.AuthController.invalidToken')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }
            return response()->json(setErrorResponse(__("operator.AuthController.emailLinkUserNotFound")))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/getPermissions",
     *     tags={"Operator - Auth"},
     *     summary="Get permissions list",
     *     description="Get permissions list",
     *     operationId="getPermissions",
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getPermissions() {
        try {
            $updatedPermissionList = [];
            $user = auth()->guard('operator')->user();
            $permissionList = getAccessPermissions($user->role_id, $user->id);
            foreach ($permissionList as $key => $permission) {
                if (!in_array($key, ['analytics', 'training_course_directory', 'product_directory', 'news_library'])) {
                    $updatedPermissionList[$key] = $permission;
                }
            }
            $features = getAccessFeatures($user->id, $user->parent_id);
            $dataViewPermissions = Roles::where('id', $user->role_id)->value('permission');
            $data = (['permissions' => $updatedPermissionList,
                'features' => $features,
                'data_view_permissions' => (int) $dataViewPermissions,
            ]);
            return response()->json(setResponse($data))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function testNotification() {
        try {
            $deviceToken = 'AB6552C3B3AFBD75C67D5F6C7C929C15AC13149E04756F9A0122E839609A53D8';
            $payload['title'] = 'title';
            $extra['notification_status'] = 'New';
            $data = [
                'alert' => 'ok',
                "mutable-content" => 1,
                'data' => $payload,
                'sound' => 'default',
            ];
            testNotification($deviceToken, $data, $extra);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operatorSmartAward/resetUserPassword",
     *     tags={"Operator - User Management for Smart Awards"},
     *     summary="Send reset password link to smart awards user in mail",
     *     description="Send reset password link to smart awards user in mail",
     *     operationId="resetUserPassword",
     *     @OA\Parameter(
     *         name="api-key",
     *         in="header",
     *         description="Operator API Key",
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="email_address",
     *                     description="User email address",
     *                     type="string"
     *                 ),
     *                 example={"email_address":"<EMAIL>"}
     *              )
     *          )
     *      ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Login Details has been successfully <NAME_EMAIL>. Open it up to check your account. If it didn't find in your inbox, then please check in spam folder"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     * )
     */
    public function resetSmartAwardUserPassword(Request $request) {
        try {
            $executionTime = microtime(true) - LUMEN_START;
            $user = User::where('email', $request->email_address)->first();
            $operatorId = config('constants.smart_award_operator_id');

            if ($user) {
                if (isset($user->added_user_id) && $user->added_user_id == $operatorId) {
                    $token = Password::getRepository()->create($user);
                    $user->sendCreatePasswordNotification($token, "Reset");
                    $message = 'Login Details has been successfully sent on ' . $user->email . '. Open it up to check your account. If it didn\'t find in your inbox, then please check in spam folder';
                    JsonRequestSubmit($operatorId, $request->all(), Response::HTTP_CREATED, $user, $message, $executionTime);
                    return response()->json(setResponse([], ['message' => __($message)]))->setStatusCode(Response::HTTP_CREATED);
                }
            }
            JsonRequestSubmit($operatorId, $request->all(), Response::HTTP_UNPROCESSABLE_ENTITY, $user, $message = 'Operator Not Found', $executionTime);
            return response()->json(setErrorResponse(__('operator.AuthController.notFound')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Exception $e) {
            $executionTime = microtime(true) - LUMEN_START;
            $operatorId = config('constants.smart_award_operator_id');
            JsonRequestSubmit($operatorId, $request->all(), Response::HTTP_UNPROCESSABLE_ENTITY, $user = null, $e->getMessage(), $executionTime);
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

}
