<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTypeColumnInPracticalAssessmentMediaTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('practical_assessment_media', function (Blueprint $table) {
            if (!Schema::hasColumn('practical_assessment_media', 'type')) {
                $table->string('type')->default('PA')->after('user_ids');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('practical_assessment_media', function (Blueprint $table) {
            if (Schema::hasColumn('practical_assessment_media', 'type')) {
                $table->dropColumn('type');
            }
        });
    }
}
