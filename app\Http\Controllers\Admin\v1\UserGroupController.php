<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\UserGroup;
use App\Models\TrainingCourse;
use Illuminate\Http\Response;
use App\Http\Resources\CustomCollection;
use App\Repositories\Admin\v1\UserGroupRepository;
use App\Http\Requests\Admin\v1\UserGroupRequest;
use App\Http\Resources\Admin\v1\UserGroupResource;
use App\Http\Requests\Admin\v1\UserGroupStatusRequest;
use App\Http\Requests\Admin\v1\UserGroupDeleteRequest;


class UserGroupController extends Controller
{   
    private $model;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->group = new UserGroup();
        $this->user_group_repository = new UserGroupRepository($this->group);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Display a listing of all User group.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getUserGroupList(Request $request)
    {   
        try {
            $perPage = isset($request->per_page) ? $request->per_page : 10;
            $master_user_id = auth()->guard('admin')->id();
            $groupTemp = UserGroup::select('id','name','manager_email','unique_id','status')
            ->where('master_user_id',$master_user_id);
            //filter
            $i=0;
            if($request->has('filter')){
                $filterBy=$request->filter;
                foreach ($filterBy as $fkey => $value) {
                    if ($i==0) {
                        $groupTemp->where($fkey,$value);
                    } else {
                        $groupTemp->orWhere($fkey,$value);
                    }
                    $i++;
                }
                
            }

            // search key filter
            if($request->has('search_key') && $request->search_key !=''){
                $searchTerm=$request->search_key;
                $groupTemp->where(function ($query) use ($searchTerm) {
                    $query->where('name', 'like', '%' . $searchTerm . '%')
                    ->orWhere('manager_email', 'like', '%' . $searchTerm . '%')
                    ->orWhere('unique_id', 'like', '%' . $searchTerm . '%')
                    ->orWhere('status', 'like', '%' . $searchTerm . '%');
                });
            }

            //Sorting
            if($request->has('sort_by')){
                $sortBy=$request->input('sort_by');
                $orderBy=($request->input('sort_by')=='asc')?'ASC':'DESC';
                $groupTemp->orderBy($sortBy,$orderBy);
                
            } else {
                $groupTemp =  $groupTemp->orderByDesc('id');
            }

            if ($request->isExport) {
                return $this->user_group_repository->exportCsv($groupTemp->get());
            }
            $groupTemp = $groupTemp->paginate($perPage);
            return new CustomCollection($groupTemp, 'App\Http\Resources\Admin\v1\UserGroupResource');
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Add User Group
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(UserGroupRequest $request)
    {
        try {
            $moduleData = $request;
            $result=$this->user_group_repository->create($moduleData);
            $retunData=array('id'=>$result->id,'name'=>$result->name);
            return response()->json(setResponse($retunData, ['message' => __('admin.UserGroupController.add')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Show User Group Details
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    
    public function show($id)
    {   
        try {
            $groupDetail=UserGroup::with('assignCourses')->findOrFail($id);
            
            $groupDetail = new UserGroupResource($groupDetail);
            $message = !empty($groupDetail) ? __('admin.UserGroupController.found') : __('admin.UserGroupController.notFound');
            return response()->json(setResponse($groupDetail, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update User Group details
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(UserGroupRequest $request, $id)
    {   
        try {
            $this->user_group_repository->update($request, $id);
            return response()->json(setResponse([], ['message' => __('admin.UserGroupController.update')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Delete User Groups
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(UserGroupDeleteRequest $request)
    {
        try {
            $this->user_group_repository->delete($request);
            return response()->json(setResponse([], ['message' => __('admin.UserGroupController.delete')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * User Groups Change Status
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function changeStatus(UserGroupStatusRequest $request) 
    {
        try {
            $this->user_group_repository->change_status($request);
            return response()->json(setResponse([], ['message' => __('admin.UserGroupController.status')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Courses list
     *
     * @return \Illuminate\Http\Response
     */
    public function getCoursesList(Request $request)
    {
        // return $request->all();
        try {
            $perPage = isset($request->per_page) ? $request->per_page : 10;
            $courseTemp = TrainingCourse::join('master_users', 'master_users.id', '=', 'training_course.master_user_id')->select('training_course.id','training_course.title as CourseName','master_users.name as createdBy');

            // search key filter
            if($request->has('search_key') && $request->search_key !=''){
                $courseTemp->where('title', 'like', '%' . $request->search_key . '%')
                ->orWhere('master_users.name', 'like', '%' . $request->search_key . '%');
            }

            //Sorting
            if($request->has('sort_by')){
                $sortBy=$request->input('sort_by');
                $orderBy=($request->input('order_by')=='asc')?'ASC':'DESC';
                if ($sortBy=='CourseName') {
                    $courseTemp->orderBy('training_course.title',$orderBy);
                }
                if ($sortBy=='createdBy') {
                    $courseTemp->orderBy('createdBy',$orderBy);
                }
                
            } else {
                $courseTemp =  $courseTemp->orderByDesc('id');
            }

            $courseTemp = $courseTemp->paginate($perPage);
            $message = (!empty($courseTemp) && count($courseTemp)>0) ? __('admin.UserGroupController.courseListFound') : __('admin.UserGroupController.courseListNotFound');
            return response()->json(setResponse($courseTemp, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
