<?php
/**
     * @OA\Get(
     *     path="/api/notifications",
     *     tags={"Mobile - Notifications"},
     *     summary="Get Notification Settings List",
     *     description="Get Notification Settings List",
     *     operationId="index",
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */

/**
     * @OA\Post(
     *     path="/api/notifications",
     *     tags={"Mobile - Notifications"},
     *     summary="Store Notification Settings",
     *     description="Store Notification Settings",
     *     operationId="store",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="id",
     *                     description="Notification ID",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="is_on",
     *                     description="Is ON",
     *                     type="integer",
     *                     enum={0, 1}
     *                 ),
     *                 example={"id": 1, "is_on": 0}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */