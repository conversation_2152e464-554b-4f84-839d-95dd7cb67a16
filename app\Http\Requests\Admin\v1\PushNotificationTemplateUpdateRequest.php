<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;
use DBTableNames;

class PushNotificationTemplateUpdateRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array
    { 
        return [
            'title' => 'required|max:255',
            'message' => 'required',
        ];
    }
    
    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'title.required' => __('Please enter title.'),
            'message.required' => __('Please enter message.'),
        ];
    }
    
}
