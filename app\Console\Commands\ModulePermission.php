<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Roles;
use App\Models\Features;
use App\Models\MasterUser;
use App\Models\FeatureSettings;
use App\Models\RolePermissions;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ModulePermission extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'operator:module_permission';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assign Module & Feature Setting Permission To Operator & Team Member';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $FeatureList= config('constants.features');
        foreach($FeatureList as $key=>$value){
            Features::updateOrCreate([
                'permission_key' => $key
            ],
            [   
                'title' => $value,
                'permission_key' => $key,
                'status' =>  'Active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }

        // Assign Feature Listing to Operator
        $operatorList=MasterUser::select('id')->whereNull('parent_id')->where('user_type','Operator')->get()->toArray();
        foreach($operatorList as $operatorId){
            $operatorFeatureList=Features::select('id')->get()->toArray();
            foreach($operatorFeatureList as $operatorFeature){
                $FeatureExits=FeatureSettings::where('master_user_id',$operatorId['id'])->where('feature_id',$operatorFeature['id'])->first();
                if(!empty($FeatureExits)){
                FeatureSettings::updateOrCreate([
                    'master_user_id' => $operatorId['id'],
                    'feature_id' => $operatorFeature['id']
                    ],
                    ['master_user_id' => $operatorId['id'],
                    'feature_id' => $operatorFeature['id'],
                    'is_feature_on' => ($operatorFeature['id'] == 7) ? 0 : $FeatureExits->is_feature_on,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                    ]);
                }else{
                    $data=array('master_user_id'=>$operatorId['id'],"feature_id"=>$operatorFeature['id'],"is_feature_on"=>1,"created_at"=>date('Y-m-d H:i:s'),"updated_at"=>date('Y-m-d H:i:s'));
                    DB::table('feature_settings')->insert($data);
                }
            }
        }

        //Assign Modules Permission to Operator & Team Member
        $modulePermissions = config('constants.permission_modules');
        $roleList=Roles::select('id')->where('master_user_type','Operator')->get()->toArray();
        foreach($roleList as $role){
            foreach($modulePermissions as $module){
                $Exits=RolePermissions::where('role_id',$role['id'])->where('module_name',$module)->first();
                if(empty($Exits))
                {
                    $data=array('role_id'=>$role['id'],"module_name"=>$module,"view"=>1,"add"=>1,"edit"=>1,"delete"=>1);
                    DB::table('role_permissions')->insert($data);
                }
            }
            $TeamMemberFeatureList= config('constants.features');
            foreach($TeamMemberFeatureList as $module=> $teammodulelist)
            {
                $Exits=RolePermissions::where('role_id',$role['id'])->where('module_name',$module)->first();
                if(empty($Exits))
                {
                    $featureId=Features::where('permission_key',$module)->value('id');
                    $featureOn=FeatureSettings::select('master_user_id','is_feature_on')->where('feature_id',$featureId)->get()->toArray();
                    foreach($featureOn as $feature_on){
                    $masterUserRole=MasterUser::where('id',$feature_on['master_user_id'])->value('role_id');
                    if(!empty($masterUserRole)){
                        $roleExits=RolePermissions::where('role_id',$masterUserRole)->where('module_name',$module)->first();
                        if(empty($roleExits)){
                        $data=array('role_id'=>$masterUserRole,"module_name"=>$module,"view"=>1,"add"=>1,"edit"=>1,"delete"=>1);
                        DB::table('role_permissions')->insert($data);
                        }
                    }
                    }
                }
            }



            //Admin Module Permission
            $adminModulePermissions= config('constants.admin_permission_modules');
            $adminRoleList=Roles::select('id')->where('master_user_type','Admin')->get()->toArray();
            foreach($adminRoleList as $adminRole){
                RolePermissions::where('role_id',$adminRole['id'])->delete();
                foreach($adminModulePermissions as $adminModule){
                    $Exits=RolePermissions::where('role_id',$adminRole['id'])->where('module_name',$adminModule)->first();
                    if(empty($Exits))
                    {
                        $data=array('role_id'=>$adminRole['id'],"module_name"=>$adminModule,"view"=>1,"add"=>1,"edit"=>1,"delete"=>1);
                        DB::table('role_permissions')->insert($data);
                    }
                }
            }
        }
    }
}
