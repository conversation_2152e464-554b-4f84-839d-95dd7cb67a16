<?php

namespace App\Http\Controllers\Operator\v1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use App\Models\Roles;
use App\Models\RolePermissions;
use App\Models\FeatureSettings;
use App\Repositories\Operator\v1\RolesRepository;
use App\Repositories\Operator\v1\RolePermissionsRepository;
use App\Http\Resources\Operator\v1\RolesResource;
use App\Http\Resources\Operator\v1\ShowRolesResource;
use App\Http\Resources\Operator\v1\RolePermissionsResource;
use App\Http\Requests\Operator\v1\RolesRequest;
use App\Http\Requests\Operator\v1\CommonListingRequest;
use App\Http\Requests\Operator\v1\RolePermissionsRequest;
use App\Http\Requests\Operator\v1\RolePermissionsDeleteRequest;
use App\Http\Requests\Operator\v1\RolePermissionsStatusRequest;

class RolePermissionsController extends Controller
{
    private $model;

    public function __construct() {
        $this->role = new Roles();
        $this->role_repository = new RolesRepository($this->role);

        $this->rolespermissions = new RolePermissions();
        $this->role_permission_repository = new RolePermissionsRepository($this->rolespermissions);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * @OA\Post(
     *     path="/operator/roles/getRoleList",
     *     tags={"Operator - Roles"},
     *     summary="Get All Roles List ",
     *     description="Get All Roles List ",
     *     operationId="getRoleList",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 example={"per_page": 10, "page": 1, "search_key": "", "sort_by": "name", "order_by": "asc", "filters": {"name": "", "status": "", "created_at": ""}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Role Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getRoleList(CommonListingRequest $request) {
        try {
            $roles = $this->role_repository->getRolesListing($request->all());
            return RolesResource::collection($roles->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
      /**
     * @OA\Post(
     *     path="/operator/roles",
     *     tags={"Operator - Roles"},
     *     summary="Add Roles ",
     *     description="Add  Roles ",
     *     operationId="store",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="Role Name",
     *                     type="string"
     *                 ),
    *                 @OA\Property(
     *                     property="status",
     *                     description="Role Status",
     *                     type="string",
                           enum={"Active", "Inactive"}
     *                 ),
     *                 @OA\Property(
     *                     property="permission",
     *                     description="1=View All Users Data, 0=View Only Assigned Users Data",
     *                     type="string",
                           enum={"1", "0"}
     *                 ),
     *                 example={"name":"Editor",
                      "status":"Active",
                      "permission":"1",
                      "module_permission": {
                            "dashboard": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "courses": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "user_groups": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "users": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "analytics": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "products": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "resources": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "training_course_directory": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "product_directory": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "news": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "news_library": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "feedback": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "reports": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "roles": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            }
                      }
                    }
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Role Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function store(RolesRequest $request)
    {
        try {
            $result = DB::transaction(function () use ($request) {
                $moduleData = $request;
                $this->role_repository->create($moduleData);
            });
            return response()->json(setResponse([], ['message' => __('operator.RolePermission.add')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/roles/{id}",
     *     tags={"Operator - Roles"},
     *     summary="Show Role Details",
     *     description="Show Role Details",
     *     operationId="show",
     *     @OA\Parameter(
     *         description="Id of role to fetch",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function show($id)
    {
        try {
            $rolesDetail=Roles::findOrFail($id);
            $permissionList=RolePermissions::where('role_id',$id)->get();
            $data=[];
            if (count($permissionList)>0) {
                foreach ($permissionList as $key => $perm) {
                    $record=[];
                    $record['view']=($perm->view==1)?true:false;
                    $record['add']=($perm->add==1)?true:false;
                    $record['delete']=($perm->delete==1)?true:false;
                    $record['edit']=($perm->edit==1)?true:false;
                    $data[$perm->module_name]=$record;

                }
            }
            $rolesDetail['permissionList']=$data;
            $rolesDetail = new ShowRolesResource($rolesDetail);
            $message = !empty($rolesDetail) ? __('operator.RolePermission.found') : __('operator.RolePermission.notFound');
            return response()->json(setResponse($rolesDetail, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * @OA\Put(
     *     path="/operator/roles/{id}",
     *     tags={"Operator - Roles"},
     *     summary="Update Role details",
     *     description="Update Role details",
     *     operationId="update",
     *     @OA\Parameter(
     *         description="Id of role to update",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
    *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="Role Name",
     *                     type="string"
     *                 ),
    *                 @OA\Property(
     *                     property="status",
     *                     description="Role Status",
     *                     type="string",
                           enum={"Active", "Inactive"}
     *                 ),
     *                 @OA\Property(
     *                     property="permission",
     *                     description="1=View All Users Data, 0=View Only Assigned Users Data",
     *                     type="string",
                           enum={"1", "0"}
     *                 ),
     *                 example={"name":"Editor",
                      "status":"Active",
                      "permission":"1",
                      "module_permission": {
                            "dashboard": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "courses": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "user_groups": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "users": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "analytics": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "products": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "resources": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "training_course_directory": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "product_directory": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "news": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "news_library": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "feedback": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "reports": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "roles": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            }
                      }
                    }
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Role Updated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function update(RolesRequest $request, $id)
    {
        try {
            $this->role_repository->update($request, $id);
            return response()->json(setResponse([], ['message' => __('operator.RolePermission.update')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/roles/delete",
     *     tags={"Operator - Roles"},
     *     summary="Delete  Role",
     *     description="Delete  Role",
     *     operationId="delete",
           @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="id",
     *                     description="id",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={ "ids":{
                                "0":"8",
                                "1":"9"
                            }
                        }
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Role Deleted successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function destroy(RolePermissionsDeleteRequest $request)
    {
        try {
            $result = $this->role_repository->delete($request);
            if (!empty($result)) {
                    return response()->json(setErrorResponse(__($result)))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else {
                return response()->json(setResponse([], ['message' => __('operator.RolePermission.delete')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/roles/changeStatus",
     *     tags={"Operator - Roles"},
     *     summary="Role Change Status",
     *     description="Role Change Status",
     *     operationId="changeStatus",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="id",
     *                     description="id",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={"status":"Inactive",
                            "ids":{
                                "0":"8",
                                "1":"9"
                            }
                        }
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Status changed successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */

    public function changeStatus(RolePermissionsStatusRequest $request)
    {
        try {
            $this->role_repository->change_status($request);
            return response()->json(setResponse([], ['message' => __('operator.RolePermission.status')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Display a listing of all roles.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
      /**
     * @OA\Post(
     *     path="/operator/roles/roleFieldsList",
     *     tags={"Operator - Roles"},
     *     summary="Get Role Name List ",
     *     description="Get Role Name List ",
     *     operationId="roleFieldsList",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *     *                 example={"filter": {"name": {"0":"r"}}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Role Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function roleFieldsList(Request $request)
    {
        try {
            $roleTemp = Roles::orderBy('name');

            //Sorting
            if($request->has('filter')){
                $filterData=$request->filter;
                foreach ($filterData as $key => $filter) {
                    foreach ($filter as $fkey => $value) {
                        $roleTemp->where('name', 'like', '%' . $value . '%');
                    }
                }
            }
            $roleTemp = $roleTemp->pluck('name');
            $message = !empty($roleTemp) ? __('operator.RolePermission.found') : __('operator.RolePermission.notFound');
            return response()->json(setResponse($roleTemp, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Display All Role Module List.
     *
     * @return \Illuminate\Http\Response
     */
    /**
     * @OA\Get(
     *     path="/operator/roles/roleModuleList",
     *     tags={"Operator - Roles"},
     *     summary="Get  Role Module List",
     *     description="Get  Role Module List",
     *     operationId="show",
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function roleModuleList(Request $request)
    {
        try {
            $data2 = array();
            $parentId = auth()->guard('operator')->user()->parent_id;
            $operatorId = auth()->guard('operator')->user()->id;

            if($parentId == null){
                $featureData = FeatureSettings::with('feature')
                            ->where('master_user_id', $operatorId)
                            ->get();

                foreach($featureData as $key => $feature){

                    if($feature->is_feature_on == 1){

                        $data2[$feature->feature->permission_key] =

                            array (
                            'view' => false,
                            'edit' => false,
                            'add' => false,
                            'delete' => false,
                            );

                    }

                }
            }else{
                $featureData = FeatureSettings::with('feature')
                            ->where('master_user_id', $parentId)
                            ->get();
                foreach($featureData as $key => $feature){
                    if($feature->is_feature_on == 1){

                        $data2[$feature->feature->permission_key] =

                            array (
                            'view' => false,
                            'edit' => false,
                            'add' => false,
                            'delete' => false,
                            );

                    }
                }
            }

            $moduleList=config('constants.permission_modules');
            foreach($moduleList as $list)
            {
                $data[$list]=
                    array (
                        'view' => false,
                        'edit' => false,
                        'add' => false,
                        'delete' => false
                    );
            }

            $allData = array_merge($data,$data2);
            return response()->json(setResponse($allData))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
