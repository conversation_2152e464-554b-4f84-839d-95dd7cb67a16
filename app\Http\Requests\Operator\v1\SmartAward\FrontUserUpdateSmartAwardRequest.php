<?php

namespace App\Http\Requests\Operator\v1\SmartAward;
use App\Models\User;
use App\Http\Requests\CustomFormRequest;

class FrontUserUpdateSmartAwardRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {

        // echo $this->email;exit;
        \Validator::extend('new_email_address_exists',
            function($attribute, $value, $parameters)
            {
                $operatorId = 64;
                if(isset($parameters[0]) && !is_null($parameters[0])){
                    $existUser = User::where('email',$value)->where('id','!=', $parameters[0])->first();
                }else{
                    $existUser = User::where('email',$value)->first();
                }
                return $existUser ? false : true;
            }
        );

        return [
            
            "current_email_address" => 'required',
            "new_email_address" => 'required|new_email_address_exists:'.$this->new_email_address,
        ];
    }

    public function messages() : array
    {
        return [
            'current_email_address.required' => __('Current email address is required.'),
            'new_email_address.required' => __('New email address is required.'),
            'new_email_address.email' => __('Invalid email.'),
            'new_email_address.new_email_address_exists' => __('This email is already taken'),
        ];
    }
}
