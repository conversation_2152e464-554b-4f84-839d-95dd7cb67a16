<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Api\V1\MfaConfigurationController;
use App\Http\Controllers\Controller;
use App\Http\Requests\V1\ForgotPasswordRequest;
use App\Http\Requests\V1\LoginRequest;
use App\Http\Requests\V1\RegisterRequest;
use App\Http\Requests\V1\ResetPasswordRequest;
use App\Http\Resources\V1\User\UserLoginResource;
use App\Http\Resources\V1\User\UserResource;
use App\Jobs\ResendVerifyEmailLinkJob;
use App\Models\DeviceToken;
use App\Models\DomainMapping;
use App\Models\MasterUser;
use App\Models\TrainingCourse;
use App\Models\User;
use App\Models\UserAssignTrainingCourses;
use App\Models\UserRelation;
use App\Models\whiteLabelSetting;
use App\Notifications\SendLoginLink;
use App\Services\GetStreamService;
use Carbon\Carbon;
use DB;
use Google\Auth\Credentials\ServiceAccountCredentials;
use GuzzleHttp\Client;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Laravel\Passport\Passport;
use Laravel\Passport\Token;
use App\Services\DeeplinkService;

class AuthController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
     */

    private $model;
    

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->deeplinkService =new DeeplinkService();
        $this->model = new User();
    }
    public function testAndroidNotification(Request $request)
    {
        $deviceType = $request['device_type']; 
        $deviceToken = $request['device_token'];
        $projectId = 'skillsbase-a07ec';
        // Path to your service account JSON key file
        $serviceAccountFile = base_path('private/skillsbase-a07ec-firebase-adminsdk-fx22r-72cc518ccc.json');
        // Define the FCM v1 endpoint
        $url = "https://fcm.googleapis.com/v1/projects/$projectId/messages:send";
        // Load the service account credentials
        $scopes = ['https://www.googleapis.com/auth/firebase.messaging'];
        $credentials = new ServiceAccountCredentials($scopes, $serviceAccountFile);
        // Get the OAuth 2.0 token
        $token = $credentials->fetchAuthToken()['access_token'];
        // Create the notification payload
        $notification = [
            'message' => [
                'token' => $deviceToken,
                'notification' => [
                    'title' => 'Test Notification',
                    'body' => 'Test Notification From Skillsbase',
                ],
                'android' => [
                    'priority' => 'high',
                    'notification' => [
                        'sound' => 'default',
                    ],
                ],
                'apns' => [
                    'payload' => [
                        'aps' => [
                            'sound' => 'default',
                        ],
                    ],
                ],
            ],
        ];
        // Send the notification via HTTP POST
        $client = new Client();
        $response = $client->post($url, [
            'headers' => [
                'Authorization' => "Bearer $token",
                'Content-Type' => 'application/json',
            ],
            'json' => $notification,
        ]);
        // Return the response from Firebase
        return $response->getBody()->getContents();
    }

    public function testiOSNotification(Request $request)
    {
        $deviceType = $request['device_type'];
        $deviceToken = $request['device_token'];
        $projectId = 'skillsbase-a07ec';
        $APNS_TOPIC = env('APNS_TOPIC');
        $SEND_PUSH_CERTIFICATE = config_path() . env('SEND_PUSH_CERTIFICATE');
        $IOS_PASSPHRASE = env('IOS_PASSPHRASE');
        $SEND_PUSH_URL = env('SEND_PUSH_URL');

        $ch = curl_init();

        $payload = [
            'aps' => [
                'alert' => [
                    'title' => 'Hello iOS User!',
                    'body' => 'This is a push notification from Firebase!',
                ],
                'sound' => 'default', // Optional
            ],
        ];

        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_2_0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array("apns-topic: $APNS_TOPIC"));
        curl_setopt($ch, CURLOPT_SSLCERT, $SEND_PUSH_CERTIFICATE);
        curl_setopt($ch, CURLOPT_SSLCERTPASSWD, $IOS_PASSPHRASE);
        curl_setopt($ch, CURLOPT_VERBOSE, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // Ensure you can capture the response

        $payload = json_encode($payload);
        $url = $SEND_PUSH_URL . $deviceToken;
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);

        $response = curl_exec($ch);

        // Check for CURL errors
        if ($response === false) {
            echo 'Curl error: ' . curl_error($ch);
        } else {
            echo 'Response: ' . $response;
        }

        $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        echo "HTTP Code: " . $httpcode;

        curl_close($ch);die;
    }


    /**
     * @param Request
     * @return Http Response
     */
    public function register(RegisterRequest $request)
    {
        try {
            $result = DB::transaction(function () use ($request) {
                $email = trim($request->input('email'));
                $name = trim($request->input('name'));
                $verificationCode = Str::random(30); //Generate verification code

                // Assign Operator
                $bundleId = $request->header('bundle-id');
                if (!empty($bundleId)) {
                    $operatorId = whiteLabelSetting::select('operator_id')->where('is_white_label_feature_on', 1)->where('ios_package_name', $request->header('bundle-id'))->orWhere('android_package_name', $request->header('bundle-id'))->first();
                    if (isset($operatorId) && !empty($operatorId->operator_id)) {
                        $skillbaseOrig = MasterUser::where('id', $operatorId->operator_id)->first();
                    } else {
                        $skillbaseOrig = MasterUser::whereUserType('Operator')->whereNotNull('managed_by')->first();
                    }
                } else {
                    $skillbaseOrig = MasterUser::where('id', 1)->whereUserType('Operator')->whereNotNull('managed_by')->first();
                }
                $userExists=User::where('email',$email)->first();
                if(isset($userExists->id) && !empty($userExists->id))
                {
                    
                    $userRelationUpdate = UserRelation::where('user_id', $userExists->id)->update(['is_current_operator' => 0]);
                    if($userExists->is_logout==1){
                    // Update the is_logout column for the user with the specified ID
                    User::where('id', $userExists->id)->update(['is_logout' => 0,'status' => 'Inactive','email_verified_at' => NULL,'name' => $name,'name' => $name,'remember_token' => $verificationCode,'password'=>bcrypt(trim($request->input('password')))]);
                    }
                    // Check if the user relation already exists
                    $userRelationExists = UserRelation::where([
                        ['user_id', '=', $userExists->id],
                        ['master_user_id', '=', $skillbaseOrig->id],
                        ['is_current_operator', '=', 1]
                    ])->first();

                    // Create a new relation if it doesn't exist
                    if (!$userRelationExists) {
                        UserRelation::create([
                            'user_id' => $userExists->id,
                            'master_user_id' => $skillbaseOrig->id,
                            'manager_id' => $skillbaseOrig->id,
                            'is_current_operator' => 1,
                            'manager_email' => $skillbaseOrig->email,
                            'unique_id' => $skillbaseOrig->unique_id
                        ]);

                    $allDefaultCourses = TrainingCourse::whereMasterUserId($skillbaseOrig->id)->whereIsDefault(1)->get();
                    $allUsers = UserRelation::whereMasterUserId($skillbaseOrig->id)->whereUserId($userExists->id)->get();
                    if ($allDefaultCourses->count() > 0) {
                        foreach ($allDefaultCourses as $key => $defaultCourse) {
                            if ($allUsers->count() > 0) {
                                foreach ($allUsers as $key => $defaultUser) {
                                    // Check if the user relation already exists
                                    $UserAssignTrainingCourses = UserAssignTrainingCourses::where([
                                        ['user_id', '=', $userExists->id],
                                        ['master_user_id', '=', $skillbaseOrig->id],
                                        ['training_course_id', '=', $defaultCourse->id]
                                    ])->first();
                                    if (!$UserAssignTrainingCourses) {
                                    UserAssignTrainingCourses::create([
                                        'user_id' => $userExists->id,
                                        'training_course_id' => $defaultCourse->id,
                                        'master_user_id' => $skillbaseOrig->id,
                                        'is_default_course' => 1,
                                        'is_manual_course' => 0,
                                    ]);
                                    }
                                }
                            }
                        }
                    }
                    }else{
                        return response()->json(setUnprocessableResponse('Email has already been taken.'))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                    }
                    // Send Email (Query string params is for mobile developers used for deep linking)
                $encrEmail = base64_encode($email);
                $link = env('APP_URL') . '/v1/api/user-disabled/' . $encrEmail . '/' . $verificationCode;
                if ($link) {
                    $params = ['name' => $name, 'link' => $link];
                    //Checking for email address if smartawards contains only digits before @
                    $smartAwardsMailSendFlag = smartAwardsEmailSendResctrict($email);
                    if ($smartAwardsMailSendFlag == 1) {
                        $appName = GetAppName($request->header());
                        $headerData = [
                            'guard' => 'api',
                            'user_id' => $userExists->id ?? null,
                            'extra' => '',
                        ];
                        app('queue')->createPayloadUsing(function () use ($headerData) {
                            return ['headerData' => $headerData];
                        });
                        $send_email_job = (new \App\Jobs\SendEmailToRegisterUserJob($userExists, $params, $appName));
                        dispatch($send_email_job);
                    }
                    // $message = 'Thanks for signing up! We\'ve sent an email to ' . $email . '. Open it up to activate your account. If it didn\'t find in your inbox, then please check in spam folder';
                    $message='Your account has been created.';
                    return response()->json(setResponse([], ['message' =>$message]))->setStatusCode(Response::HTTP_CREATED);
                }
                }else{
                    $user = User::create([
                        'email' => $email,
                        'password' => bcrypt(trim($request->input('password'))),
                        'name' => $name,
                        'device_type' => $request->input('device_type'),
                        'device_token' => $request->input('device_token'),
                        'remember_token' => $verificationCode,
                    ]);
                if($skillbaseOrig->enable_manager_email == 0 && $skillbaseOrig->enable_unique_id == 0){
                    UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseOrig->id, 'manager_id' => $skillbaseOrig->id, 'manager_email'=> $skillbaseOrig->email, 'unique_id'=> $skillbaseOrig->unique_id]);
                }elseif($skillbaseOrig->enable_manager_email == 0){
                    UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseOrig->id, 'manager_id' => $skillbaseOrig->id, 'manager_email'=> $skillbaseOrig->email]);
                }elseif($skillbaseOrig->enable_unique_id == 0){
                    UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseOrig->id, 'unique_id'=> $skillbaseOrig->unique_id]);
                }else{
                    UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseOrig->id]);
                }
                // Skillbase original Course Assignment
                $allDefaulCourses = TrainingCourse::whereMasterUserId($skillbaseOrig->id)->get();
                if (!$allDefaulCourses->isEmpty()) {
                    foreach ($allDefaulCourses as $course) {
                        UserAssignTrainingCourses::create([
                            'user_id' => $user->id,
                            'master_user_id' => $skillbaseOrig->id,
                            'training_course_id' => $course->id,
                        ]);
                    }
                }

                // Create default Notification
                createDefaultUserNotifications($user);

                $domainName = trim(substr(strrchr($email, "@"), 1));
                $operatorList = MasterUser::whereUserType('Operator')->where('email', 'LIKE', "%" . $domainName . "%")->whereNull('managed_by')->whereNull('parent_id')->get();
                $defaultOperatorId = '';
                if (!$operatorList->isEmpty()) {
                    foreach ($operatorList as $operator) {

                        UserRelation::create(['user_id' => $user->id, 'master_user_id' => $operator->id]);
                        if ($defaultOperatorId == '') {
                            $defaultOperatorId = $operator->id;
                        }
                        // Update while enable_manager_email unchecked from an operator
                        if ($operator->enable_manager_email == 0) {
                            UserRelation::where(['user_id' => $user->id, 'master_user_id' => $operator->id])->update(['manager_email' => $operator->email]);
                        }

                        // Update while enable_unique_id unchecked from an operator
                        if ($operator->enable_unique_id == 0) {
                            UserRelation::where(['user_id' => $user->id, 'master_user_id' => $operator->id])->update(['unique_id' => $operator->unique_id]);
                        }
                        $allDefaultCourses = TrainingCourse::whereMasterUserId($operator->id)->whereIsDefault(1)->get();
                        $allUsers = UserRelation::with('user')->whereHas('user', function (Builder $query) {
                            $query->whereNull('deleted_at');
                        })->whereMasterUserId($operator->id)->get();
                        $defaultOperatorCoursesJob = (new \App\Jobs\DefaultOperatorCoursesJob($allDefaultCourses, $allUsers, $operator->id, 1, 1, 'create'))->delay(3);
                        dispatch($defaultOperatorCoursesJob);
                    }
                }
                // Domain Mapping
                $domainMappingList = DomainMapping::whereDomainName($domainName)->get();
                if (!$domainMappingList->isEmpty()) {
                    foreach ($domainMappingList as $domain) {
                        $operator = $domain->operator;
                        $isAlreadyExist = UserRelation::whereUserId($user->id)->whereMasterUserId($domain->master_user_id)->first();
                        if (!$isAlreadyExist) {
                            UserRelation::create(['user_id' => $user->id, 'master_user_id' => $domain->master_user_id, 'mapped_domain' => $domain->domain_name]);

                            $allDefaultCourses = TrainingCourse::whereMasterUserId($domain->master_user_id)->whereIsDefault(1)->get();
                            $allUsers = UserRelation::with('user')->whereHas('user', function (Builder $query) {
                                $query->whereNull('deleted_at');
                            })->whereMasterUserId($domain->master_user_id)->get();
                            $defaultOperatorCoursesJob = (new \App\Jobs\DefaultOperatorCoursesJob($allDefaultCourses, $allUsers, $domain->master_user_id, 1, 1, 'create'))->delay(3);
                            dispatch($defaultOperatorCoursesJob);
                            // Update while enable_manager_email unchecked from an operator
                            if (isset($operator->enable_manager_email) && $operator->enable_manager_email == 0) {
                                UserRelation::where(['user_id' => $user->id, 'master_user_id' => $domain->master_user_id])->update(['manager_email' => $operator->email]);
                            }
                            // Update while enable_unique_id unchecked from an operator
                            if (isset($operator->enable_unique_id) && $operator->enable_unique_id == 0) {
                                UserRelation::where(['user_id' => $user->id, 'master_user_id' => $domain->master_user_id])->update(['unique_id' => $operator->unique_id]);
                            }
                            if ($defaultOperatorId == '') {
                                $defaultOperatorId = $domain->master_user_id;
                            }
                        }
                    }
                }

                // Finally Set default operator for user
                $allRelations = UserRelation::where(['user_id' => $user->id])->orderBy('id')->get();
                if (!$allRelations->isEmpty()) {
                    $total = count($allRelations);
                    if ($total == 1) {
                        UserRelation::where(['user_id' => $user->id, 'master_user_id' => $skillbaseOrig->id])->update(['is_current_operator' => 1]);
                    } elseif ($total == 2) {
                        UserRelation::where(['user_id' => $user->id, 'master_user_id' => $defaultOperatorId])->update(['is_current_operator' => 1]);
                    } else {
                        UserRelation::where(['user_id' => $user->id, 'master_user_id' => $skillbaseOrig->id])->update(['is_current_operator' => 1]);
                    }
                }
                // Send Email (Query string params is for mobile developers used for deep linking)
                $encrEmail = base64_encode($email);
                $link = env('APP_URL') . '/v1/api/user-disabled/' . $encrEmail . '/' . $verificationCode;
                if ($link) {
                    $message = 'Thanks for signing up! We\'ve sent an email to ' . $email . '. Open it up to activate your account. If it didn\'t find in your inbox, then please check in spam folder';
                    $params = ['name' => $user->name, 'link' => $link];
                    //Checking for email address if smartawards contains only digits before @
                    $smartAwardsMailSendFlag = smartAwardsEmailSendResctrict($user->email);
                    if ($smartAwardsMailSendFlag == 1) {
                        $appName = GetAppName($request->header());
                        $headerData = [
                            'guard' => 'api',
                            'user_id' => $user->id ?? null,
                            'extra' => '',
                        ];
                        app('queue')->createPayloadUsing(function () use ($headerData) {
                            return ['headerData' => $headerData];
                        });
                        $send_email_job = (new \App\Jobs\SendEmailToRegisterUserJob($user, $params, $appName));
                        dispatch($send_email_job);
                    }
                    //Generate GetStream Token
                    app(GetStreamService::class)->GenerateGetStreamToken($user);

                    //\Notification::send($user, new VerifyEmail($params));
                    return response()->json(setResponse([], ['message' => __($message)]))->setStatusCode(Response::HTTP_CREATED);
                }
                return response()->json(setErrorResponse(__('user.AuthController.sendEmailError')))->setStatusCode(Response::HTTP_INTERNAL_SERVER_ERROR);
            }
            });
            return $result;
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @param Request
     * @return Http Response
     */
    public function resendVerifyEmailLink(Request $request)
    {
        try {
            $result = DB::transaction(function () use ($request) {
                $email = trim($request->email);
                $user = User::where(['email' => $email, 'status' => 'Pending'])->first();
                if ($user && $user->remember_token) {
                    // Send Email (Query string params is for mobile developers used for deep linking)
                    $actualURL = url(route('verifyEmail', ['key' => $user->remember_token]) . '?email=' . $email . '&token=' . $user->remember_token);
                    $message = 'We\'ve sent an email to ' . $email . '. Open it up to activate your account. If it didn\'t find in your inbox, then please check in spam folder';
                    $link = env('APP_URL') . '/v1/api/user-disabled/' . base64_encode($email) . '/' . $user->remember_token;
                    $params = ['name' => $user->name, 'link' => $link];
                    //Checking for email address if smartawards contains only digits before @
                    $smartAwardsMailSendFlag = smartAwardsEmailSendResctrict($user->email);
                    if ($smartAwardsMailSendFlag == 1) {
                        dispatch(new ResendVerifyEmailLinkJob($user, $params));
                    }
                    return response()->json(setResponse([], ['message' => __($message)]))->setStatusCode(Response::HTTP_CREATED);
                }
                return response()->json(setErrorResponse(__('user.AuthController.sendEmailError')))->setStatusCode(Response::HTTP_INTERNAL_SERVER_ERROR);
            });
            return $result;
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @param Request
     * @return Http Response
     */
    public function viewVerifyEmail(Request $request)
    {
        try {
            $user = User::where(['remember_token' => $request->key])->first();
            bladeAppNameAndLogo(($user->id ?? null));
            if ($user) {
                $user->email_verified_at = \Carbon\Carbon::now();
                $user->remember_token = '';
                $user->status = 'Active';
                $user->save();
                return view('email-verified');
            }
            return view('token-not-found');
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @param Request
     * @return Http Response
     */
    public function verifyUserEmail(Request $request)
    {
        try {
            $user = User::where(['remember_token' => $request->key])->first();
            if ($user) {
                $user->email_verified_at = \Carbon\Carbon::now();
                $user->remember_token = '';
                $user->status = 'Active';
                $user->save();

                // Register device token
                $token = $user->createToken(env('APP_NAME'))->accessToken;
                if ($request->has(['device_type', 'device_token'])) {
                    $user->device_token()->create($request->all());
                }
                return (new UserResource($user))->additional(['extra_meta' => ['token' => $token]]);
            }
            return response()->json(setErrorResponse(__('user.AuthController.linkExpired')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @param Request
     * @return Http Response
     */
    public function login(LoginRequest $request)
    {
        try {
            // $command = Artisan::call('aws:generate-pa-pdf-files');
            // print_r($command);exit;
            $user = $this->model->where(['email' => $request->email])->first();
            $header_bundle_id = strtolower($request->header('bundle-id'));
            $bundle_ids = array_change_key_case(config('constants.bundle_ids'), CASE_LOWER);
            if (in_array($header_bundle_id, array_keys($bundle_ids))) {
                $operator_id = $bundle_ids[$header_bundle_id];
                $skillbaseOrig = MasterUser::whereUserType('Operator')->whereNotNull('managed_by')->first();
                /** Operator Assign based on bundle-id Start  */
                if ($request->header('bundle-id') != null && Hash::check($request->get('password'), $user->password)) {
                    $this->AssignOperator($operator_id, $user);
                    $this->AssignSkillsBaseOperator($skillbaseOrig->id, $user);
                }
                /** Operator Assign based on bundle-id End  */
                $WLOperator = UserRelation::where('user_id', $user->id)->where('master_user_id', $operator_id)->first();
                if (empty($WLOperator)) {
                    $operatorDetail = MasterUser::where('id', $operator_id)->first();
                    return response()->json(setErrorResponse(__('user.AuthController.wlOperator', ['operator_name' => $operatorDetail->name])))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else {
                    UserRelation::where('user_id', $user->id)->update(['is_current_operator' => 0]);
                    UserRelation::where('user_id', $user->id)->where('master_user_id', $operator_id)->update(['is_current_operator' => 1]);
                }
            }

            if ($user) {
                if ($user->is_logout==1) {
                    return response()->json(setErrorResponse(__('Email or password is incorrect.')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
                if (empty($user->user_relation)) {
                    return response()->json(setErrorResponse(__('user.AuthController.OperatorNotAssigned')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->user_relation->is_disabled) {
                    return response()->json(setErrorResponse(__('user.AuthController.accountdisabled')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Active') {
                    $updateData = [];

                    if (!in_array($header_bundle_id, array_keys($bundle_ids))) {
                        $updateData['device_type'] = $request->input('device_type');
                        $updateData['device_token'] = $request->input('device_token');
                    }
                    $updateData['last_logged_in_at'] = Carbon::now();
                    $user = tap(User::with('user_relation:user_id,master_user_id,is_current_operator,manager_email,unique_id,user_group_id,is_mfa_enable', 'user_relation.operator:id,name,image,enable_manager_email,enable_unique_id', 'user_relation.group:id,name')->where('id', $user->id))->update($updateData)->first();

                    if ($user->added_by != 'Self' && $user->is_password_change == 0) {
                        $user->is_login_first_time = 1;
                    } else {
                        $user->is_login_first_time = 0;
                    }

                    // Verify the password and generate the token
                    if (Hash::check($request->get('password'), $user->password)) {

                        // Refresh token to prevent from multiple login with same credential
                        $tokens = $user->tokens->pluck('id');
                        Passport::personalAccessTokensExpireIn(\Carbon\Carbon::now()->addMonths(6));
                        if (empty($user->get_stream_token)) {
                            app(GetStreamService::class)->GenerateGetStreamToken($user);
                        }
                        return $this->createLoginToken($user, $request);
                    } else {
                        return response()->json(setErrorResponse(__('user.AuthController.wrongCredentials')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                    }
                } else if ($user->status == 'Pending') {
                    return response()->json(setErrorResponse(__('user.AuthController.accountPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if (is_null($user->email_verified_at)) {
                    return response()->json(setErrorResponse(__('user.AuthController.verificationPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Inactive') {
                    return response()->json(setErrorResponse(__('user.AuthController.accountInactive')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Rejected') {
                    return response()->json(setErrorResponse(__('user.AuthController.accountRejected')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            } else {
                return response()->json(setErrorResponse(__('user.AuthController.accountNotFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Assign operator based on bundle-id
     */
    private function AssignOperator($operatorId, $user)
    {
        $skillbaseOrig = MasterUser::where('id', $operatorId)->first();
        if ($skillbaseOrig->enable_manager_email == 0 && $skillbaseOrig->enable_unique_id == 0) {
            if (!UserRelation::where('user_id', $user->id)->where('master_user_id', $skillbaseOrig->id)->exists()) {
                UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseOrig->id, 'manager_id' => $skillbaseOrig->id, 'manager_email' => $skillbaseOrig->email, 'unique_id' => $skillbaseOrig->unique_id]);
            }
        } elseif ($skillbaseOrig->enable_manager_email == 0) {
            if (!UserRelation::where('user_id', $user->id)->where('master_user_id', $skillbaseOrig->id)->exists()) {
                UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseOrig->id, 'manager_id' => $skillbaseOrig->id, 'manager_email' => $skillbaseOrig->email]);
            }
        } elseif ($skillbaseOrig->enable_unique_id == 0) {
            if (!UserRelation::where('user_id', $user->id)->where('master_user_id', $skillbaseOrig->id)->exists()) {
                UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseOrig->id, 'unique_id' => $skillbaseOrig->unique_id]);
            }
        } else {
            if (!UserRelation::where('user_id', $user->id)->where('master_user_id', $skillbaseOrig->id)->exists()) {
                UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseOrig->id]);
            }
        }

        $allDefaultCourses = TrainingCourse::whereMasterUserId($operatorId)->whereIsDefault(1)->get();
        $allUsers = UserRelation::with('user')->whereHas('user', function (Builder $query) {
            $query->whereNull('deleted_at');
        })->whereMasterUserId($operatorId)->get();
        $defaultOperatorCoursesJob = (new \App\Jobs\DefaultOperatorCoursesJob($allDefaultCourses, $allUsers, $operatorId, 1, 1, 'create'))->delay(3);
        dispatch($defaultOperatorCoursesJob);
    }

    /**
     * Assign operator without bundle-id
     */
    private function AssignSkillsBaseOperator($operatorId, $user)
    {
        $skillbaseOrig = MasterUser::where('id', $operatorId)->first();
        if ($skillbaseOrig->enable_manager_email == 0 && $skillbaseOrig->enable_unique_id == 0) {
            if (!UserRelation::where('user_id', $user->id)->where('master_user_id', $skillbaseOrig->id)->exists()) {
                UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseOrig->id, 'manager_id' => $skillbaseOrig->id, 'manager_email' => $skillbaseOrig->email, 'unique_id' => $skillbaseOrig->unique_id]);
            }
        } elseif ($skillbaseOrig->enable_manager_email == 0) {
            if (!UserRelation::where('user_id', $user->id)->where('master_user_id', $skillbaseOrig->id)->exists()) {
                UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseOrig->id, 'manager_id' => $skillbaseOrig->id, 'manager_email' => $skillbaseOrig->email]);
            }
        } elseif ($skillbaseOrig->enable_unique_id == 0) {
            if (!UserRelation::where('user_id', $user->id)->where('master_user_id', $skillbaseOrig->id)->exists()) {
                UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseOrig->id, 'unique_id' => $skillbaseOrig->unique_id]);
            }
        } else {
            if (!UserRelation::where('user_id', $user->id)->where('master_user_id', $skillbaseOrig->id)->exists()) {
                UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseOrig->id]);
            }
        }

        $allDefaultCourses = TrainingCourse::whereMasterUserId($operatorId)->whereIsDefault(1)->get();
        $allUsers = UserRelation::with('user')->whereHas('user', function (Builder $query) {
            $query->whereNull('deleted_at');
        })->whereMasterUserId($operatorId)->get();
        $defaultOperatorCoursesJob = (new \App\Jobs\DefaultOperatorCoursesJob($allDefaultCourses, $allUsers, $operatorId, 1, 1, 'create'))->delay(3);
        dispatch($defaultOperatorCoursesJob);
    }

    /**
     * create Login Token for auth user
     */
    private function createLoginToken(User $user, $request)
    {
        try {
            $token = $user->createToken(env('APP_NAME'))->accessToken; // get access token
            $user->token = $token;
            //register device token
            $user->device_token()->create($request->all());
            if (!empty($request->header('bundle-id'))) {
                $header_bundle_id = strtolower($request->header('bundle-id'));
                $bundle_ids = array_change_key_case(config('constants.bundle_ids'), CASE_LOWER);
                if (in_array($header_bundle_id, array_keys($bundle_ids))) {
                    $this->DeviceToken($user->id, $request->all(), $request->header('bundle-id'));
                }
            }
            return $data = (new UserLoginResource($user))->additional(['extra_meta' => ['token' => $token]]);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    private function DeviceToken($UserId, $data, $BundleId)
    {
        DeviceToken::updateOrCreate([
            'user_id' => $UserId,
            'bundle_id' => $BundleId,
        ],
            ['user_id' => $UserId,
                'bundle_id' => strtolower($BundleId),
                'operator' => 'Dexgreen',
                'device_type' => $data['device_type'],
                'device_token' => $data['device_token'],
            ]);
    }

    /**
     * signout loggedIn user
     */
    public function logout(Request $request)
    {
        try {
            $authUser = Auth::user();
            if ($authUser) {
                $operatorIDs = UserRelation::where('user_id', $authUser->id)->whereNotNull('manager_email')->pluck('master_user_id')->toArray();
                if (strtolower($request->header('bundle-id')) == strtolower(config('constants.dexgreen_bundle_id'))) {
                    DB::table('device_tokens')->where('bundle_id', strtolower($request->header('bundle-id')))->where('user_id', $authUser->id)->delete();
                } else {
                    if (count($operatorIDs) > 1) {
                        if (strtolower($request->header('bundle-id')) == "") {
                            $this->AutoswitchOperator($operatorIDs[1]);
                        }
                    }
                }
                if ($request->device_token) {
                    $authUser->device_token()->where(['device_token' => $request->device_token])->delete();
                }
                $accessToken = $authUser->token();

                DB::table('oauth_refresh_tokens')->where('access_token_id', $accessToken->id)->update(['revoked' => true]);
                // for web logout after logout from any of mobile device
                DB::table('oauth_access_tokens')->where('name', 'WebApp')->where('user_id', $authUser->id)->update(['revoked' => true]);
                $accessToken->revoke();
                // remove device token while logout from the App
                User::whereId($authUser->id)->whereDeviceToken($request->device_token)->update(['device_token' => '']);
                return response()->json(setResponse([], ['message' => __('user.AuthController.logoutSuccess')]))->setStatusCode(Response::HTTP_OK);
            } else {
                return response()->json(setErrorResponse(__('user.AuthController.logoutattempt')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @param Request
     * @return Http Response
     */
    public function forgotPassword(ForgotPasswordRequest $request)
    {
        try {
            // send forgot password email
            $email = $request->only('email');
            $user = $this->model->where(['email' => $email])->first();
            if ($user && $user->status == 'Active') {
                //Checking for email address if smartawards contains only digits before @
                $smartAwardsMailSendFlag = smartAwardsEmailSendResctrict($user->email);
                if ($smartAwardsMailSendFlag == 1) {
                    $forgot_password_job = (new \App\Jobs\ForgotPasswordUserJob($request->only('email')));
                    dispatch($forgot_password_job);
                }
                //$mailResponse = Password::broker('users')->sendResetLink($request->only('email'));
                $message = Password::RESET_LINK_SENT ? __('user.AuthController.passwordResetLinkSuccess') : __('user.AuthController.passwordResetLinkError');
                return response()->json(setResponse([], ['message' => $message]))
                    ->setStatusCode(Response::HTTP_OK);
            } else if ($user->status == 'Pending') {
                return response()->json(setErrorResponse(__('user.AuthController.accountPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else if (is_null($user->email_verified_at)) {
                return response()->json(setErrorResponse(__('user.AuthController.verificationPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else if ($user->status == 'Inactive') {
                return response()->json(setErrorResponse(__('user.AuthController.accountInactive')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else if ($user->status == 'Rejected') {
                return response()->json(setErrorResponse(__('user.AuthController.accountRejected')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else {
                return response()->json(setErrorResponse(__('user.AuthController.accountNotFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @param Request
     * @return Http Response
     */
    public function resetPassword(ResetPasswordRequest $request)
    {
        try {
            // Reset the password
            $credentials = $request->only('email', 'password', 'password_confirmation', 'token');
            $response = Password::broker('users')->reset(
                $credentials, function ($user, $password) {
                    $user->password = bcrypt($password);
                    $user->email_verified_at = Carbon::now();
                    $user->save();
                    if ($user->mfa_user_id != '' && $user->mfa_user_id != null) {
                        $mfaConfig = new MfaConfigurationController();
                        $mfaConfig->adminSetUserPassword($user->mfa_user_id, $password);
                    }
                });
            if ($response === 'passwords.reset') {
                //representing a successfully reset password.Your password has been reset!
                return response()->json(setResponse([], ['message' => __('user.AuthController.passwordReset')]))->setStatusCode(Response::HTTP_OK);
            } else if ($response === 'passwords.token') {
                //representing an invalid token.
                return response()->json(setErrorResponse(__('user.AuthController.invalidToken')))->setStatusCode(Response::HTTP_NOT_FOUND);
            } else if ($response === 'passwords.user') {
                //representing the user not found response.
                return response()->json(setErrorResponse(__("user.AuthController.emailLinkUserNotFound")))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Handle the reset password "GET" request
     *
     * @param \Illuminate\Http\Request
     * @param String $token
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function reset(Request $request)
    {
        try {
            $token = $request->token;
            $email = $request->email;
            return view('auth.passwords.reset', compact('token', 'email'));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Generate password
     */
    public function generatePassword()
    {
        try {
            return response()
                ->json(setResponse(['password' => getRandomPassword()]))
                ->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @param Request
     * @return Http Response
     */
    public function getDomainSuggestions(Request $request)
    {
        try {
            //$domains = User::select(DB::raw('CONCAT("@", SUBSTRING_INDEX(email, "@", -1)) as domain'))->distinct(['domain'])->orderBy('domain')->get()->toArray();
            $domains = ['gmail.com', 'hotmail.com', 'yahoo.com', 'outlook.com', 'icloud.com', 'aol.com', 'protonmail.com', 'hotmail.co.uk', 'live.com'];
            $listDomains = [];
            foreach ($domains as $domain) {
                $listDomains[] = ['domain' => '@' . $domain];
            }
            return response()->json(setResponse(['domains' => $listDomains]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @param Request
     * @return Http Response
     */
    public function loginWithEmailLink(Request $request)
    {
        try {
            $credentials = $request->only('email');
            $rules = ['email' => 'required|email'];
            $messages = [
                'email.required' => 'Please enter email.',
                'email.email' => 'Email must be a valid email address (E.g.: <EMAIL>).',
            ];
            $validator = Validator::make($credentials, $rules, $messages);
            if ($validator->fails()) {
                $err = implode('<br/>', $validator->errors()->all());
                return response()->json(setErrorResponse($err, Response::HTTP_BAD_REQUEST))->setStatusCode(Response::HTTP_BAD_REQUEST);
            }
            $user = User::where('email', $request->email)->first();
            if (!empty($user)) {
                if ($user->status == 'Pending') {
                    return response()->json(setErrorResponse(__('user.AuthController.accountPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if (is_null($user->email_verified_at)) {
                    return response()->json(setErrorResponse(__('user.AuthController.verificationPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Inactive') {
                    return response()->json(setErrorResponse(__('user.AuthController.accountInactive')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Rejected') {
                    return response()->json(setErrorResponse(__('user.AuthController.accountRejected')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if (is_null($user->status)) {
                    return response()->json(setErrorResponse(__('user.AuthController.verificationPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }

                $user->email_token = Str::random(30);
                $user->save();

                // Generate Deep Link and Send in Email
                $actualURL = url(route('verifyLoginEmailLinkToken') . '?email=' . $user->email . '&email_token=' . $user->email_token);
                $operatorId = ($user->user_relation ? $user->user_relation->master_user_id : null);
                                $dynamicLink =$this->deeplinkService->generateDeeplink([
                                    'target_url' => $actualURL,
                                    'operator_id' => $operatorId,
                                    'type' => 'loginWithEmailLink',
                                    'entity_id' => $user->id,
                                    'entity_type' => 'Login'
                                ]);
                // $dynamicLink = generateFirebaseDeepLink($actualURL, $operatorId);
                if ($dynamicLink) {
                    if ($request->header('bundle-id')) {
                        $safeLink = env('APP_URL') . '/v1/api/login_link?email=' . $user->email . '&email_token=' . $user->email_token . '&bundle_id=' . $request->header('bundle-id');
                    } else {
                        $safeLink = env('APP_URL') . '/v1/api/login_link?email=' . $user->email . '&email_token=' . $user->email_token;
                    }
                    $params = ['name' => $user['name'], 'email' => $user['email'], 'link' => $dynamicLink, 'safe_link' => $safeLink];
                    //Checking for email address if smartawards contains only digits before @
                    $smartAwardsMailSendFlag = smartAwardsEmailSendResctrict($user['email']);
                    if ($smartAwardsMailSendFlag == 1) {
                        \Notification::send($user, new SendLoginLink($params));
                    }
                    return response()->json(setResponse([], ['message' => __('user.AuthController.linksentSuccess')]))->setStatusCode(Response::HTTP_OK);
                }
                return response()->json(setErrorResponse(__('user.AuthController.sendEmailError')))->setStatusCode(Response::HTTP_INTERNAL_SERVER_ERROR);
            }
            return response()->json(setErrorResponse(__('user.AuthController.invalidEmail')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @param Request
     * @return Http Response
     */
    public function verifyLoginEmailLinkToken(Request $request)
    {
        try {
            $data = $request->only(['email', 'email_token']);
            $rules = ['email' => 'required|email', 'email_token' => 'required'];
            $messages = [
                'email.required' => 'Please enter email.',
                'email.email' => 'Email must be a valid email address (E.g.: <EMAIL>).',
                'email_token.required' => 'Token is required.',
            ];
            $validator = Validator::make($data, $rules, $messages);
            if ($validator->fails()) {
                $err = implode('<br/>', $validator->errors()->all());
                return response()->json(setErrorResponse($err, Response::HTTP_BAD_REQUEST))->setStatusCode(Response::HTTP_BAD_REQUEST);
            }
            $user = User::where('email', $request->email)->first();
            if (!empty($user)) {
                if ($user->status == 'Pending') {
                    return response()->json(setErrorResponse(__('user.AuthController.accountPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if (is_null($user->email_verified_at)) {
                    return response()->json(setErrorResponse(__('user.AuthController.verificationPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Inactive') {
                    return response()->json(setErrorResponse(__('user.AuthController.accountInactive')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Rejected') {
                    return response()->json(setErrorResponse(__('user.AuthController.accountRejected')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if (is_null($user->status)) {
                    return response()->json(setErrorResponse(__('user.AuthController.verificationPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if (is_null($user->email_token) || ($request->email_token !== $user->email_token)) {
                    return response()->json(setErrorResponse(__('user.AuthController.invalidEmailToken')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($request->email_token === $user->email_token) {
                    $user->email_token = null;
                    $user->device_type = $request->device_type;
                    $user->device_token = $request->device_token;
                    $user->save();
                    // Register device token
                    $token = $user->createToken(env('APP_NAME'))->accessToken;
                    $user->token = $token;

                    if ($request->has(['device_type', 'device_token'])) { // Only for mobile devices
                        $user->device_token()->create($request->all());
                    }
                    return (new UserResource($user))->additional(['extra_meta' => ['token' => $token]]);
                }
                return response()->json(setResponse([], ['message' => __('user.AuthController.somethingWrong')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setErrorResponse(__('user.AuthController.invalidEmail')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * get QRCode
     */
    public function getQRCode()
    {
        try {
            $uniqueCode = Str::random(25);
            $data['code'] = base64_encode($uniqueCode);
            DB::table('qr_codes')->insert([
                'code' => $uniqueCode,
            ]);
            return response()->json(setResponse($data, ['message' => __('user.AuthController.qrcodesuccess')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
        //return response()->json(setErrorResponse(__('user.AuthController.qrcodeError')))->setStatusCode(Response::HTTP_INTERNAL_SERVER_ERROR);
    }

    /**
     * @param Request $request
     * verify QRCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyQRCode(Request $request)
    {
        try {
            if ($request->qrcode) {
                $encryptedQR = $request->qrcode;
                $qrCode = \base64_decode($request->qrcode);
                $isValid = DB::table('qr_codes')->where('code', $qrCode)->get()->toArray();
                if ($isValid) {

                    // Delete all old QRcode whose time less than 1 hr
                    DB::table('qr_codes')->whereRaw('generated_at < (NOW() - INTERVAL 1 HOUR)')->delete();

                    // Creating a token with WebApp name
                    $user = Auth::user();
                    $token = $user->createToken("WebApp")->accessToken; // get access token
                    $userData['data'] = $user;
                    $userData['token'] = $token;
                    $userData['qrcode'] = $encryptedQR;

                    // Delete verified old QRcode
                    DB::table('qr_codes')->where('code', $qrCode)->delete();

                    // event(new QrScanEvent($userData));

                    return $data = (new UserResource($user))->additional(['extra_meta' => ['token' => $token, 'qrcode' => $encryptedQR]]);
                } else {
                    return response()->json(setResponse([], ['message' => __('user.AuthController.invalidqr')]))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @param Request $request
     * verify user email address via link
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyUseremailLink(Request $request)
    {
        try {
            $user = User::where(['remember_token' => $request->token])->first();
            if ($user) {
                $user->email_verified_at = \Carbon\Carbon::now();
                $user->remember_token = '';
                $user->status = 'Active';
                $user->save();
                return ['success' => true];
            } else {
                return ['success' => false];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
            ];
        }
    }

    public function verifyUseremailLinkView($token, $appName)
    {
        try {
            $user = User::where(['remember_token' => $token])->first();
            bladeAppNameAndLogo($user->id ?? null);
            $result = (!empty($user) ? 1 : 0);
            $logoUrl = getWhitelabelEmailLogoPath($appName);
            $wlSettings = whiteLabelSetting::select('operator_id', 'android_package_name', 'ios_app_store_id', 'view_background_image', 'fallback_link', 'app_name', 'main_color_scheme')->where('is_white_label_feature_on', 1)->where('app_name', base64_decode($appName))->first();
            return view('user_email_verified', compact('token', 'result', 'appName', 'logoUrl', 'wlSettings'));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @param Request
     * login for web panel
     * @return Http Response
     */
    public function webLogin(LoginRequest $request)
    {
        try {
            $user = $this->model->where(['email' => $request->email])->first();
            if ($user) {
                if ($user->is_logout==1) {
                    return response()->json(setErrorResponse(__('Email or password is incorrect.')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
                if ($user->user_relation->is_disabled) {
                    return response()->json(setErrorResponse(__('user.AuthController.accountdisabled')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Active') {
                    $updateData = [];
                    $updateData['device_type'] = $request->input('device_type');
                    $updateData['device_token'] = $request->input('device_token');
                    $updateData['last_logged_in_at'] = Carbon::now();
                    $user = tap(User::with('user_relation:user_id,master_user_id,is_current_operator,manager_email,unique_id,user_group_id', 'user_relation.operator:id,name,image,enable_manager_email,enable_unique_id', 'user_relation.group:id,name')->where('id', $user->id))->update($updateData)->first();

                    if ($user->added_by != 'Self' && $user->is_password_change == 0) {
                        $user->is_login_first_time = 1;
                    } else {
                        $user->is_login_first_time = 0;
                    }

                    // Verify the password and generate the token
                    if (Hash::check($request->get('password'), $user->password)) {

                        // Refresh token to prevent from multiple login with same credential
                        $tokens = $user->tokens->pluck('id');
                        // Token::whereIn('id', $tokens)->update(['revoked'=> true]);
                        // RefreshToken::whereIn('access_token_id', $tokens)->update(['revoked' => true]);

                        Passport::personalAccessTokensExpireIn(\Carbon\Carbon::now()->addMonths(6));

                        return $this->createLoginToken($user, $request);
                    } else {
                        return response()->json(setErrorResponse(__('user.AuthController.wrongCredentials')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                    }
                } else if ($user->status == 'Pending') {
                    return response()->json(setErrorResponse(__('user.AuthController.accountPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if (is_null($user->email_verified_at)) {
                    return response()->json(setErrorResponse(__('user.AuthController.verificationPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Inactive') {
                    return response()->json(setErrorResponse(__('user.AuthController.accountInactive')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Rejected') {
                    return response()->json(setErrorResponse(__('user.AuthController.accountRejected')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            } else {
                return response()->json(setErrorResponse(__('user.AuthController.accountNotFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @param Request
     * forgot password for web panel
     * @return Http Response
     */
    public function webForgotPassword(ForgotPasswordRequest $request)
    {
        try {
            // send forgot password email
            $email = $request->only('email');
            $user = $this->model->where(['email' => $email])->first();
            if ($user && $user->status == 'Active') {
                //Checking for email address if smartawards contains only digits before @
                $smartAwardsMailSendFlag = smartAwardsEmailSendResctrict($user->email);
                if ($smartAwardsMailSendFlag == 1) {
                    $mailResponse = Password::broker('users')->sendResetLink($request->only('email'));
                }
                $message = Password::RESET_LINK_SENT ? __('user.AuthController.passwordResetLinkSuccess') : __('user.AuthController.passwordResetLinkError');
                return response()->json(setResponse([], ['message' => $message]))
                    ->setStatusCode(Response::HTTP_OK);
            } else if ($user->status == 'Pending') {
                return response()->json(setErrorResponse(__('user.AuthController.accountPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else if (is_null($user->email_verified_at)) {
                return response()->json(setErrorResponse(__('user.AuthController.verificationPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else if ($user->status == 'Inactive') {
                return response()->json(setErrorResponse(__('user.AuthController.accountInactive')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else if ($user->status == 'Rejected') {
                return response()->json(setErrorResponse(__('user.AuthController.accountRejected')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else {
                return response()->json(setErrorResponse(__('user.AuthController.accountNotFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @param Request
     * Reset password for web panel
     * @return Http Response
     */
    public function webResetPassword(ResetPasswordRequest $request)
    {
        try {
            // Reset the password
            $credentials = $request->only('email', 'password', 'password_confirmation', 'token');
            $response = Password::broker('users')->reset(
                $credentials, function ($user, $password) {
                    $user->password = bcrypt($password);
                    $user->email_verified_at = Carbon::now();
                    $user->save();
                    if ($user->mfa_user_id != '' && $user->mfa_user_id != null) {
                        $mfaConfig = new MfaConfigurationController();
                        $mfaConfig->adminSetUserPassword($user->mfa_user_id, $password);
                    }
                });
            if ($response === 'passwords.reset') {
                //representing a successfully reset password.Your password has been reset!
                return response()->json(setResponse([], ['message' => __('user.AuthController.passwordReset')]))->setStatusCode(Response::HTTP_OK);
            } else if ($response === 'passwords.token') {
                //representing an invalid token.
                return response()->json(setErrorResponse(__('user.AuthController.invalidToken')))->setStatusCode(Response::HTTP_NOT_FOUND);
            } else if ($response === 'passwords.user') {
                //representing the user not found response.
                return response()->json(setErrorResponse(__("user.AuthController.emailLinkUserNotFound")))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function AutoswitchOperator($operatorId)
    {
        try {
            $user = auth()->user();
            if ($user) {
                $masterUser = UserRelation::where(['user_id' => $user->id, 'master_user_id' => $operatorId])->value('id');
                if (!empty($masterUser)) {
                    UserRelation::where(['user_id' => $user->id, 'is_current_operator' => 1])->update(['is_current_operator' => 0]);
                    UserRelation::where(['user_id' => $user->id, 'master_user_id' => $operatorId])->update(['is_current_operator' => 1]);
                } else {
                    return response()->json(setErrorResponse(__('user.UserController.MasterUserNotFound')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            }
            return response()->json(setErrorResponse(__('user.UserController.notFound')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
