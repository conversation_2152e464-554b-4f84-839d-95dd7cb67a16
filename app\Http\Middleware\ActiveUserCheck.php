<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Contracts\Auth\Factory as Auth;
use Illuminate\Support\Facades\Auth as Auth1;
use Illuminate\Http\Response;
use  App\Models\User;
use Carbon\Carbon;

class ActiveUserCheck
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */

    public function handle($request, Closure $next)
    {
        if(Auth1::user()){
            User::where('id', Auth1::user()->id)->update(['last_logged_in_at' => Carbon::now()]);
        }
        if($request->user() && $request->user()->status == 'Active'){
            // Update accessToken in all the API request
            if($request->hasHeader('device_type') && $request->hasHeader('device_token')){
                User::where('id', $request->user()->id)->update(['device_type' => $request->header('device_type'), 'device_token' => $request->header('device_token')]);
            }
            if(isset($request->user()->user_relation) && $request->user()->user_relation->is_disabled == 0){
                $response = $next($request);
            }else{  
                return $next($request);
            }   
        }else{
            return response()->json(setErrorResponse('Unauthorized, Your accout is inactivated.', Response::HTTP_UNAUTHORIZED))
                            ->setStatusCode(Response::HTTP_UNAUTHORIZED);
        }
        return $response;        
    }
}
