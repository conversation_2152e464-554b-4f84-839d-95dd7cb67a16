<?php

namespace App\Http\Controllers\Operator\v1;

use App\Models\News;
use App\Models\User;
use App\Models\Product;
use App\Models\Resources;
use App\Models\UserGroup;
use App\Models\UserRelation;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\TrainingCourse;
use App\Models\DashBoardStatics;
use App\Models\TrainingCourseModules;

class DashboardController extends Controller
{
    /**
     * @OA\Get(
     *     path="/operator/dashboard",
     *     tags={"Operator - Dashboard"},
     *     summary="Dashboard Data",
     *     description="Dashboard Data",
     *     operationId="index",
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function index(Request $request) {
      try {
        $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
        $response = [];
        // Recent Courses
        $response['recent_courses'] = TrainingCourse::recentCourses();

        // Recent Modules
        $response['recent_modules'] = TrainingCourseModules::recentModules();
        // Users Analytics
        // $response['users'] =Users::statistics();
        $response['users'] = json_decode(DashBoardStatics::where('operator_id',$operatorId)->value('users'));

        // Resource Analytics
        // $response['resources'] = Resources::statistics();
        $response['resources'] =json_decode(DashBoardStatics::where('operator_id',$operatorId)->value('resources'));

        // Product Analytics
        // $response['products'] = Product::statistics();
        $response['products'] = json_decode(DashBoardStatics::where('operator_id',$operatorId)->value('products'));

        // News Analytics
        // $response['news'] = News::statistics();
        $response['news'] = json_decode(DashBoardStatics::where('operator_id',$operatorId)->value('news'));

        // Groups Analytics
        // $response['groups'] = UserGroup::statistics();
        $response['groups'] = json_decode(DashBoardStatics::where('operator_id',$operatorId)->value('groups'));

        // Modules Analytics
        // $response['modules'] =  TrainingCourseModules::statistics();
        $response['modules'] =  json_decode(DashBoardStatics::where('operator_id',$operatorId)->value('modules'));

        // Training Course Analytics
        // $response['courses'] = TrainingCourse::statistics();
        $response['courses'] = json_decode(DashBoardStatics::where('operator_id',$operatorId)->value('courses'));

          // Training Course List
          // $response['course_list'] = TrainingCourse::CourseList();

        return $response;
      } catch (\Exception $e) {
          return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
      }
    }
}
