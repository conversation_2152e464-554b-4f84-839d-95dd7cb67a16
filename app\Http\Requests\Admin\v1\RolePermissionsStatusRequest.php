<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;

class RolePermissionsStatusRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {
        return [
            'ids' => 'required|array', 
            'ids.*' => 'required|int',
            'status' => 'required|in:Active,Inactive',
        ];
    }

    public function messages() : array
    {
        return [
            'status.required' => __('Status is required.'),
            'ids.required' => __('Role id is required.'),
            'ids.*' => __('Role id must be integer.'),
        ];
    }
}
