<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;
use App\Models\Roles;

class RolesRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {

        \Validator::extend('name_exists',
            function($attribute, $value, $parameters)
            {
                $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
                if(isset($parameters[0]) && !is_null($parameters[0])){
                    $existNews = Roles::where('name',$value)->whereMasterUserId($operatorId)->where('id','!=', $parameters[0])->first();
                }else{
                    $existNews = Roles::where('name',$value)->whereMasterUserId($operatorId)->first();
                }
                return $existNews ? false : true;
            }
        );
        return [
            "name" => 'required|name_exists:'.$this->id,
            "status" => 'required',
            "permission" => 'required',
        ];
    }

    public function messages() : array
    {
        return [
            'name.required' => __('The name field is required.'),
            'name.name_exists' => __('Role name already exists.'),
            'status.required' => __('The status field is required.'),
            'permission.required' => __('The permission field is required.'),
        ];
    }
}
