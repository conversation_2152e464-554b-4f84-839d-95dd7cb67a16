<?php

namespace App\Http\Requests\Operator\v1;

use DBTableNames;
use App\Http\Requests\CustomFormRequest;

class TrainingCourseSubmodulePocManageCommentRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {
        return [
            'id' => 'required|integer|exists:' . DBTableNames::HOTSPOT_UPLOADER_MEDIA . ',id',
            'description' => 'required',
        ];
    }

    public function messages() : array
    {
        return [
            'id.required' => __('Region id is required.'),
            'id.integer' => __('Region id must be an integer.'),
            'id.exists' => __('Region doesn\'t exists.'),
            'description.required' => __('Description is required.'),
        ];
    }
}
