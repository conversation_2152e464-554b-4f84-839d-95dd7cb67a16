<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Contracts\Auth\Factory as Auth;
use Illuminate\Support\Facades\Auth as Auth1;
use App\Models\SystemLog;
use App\Models\User;
use App\Models\MasterUser;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Illuminate\Http\Response;

class AppLogout
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next, $guard = '')
    {
        $authUser = Auth1::user();
        $user_id = isset($authUser) ? $authUser->id:NULL;
        $userRecord=User::select('is_logout')->where('id',$user_id)->first();
        if($userRecord->is_logout==1){
                return response()->json(setErrorResponse(__('You have been Signed Out')))
                        ->setStatusCode(Response::HTTP_UNAUTHORIZED);    
        }
        $response = $next($request);
        return $response;
    }
}
