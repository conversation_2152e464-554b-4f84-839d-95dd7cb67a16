<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\TrainingCourse;

class PublishCourseCron extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'publish:course';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command is for published course from schedule';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $CurrentUtcDate = date('Y-m-d H:i:s');
        $startTime = date("Y-m-d H:i:s", strtotime('-1 minutes', strtotime($CurrentUtcDate)));
        $endTime = date("Y-m-d H:i:s", strtotime('+1 minutes', strtotime($CurrentUtcDate)));
        $notifyCourseList = TrainingCourse::where('publish_now', 0)->whereStatus('Active')->whereBetween('schedule_at', array($startTime, $endTime))->get();
        TrainingCourse::where('publish_now', 0)
            ->whereBetween('schedule_at', array($startTime, $endTime))
            ->update(['publish_now' => 1]);
        if (count($notifyCourseList) > 0) {
            foreach ($notifyCourseList as $key => $course) {
                TrainingCourse::sendChangeStatusCourseNotification($course->id, $course);
            }
        }

    }
}
