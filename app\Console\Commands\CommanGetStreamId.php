<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\MasterUser;
use Illuminate\Console\Command;
use App\Services\GetStreamService;
use Illuminate\Support\Facades\DB;

class CommanGetStreamId extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'comman:get-stream-user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate Comman GetStream Id For Team Member & User';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $record= MasterUser::select('id','parent_id','name','email','image')->whereNotNull('parent_id')->whereNull('comman_get_stream_id')->get()->toArray();
        foreach($record as $value){
            app(GetStreamService::class)->GenerateTeamMemberGetStreamId($value['id'],$value['name'],$value['email'],$value['image'],2,$value['parent_id']);
        }
        $team= MasterUser::select('id','get_stream_token','comman_get_stream_id','email')->where('user_type','Operator')->whereNotNull('parent_id')->get()->toArray();
        foreach($team as $team_value){
            // app(GetStreamService::class)->DeleteTeamMemberGetStream($team_value['id']);
            $Users=User::where('email',$team_value['email'])->get()->toArray();
            if(!empty($Users)){
                User::where('id', $Users[0]['id'])->update(['comman_get_stream_id'=>$team_value['comman_get_stream_id'],'get_stream_token'=>$team_value['get_stream_token']]);
                app(GetStreamService::class)->UpdateCommanIdForUser($team_value['comman_get_stream_id'],$Users[0]);
            }

        }

    }
}
