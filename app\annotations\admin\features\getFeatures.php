<?php
/**
     * @OA\Get(
     *     path="/admin/features/getFeatures/{operatorId}",
     *     tags={"Admin - Features"},
     *     summary="Get All Features Listing",
     *     description="Get All Features Listing",
     *     operationId="getFeatures",
     *     @OA\Parameter(
     *         description="Operator Id",
     *         name="operatorId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */