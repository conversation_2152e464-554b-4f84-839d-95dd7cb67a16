<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Response;
use App\Models\News;
use App\Http\Resources\V1\NewsListingResource;
use App\Http\Resources\V1\NewsDetailResource;
use Illuminate\Contracts\Auth\Factory as Auth;
use Illuminate\Support\Facades\Auth as Auth1;

class NewsController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        
    }

    /**
     * News List
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index() {
        try {
            $user = Auth1::user();
            $news = News::active()->where('master_user_id', $user->user_relation->master_user_id)->latest()->get();
            return NewsListingResource::collection($news);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * News Detail
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function show($id) {
        try {
            $news = News::find($id);
            if($news->status == 'Inactive'){
                return response()->json(setErrorResponse(__('user.News.unAuthorized')))->setStatusCode(Response::HTTP_OK);
            }
            return ($news)?
                    (new NewsDetailResource($news)):
                    response()->json(setErrorResponse(__('operator.news.not-found')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
