<?php

namespace App\Http\Controllers\Operator\v1;

use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Models\TrainingCourseSubModuleFeedbackQuestionAnswer;
use App\Models\TrainingCourseSubModuleFeedbackPDFResults;
use App\Models\User;
use App\Http\Requests\Operator\v1\TrainingCourseSubModuleFeedbackListingRequest;
use App\Repositories\Operator\v1\TrainingCourseSubModuleCourseFeedbackRepository;
use App\Http\Resources\Operator\v1\TrainingCourseSubModuleCourseFeedbackResource;
use App\Http\Resources\Operator\v1\TrainingCourseSubModuleCourseFeedbackUserResource;

class TrainingCourseSubmoduleCourseFeedbackController extends Controller
{   
    protected $courseFeedbackModel;
    protected $courseFeedbackRepository;
    
    public function __construct() {
        $this->courseFeedbackModel = new TrainingCourseSubModuleFeedbackQuestionAnswer();
        $this->courseFeedbackRepository = new TrainingCourseSubModuleCourseFeedbackRepository($this->courseFeedbackModel);
    }
    
    /**
     * @OA\Post(
     *     path="/operator/trainingCourseSubmodule/feedback/user/getListing",
     *     tags={"Operator - Training Course Submodule Feedback Response"},
     *     summary="Get All Feedback Responses",
     *     description="Get All Feedback Responses",
     *     operationId="getListing",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="submodule_id",
     *                     description="Submodule Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                  @OA\Property(
     *                     property="isExport",
     *                     description="for export csv",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 example={"submodule_id": 1, "per_page": 10, "page": 1, "search_key": "", "sort_by": "name", "order_by": "asc", "isExport": 0, "filters": {"uname": "", "gname": "", "status": ""}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    
    public function getListing(TrainingCourseSubModuleFeedbackListingRequest $request) {
        try {
            $data = $this->courseFeedbackRepository->getFeedbackResponseListing($request->all());
            
            if ($request->isExport) {
                $exportData = $data->get()->toArray();
                $uids = [];
                foreach($exportData as $data){
                    $uids[] = $data['uid'];
                }

                $feedbackPDFResults = TrainingCourseSubModuleFeedbackPDFResults::whereIn('user_id',$uids)
                                                                                ->where('submodule_id',$request->submodule_id)
                                                                                ->get()->pluck('pdf_url','user_id')->toArray();
                foreach ($exportData as $key => $data) {
                    if (array_key_exists($data['uid'], $feedbackPDFResults)){
                        $exportData[$key]['pdf'] = $feedbackPDFResults[$data['uid']];
                    }
                    else{
                        $exportData[$key]['pdf'] = '--';
                    }
                }

                return $this->courseFeedbackRepository->exportCsv($exportData, $request->exportFields);
            }
            return TrainingCourseSubModuleCourseFeedbackResource::collection($data->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * @OA\Get(
     *     path="/operator/trainingCourseSubmodule/feedback/{submoduleId}/user/{userId}",
     *     tags={"Operator - Training Course Submodule Feedback Response"},
     *     summary="Get Specific Feedback Response",
     *     description="Get Specific Feedback Response",
     *     operationId="userResponse",
     *     @OA\Parameter(
     *         description="Id of Submodule",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Parameter(
     *         description="Id of User",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Role Deleted successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function userResponse($submoduleId, $userId) {
        try {
            $feedback = $this->courseFeedbackModel->where(['submodule_id' => $submoduleId, 'user_id' => $userId])->get();
            if (count($feedback) > 0) {
                $user = User::find($userId);
                if ($user) {
                    return [
                        "name" => $user->name,
                        "email" => $user->email,
                        "photo" => $user->photo_url,
                        "data" => TrainingCourseSubModuleCourseFeedbackUserResource::collection($feedback)
                    ];
                }
            }
            return response()->json(setResponse([], ['message' => __('operator.feedback.not-found')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
}
