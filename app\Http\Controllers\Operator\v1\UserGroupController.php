<?php

namespace App\Http\Controllers\Operator\v1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\UserGroup;
use App\Models\TrainingCourse;
use App\Models\UserRelation;
use Illuminate\Http\Response;
use App\Http\Resources\CustomCollection;
use App\Repositories\Operator\v1\UserGroupRepository;
use App\Http\Requests\Operator\v1\UserGroupRequest;
use App\Http\Resources\Operator\v1\UserGroupResource;
use App\Http\Requests\Operator\v1\UserGroupStatusRequest;
use App\Http\Requests\Operator\v1\UserGroupDeleteRequest;
use App\Http\Requests\Operator\v1\CommonListingRequest;


class UserGroupController extends Controller
{
    private $model;

    public function __construct() {
        $this->group = new UserGroup();
        $this->user_group_repository = new UserGroupRepository($this->group);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Display a listing of all User group.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
      /**
     * @OA\Post(
     *     path="/operator/userGroups/getUserGroupList",
     *     tags={"Operator - User Groups"},
     *     summary="Get All User Group List ",
     *     description="Get All User Group List ",
     *     operationId="getUserGroupList",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="User Group Name",
     *                     type="string"
     *                 ),
    *                 @OA\Property(
     *                     property="status",
     *                     description="User Group Status",
     *                     type="string",
                           enum={"Active", "Inactive"}
     *                 ),
     *                 example={
                        "per_page": 10,
                        "page": 1,
                        "search_key": "My Group",
                        "filter": {
                            "name": "my group 1",
                            "manager_email": "<EMAIL>",
                            "unique_id": "UID456",
                            "status": "Active"
                        },
                        "sort_by": "name",
                        "order_by": "asc",
                        "isExport": 0
                    }
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="User Group list genetated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getUserGroupList(CommonListingRequest $request)
    {
        try {
            $perPage = isset($request->per_page) ? $request->per_page : 10;
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $groupTemp = UserGroup::select('groups.id','groups.name','groups.manager_email','groups.unique_id','status',\DB::raw('COUNT(user_relations.user_group_id) as total_users'))
            ->leftJoin('user_relations', function($join) use($operatorId)
                {
                    $join->on('groups.id', '=', 'user_relations.user_group_id');
                    $join->where('user_relations.master_user_id',$operatorId);
                })
            ->groupBy('user_relations.user_group_id')
            ->groupBy('groups.id')
            ->where('groups.parent_id',$operatorId);
            //filter
            if($request->has('filters')){
                $filterBy=$request->filters;
                foreach ($filterBy as $fkey => $value) {
                    $groupTemp->where($fkey, 'LIKE', '%' . $value . '%');
                }

            }

            // search key filter
            if($request->has('search_key') && $request->search_key !=''){
                $searchTerm=$request->search_key;
                $groupTemp->where(function ($query) use ($searchTerm) {
                    $query->where('groups.name', 'like', '%' . $searchTerm . '%')
                    ->orWhere('groups.manager_email', 'like', '%' . $searchTerm . '%')
                    ->orWhere('groups.unique_id', 'like', '%' . $searchTerm . '%')
                    ->orWhere('groups.status', 'like', '%' . $searchTerm . '%');
                });
            }

            //Sorting
            if($request->has('sort_by')){
                $sortBy=$request->input('sort_by');
                $orderBy=($request->input('order_by')=='asc' || $request->input('order_by')=='ASC')?'ASC':'DESC';
                $groupTemp->orderBy($sortBy,$orderBy);

            } else {
                $groupTemp =  $groupTemp->orderByDesc('id');
            }

            if ($request->isExport) {
                return $this->user_group_repository->exportCsv($groupTemp->get());
            }
            $groupTemp = $groupTemp->paginate($perPage);
            return new CustomCollection($groupTemp, 'App\Http\Resources\Operator\v1\UserGroupResource');
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    /**
     * @OA\Post(
     *     path="/operator/userGroups",
     *     tags={"Operator - User Groups"},
     *     summary="Add User Group ",
     *     description="Add User Group ",
     *     operationId="store",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="User Group Name",
     *                     type="string"
     *                 ),
    *                 @OA\Property(
     *                     property="status",
     *                     description="User Group Status",
     *                     type="string",
                           enum={"Active", "Inactive"}
     *                 ),
     *                 example={"0":{
                      "step":1,
                      "name":"My Group"
                    },"1":{
                    "step":2,
                      "groupId":12,
                            "trainingCourseIds":{
                                "0":1,
                                "1":3
                            }
                    },"2":{
                        "step":3,
                        "groupId":12,
                        "address":"76 nyc street CA",
                        "contact_no":"1111111111",
                        "latitude":"22.725524",
                        "longitude":"75.854401",
                        "email":"<EMAIL>",
                        "website":"https://www.abc.com",
                    }}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="User Group Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function store(UserGroupRequest $request)
    {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $moduleData = $request;
            $moduleData['parent_id'] = $operatorId;
            $result=$this->user_group_repository->create($moduleData);
            $retunData=array('id'=>$result->id,'name'=>$result->name);
            return response()->json(setResponse($retunData, ['message' => __('operator.UserGroupController.add')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/userGroups/{id}",
     *     tags={"Operator - User Groups"},
     *     summary="Show User Group Details",
     *     description="Show User Group Details",
     *     operationId="show",
     *     @OA\Parameter(
     *         description="Id of User group to fetch",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function show($id)
    {
        try {
            $groupDetail=UserGroup::with('assignCourses')->findOrFail($id);
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $allGroups = UserGroup::where('parent_id', $operatorId)->pluck('id')->toArray();
            if(in_array($id, $allGroups)){
                $groupDetail = new UserGroupResource($groupDetail);
                $message = !empty($groupDetail) ? __('operator.UserGroupController.found') : __('operator.UserGroupController.notFound');
                return response()->json(setResponse($groupDetail, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
            }else{
                return response()->json(setErrorResponse(__('You cannot access this record')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * @OA\Put(
     *     path="/operator/userGroups/{id}",
     *     tags={"Operator - User Groups"},
     *     summary="Update User Group details",
     *     description="Update User Group details",
     *     operationId="update",
     *     @OA\Parameter(
     *         description="Id of User group to update",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
    *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="User Group Name",
     *                     type="string"
     *                 ),
    *                 @OA\Property(
     *                     property="status",
     *                     description="group Status",
     *                     type="string",
                           enum={"Active", "Inactive"}
     *                 ),
     *                 example={"0":{
                      "step":1,
                      "name":"My Group"
                    },"1":{
                    "step":2,
                            "trainingCourseIds":{
                                "0":1,
                                "1":3
                            }
                    },"2":{
                        "step":3,
                        "address":"76 nyc street CA",
                        "contact_no":"1111111111",
                        "latitude":"22.725524",
                        "longitude":"75.854401",
                        "email":"<EMAIL>",
                        "website":"https://www.abc.com",
                    }}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Role Updated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function update(UserGroupRequest $request, $id)
    {
        try {
            $this->user_group_repository->update($request, $id);
            return response()->json(setResponse([], ['message' => __('operator.UserGroupController.update')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/userGroups/delete",
     *     tags={"Operator - User Groups"},
     *     summary="Delete  User Groups",
     *     description="Delete  Role",
     *     operationId="destroy",
           @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="id",
     *                     description="id",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={ "ids":{
                                "0":"1",
                                "1":"2"
                            }
                        }
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="User Group Deleted successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function destroy(UserGroupDeleteRequest $request)
    {
        try {
            $this->user_group_repository->delete($request);
            return response()->json(setResponse([], ['message' => __('operator.UserGroupController.delete')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/userGroups/changeStatus",
     *     tags={"Operator - User Groups"},
     *     summary="User Groups Change Status",
     *     description="User group Change Status",
     *     operationId="changeStatus",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="id",
     *                     description="id",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={"status":"Inactive",
                            "ids":{
                                "0":"1",
                                "1":"2"
                            }
                        }
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Status changed successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */

    public function changeStatus(UserGroupStatusRequest $request)
    {
        try {
            $this->user_group_repository->change_status($request);
            return response()->json(setResponse([], ['message' => __('operator.UserGroupController.status')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    /**
     * @OA\Post(
     *     path="/operator/userGroups/getCoursesList",
     *     tags={"Operator - User Groups"},
     *     summary="Get Courses list",
     *     description="Get Courses list",
     *     operationId="getCoursesList",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="User Group Name",
     *                     type="string"
     *                 ),
    *                 @OA\Property(
     *                     property="status",
     *                     description="User Group Status",
     *                     type="string",
                           enum={"Active", "Inactive"}
     *                 ),
     *                 example={
                        "per_page":10,
                        "sort_by":"createdBy/CourseName",
                        "order_by":"asc",
                        "search_key":"my course"
                        }
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Courses list genetated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */

    public function getCoursesList(Request $request)
    {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $perPage = isset($request->per_page) ? $request->per_page : 10;
            $courseTemp = TrainingCourse::join('master_users', 'master_users.id', '=', 'training_course.master_user_id')->select('training_course.id','training_course.title as CourseName','master_users.name as createdBy', 'training_course.updated_at as updated_at')
            ->where('training_course.master_user_id',$operatorId)
            ->where(['training_course.status'=>'Active','training_course.publish_now'=>1]);

            // search key filter
            if($request->has('search_key') && $request->search_key !=''){
                $courseTemp->where('title', 'like', '%' . $request->search_key . '%')
                ->orWhere('master_users.name', 'like', '%' . $request->search_key . '%');
            }

            //Sorting
            if($request->has('sort_by')){
                $sortBy=$request->input('sort_by');
                $orderBy=($request->input('order_by')=='asc')?'ASC':'DESC';
                if ($sortBy=='CourseName') {
                    $courseTemp->orderBy('training_course.title',$orderBy);
                }
                if ($sortBy=='createdBy') {
                    $courseTemp->orderBy('createdBy',$orderBy);
                }

            } else {
                $courseTemp =  $courseTemp->orderByDesc('id');
            }

            $courseTemp = $courseTemp->paginate($perPage);
            $message = (!empty($courseTemp) && count($courseTemp)>0) ? __('operator.UserGroupController.courseListFound') : __('operator.UserGroupController.courseListNotFound');
            // return response()->json(setResponse($courseTemp, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
            return new CustomCollection($courseTemp, 'App\Http\Resources\Operator\v1\UserGroupCourseListResource');
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
