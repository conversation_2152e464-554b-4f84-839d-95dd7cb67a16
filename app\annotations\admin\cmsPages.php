<?php
/**
     * @OA\Post(
     *     path="/admin/cmsPages",
     *     tags={"Admin - CMS Pages"},
     *     summary="Add CMS Page ",
     *     description="Add CMS Page ",
     *     operationId="store",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="template_title",
     *                     description="Title",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="email_subject",
     *                     description="Email subject",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="email_content",
     *                     description="Email Content",
     *                     type="string"
     *                 ),
     *                 example={"title": "", "slug": "","status": "Active", "body": ""}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="CMS Page Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */ 

/**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    /**
     * @OA\Get(
     *     path="/admin/cmsPages/{id}",
     *     tags={"Admin - CMS Pages"},
     *     summary="Get CMS Page Details",
     *     description="Get CMS Page Details",
     *     operationId="show",
     *     @OA\Parameter(
     *         description="id",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */

/**
     * @OA\Put(
     *     path="/admin/cmsPages/{id}",
     *     tags={"Admin - CMS Pages"},
     *     summary="Update CMS Page Details",
     *     description="Update CMS Page Details",
     *     operationId="update",
     *     @OA\Parameter(
     *         description="Id of Page to update",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="template_title",
     *                     description="Title",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="email_subject",
     *                     description="Email subject",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="email_content",
     *                     description="Email Content",
     *                     type="string"
     *                 ),
     *                 example={"title": "", "slug": "","status": "Active", "body": ""}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */ 