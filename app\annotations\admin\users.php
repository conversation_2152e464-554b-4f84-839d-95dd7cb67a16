<?php
/**
     * @OA\Post(
     *     path="/admin/users",
     *     tags={"Admin - Users"},
     *     summary="Add User",
     *     description="Add User",
     *     operationId="store",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="User Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="User Status",
     *                     type="string",
     *                      enum={"Active", "Inactive"}
     *                 ),
     *                 example={"name":"My Group",
     *                 "status":"Active",
     *                 "manager_email":"<EMAIL>",
     *                 "unique_id":"UID123"
     *               }
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="User Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */ 

/**
     * @OA\Put(
     *     path="/admin/users/{id}",
     *     tags={"Admin - Users"},
     *     summary="Update User details",
     *     description="Update User details",
     *     operationId="update",
     *     @OA\Parameter(
     *         description="Id of User to update",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="User Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="group Status",
     *                     type="string",
     *                      enum={"Active", "Inactive"}
     *                 ),
     *                 example={"name":"John",
     *                 "email":"<EMAIL>",
     *                 "manager_email":"<EMAIL>",
     *                 "unique_id":"UID123",
     *                 "status":"Active"
     *               }
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="User Updated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */ 