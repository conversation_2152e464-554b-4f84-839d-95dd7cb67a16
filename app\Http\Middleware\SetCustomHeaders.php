<?php

namespace App\Http\Middleware;

use Closure;

class SetCustomHeaders
{

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next, $guard = '', $extra = '')
    {
        // Prepare header data for the job
        $headerData = [
            'guard' => $guard,
            'user_id' => auth()->guard($guard)->user()->id ?? null,
            'extra' => $extra,
            'bundle-id' => $request->header('bundle-id'),
        ];

        // Create payload for the queue using the header data
        app('queue')->createPayloadUsing(function () use ($headerData) {
            return ['headerData' => $headerData];
        });

        // Update device token for the current app activities
        $headerBundleId = $request->header('bundle-id') ? strtolower($request->header('bundle-id')) : '';
        $bundleIds = array_change_key_case(config('constants.bundle_ids'), CASE_LOWER);

        if ($guard === 'api' && auth()->check() && array_key_exists($headerBundleId, $bundleIds)) {
            $deviceType = $request->header('device-type');
            $deviceToken = $request->header('device-token');
            if (!empty($deviceType) && !empty($deviceToken) && $deviceToken !== 'iOSSimulator') {
                $userId = auth()->user()->id;
                \App\Models\DeviceToken::updateOrCreate(
                    [
                        'user_id' => $userId,
                        'bundle_id' => $headerBundleId,
                    ],
                    [
                        'operator' => $bundleIds['app_name_' . ($bundleIds[$headerBundleId] ?? '')] ?? 'Skillsbase',
                        'device_type' => $deviceType,
                        'device_token' => $deviceToken,
                    ]
                );
            }
        }
        return $next($request);
    }

}
