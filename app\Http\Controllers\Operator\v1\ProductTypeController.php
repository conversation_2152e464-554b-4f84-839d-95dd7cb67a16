<?php

namespace App\Http\Controllers\Operator\v1;

use Illuminate\Http\Response;
use App\Models\ProductType;
use App\Models\Product;
use App\Http\Requests\Operator\v1\CommonListingRequest;
use App\Http\Requests\Operator\v1\ProductTypeRequest;
use App\Http\Requests\Operator\v1\ImportCSVFileRequest;
use App\Http\Requests\Operator\v1\CommonBulkStatusChangeRequest;
use App\Http\Resources\Operator\v1\ProductTypeResource;
use App\Repositories\Operator\v1\ProductTypeRepository;
use App\Http\Requests\Operator\v1\enableProductTypeGridViewRequest;

class ProductTypeController extends Controller {

    private $model;
    private $repository;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->model = new ProductType();
        $this->repository = new ProductTypeRepository($this->model);
    }

    /**
     * @OA\Post(
     *     path="/operator/productTypes/getListing",
     *     tags={"Operator - Product Types"},
     *     summary="Get Product Types List",
     *     description="Get Product Types List",
     *     operationId="getListing",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="isExport",
     *                     description="for export csv",
     *                     type="integer"
     *                 ),
     *                 example={"per_page": 10, "page": 1, "search_key": "", "sort_by": "name", "order_by": "asc", "filters": {"name": "", "created_at": "", "status": ""}, "isExport": 0}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Fetched successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getListing(CommonListingRequest $request) {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $productTypes = $this->repository->getListing($request->all());
            if ($request->isExport) {
                return $this->repository->exportCsv($productTypes->where('master_user_id', $operatorId)->get());
            }
            return ProductTypeResource::collection($productTypes->where('master_user_id', $operatorId)->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/productTypes",
     *     tags={"Operator - Product Types"},
     *     summary="Add Product Types ",
     *     description="Add Product Types ",
     *     operationId="store",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={"name": "Discrete Connectors", "status": "Active"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function store(ProductTypeRequest $request) {
        try {
            $data = $request->all();
            $productType = $this->repository->create($request->all());
            $response = new ProductTypeResource($productType);
            return response()->json(setResponse($response, ['message' => __('operator.product-type.created')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/productTypes/{id}",
     *     tags={"Operator - Product Types"},
     *     summary="Get Product Type Details",
     *     description="Get Product Type Details",
     *     operationId="show",
     *     @OA\Parameter(
     *         description="ID of Product type",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Fetched successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function show($id) {
        try {
            $productType = $this->model->whereAddedBy('Operator')->whereId($id)->first();
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $productTypeList = $this->model->where('master_user_id', $operatorId)->pluck('id')->toArray();
            if (in_array($id, $productTypeList)) {
                return ($productType) ?
                    (new ProductTypeResource($productType)) :
                    response()->json(setResponse([], ['message' => __('operator.product-type.not-found')]))->setStatusCode(Response::HTTP_NOT_FOUND);
            } else {
                return response()->json(setErrorResponse(__('You cannot access this record')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Put(
     *     path="/operator/productTypes/{id}",
     *     tags={"Operator - Product Types"},
     *     summary="Update Product Types Details",
     *     description="Update Product Types Details",
     *     operationId="update",
     *     @OA\Parameter(
     *         description="ID of Product type to Update",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="integer"
     *         )
     *     ),
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={"name": "Discrete Connectors", "status": "Active"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Updated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function update(ProductTypeRequest $request, $id) {
        try {
            $productType = $this->model->find($id);

            if ($productType) {
                $this->repository->update($request->all(), $productType);
                return response()->json(setResponse([], ['message' => __('operator.product-type.updated')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setResponse([], ['message' => __('operator.product-type.not-found')]))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Delete(
     *     path="/operator/productTypes/{id}",
     *     tags={"Operator - Product Types"},
     *     summary="Delete Product Types",
     *     description="Delete Product Types",
     *     operationId="delete",
     *     @OA\Parameter(
     *         description="ID of Product type to Delete",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Deleted successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function destroy($id) {
        try {
            $productType = $this->model->find($id);
            if ($productType) {
                Product::where('product_type_id',$id)->whereNull('deleted_at')->update(['product_type_id'=>null]);
                $productType->delete();
                $allNextProductTypes = ProductType::whereMasterUserId($productType->master_user_id)->where('display_order', '>', $productType->display_order)->orderBy('display_order')->get();
                if (!$allNextProductTypes->isEmpty()) {
                    foreach ($allNextProductTypes as $key => $productTypeData) {
                        $productTypeData->display_order = (!is_null($productTypeData->display_order) ? ($productTypeData->display_order - 1) : 0);
                        $productTypeData->save();
                    }
                }
                return response()->json(setResponse([], ['message' => __('operator.product-type.deleted')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setResponse([], ['message' => __('operator.product-type.not-found')]))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/productTypes/changeStatus",
     *     tags={"Operator - Product Types"},
     *     summary="Change Product Type(s) Status",
     *     description="Change Product Type(s) Status",
     *     operationId="changeStatus",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="ids",
     *                     description="ids",
     *                     type="array",
     *                     @OA\Items()
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={"ids": {"1","2"}, "status": "Active"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Updated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function changeStatus(CommonBulkStatusChangeRequest $request) {
        try {
            $this->model->whereIn('id', $request->ids)->whereAddedBy('Operator')->update(['status' => $request->status]);
            return response()->json(setResponse([], ['message' => __('operator.product-type.status-updated')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/productTypes/importProductTypes",
     *     tags={"Operator - Product Types"},
     *     summary="Import Product Types",
     *     description="Import Product Types",
     *     operationId="importProductTypes",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="file",
     *                     description="CSV file of Users",
     *                     type="file"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function importProductTypes(ImportCSVFileRequest $request) {
        try {
            $isImported = $this->repository->importProductTypes($request);
            if ($isImported) {
                return response()->json(setResponse([], ['message' => __('operator.product-type.import-success')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setErrorResponse(__('operator.product-type.import-error')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/productTypes/enableGridView",
     *     tags={"Operator - Product Types"},
     *     summary="Enable/Disable Grid View",
     *     description="Enable/Disable Grid View",
     *     operationId="enableGridView",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="set_product_type_grid_view",
     *                     description="Enable/Disable Grid View",
     *                     type="boolean",
     *                     enum={true, false}
     *                 ),
     *                 example={"enable_grid": true}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Updated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function enableGridView(enableProductTypeGridViewRequest $request) {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            \App\Models\MasterUser::where('id', $operatorId)->update(['set_product_type_grid_view' => $request->enable_grid]);
            $message = ($request->enable_grid == true) ? 'operator.product-type.enable-grid-view' : 'operator.product-type.disable-grid-view';
            return response()->json(setResponse([], ['message' => __($message)]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function getGridView() {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $operatorData = \App\Models\MasterUser::where('id', $operatorId)->first();
            $data['data']['enable_grid'] = $operatorData->set_product_type_grid_view;
            return $data;
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/productTypes/getDisplayOrder",
     *     tags={"Operator - Product Types"},
     *     summary="Get display order",
     *     description="Get display order",
     *     operationId="getDisplayOrder",
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getDisplayOrder() {
        try {
            $productTypes = [];
            $defaultOrder = [
                'id' => 0,
                'name' => 'New Product Type',
                'display_order_no' => 1,
                'display_order' => 1,
                'image' => url('/images/default.png'),
            ];

            $existingProductTypes = ProductType::select('id', 'name', 'display_order', 'image')->operator()->orderBy('display_order')->get();

            foreach ($existingProductTypes as $productType) {
                $productTypes[] = [
                    'id' => $productType->id,
                    'name' => $productType->name,
                    'display_order_no' => $productType->display_order,
                    'display_order' => $productType->id,
                    'image' => $productType->image_url
                ];
            }

            if (count($productTypes) > 0) {
                $lastRecord = end($productTypes);
                $defaultOrder['display_order_no'] = $lastRecord['display_order_no'] + 1;
                $defaultOrder['display_order'] = $lastRecord['display_order'] + 1;
            }
            return array_merge($productTypes, [$defaultOrder]);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
