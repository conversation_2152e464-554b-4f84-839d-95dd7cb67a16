<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class CustomDeeplink extends Model
{
    protected $fillable = [
        'short_code',
        'operator_id',
        'target_url',
        'deeplink_type',
        'entity_id',
        'entity_type',
        'ios_package_name',
        'android_package_name',
        'ios_app_store_id',
        'fallback_url',
        'click_count',
        'last_clicked_at',
        'is_active',
        'expires_at',
        'metadata',
        'universal_link',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'expires_at' => 'datetime',
        'last_clicked_at' => 'datetime',
        'metadata' => 'array',
        'click_count' => 'integer',
    ];

    /**
     * Get the operator that owns the deeplink.
     */
    public function operator(): BelongsTo
    {
        return $this->belongsTo(MasterUser::class, 'operator_id');
    }

    /**
     * Get the entity that the deeplink points to.
     */
    public function entity(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope a query to only include active deeplinks.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where(function ($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                    });
    }

    /**
     * Scope a query to only include deeplinks for a specific operator.
     */
    public function scopeByOperator($query, $operatorId)
    {
        return $query->where('operator_id', $operatorId);
    }

    /**
     * Scope a query to only include deeplinks of a specific type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('deeplink_type', $type);
    }

    /**
     * Scope a query to only include deeplinks for a specific entity.
     */
    public function scopeByEntity($query, $entityType, $entityId)
    {
        return $query->where('entity_type', $entityType)
                    ->where('entity_id', $entityId);
    }

    /**
     * Get the full deeplink URL.
     */
    public function getFullUrlAttribute(): string
    {
        $domain = $this->operator && $this->operator->whiteLabelSettings && $this->operator->whiteLabelSettings->custom_deeplink_domain
            ? $this->operator->whiteLabelSettings->custom_deeplink_domain
            : config('app.url');

        return rtrim($domain, '/') . '/dl/' . $this->short_code;
    }

    /**
     * Check if the deeplink is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Increment the click count.
     */
    public function incrementClickCount(array $metadata = []): void
    {
        $this->increment('click_count');
        $this->update([
            'last_clicked_at' => now(),
            'metadata' => array_merge($this->metadata ?? [], [
                'last_click' => array_merge($metadata, ['timestamp' => now()->toISOString()])
            ])
        ]);
    }
}
