<?php

namespace App\Http\Controllers\Operator\v1;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Http\Requests\Operator\v1\AnalyticsAPIRequest;
use App\Http\Resources\Operator\v1\FrontAnalyticsUserResource;
use App\Http\Resources\Operator\v1\AnalyticsQuizDataResource;
use App\Http\Resources\Operator\v1\AnalyticsCourseDataResource;
use App\Http\Resources\Operator\v1\TrainingCourseJobsResource;
use App\Models\User;
use App\Repositories\Operator\v1\FrontUserRepository;
use App\Repositories\Operator\v1\TrainingCourseSubModuleQuizResultRepository;
use App\Repositories\Operator\v1\TrainingCourseProgressRepository;
use App\Repositories\Operator\v1\TrainingCourseJobProgressRepository;
use App\Models\TrainingCourseSubModuleQuizResults;
use App\Models\TrainingCourseProgress;
use App\Models\TrainingCourseSubmoduleJobProgress;
use Illuminate\Database\Eloquent\Builder;
use DBTableNames;
use DB;

class AnalyticsController extends Controller
{

    /*
    |---------------------------------------------------------------------------
    | Analytics Controller
    |--------------------------------------------------------------------------
    |
    */
    public function __construct()
    {
        $this->model = new User();
        $this->user_repository = new FrontUserRepository($this->model);
        $this->quizResultModel = new TrainingCourseSubModuleQuizResults();
        $this->quizResultRepository = new TrainingCourseSubModuleQuizResultRepository($this->quizResultModel);
        $this->trainingCourseProgressModel = new TrainingCourseProgress();
        $this->trainingCourseProgressRepository = new TrainingCourseProgressRepository($this->trainingCourseProgressModel);
        $this->trainingCourseSubmoduleJobProgress = new TrainingCourseSubmoduleJobProgress();
        $this->trainingCourseJobProgressRepository = new TrainingCourseJobProgressRepository($this->trainingCourseSubmoduleJobProgress);
    }

    /**
     * @OA\Get(
     *     path="/operator/analytics/newUsers",
     *     tags={"Operator - Analytics API"},
     *     summary="Users List",
     *     description="Users List",
     *     operationId="newUsers",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="endDate",
     *                     description="End Date",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="startDate",
     *                     description="Start Date",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="operatorID",
     *                     description="Operator ID",
     *                     type="integer"
     *                 ),
     *                 example={"startDate": "2023-03-22 00:00:00", "endDate": "2023-03-22 00:00:00", "operatorID" : 5, "per_page": 10, "page": 1}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function newUsers(AnalyticsAPIRequest $request)
    {
        try {
            $executionTime = microtime(true) - LUMEN_START;
            $perPage = isset($request->per_page) ? $request->per_page : 10;
            $users = $this->userRecords($request->all());
            JsonRequestSubmit($request->operatorID,$request->all(),$status=200,$users->paginate($perPage),$error=null,$executionTime);
            return FrontAnalyticsUserResource::collection($users->paginate($perPage));
        } catch (\Exception $e) {
            $executionTime = microtime(true) - LUMEN_START;
            JsonRequestSubmit($request->operatorID,$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$users=null,$e->getMessage(),$executionTime);
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get users record - Common function
     */
    public function userRecords($data)
    {
        $users = $this->user_repository->getAnalyticsUsersListing($data);
        return $users;
    }

    /**
     * @OA\Get(
     *     path="/operator/analytics/totalUsers",
     *     tags={"Operator - Analytics API"},
     *     summary="Total Users List of Operator",
     *     description="Total Users List of Operator",
     *     operationId="totalUsers",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="operatorID",
     *                     description="Operator ID",
     *                     type="integer"
     *                 ),
     *                 example={"operatorID" : 5}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function totalUsers(Request $request)
    {
        try {
            $executionTime = microtime(true) - LUMEN_START;
            $users = $this->userRecords($request->all())->get()->count();
            JsonRequestSubmit($request->operatorID,$request->all(),$status=200,["totalUsers" => $users],$error=null,$executionTime);
            return response()->json(["totalUsers" => $users])->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            $executionTime = microtime(true) - LUMEN_START;
            JsonRequestSubmit($request->operatorID,$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$users=null,$e->getMessage(),$executionTime);
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/analytics/courseData",
     *     tags={"Operator - Analytics API"},
     *     summary="Course Data List",
     *     description="Course Data List",
     *     operationId="courseData",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="endDate",
     *                     description="End Date",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="startDate",
     *                     description="Start Date",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="operatorID",
     *                     description="Operator ID",
     *                     type="integer"
     *                 ),
     *                 example={"startDate": "2023-03-22 00:00:00", "endDate": "2023-03-22 00:00:00", "operatorID" : 5, "per_page": 10, "page": 1}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function courseData(AnalyticsAPIRequest $request)
    {
        try {
            $executionTime = microtime(true) - LUMEN_START;
            $perPage = isset($request->per_page) ? $request->per_page : 10;
            $results = $this->trainingCourseProgressRepository->gettrainingCourseProgressListing($request->all());
            JsonRequestSubmit($request->operatorID,$request->all(),$status=200,$results->paginate($perPage),$error=null,$executionTime);
            return AnalyticsCourseDataResource::collection($results->paginate($perPage));
        } catch (\Exception $e) {
            $executionTime = microtime(true) - LUMEN_START;
            JsonRequestSubmit($request->operatorID,$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$results=null,$e->getMessage(),$executionTime);
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/analytics/quizData",
     *     tags={"Operator - Analytics API"},
     *     summary="Quiz Data",
     *     description="Quiz Data",
     *     operationId="quizData",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="endDate",
     *                     description="End Date",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="startDate",
     *                     description="Start Date",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="operatorID",
     *                     description="Operator ID",
     *                     type="integer"
     *                 ),
     *                 example={"startDate": "2023-03-22 00:00:00", "endDate": "2023-03-22 00:00:00", "operatorID" : 5, "per_page": 10, "page": 1}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function quizData(AnalyticsAPIRequest $request)
    {
        try {
            $executionTime = microtime(true) - LUMEN_START;
            $perPage = isset($request->per_page) ? $request->per_page : 10;
            $results = $this->quizResultRepository->getAnalyticsQuizResultsListing($request->all());
            JsonRequestSubmit($request->operatorID,$request->all(),$status=200,$results->paginate($perPage),$error=null,$executionTime);
            return AnalyticsQuizDataResource::collection($results->paginate($perPage));   
        } catch (\Exception $e) {
            $executionTime = microtime(true) - LUMEN_START;
            JsonRequestSubmit($request->operatorID,$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$results=null,$e->getMessage(),$executionTime);
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/analytics/jobData",
     *     tags={"Operator - Analytics API"},
     *     summary="Course Data List",
     *     description="Course Job Data List",
     *     operationId="jobData",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="endDate",
     *                     description="End Date",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="startDate",
     *                     description="Start Date",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="operatorID",
     *                     description="Operator ID",
     *                     type="integer"
     *                 ),
     *                 example={"startDate": "2023-03-22 00:00:00", "endDate": "2023-03-22 00:00:00", "operatorID" : 5, "per_page": 10, "page": 1}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function jobData(AnalyticsAPIRequest $request)
    {
        try {
            $executionTime = microtime(true) - LUMEN_START;
            $perPage = isset($request->per_page) ? $request->per_page : 10;
            $results = $this->trainingCourseJobProgressRepository->gettrainingCourseJobProgressListing($request->all());
            JsonRequestSubmit($request->operatorID,$request->all(),$status=200,$results->paginate($perPage),$error=null,$executionTime);
            return TrainingCourseJobsResource::collection($results->paginate($perPage));
        } catch (\Exception $e) {
            $executionTime = microtime(true) - LUMEN_START;
            JsonRequestSubmit($request->operatorID,$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$results=null,$e->getMessage(),$executionTime);
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
