<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;

class DeletedCourseRestoreRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array
    {
        return [
            'per_page' => 'numeric',
            'page' => 'numeric',
            'order_by' => "in:ASC,DESC,asc,desc",
            'filters' => 'array',
            'operator_id' => 'required'
        ];
    }
    
    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'per_page.numeric' => __('Please enter valid per page limit'),
            'page.numeric' => __('Please enter valid page number'),
            'order_by.in' => __('Please enter valid sort order'),
            'operator_id.required' => __('OPerator Id required.'),
        ];
    }
    
}
