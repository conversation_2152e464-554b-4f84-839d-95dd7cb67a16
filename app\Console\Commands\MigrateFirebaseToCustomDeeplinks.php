<?php

namespace App\Console\Commands;

use App\Models\CustomDeeplink;
use App\Services\DeeplinkService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MigrateFirebaseToCustomDeeplinks extends Command
{
    protected $signature = 'deeplinks:migrate-firebase {--batch-size=100} {--dry-run} {--table=} {--operator-id=}';
    protected $description = 'Migrate existing Firebase deeplinks to custom deeplinks';

    private DeeplinkService $deeplinkService;

    public function __construct(DeeplinkService $deeplinkService)
    {
        parent::__construct();
        $this->deeplinkService = $deeplinkService;
    }

    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        $batchSize = (int) $this->option('batch-size');
        $specificTable = $this->option('table');
        $operatorId = $this->option('operator-id');

        $this->info('Starting Firebase to Custom Deeplinks migration...');
        
        if ($isDryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        $tables = $this->getDeeplinkTables();

        // Filter to specific table if provided
        if ($specificTable) {
            $tables = array_filter($tables, function($key) use ($specificTable) {
                return $key === $specificTable;
            }, ARRAY_FILTER_USE_KEY);
            
            if (empty($tables)) {
                $this->error("Table '{$specificTable}' not found in deeplink tables list");
                return 1;
            }
        }

        foreach ($tables as $tableName => $config) {
            $this->migrateTable($tableName, $config, $batchSize, $isDryRun, $operatorId);
        }

        $this->info('Migration completed successfully!');
        return 0;
    }

    private function getDeeplinkTables(): array
    {
        return [
            'training_course_whitelabel_deeplink' => [
                'type' => 'training_course',
                'entity_type' => 'TrainingCourse',
                'entity_id_field' => 'training_course_id'
            ],
            'products_whitelabel_deeplink' => [
                'type' => 'product',
                'entity_type' => 'Product', 
                'entity_id_field' => 'product_id'
            ],
            'resources_whitelabel_deeplink' => [
                'type' => 'resource',
                'entity_type' => 'Resource',
                'entity_id_field' => 'resource_id'
            ],
            'training_course_submodule_details_whitelabel_deeplink' => [
                'type' => 'training_course_submodule',
                'entity_type' => 'TrainingCourseSubmoduleDetails',
                'entity_id_field' => 'training_course_submodule_details_id'
            ],
            'training_course_submodule_happy_unhappy_whitelabel_deeplink' => [
                'type' => 'training_course_submodule_happy_unhappy',
                'entity_type' => 'TrainingCourseSubModuleHappyUnhappy',
                'entity_id_field' => 'submodule_id'
            ],
            'training_course_submodule_video_guide_whitelabel_deeplink' => [
                'type' => 'training_course_submodule_video_guide',
                'entity_type' => 'TrainingCourseSubmoduleDetails',
                'entity_id_field' => 'submodule_id'
            ],
            'training_course_submodule_image_gallery_whitelabel_deeplink' => [
                'type' => 'training_course_submodule_image_gallery',
                'entity_type' => 'TrainingCourseSubmoduleDetails',
                'entity_id_field' => 'submodule_id'
            ],
            'training_course_submodule_product_list_whitelabel_deeplink' => [
                'type' => 'training_course_submodule_product_list',
                'entity_type' => 'TrainingCourseSubmoduleDetails',
                'entity_id_field' => 'submodule_id'
            ]
        ];
    }

    private function migrateTable(string $tableName, array $config, int $batchSize, bool $isDryRun, ?string $operatorFilter = null)
    {
        $this->info("Processing table: {$tableName}");
        
        $query = DB::table($tableName)->orderBy('id');
        
        if ($operatorFilter) {
            $query->where('operator_id', $operatorFilter);
        }
        
        $totalRecords = $query->count();
        $this->info("Total records: {$totalRecords}");

        if ($totalRecords === 0) {
            $this->warn("No records found in {$tableName}");
            return;
        }

        $bar = $this->output->createProgressBar($totalRecords);
        $processed = 0;
        $successful = 0;
        $failed = 0;

        $query->chunk($batchSize, function ($records) use ($config, $isDryRun, &$processed, &$successful, &$failed, $bar, $tableName) {
            foreach ($records as $record) {
                if (!$isDryRun) {
                    $result = $this->createCustomDeeplink($record, $config, $tableName);
                    if ($result) {
                        $successful++;
                    } else {
                        $failed++;
                    }
                }
                $processed++;
                $bar->advance();
            }
        });

        $bar->finish();
        $this->newLine();
        $this->info("Processed {$processed} records from {$tableName}");
        
        if (!$isDryRun) {
            $this->info("Successful: {$successful}, Failed: {$failed}");
        }
    }

    private function createCustomDeeplink($record, array $config, string $tableName): bool
    {
        try {
            $targetUrl = $this->extractTargetUrl($record->share_url);
            
            if (!$targetUrl) {
                $this->warn("Could not extract target URL from: {$record->share_url}");
                return false;
            }

            $customDeeplinkUrl = $this->deeplinkService->generateDeeplink([
                'target_url' => $targetUrl,
                'operator_id' => $record->operator_id,
                'type' => $config['type'],
                'entity_id' => $record->{$config['entity_id_field']} ?? null,
                'entity_type' => $config['entity_type'],
                'metadata' => [
                    'migrated_from' => 'firebase',
                    'original_url' => $record->share_url,
                    'migration_date' => now()->toISOString(),
                    'table_name' => $tableName
                ]
            ]);

            // Update the original record with new custom deeplink
            DB::table($tableName)
                ->where('id', $record->id)
                ->update([
                    'share_url' => $customDeeplinkUrl,
                    'updated_at' => now()
                ]);

            return true;

        } catch (\Exception $e) {
            $this->error("Failed to migrate record ID {$record->id}: " . $e->getMessage());
            
            Log::error('Deeplink migration failed', [
                'table' => $tableName,
                'record_id' => $record->id,
                'original_url' => $record->share_url ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    private function extractTargetUrl(string $firebaseUrl): ?string
    {
        // Try to extract the actual target URL from Firebase dynamic link
        
        // Method 1: Look for 'link=' parameter in the URL
        if (preg_match('/[?&]link=([^&]+)/', $firebaseUrl, $matches)) {
            return urldecode($matches[1]);
        }
        
        // Method 2: If it's a Firebase page.link URL, try to resolve it
        if (strpos($firebaseUrl, '.page.link') !== false) {
            // For Firebase URLs, we might need to make an HTTP request to get the redirect
            // For now, we'll try to parse common patterns
            
            // Extract domain and path
            $parsed = parse_url($firebaseUrl);
            if (!$parsed) {
                return null;
            }
            
            // Try to find the actual URL in the path or query
            if (isset($parsed['query'])) {
                parse_str($parsed['query'], $queryParams);
                if (isset($queryParams['link'])) {
                    return urldecode($queryParams['link']);
                }
            }
        }
        
        // Method 3: If it looks like a direct URL, return as-is
        if (filter_var($firebaseUrl, FILTER_VALIDATE_URL)) {
            // Check if it's already a direct URL to our domain
            $parsed = parse_url($firebaseUrl);
            $appDomain = parse_url(config('app.url'), PHP_URL_HOST);
            
            if (isset($parsed['host']) && $parsed['host'] === $appDomain) {
                return $firebaseUrl;
            }
        }
        
        return null;
    }
}
