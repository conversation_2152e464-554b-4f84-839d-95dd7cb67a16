<?php

namespace App\Services;

use App\Models\CableMatrixData;
use App\Models\TrainingCourseSubModulePracticalAssessmentQuestion;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CableMatrixService
{
    /**
     * Process uploaded CSV file and store cable matrix data
     */
    public function processCsvFile($questionId, $filePath)
    {
        try {
            DB::beginTransaction();

            $question = TrainingCourseSubModulePracticalAssessmentQuestion::findOrFail($questionId);
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;

            // Clear existing data for this question
            CableMatrixData::where('question_id', $questionId)
                ->where('master_user_id', $operatorId)
                ->delete();

            // Read and parse CSV
            $csvData = $this->parseCsvFromS3($filePath);

            // Validate CSV structure
            $validation = $this->validateCsvStructure($csvData);
            if (!$validation['valid']) {
                throw new \Exception('Invalid CSV structure: ' . implode(', ', $validation['errors']));
            }

            // Process and store data
            $result = $this->storeCsvData($csvData, $questionId, $operatorId);

            DB::commit();

            return [
                'success' => true,
                'data' => $result
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Cable Matrix CSV Processing Error: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Process CSV file from file path (used during question creation)
     */
    public function processCsvFromFilePath($questionId, $filePath, $operatorId = null)
    {
        try {
            Log::info('Cable Matrix: Starting CSV processing for question ' . $questionId . ' with file path: ' . $filePath);

            DB::beginTransaction();

            // Get operator ID from parameter or auth
            if (!$operatorId) {
                $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            }
            Log::info('Cable Matrix: Operator ID: ' . $operatorId);

            // Clear existing data for this question
            $deletedCount = CableMatrixData::where('question_id', $questionId)
                ->where('master_user_id', $operatorId)
                ->delete();
            Log::info('Cable Matrix: Deleted ' . $deletedCount . ' existing records');

            // Use the file path directly since it's already a full URL from frontend
            $csvData = $this->parseCsvFromS3($filePath);

            // Validate CSV structure
            $validation = $this->validateCsvStructure($csvData);
            if (!$validation['valid']) {
                throw new \Exception('Invalid CSV structure: ' . implode(', ', $validation['errors']));
            }

            // Process and store data
            $result = $this->storeCsvData($csvData, $questionId, $operatorId);
            Log::info('Cable Matrix: Successfully stored ' . $result['processed_rows'] . ' rows');

            DB::commit();

            return [
                'success' => true,
                'data' => $result
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Cable Matrix CSV Processing Error: ' . $e->getMessage());
            Log::error('Cable Matrix CSV Processing Stack Trace: ' . $e->getTraceAsString());

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Extract S3 path from full URL
     */
    private function extractS3PathFromUrl($filePath)
    {
        // If it's already a relative path, return as is
        if (strpos($filePath, 'http') !== 0) {
            return $filePath;
        }

        // Extract path from full S3 URL
        $bucketName = env('AWS_BUCKET');
        $region = env('AWS_DEFAULT_REGION');
        $s3BaseUrl = "https://s3-{$region}.amazonaws.com/{$bucketName}/";

        if (strpos($filePath, $s3BaseUrl) === 0) {
            return substr($filePath, strlen($s3BaseUrl));
        }

        // Alternative CDN URL format
        $cdnUrl = env('CDN_URL');
        if ($cdnUrl && strpos($filePath, $cdnUrl) === 0) {
            return substr($filePath, strlen($cdnUrl));
        }

        return $filePath;
    }

    /**
     * Parse CSV from S3 file path
     */
    private function parseCsvFromS3($filePath)
    {
        // If filePath is already a full URL, use it directly
        if (strpos($filePath, 'http') === 0) {
            $fullUrl = $filePath;
        } else {
            // Otherwise, construct the full URL
            $fullUrl = env('CDN_URL') . $filePath;
        }

        Log::info('Cable Matrix: Attempting to read CSV from URL: ' . $fullUrl);

        $csvContent = file_get_contents($fullUrl);

        if ($csvContent === false) {
            throw new \Exception('Unable to read CSV file from S3: ' . $fullUrl);
        }

        $lines = str_getcsv($csvContent, "\n");
        $csvData = [];
        $headers = null;

        foreach ($lines as $line) {
            $row = str_getcsv($line);

            if ($headers === null) {
                $headers = $row;
            } else {
                if (count($row) === count($headers)) {
                    $csvData[] = array_combine($headers, $row);
                }
            }
        }

        Log::info('Cable Matrix: Successfully parsed CSV with ' . count($csvData) . ' rows');
        return $csvData;
    }

    /**
     * Validate CSV structure
     */
    private function validateCsvStructure($csvData)
    {
        $requiredHeaders = ['Cable Diameter (mm)', 'Cable Type', 'Cable Description'];
        $errors = [];

        if (empty($csvData)) {
            $errors[] = 'CSV file is empty or invalid';
            return ['valid' => false, 'errors' => $errors];
        }

        $headers = array_keys($csvData[0]);
        $missingHeaders = array_diff($requiredHeaders, $headers);

        if (!empty($missingHeaders)) {
            $errors[] = 'Missing required headers: ' . implode(', ', $missingHeaders);
        }

        // Validate data rows and check for duplicates
        $validRows = 0;
        $descriptions = [];
        $duplicates = [];
        
        foreach ($csvData as $index => $row) {
            $description = trim($row['Cable Description'] ?? '');
            
            if (!empty($description)) {
                if (in_array($description, $descriptions)) {
                    $duplicates[] = $description;
                } else {
                    //$descriptions[] = $description;
                    $validRows++;
                }
            }
        }

        if ($validRows === 0) {
            $errors[] = 'No valid data rows found with Cable Description';
        }

        if (!empty($duplicates)) {
            $errors[] = 'Duplicate Cable Descriptions found: ' . implode(', ', array_unique($duplicates));
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'total_rows' => count($csvData),
            'valid_rows' => $validRows,
            'duplicates' => $duplicates
        ];
    }

    /**
     * Store CSV data in database
     */
    private function storeCsvData($csvData, $questionId, $operatorId)
    {
        $processedRows = 0;
        $skippedRows = 0;

        Log::info('Cable Matrix: Starting to store ' . count($csvData) . ' CSV rows');

        foreach ($csvData as $rowIndex => $row) {
            // Skip rows without cable description or with blank description
            $description = trim($row['Cable Description'] ?? '');
            // if (empty($description)) {
            //     $skippedRows++;
            //     Log::debug('Cable Matrix: Skipping row ' . $rowIndex . ' - empty description');
            //     continue;
            // }

            // Prepare additional data (exclude main columns)
            $additionalData = array_diff_key($row, array_flip([
                'Cable Diameter (mm)', 'Cable Type', 'Cable Description'
            ]));

            // Clean diameter value
            $diameter = $this->cleanDiameterValue($row['Cable Diameter (mm)'] ?? '');

            try {
                CableMatrixData::where('question_id', $questionId)
                    ->where('master_user_id', $operatorId)                    
                    ->delete();
                $cableData = CableMatrixData::create([
                    'question_id' => $questionId,
                    'master_user_id' => $operatorId,
                    'cable_diameter' => $diameter,
                    'cable_type' => trim($row['Cable Type'] ?? ''),
                    'cable_description' => $description,
                    'additional_data' => $additionalData
                ]);

                $processedRows++;

                if ($processedRows <= 3) {
                    Log::info('Cable Matrix: Created record ' . $cableData->id . ' - ' . $description);
                }
            } catch (\Exception $e) {
                Log::error('Cable Matrix: Failed to create record for row ' . $rowIndex . ': ' . $e->getMessage());
                $skippedRows++;
            }
        }

        Log::info('Cable Matrix: Finished storing data - Processed: ' . $processedRows . ', Skipped: ' . $skippedRows);

        return [
            'total_rows' => count($csvData),
            'processed_rows' => $processedRows,
            'skipped_rows' => $skippedRows,
            'unique_diameters' => CableMatrixData::getDistinctDiameters($questionId, $operatorId)->count(),
            'unique_types' => CableMatrixData::getDistinctTypes($questionId, $operatorId)->count()
        ];
    }

    /**
     * Clean diameter value for consistent filtering
     */
    private function cleanDiameterValue($diameter)
    {
        $diameter = trim($diameter);

        // Handle special cases
        if (in_array(strtolower($diameter), ['', 'null'])) {
            return null;
        }

        // Extract numeric value if it contains units
        // if (preg_match('/(\d+\.?\d*)/', $diameter, $matches)) {
        //     return $matches[1];
        // }

        return $diameter;
    }

    /**
     * Get hierarchical filter options
     */
    public function getFilterOptions($questionId, $diameter = null, $type = null)
    {
        $question = TrainingCourseSubModulePracticalAssessmentQuestion::findOrFail($questionId);
        $masterUserId = $question->subModule->trainingCourse->master_user_id;

        $result = [];
        $result['diameters'] = CableMatrixData::getDistinctDiameters($questionId, $masterUserId);
        if (!$diameter) {
            // Return diameter options
            $result['diameters'] = CableMatrixData::getDistinctDiameters($questionId, $masterUserId);
        } elseif (!$type) {
            // Return type options for selected diameter
            $result['types'] = CableMatrixData::getDistinctTypes($questionId, $masterUserId, $diameter);
        } else {
            // Return descriptions for selected diameter and type with pagination
            $result['descriptions'] = CableMatrixData::getDescriptions($questionId, $masterUserId, $diameter, $type, 1, 20);
        }

        return $result;
    }

    /**
     * Search cable matrix data with pagination
     */
    public function searchCableMatrix($questionId, $searchTerm, $page = 1, $perPage = 20)
    {
        $question = TrainingCourseSubModulePracticalAssessmentQuestion::findOrFail($questionId);
        $masterUserId = $question->subModule->trainingCourse->master_user_id;

        return CableMatrixData::searchPaginated($questionId, $masterUserId, $searchTerm, $page, $perPage);
    }

    /**
     * Validate CSV file during upload (without question_id)
     */
    public function validateCsvFile($filePath)
    {
        try {
            // Read and parse CSV
            $csvData = $this->parseCsvFromS3($filePath);

            // Validate CSV structure
            $validation = $this->validateCsvStructure($csvData);

            if ($validation['valid']) {
                return [
                    'success' => true,
                    'data' => [
                        'total_rows' => $validation['total_rows'],
                        'valid_rows' => $validation['valid_rows'],
                        'headers' => array_keys($csvData[0] ?? []),
                        'sample_data' => array_slice($csvData, 0, 3) // First 3 rows as sample
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => implode(', ', $validation['errors'])
                ];
            }
        } catch (\Exception $e) {
            Log::error('Cable Matrix CSV Validation Error: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Failed to validate CSV file: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get diameters with optional search and distinct filtering
     */
    public function getDiameters($questionId, $search = null, $distinct = true)
    {
        $question = TrainingCourseSubModulePracticalAssessmentQuestion::findOrFail($questionId);
        $masterUserId = $question->subModule->trainingCourse->master_user_id;

        $query = CableMatrixData::where('question_id', $questionId)
            ->where('master_user_id', $masterUserId)
            ->whereNotNull('cable_diameter')
            ->where('cable_diameter', '!=', '');

        // Apply search filter
        if ($search) {
            $query->where('cable_diameter', 'LIKE', '%' . $search . '%');
        }

        // Apply distinct filter
        if ($distinct) {
            $query->distinct();
        }

        $diameters = $query->orderBy('cable_diameter', 'asc')
            ->pluck('cable_diameter')
            ->values()
            ->toArray();

        return [
            'diameters' => $diameters,
            'count' => count($diameters)
        ];
    }

    /**
     * Get types with optional diameter filter, search and distinct filtering
     */
    public function getTypes($questionId, $diameter = null, $search = null, $distinct = true)
    {
        $question = TrainingCourseSubModulePracticalAssessmentQuestion::findOrFail($questionId);
        $masterUserId = $question->subModule->trainingCourse->master_user_id;

        $query = CableMatrixData::where('question_id', $questionId)
            ->where('master_user_id', $masterUserId)
            ->whereNotNull('cable_type')
            ->where('cable_type', '!=', '');

        // Apply diameter filter
        if ($diameter) {
            $query->where('cable_diameter', $diameter);
        }

        // Apply search filter
        if ($search) {
            $query->where('cable_type', 'LIKE', '%' . $search . '%');
        }

        // Apply distinct filter
        if ($distinct) {
            $query->distinct();
        }

        $types = $query->orderBy('cable_type', 'asc')
            ->pluck('cable_type')
            ->values()
            ->toArray();

        return [
            'types' => $types,
            'count' => count($types),
            'filters' => [
                'diameter' => $diameter
            ]
        ];
    }

    /**
     * Get descriptions with optional filters, search, pagination and distinct filtering
     */
    public function getDescriptions($questionId, $diameter = null, $type = null, $search = null, $page = 1, $pageSize = 20, $distinct = true)
    {
        $question = TrainingCourseSubModulePracticalAssessmentQuestion::findOrFail($questionId);
        $masterUserId = $question->subModule->trainingCourse->master_user_id;

        $query = CableMatrixData::where('question_id', $questionId)
            ->where('master_user_id', $masterUserId)
            ->whereNotNull('cable_description')
            ->where('cable_description', '!=', '');

        // Apply diameter filter
        if ($diameter) {
            $query->where('cable_diameter', $diameter);
        }

        // Apply type filter
        if ($type) {
            $query->where('cable_type', $type);
        }

        // Apply search filter across multiple fields
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('cable_description', 'LIKE', '%' . $search . '%')
                  ->orWhere('cable_diameter', 'LIKE', '%' . $search . '%')
                  ->orWhere('cable_type', 'LIKE', '%' . $search . '%')
                  ->orWhereRaw('JSON_SEARCH(additional_data, "all", ?) IS NOT NULL', ['%' . $search . '%']);
            });
        }

        // Apply distinct filter
        if ($distinct) {
            $query->distinct();
            $query->select('cable_description', 'cable_diameter', 'cable_type', 'additional_data');
        }

        // Get total count before pagination
        $totalCount = $query->count();

        // Apply pagination
        $offset = ($page - 1) * $pageSize;
        $results = $query->orderBy('cable_description', 'asc')
            ->offset($offset)
            ->limit($pageSize)
            ->get();

        // Format results
        $descriptions = $results->map(function($item) {
            return [
                'cable_description' => $item->cable_description,
                'cable_diameter' => $item->cable_diameter,
                'cable_type' => $item->cable_type,
                'additional_data' => $item->additional_data
            ];
        })->toArray();

        return [
            'descriptions' => $descriptions,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $pageSize,
                'total' => $totalCount,
                'last_page' => ceil($totalCount / $pageSize),
                'from' => $offset + 1,
                'to' => min($offset + $pageSize, $totalCount)
            ],
            'filters' => [
                'diameter' => $diameter,
                'type' => $type,
                'search' => $search
            ]
        ];
    }
}
