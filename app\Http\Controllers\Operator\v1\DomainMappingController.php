<?php

namespace App\Http\Controllers\Operator\v1;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\DomainMapping;
use App\Models\UserRelation;
use App\Models\TrainingCourseProgress;
use App\Repositories\Operator\v1\DomainMappingRepository;
use App\Http\Resources\Operator\v1\DomainMappingResource;
use App\Http\Requests\Operator\v1\DomainMappingRequest;
use DB; 
class DomainMappingController extends Controller
{
    protected $model;

    protected $repository;
    
    public function __construct() {
        $this->model = new DomainMapping();
        $this->repository = new DomainMappingRepository($this->model);
    }

    /**
     * @OA\Get(
     *     path="/operator/domainMapping",
     *     tags={"Operator - Domain Mapping"},
     *     summary="Domain Mapping List",
     *     description="Specific operators domains",
     *     operationId="index",
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function index() {
        try {
            $masterUserId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $allDomains = DomainMapping::whereMasterUserId($masterUserId)->get();
            $message = $allDomains->count() > 0 ? 'operator.Domain.found' : 'operator.Domain.notFound';
            return response()->json(setResponse($allDomains, ['message' => __($message)]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/domainMapping",
     *     tags={"Operator - Domain Mapping"},
     *     summary="Domain Mapping List",
     *     description="Add / update operators domains",
     *     operationId="store",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="domain",
     *                     description="Domain List",
     *                     type="string"
     *                 ),
     *                 example={"domain": "['grr.la', 'gmail.com', 'yahoo.in']"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function store(DomainMappingRequest $request) {
        try {
            $result = DB::transaction(function () use ($request) {
                $domainList = $request->domain;
                $existedDomains = [];
                $masterUserId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
                $oldDomains = DomainMapping::whereMasterUserId($masterUserId)->get()->pluck('domain_name')->toArray();
                
                if(isset($request->domain) && count($domainList) > 0){
                    foreach($domainList as $domain){
                        if(!empty(trim($domain))){
                            $alreadyExist = DomainMapping::whereMasterUserId($masterUserId)->whereDomainName(trim($domain))->first();
                            if(!$alreadyExist){
                                DomainMapping::create([
                                    'master_user_id' => $masterUserId,
                                    'domain_name' => trim($domain)
                                ]);
                            }
                        }
                        $existedDomains[] = trim($domain);
                    }
                    $deletedDomainList = array_diff($oldDomains, $domainList);
                    if(count($deletedDomainList) > 0){
                        DomainMapping::whereMasterUserId($masterUserId)->whereIn('domain_name', $deletedDomainList)->delete();
                        
                        // delete domain users
                        $totalUsers = UserRelation::whereMasterUserId($masterUserId)->whereIn('mapped_domain', $deletedDomainList)->get()->pluck('user_id')->toArray();
                        if(count($totalUsers) > 0){
                            // Unmapped deleted mapped domain users
                            $unmappedDomainUsersJob = (new \App\Jobs\UnmappedDomainUsersJob($masterUserId, $totalUsers))->delay(env('QUEUE_JOB_DELAY_TIME'));
                            dispatch($unmappedDomainUsersJob);
                            // TrainingCourseProgress::whereMasterUserId($masterUserId)->whereIn('user_id', $totalUsers)->delete();
                        }
                        UserRelation::whereMasterUserId($masterUserId)->whereIn('mapped_domain', $deletedDomainList)->delete();
                    }
                }else{
                    if(count($oldDomains) > 0){
                        DomainMapping::whereMasterUserId($masterUserId)->delete();
            
                        // delete domain users
                        $totalUsers = UserRelation::whereMasterUserId($masterUserId)->whereIn('mapped_domain', $oldDomains)->get()->pluck('user_id')->toArray();
                        if(count($totalUsers) > 0){
                            // Unmapped deleted mapped domain users
                            $unmappedDomainUsersJob = (new \App\Jobs\UnmappedDomainUsersJob($masterUserId, $totalUsers))->delay(env('QUEUE_JOB_DELAY_TIME'));
                            dispatch($unmappedDomainUsersJob);
                            // TrainingCourseProgress::whereMasterUserId($masterUserId)->whereIn('user_id', $totalUsers)->delete();
                        }
                        UserRelation::whereMasterUserId($masterUserId)->whereIn('mapped_domain', $oldDomains)->delete();
                    }
                }
                return response()->json(setResponse([], ['message' => __('operator.Domain.updated')]))->setStatusCode(Response::HTTP_OK);
            });
            return $result;
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
