<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class SelfAssessmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        \DB::table('training_course_submodule_types')->insert([
            'name' => 'Self-Assessment',
            'description' => 'An Interactive Assessment module ideal for self assessments, allowing for bulk completion and a weighted scoring pass/ fail.',
            'image' => '16.jpg',
            'display_order' => 19,
            'pdf' => 'practical assessment.pdf',
            'custom_progression_url' => 'https://www.youtube.com/embed/pA6UQAN9440?si=IZhFarNCqOPhE0Aa',
            'status' => 'Active',
            'default_values' => '{"name": "Self Assessment","question": "Question 1","question_type": "text","is_required": "1","severity": [{"severity_name": "No Severity", "severity_occurrence": null, "severity_color_scheme": null}]}',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }
}
