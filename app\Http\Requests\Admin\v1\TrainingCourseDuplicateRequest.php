<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;
use App\Models\TrainingCourse;

class TrainingCourseDuplicateRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array
    {

        return [
            'training_course_id' => 'required|int|exists:'.\DBTableNames::TRAINING_COURSE.',id',
            'master_user_id' => 'required|int|exists:'.\DBTableNames::MASTER_USERS.',id',
        ];
    }
    
    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'training_course_id.required' => __('Training course id is required.'),
            'training_course_id.int' => __('Training course id is invalid.'),
            'master_user_id.required' => __('Master user id is required.'),
            'master_user_id.int' => __('Master user id is invalid.'),
        ];
    }
    
}
