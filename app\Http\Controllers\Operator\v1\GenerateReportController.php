<?php

namespace App\Http\Controllers\Operator\v1;

use DB;
use DBTableNames;
use App\Models\User;
use App\Models\UserRelation;
use Illuminate\Http\Request;
use App\Models\ScormProgress;
use Illuminate\Http\Response;
use App\Models\TrainingCourse;
use App\Models\JobProgressMedia;
use App\Http\Controllers\Controller;
use App\Models\TrainingCourseProgress;
use App\Http\Resources\CustomCollection;
use Illuminate\Database\Eloquent\Builder;
use App\Models\TrainingCourseSubmoduleJobProgress;
use App\Models\TrainingCourseSubModuleQuizResults;
use App\Http\Requests\Operator\v1\SendReminderRequest;
use App\Http\Requests\Operator\v1\CommonListingRequest;
use App\Http\Requests\Operator\v1\ProgressReportRequest;
use App\Repositories\Operator\v1\GenerateReportRepository;
use App\Http\Requests\Operator\v1\CommonNeverReportRequest;
use App\Http\Resources\Operator\v1\LowCompletionReportResource;
use App\Http\Resources\Operator\v1\NotStartedAnyCourseResource;
use App\Models\TrainingCourseSubModulePracticalAssessmentResults;
use App\Http\Resources\Operator\v1\GenerateProgressReportResource;
use App\Http\Resources\Operator\v1\AllCoursesReportListingResource;
use App\Http\Resources\Operator\v1\PartialCompletionReportResource;
use App\Repositories\Operator\v1\TrainingCourseSubModuleScormRepository;
use App\Http\Resources\Operator\v1\GenerateAssessmentProgressReportResource;
use App\Repositories\Operator\v1\TrainingCourseSubModuleJobProgressRepository;
use App\Repositories\Operator\v1\TrainingCourseSubModuleJobProgressMediaRepository;
use App\Http\Resources\Operator\v1\TrainingCourseSubModuleScormReportListingResource;

class GenerateReportController extends Controller
{

    public function __construct() {

        $this->jobsMediaModel = new JobProgressMedia();
        $this->jobsProgressModel = new TrainingCourseSubmoduleJobProgress();
        $this->trainingCourseProgress = new TrainingCourseProgress();
        $this->scormProgressModel = new ScormProgress();
        $this->jobsMediaRepository = new TrainingCourseSubModuleJobProgressMediaRepository($this->jobsMediaModel);
        $this->jobsProgressRepository = new TrainingCourseSubModuleJobProgressRepository($this->jobsProgressModel);
        $this->generateReportRepository = new GenerateReportRepository($this->trainingCourseProgress);
        $this->scormRepository = new TrainingCourseSubModuleScormRepository($this->scormProgressModel);
    }

    /**
     * @OA\Post(
     *     path="/operator/generate_report/getListing",
     *     tags={"Operator - Reports"},
     *     summary="Generate Progress Reports",
     *     description="Generate Progress Reports",
     *     operationId="getListing",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="isExport",
     *                     description="for export csv",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="exportFields",
     *                     description="for export csv fields",
     *                     type="string"
     *                 ),
     *                 example={"per_page": 10, "page": 1, "search_key": "", "sort_by": "last_visited_at", "order_by": "asc", "filters": {"last_logged_in_at": "", "user_name": "", "group_name": "", "last_visited_at": "", "course_progress" : "", "course_name" : ""}, "isExport": 0, "exportFields": {"user_name", "group_name", "created_at", "last_visited_at", "course_progress"}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getListing(CommonListingRequest $request) {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $data = $request->all();
            $selectColumns = [
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.id',
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.user_id',
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.is_pass as quiz_result',
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.master_user_id',
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.training_course_id',
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.module_id',
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.submodule_id',
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.duration as quiz_time',
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.attempts as quiz_attempts',
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.updated_at',
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.updated_at as completion_date',
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.updated_at as last_activity_date',
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.pdf',
                'training_course.title as course_name',
                'training_course_modules.name as module_name',
                'training_course_submodule_details.submodule_name as submodule_name',
                'users.name as user_name',
                'users.last_logged_in_at',
                'users.email as user_email',
                'training_course_progress.course_progress',
                'master_users.name as manager_name',
                'master_users.email as manager_email',
            ];
            $query = TrainingCourseSubModuleQuizResults::whereHas('user', function (Builder $query) {
                $query->whereNull('deleted_at');
            });
            $query->whereRaw('training_course_submodule_quiz_results.id IN (select MAX(id) FROM training_course_submodule_quiz_results GROUP BY user_id,training_course_id,module_id,submodule_id)')
            ->leftJoin(DBTableNames::USERS, DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.user_id', '=', 'users.id')
            ->leftJoin(DBTableNames::TRAINING_COURSE, DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.training_course_id', '=', 'training_course.id')
            ->leftJoin('training_course_modules', DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.module_id', '=', 'training_course_modules.id')
            ->leftJoin('training_course_submodule_details', DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.submodule_id', '=', 'training_course_submodule_details.id')
            ->leftJoin('master_users', DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.master_user_id', '=', 'master_users.id')
            ->leftJoin(DBTableNames::TRAINING_COURSE_PROGRESS, function($join) use ($operatorId){
                $join->on(DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.user_id', '=', 'training_course_progress.user_id');
                $join->on(DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.master_user_id', '=', 'training_course_progress.master_user_id');
                $join->on(DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.training_course_id', '=', 'training_course_progress.training_course_id');
            })
            ->select($selectColumns);

            // Searching
            $search = isset($data['search_key']) ? trim($data['search_key']) : "";
            $searchFields = [DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.updated_at',
            'training_course.title',
            'training_course_modules.name',
            'training_course_submodule_details.submodule_name',
            'training_course_submodule_quiz_results.is_pass',
            'training_course_submodule_quiz_results.duration',
            'training_course_submodule_quiz_results.attempts',
            'users.name',
            'users.email',
            'training_course_progress.course_progress',
            'users.last_logged_in_at'];
            if (!empty($search)) {
                $query = $query->where(function($query) use ($searchFields, $search) {
                            foreach ($searchFields as $key => $field) {
                                $query->orWhere($field, 'LIKE', '%'.$search.'%');
                            }
                        });
            }

            // Ordering (Default DESC)
            $order = isset($data['order_by']) ? $data['order_by'] : 'DESC';

            $query->where(DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS.'.master_user_id', $operatorId);
            $query->where(DBTableNames::TRAINING_COURSE_PROGRESS.'.is_new','0');

            /* Advance Filters */
            if (isset($data['filters']) && count($data['filters']) > 0) {
                if (isset($data['filters']['user_name']) && $data['filters']['user_name'] != "") {
                    $query = $query->where('users.name', 'LIKE', '%' . $data['filters']['user_name'] . '%');
                }
                if (isset($data['filters']['user_email']) && $data['filters']['user_email'] != "") {
                    $query = $query->where('users.email', 'LIKE', '%' . $data['filters']['user_email'] . '%');
                }
                if (isset($data['filters']['course_name']) && $data['filters']['course_name'] != "") {
                    $query = $query->where('training_course.title', 'LIKE', '%' . $data['filters']['course_name'] . '%');
                }
                if (isset($data['filters']['module_name']) && $data['filters']['module_name'] != "") {
                    $query = $query->where('training_course_modules.name', 'LIKE', '%' . $data['filters']['module_name'] . '%');
                }
                if (isset($data['filters']['submodule_name']) && $data['filters']['submodule_name'] != "") {
                    $query = $query->where('training_course_submodule_details.submodule_name', 'LIKE', '%' . $data['filters']['submodule_name'] . '%');
                }
                if (isset($data['filters']['last_activity_date']) && $data['filters']['last_activity_date'] != "") {
                    $query = $query->whereDate('training_course_submodule_quiz_results.updated_at', $data['filters']['last_activity_date']);
                }
                if (isset($data['filters']['completion_date']) && $data['filters']['completion_date'] != "") {
                    $query = $query->whereDate('training_course_submodule_quiz_results.updated_at', $data['filters']['completion_date']);
                }
                if (isset($data['filters']['quiz_result']) && $data['filters']['quiz_result'] != "") {
                    if(strtolower($data['filters']['quiz_result'])=='pass'){
                        $quiz_result=1;
                    }
                    else if (strtolower($data['filters']['quiz_result'])=='fail'){
                        $quiz_result=0;
                    }else{
                        $quiz_result='';
                    }
                    $query = $query->where('is_pass', $quiz_result);
                }
                if (isset($data['filters']['quiz_attempts']) && $data['filters']['quiz_attempts'] != "") {
                    $query = $query->where('training_course_submodule_quiz_results.attempts', $data['filters']['quiz_attempts']);
                }
                if (isset($data['filters']['quiz_time']) && $data['filters']['quiz_time'] != "") {
                    $query = $query->where('training_course_submodule_quiz_results.duration', $data['filters']['quiz_time']);
                }
                if (isset($data['filters']['course_progress']) && $data['filters']['course_progress'] != "") {
                    $query = $query->where('training_course_progress.course_progress', $data['filters']['course_progress']);
                }
            }
            // Sorting
            $sort = 'updated_at'; // Default sort by ID
            $sortingKeys = [
                'user_name' => 'user_name',
                'user_email' => 'user_email',
                'last_activity_date' => 'last_activity_date',
                'course_name' => 'course_name',
                'module_name' => 'module_name',
                'submodule_name' => 'submodule_name',
                'quiz_result' => 'quiz_result',
                'quiz_attempts' => 'quiz_attempts',
                'quiz_time' => 'quiz_time',
                'course_progress' => 'course_progress',
                'completion_date' => 'completion_date',
                'updated_at' => 'updated_at',
            ];
            if (isset($data['sort_by']) && $data['sort_by'] != "") {
                if (array_key_exists($data['sort_by'], $sortingKeys)) {
                    $sort = $sortingKeys[$data['sort_by']];
                }
            }

            // Ordering (Default DESC)
            $order = isset($data['order_by']) ? $data['order_by'] : 'DESC';

            // Apply Sorting and Ordering
            if (in_array($sort, $sortingKeys)) {
                $query = $query->orderBy($sort, $order);
            }

            $userData = $query;
            
            if ($request->isExport && isset($request->ids) && !empty($request->ids)) {
                return $this->QuizProgressReportCsv($request->ids, $request->exportFields);
            }
            else if($request->isExport) {
                return $this->QuizReportCsv($userData->get(), $request->exportFields);
            }
            return GenerateProgressReportResource::collection($userData->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    public function QuizProgressReportCsv($userIds=null, $fields = null,$type = null) {
        try {
            $columns = [
            'user_name' => 'User Name',
            'user_email' => 'Email',
            'manager_name' => 'Manager Name',
            'manager_email' => 'Manager Email',
            'course_name' => 'Training Course',
            'module_name' => 'Module Name',
            'submodule_name' => 'SubModule Name',
            'course_progress' => 'Course Progress',
            'quiz_attempts'=>'Quiz Attempts',
            'quiz_result' => 'Quiz Result',
            "last_activity_date"=> 'Last Activity Date',
            "completion_date"=>'Completion Date',
            "pdf"=>'Latest Pdf',];
            $exportFields = array_flip((empty($fields))? array_keys($columns): $fields);
            $fileName = '../storage/' . time() . '-' . randomString() . '.csv';
            $fp = fopen($fileName, 'w');
            fputcsv($fp, array_intersect_key($columns, $exportFields));
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $selectColumns = [
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.id',
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.user_id',
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.is_pass',
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.master_user_id',
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.training_course_id',
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.duration',
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.attempts',
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.updated_at',
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.total_questions',
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.correct_answers',
                DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.pdf',
                'training_course.title as course_name',
                'training_course_modules.name as module_name',
                'training_course_submodule_details.submodule_name as submodule_name',
                'users.name as user_name',
                'users.last_logged_in_at',
                'users.email as email',
                'master_users.name as manager_name',
                'master_users.email as manager_email',
            ];
            $quizResults = TrainingCourseSubModuleQuizResults::query();
            if($type == 'import'){
                $quizResults->whereRaw('training_course_submodule_quiz_results.id IN (select MAX(id) FROM training_course_submodule_quiz_results GROUP BY user_id,training_course_id)');
            }
            $quizResults->leftJoin(DBTableNames::USERS, DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.user_id', '=', 'users.id')
            ->leftJoin(DBTableNames::TRAINING_COURSE, DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.training_course_id', '=', 'training_course.id')
            ->leftJoin('training_course_modules', DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.module_id', '=', 'training_course_modules.id')
            ->leftJoin('training_course_submodule_details', DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.submodule_id', '=', 'training_course_submodule_details.id')
            ->leftJoin('master_users', DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS . '.master_user_id', '=', 'master_users.id')
            ->select($selectColumns)
            ->where(DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS.'.master_user_id', $operatorId);
            if($type == 'import'){
                $quizResults->whereIn(DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS.'.user_id', $userIds);
            }else{
                $quizResults->whereIn(DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS.'.id', $userIds);
            }
            $userData = $quizResults->get();
            if(count($userData)>0){
            foreach ($userData as $raw) {
                if($raw->is_pass==1){
                    $last_date='';
                    $result='Pass';
                    $completion_date=date_format($raw->updated_at, 'Y-m-d H:i:s');
                }else{
                    $result='Fail';
                    $completion_date='';
                    $last_date=date_format($raw->updated_at, 'Y-m-d H:i:s');
                }
                $QuizResult=TrainingCourseSubModuleQuizResults::where('master_user_id',$operatorId)->where('training_course_id',$raw->training_course_id)->where('user_id',$raw->user_id)->get();
                $Attempts='';
                $progress=TrainingCourseProgress::where('master_user_id',$operatorId)->where('training_course_id',$raw->training_course_id)->where('user_id',$raw->user_id)->value('course_progress');
                foreach($QuizResult as $quiz_value){
                    $Attempts.='Attempt #'.$quiz_value->attempts.',Result ('.$quiz_value->correct_answers.' Out Of '.$quiz_value->total_questions.'),Quiz Time:'.$quiz_value->duration.',Pdf:'.env('CDN_URL').getTrainingCourseSubmoduleQuizPath($quiz_value->training_course_id).$quiz_value->pdf.'|';
                }
                $output = [
                    "user_name" => $raw->user_name,
                    "user_email" => $raw->email,
                    'manager_name' => $raw->manager_name,
                    'manager_email' => $raw->manager_email,
                    "course_name" => $raw->course_name,
                    'module_name' => $raw->module_name,
                    'submodule_name' => $raw->submodule_name,
                    'course_progress' =>!is_null($progress)?$progress:0,
                    "quiz_attempts"=>$Attempts,
                    "quiz_result" => $result,
                    "last_activity_date" => $last_date,
                    "completion_date" => $completion_date,
                    "pdf" => env('CDN_URL').getTrainingCourseSubmoduleQuizPath($raw->training_course_id).$raw->pdf,
                ];
                fputcsv($fp, array_intersect_key($output, $exportFields));
            }

            return response()->download($fileName, time() . '-QuizProgressReport.csv', csvHeaders())->deleteFileAfterSend(true);
        }else{
            return response()->json(setErrorResponse(__('operator.GenerateReport.recordNotFound')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    public function QuizReportCsv($userData, $fields = null) {
        try {
            $columns = ['user_name' => 'User Name',
            'user_email' => 'Email',
            'manager_name' => 'Manager Name',
            'manager_email' => 'Manager Email',
            'course_name' => 'Training Course',
            'module_name' => 'Module Name',
            'submodule_name' => 'SubModule Name',
            'course_progress' => 'Course Progress',
            'quiz_attempts' => 'Quiz Attempts',
            'quiz_result' => 'Quiz Result',
            'last_activity_date' => 'Last Activity Date',
            'completion_date' => 'Completion Date',
            'pdf' => 'Latest Pdf',];
            $exportFields = array_flip((empty($fields))? array_keys($columns): $fields);
            $fileName = '../storage/' . time() . '-' . randomString() . '.csv';
            $fp = fopen($fileName, 'w');
            fputcsv($fp, array_intersect_key($columns, $exportFields));

            foreach ($userData as $raw) {
                if(!empty($raw->quiz_result==1)){
                    $result='Pass';
                    $last_date='';
                    $completion_date=date_format($raw->updated_at, 'Y-m-d H:i:s');
                }else{
                    $result='Fail';
                    $completion_date='';
                    $last_date=date_format($raw->updated_at, 'Y-m-d H:i:s');
                }
                $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
                $QuizResult=TrainingCourseSubModuleQuizResults::where('master_user_id',$operatorId)->where('training_course_id',$raw->training_course_id)->where('user_id',$raw->user_id)->get();
                $Attempts='';
                foreach($QuizResult as $quiz_value){
                    $Attempts.='Attempt #'.$quiz_value->attempts.',Result ('.$quiz_value->correct_answers.' Out Of '.$quiz_value->total_questions.'),Quiz Time:'.$quiz_value->duration.',Pdf:'.env('CDN_URL').getTrainingCourseSubmoduleQuizPath($quiz_value->training_course_id).$quiz_value->pdf.'|';
                }
                $progress=TrainingCourseProgress::where('master_user_id',$operatorId)->where('training_course_id',$raw->training_course_id)->where('user_id',$raw->user_id)->value('course_progress');
                $output = [
                    "user_name" => $raw->user_name,
                    "user_email" => $raw->user_email,
                    'manager_name' => $raw->manager_name,
                    'manager_email' => $raw->manager_email,
                    "course_name" => $raw->course_name,
                    'module_name' => $raw->module_name,
                    'submodule_name' => $raw->submodule_name,
                    "course_progress" => !is_null($progress)?$progress:0,
                    "quiz_attempts" => $Attempts,
                    "quiz_result" => $result,
                    "last_activity_date" => $last_date,
                    // "last_activity_date" => !is_null($raw->last_logged_in_at) ? $raw->last_logged_in_at : '',
                    "completion_date" => $completion_date,
                    "pdf" => env('CDN_URL').getTrainingCourseSubmoduleQuizPath($raw->training_course_id).$raw->pdf,
                ];

                fputcsv($fp, array_intersect_key($output, $exportFields));
            }

            return response()->download($fileName, time() . '-QuizReport.csv', csvHeaders())->deleteFileAfterSend(true);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function getAssessmentReportListing(CommonListingRequest $request) {
        try {
            // \DB::enableQueryLog();
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $data = $request->all();
            $selectColumns = [
                'training_course_submodule_practical_assessment_results.id',
                'training_course_submodule_practical_assessment_results.user_id',
                'training_course_submodule_practical_assessment_results.is_pass as result',
                'training_course_submodule_practical_assessment_results.training_course_id',
                'training_course_submodule_practical_assessment_results.module_id',
                'training_course_submodule_practical_assessment_results.submodule_id',
                'training_course_submodule_practical_assessment_results.created_at as completion_date',
                'training_course.title as course_name',
                'users.name as user_name',
                'users.email as user_email',
                'training_course_modules.name as module_name',
                'training_course_submodule_details.submodule_name as submodule_name',
                'assessor.name as assessor_name',
                'assessor.email as assessor_email',
                'assessor.assessor_id as assessor_id',
                'master_users.name as manager_name',
                'master_users.email as manager_email',
            ];
            $query = TrainingCourseSubModulePracticalAssessmentResults::whereHas('user', function (Builder $query) {
                $query->whereNull('deleted_at');
            })
            ->leftJoin(DBTableNames::USERS, DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS . '.user_id', '=', 'users.id')
            ->leftJoin('users AS assessor', DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS . '.assessor_id', '=', 'assessor.id')
            ->leftJoin('training_course_modules', DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS . '.module_id', '=', 'training_course_modules.id')
            ->leftJoin('training_course_submodule_details', DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS . '.submodule_id', '=', 'training_course_submodule_details.id')
            ->leftJoin(DBTableNames::TRAINING_COURSE, DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS . '.training_course_id', '=', 'training_course.id')
            ->leftJoin('master_users', DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS . '.master_user_id', '=', 'master_users.id')
            ->select($selectColumns);

            // Searching
            $search = isset($data['search_key']) ? trim($data['search_key']) : "";
            $searchFields = [
            'training_course.title',
            'training_course_modules.name',
            'training_course_submodule_details.submodule_name',
            'training_course_submodule_practical_assessment_results.is_pass',
            'training_course_submodule_practical_assessment_results.created_at',
            'assessor.name',
            'assessor.email',
            'users.name',
            'users.email'];
            if (!empty($search)) {
                $query = $query->where(function($query) use ($searchFields, $search) {
                            foreach ($searchFields as $key => $field) {
                                $query->orWhere($field, 'LIKE', '%'.$search.'%');
                            }
                        });
            }

            // Ordering (Default DESC)
            $order = isset($data['order_by']) ? $data['order_by'] : 'DESC';

            $query->where(DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS.'.master_user_id', $operatorId);
            $query->where(DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS.'.type', 'PA');
            /* Advance Filters */
            if (isset($data['filters']) && count($data['filters']) > 0) {
                if (isset($data['filters']['user_name']) && $data['filters']['user_name'] != "") {
                    $query = $query->where('users.name', 'LIKE', '%' . $data['filters']['user_name'] . '%');
                }
                if (isset($data['filters']['user_email']) && $data['filters']['user_email'] != "") {
                    $query = $query->where('users.email', 'LIKE', '%' . $data['filters']['user_email'] . '%');
                }
                if (isset($data['filters']['course_name']) && $data['filters']['course_name'] != "") {
                    $query = $query->where('training_course.title', 'LIKE', '%' . $data['filters']['course_name'] . '%');
                }
                if (isset($data['filters']['completion_date']) && $data['filters']['completion_date'] != "") {
                    $query = $query->whereDate('training_course_submodule_practical_assessment_results.created_at', $data['filters']['completion_date']);
                }
                if (isset($data['filters']['module_name']) && $data['filters']['module_name'] != "") {
                    $query = $query->where('training_course_modules.name', 'LIKE', '%' . $data['filters']['module_name'] . '%');
                }
                if (isset($data['filters']['submodule_name']) && $data['filters']['submodule_name'] != "") {
                    $query = $query->where('training_course_submodule_details.submodule_name', 'LIKE', '%' . $data['filters']['submodule_name'] . '%');
                }
                if (isset($data['filters']['result']) && $data['filters']['result']!="") {
                    if(strtolower($data['filters']['result'])=='pass'){
                        $result=1;
                    }
                    else if (strtolower($data['filters']['result'])=='fail'){
                        $result=0;
                    }else{
                        $result='';
                    }
                    $query = $query->where('training_course_submodule_practical_assessment_results.is_pass', $result);
                }
                if (isset($data['filters']['assessor_name']) && $data['filters']['assessor_name'] != "") {
                    $query = $query->where('assessor.name', 'LIKE', '%' . $data['filters']['assessor_name']. '%');
                }
                if (isset($data['filters']['assessor_email']) && $data['filters']['assessor_email'] != "") {
                    $query = $query->where('assessor.email', 'LIKE', '%' . $data['filters']['assessor_email']. '%');
                }

            }
            // Sorting
            $sort = 'completion_date'; // Default sort by ID
            $sortingKeys = [
                'user_name' => 'user_name',
                'user_email' => 'user_email',
                'completion_date' => 'completion_date',
                'course_name' => 'course_name',
                'module_name' => 'module_name',
                'submodule_name' => 'submodule_name',
                'assessor_name' => 'assessor_name',
                'assessor_name' => 'assessor_email',
                'result' => 'result',
            ];
            if (isset($data['sort_by']) && $data['sort_by'] != "") {
                if (array_key_exists($data['sort_by'], $sortingKeys)) {
                    $sort = $sortingKeys[$data['sort_by']];
                }
            }

            // Ordering (Default DESC)
            $order = isset($data['order_by']) ? $data['order_by'] : 'DESC';

            // Apply Sorting and Ordering
            if (in_array($sort, $sortingKeys)) {
                $query = $query->orderBy($sort, $order);
            }

            $userData = $query;
            if ($request->isExport) {
                if(isset($request->ids) && !empty($request->ids)){
                    $query->whereIn(DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS.'.id', $request->ids);
                    $ExportuserData = $query;
                    return $this->AssessmentReportCsv($ExportuserData->get(), $request->exportFields);
                }else{
                return $this->AssessmentReportCsv($userData->get(), $request->exportFields);
                }
            }
            return GenerateAssessmentProgressReportResource::collection($userData->paginate($request->per_page));

        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    public function getSelfAssessmentReportListing(CommonListingRequest $request) {
        try {
            // \DB::enableQueryLog();
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $data = $request->all();
            $selectColumns = [
                'training_course_submodule_practical_assessment_results.id',
                'training_course_submodule_practical_assessment_results.user_id',
                'training_course_submodule_practical_assessment_results.is_pass as result',
                'training_course_submodule_practical_assessment_results.training_course_id',
                'training_course_submodule_practical_assessment_results.module_id',
                'training_course_submodule_practical_assessment_results.submodule_id',
                'training_course_submodule_practical_assessment_results.created_at as completion_date',
                'training_course.title as course_name',
                'users.name as user_name',
                'users.email as user_email',
                'training_course_modules.name as module_name',
                'training_course_submodule_details.submodule_name as submodule_name',
                'assessor.name as assessor_name',
                'assessor.email as assessor_email',
                'assessor.assessor_id as assessor_id',
                'master_users.name as manager_name',
                'master_users.email as manager_email',
            ];
            $query = TrainingCourseSubModulePracticalAssessmentResults::whereHas('user', function (Builder $query) {
                $query->whereNull('deleted_at');
            })
            ->leftJoin(DBTableNames::USERS, DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS . '.user_id', '=', 'users.id')
            ->leftJoin('users AS assessor', DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS . '.assessor_id', '=', 'assessor.id')
            ->leftJoin('training_course_modules', DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS . '.module_id', '=', 'training_course_modules.id')
            ->leftJoin('training_course_submodule_details', DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS . '.submodule_id', '=', 'training_course_submodule_details.id')
            ->leftJoin(DBTableNames::TRAINING_COURSE, DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS . '.training_course_id', '=', 'training_course.id')
            ->leftJoin('master_users', DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS . '.master_user_id', '=', 'master_users.id')
            ->select($selectColumns);

            // Searching
            $search = isset($data['search_key']) ? trim($data['search_key']) : "";
            $searchFields = [
            'training_course.title',
            'training_course_modules.name',
            'training_course_submodule_details.submodule_name',
            'training_course_submodule_practical_assessment_results.is_pass',
            'training_course_submodule_practical_assessment_results.created_at',
            'assessor.name',
            'assessor.email',
            'users.name',
            'users.email'];
            if (!empty($search)) {
                $query = $query->where(function($query) use ($searchFields, $search) {
                            foreach ($searchFields as $key => $field) {
                                $query->orWhere($field, 'LIKE', '%'.$search.'%');
                            }
                        });
            }

            // Ordering (Default DESC)
            $order = isset($data['order_by']) ? $data['order_by'] : 'DESC';

            $query->where(DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS.'.master_user_id', $operatorId);
            $query->where(DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS.'.type', 'Self');
            /* Advance Filters */
            if (isset($data['filters']) && count($data['filters']) > 0) {
                if (isset($data['filters']['user_name']) && $data['filters']['user_name'] != "") {
                    $query = $query->where('users.name', 'LIKE', '%' . $data['filters']['user_name'] . '%');
                }
                if (isset($data['filters']['user_email']) && $data['filters']['user_email'] != "") {
                    $query = $query->where('users.email', 'LIKE', '%' . $data['filters']['user_email'] . '%');
                }
                if (isset($data['filters']['course_name']) && $data['filters']['course_name'] != "") {
                    $query = $query->where('training_course.title', 'LIKE', '%' . $data['filters']['course_name'] . '%');
                }
                if (isset($data['filters']['completion_date']) && $data['filters']['completion_date'] != "") {
                    $query = $query->whereDate('training_course_submodule_practical_assessment_results.created_at', $data['filters']['completion_date']);
                }
                if (isset($data['filters']['module_name']) && $data['filters']['module_name'] != "") {
                    $query = $query->where('training_course_modules.name', 'LIKE', '%' . $data['filters']['module_name'] . '%');
                }
                if (isset($data['filters']['submodule_name']) && $data['filters']['submodule_name'] != "") {
                    $query = $query->where('training_course_submodule_details.submodule_name', 'LIKE', '%' . $data['filters']['submodule_name'] . '%');
                }
                if (isset($data['filters']['result']) && $data['filters']['result']!="") {
                    if(strtolower($data['filters']['result'])=='pass'){
                        $result=1;
                    }
                    else if (strtolower($data['filters']['result'])=='fail'){
                        $result=0;
                    }else{
                        $result='';
                    }
                    $query = $query->where('training_course_submodule_practical_assessment_results.is_pass', $result);
                }
                if (isset($data['filters']['assessor_name']) && $data['filters']['assessor_name'] != "") {
                    $query = $query->where('assessor.name', 'LIKE', '%' . $data['filters']['assessor_name']. '%');
                }
                if (isset($data['filters']['assessor_email']) && $data['filters']['assessor_email'] != "") {
                    $query = $query->where('assessor.email', 'LIKE', '%' . $data['filters']['assessor_email']. '%');
                }

            }
            // Sorting
            $sort = 'completion_date'; // Default sort by ID
            $sortingKeys = [
                'user_name' => 'user_name',
                'user_email' => 'user_email',
                'completion_date' => 'completion_date',
                'course_name' => 'course_name',
                'module_name' => 'module_name',
                'submodule_name' => 'submodule_name',
                'assessor_name' => 'assessor_name',
                'assessor_name' => 'assessor_email',
                'result' => 'result',
            ];
            if (isset($data['sort_by']) && $data['sort_by'] != "") {
                if (array_key_exists($data['sort_by'], $sortingKeys)) {
                    $sort = $sortingKeys[$data['sort_by']];
                }
            }

            // Ordering (Default DESC)
            $order = isset($data['order_by']) ? $data['order_by'] : 'DESC';

            // Apply Sorting and Ordering
            if (in_array($sort, $sortingKeys)) {
                $query = $query->orderBy($sort, $order);
            }

            $userData = $query;
            if ($request->isExport) {
                if(isset($request->ids) && !empty($request->ids)){
                    $query->whereIn(DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS.'.id', $request->ids);
                    $ExportuserData = $query;
                    return $this->AssessmentReportCsv($ExportuserData->get(), $request->exportFields);
                }else{
                return $this->AssessmentReportCsv($userData->get(), $request->exportFields);
                }
            }
            return GenerateAssessmentProgressReportResource::collection($userData->paginate($request->per_page));

        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    public function AssessmentReportCsv($userData, $fields = null) {
        try {
            // echo "<pre>";
            // print_r($fields);die;
            $columns = ['user_name' => 'User Name',
            'user_email' => 'Email',
            'manager_name' => 'Manager Name',
            'manager_email' => 'Manager Email',
            'course_name' => 'Training Course',
            'module_name' => 'Module Name',
            'submodule_name' => 'SubModule Name',
            'assessor_name' => 'Assessor Name',
            'assessor_email' => 'Assessor Email',
            'result' => 'Assessment Result',
            'completion_date' => 'Completion Date'];
            $exportFields = array_flip((empty($fields))? array_keys($columns): $fields);
            $fileName = '../storage/' . time() . '-' . randomString() . '.csv';
            $fp = fopen($fileName, 'w');
            fputcsv($fp, array_intersect_key($columns, $exportFields));

            foreach ($userData as $raw) {
                if($raw->result==1){
                    $result='Pass';
                }else{
                    $result='Fail';
                }
                $output = [
                    "user_name" => $raw->user_name,
                    "user_email" => $raw->user_email,
                    "manager_name" => $raw->manager_name,
                    "manager_email" => $raw->manager_email,
                    "course_name" => $raw->course_name,
                    "module_name" => $raw->module_name,
                    "submodule_name" => $raw->submodule_name,
                    "assessor_name" => $raw->assessor_name,
                    "assessor_email" => $raw->assessor_email,
                    "result" => $result,
                    "completion_date" => $raw->completion_date
                ];

                fputcsv($fp, array_intersect_key($output, $exportFields));
            }

            return response()->download($fileName, time() . '-PracticalAssessmentProgressReport.csv', csvHeaders())->deleteFileAfterSend(true);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function importAssessmentReport(ProgressReportRequest $request){
        $fileName = $request->file;
        $file = fopen($fileName, "r");
                $count = 0;
                    while (($ImportData = fgetcsv($file)) !== FALSE) {
                        $count++;
                        // skip header
                            if ($count == 1) {
                                $Attribute=$ImportData;
                                continue;
                            }
                            $Data[]=trim($ImportData[0]);
                    }
        $emailIds=implode(',',array_unique($Data));
        $UserIds=User::whereIn('email',explode(',',$emailIds))->pluck('id');

        //Export Record
        $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
        $selectColumns = [
            'training_course_submodule_practical_assessment_results.id',
            'training_course_submodule_practical_assessment_results.user_id',
            'training_course_submodule_practical_assessment_results.is_pass as result',
            'training_course_submodule_practical_assessment_results.training_course_id',
            'training_course.title as course_name',
            'users.name as user_name',
            'users.email as user_email',
            'training_course_modules.name as module_name',
            'training_course_submodule_details.submodule_name as submodule_name',
            'assessor.name as assessor_name',
            'assessor.email as assessor_email',
            'master_users.name as manager_name',
            'master_users.email as manager_email',
        ];
        $query = TrainingCourseSubModulePracticalAssessmentResults::whereHas('user', function (Builder $query) {
            $query->whereNull('deleted_at');
        })
        ->leftJoin(DBTableNames::USERS, DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS . '.user_id', '=', 'users.id')
        ->leftJoin('users AS assessor', DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS . '.assessor_id', '=', 'assessor.id')
        ->leftJoin('training_course_modules', DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS . '.module_id', '=', 'training_course_modules.id')
        ->leftJoin('training_course_submodule_details', DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS . '.submodule_id', '=', 'training_course_submodule_details.id')
        ->leftJoin(DBTableNames::TRAINING_COURSE, DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS . '.training_course_id', '=', 'training_course.id')
        ->leftJoin('master_users', DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS . '.master_user_id', '=', 'master_users.id')
        ->select($selectColumns);
        $query->where(DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS.'.master_user_id', $operatorId);
        if(!empty($UserIds)){
        $query->whereIn(DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS.'.user_id', $UserIds);
        $ExportuserData = $query;
        $ExportuserData=$ExportuserData->get();
        if(count($ExportuserData)>0){
        return $this->AssessmentReportCsv($ExportuserData, json_decode($request->exportFields));
        }else{
            return response()->json(setErrorResponse(__('Records not found')))->setStatusCode(Response::HTTP_NOT_FOUND);
        }
        }
    }

    public function importQuizReport(ProgressReportRequest $request){
        $fileName = $request->file;
        $file = fopen($fileName, "r");
                $count = 0;
                    while (($ImportData = fgetcsv($file)) !== FALSE) {
                        $count++;
                        // skip header
                            if ($count == 1) {
                                $Attribute=$ImportData;
                                continue;
                            }
                            $Data[]=trim($ImportData[0]);
                    }
        $emailIds=implode(',',array_unique($Data));
        $UserIds=User::whereIn('email',explode(',',$emailIds))->pluck('id');
        if(!empty($UserIds)){
            return $this->QuizProgressReportCsv($UserIds, json_decode($request->exportFields));
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/generate_report/getAllCoursesListing",
     *     tags={"Operator - Reports"},
     *     summary="Generate All Courses Progress Reports",
     *     description="Generate All Courses Progress Reports",
     *     operationId="index",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 example={"per_page": 10, "page": 1, "search_key": "", "sort_by": "name", "order_by": "asc", "filters": {"name": "", "total_subdata": "", "status": ""}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Role Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getAllCoursesListing(CommonListingRequest $request) {
        try {
            $allCourseList = $this->generateReportRepository->getAllCoursesListing($request->all());
            if ($request->isExport) {
                if($allCourseList->get()->count() > 0){
                    return $this->generateReportRepository->exportAllCoursesReportCsv($allCourseList->get(), $request->exportFields);
                }else{
                    return response()->json(setResponse([], ['message' => __('operator.GenerateReport.recordNotFound')]))->setStatusCode(Response::HTTP_OK);
                }
            }
            return AllCoursesReportListingResource::collection($allCourseList->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/generate_report/allCourses/pdfExport",
     *     tags={"Operator - Reports"},
     *     summary="Certificate PDF export",
     *     description="Certificate PDF export",
     *     operationId="index",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="ids[]",
     *                     description="ids[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Role Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function pdfExport() {
        try {
            $certificateRecords = $this->generateReportRepository->pdfExport();
            if(!empty($certificateRecords)){
                return response()->json(setResponse(['certificate_pdf'=>$certificateRecords], ['message' => __('operator.GenerateReport.pdfGenerate')]))->setStatusCode(Response::HTTP_OK);
            }else{
                return response()->json(setResponse([], ['message' => __('operator.GenerateReport.pdfNotFound')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/generate_report/getScormReportListing",
     *     tags={"Operator - Reports"},
     *     summary="Generate SCORM Progress Reports",
     *     description="Generate SCORM Progress Reports",
     *     operationId="index",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 example={"per_page": 10, "page": 1, "search_key": "", "sort_by": "name", "order_by": "asc", "filters": {"name": "", "total_subdata": "", "status": ""}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Role Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getScormReportListing(CommonListingRequest $request) {
        try {
            $scormList = $this->scormRepository->getScormReportListing($request->all());
            if ($request->isExport) {
                if(count($scormList->get()) > 0){
                    return $this->scormRepository->exportScormReportCsv($scormList->get(), $request->exportFields);
                }else{
                    return response()->json(setResponse([], ['message' => __('operator.GenerateReport.recordNotFound')]))->setStatusCode(Response::HTTP_OK);
                }
            }
            return TrainingCourseSubModuleScormReportListingResource::collection($scormList->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
