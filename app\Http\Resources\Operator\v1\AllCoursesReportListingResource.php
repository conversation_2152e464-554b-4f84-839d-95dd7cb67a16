<?php

namespace App\Http\Resources\Operator\v1;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\TrainingCourseSubmoduleDetails;

class AllCoursesReportListingResource extends JsonResource
{

    public function toArray($request)
    { 
        if($this->course_progress ==100 && $this->complete_date==null)
        {
            $complete_date=($this->course_progress ==100) ? date_format($this->updated_at, 'Y-m-d H:i:s') : null;
        }else{
            $complete_date=($this->course_progress ==100) ? $this->complete_date : null;
        }
        return [
            "id" => $this->id,
            "user_id" => $this->user_id,
            "name" => $this->name ?? null,
            "email" => $this->email ?? null,
            "training_course_id" => $this->training_course_id,
            "training_course_name" => $this->training_course_name,
            "course_progress" => $this->course_progress,
            "is_certificate_generated" => $this->is_certificate_generated,
            "complete_date" => $complete_date,
        ];
    }
}
