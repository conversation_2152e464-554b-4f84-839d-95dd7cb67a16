<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;

class CommonBulkRestoreCourseRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     * @return array
     */
    protected function rules(): array
    {
        return [
            'ids' => 'required|array|min:1',
            //'operator_id' => 'required'
        ];
    }
    
    /**
     * Get the validation messages that apply to the rules.
     * @return array
     */
    public function messages(): array
    {
        return [
            'ids.required' => __('Ids are required.'),
            'ids.min' => __('Please select atleast one team member.'),
            //'operator_id.required' => __('Operator Id is required.'),
        ];
    }
    
}
