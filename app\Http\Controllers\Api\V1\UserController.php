<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\User;
use App\Models\MasterUser;
use App\Models\UserRelation;
use Illuminate\Http\Request;
use App\Models\Notifications;
use Illuminate\Http\Response;
use App\Models\TrainingCourse;
use App\Models\AssessorAssignUser;
use App\Services\GetStreamService;
use App\Http\Controllers\Controller;
use App\Repositories\UserRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\AssignUserInviteLinks;
use App\Models\TrainingCourseProgress;
use App\Http\Resources\CustomCollection;
use App\Http\Requests\V1\HelpFormRequest;
use Illuminate\Support\Facades\Validator;
use App\Http\Resources\V1\User\UserResource;
use App\Http\Requests\V1\ChangePasswordRequest;
use App\Http\Requests\V1\OperatosFromOucRequest;
use App\Http\Requests\V1\AssignUserInviteLinkRequest;
use App\Repositories\AssignUserInviteLinksRepository;
use App\Http\Controllers\Api\V1\MfaConfigurationController;
use App\Http\Resources\V1\GetAssessorAssignUserListResource;
use App\Services\DeeplinkService;

class UserController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
     */

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    protected $model;

    protected $user_repository;

    public function __construct()
    {
        $this->deeplinkService =new DeeplinkService();
        $this->model = new User();
        $this->user_repository = new UserRepository($this->model);
        $this->assignUserInviteLinkModel = new AssignUserInviteLinks();
        $this->assignUserInviteLinksRepository = new AssignUserInviteLinksRepository($this->assignUserInviteLinkModel);
    }

    /**
     * User change password process - Update
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function changePassword(ChangePasswordRequest $request)
    {
        try {
            $auth = Auth::user();
            if (!Hash::check($request->old_password, $auth->password)) {
                return response()->json(setErrorResponse(__("user.UserController.IncorrectOldPassword")))->setStatusCode(Response::HTTP_BAD_REQUEST);
            }
            Auth::user()->update(['password' => bcrypt($request->password), 'is_password_change' => 1]);
            // Mobile Notification
            $registeredUser = User::whereId(Auth::user()->id)->first();
            $message = "Your Password is successfully changed";
            $mutable = 1;
            $payload['type'] = "Password";
            $payload['title'] = $message;
            $payload['description'] = $message;
            $payload['master_user_id'] = $registeredUser->user_relation->master_user_id;
            $data = [
                'alert' => $message,
                "mutable-content" => $mutable,
                'data' => $payload,
                'sound' => 'default',
            ];
            $extra['user_id'] = $registeredUser->id;
            $extra['master_user_id'] = $registeredUser->user_relation->master_user_id;
            $extra['type'] = 'Password';
            $extra['notification_status'] = 'Extra';

            // Dispatch Change Password Notification Job
            dispatch(new \App\Jobs\ChangePasswordJob($registeredUser, $data, $extra));
            if($auth->mfa_user_id!='' && $auth->mfa_user_id!=null){
                $mfaConfig=new MfaConfigurationController();
                $mfaConfig->adminSetUserPassword($auth->mfa_user_id, $request->password);
            }
            return response()->json(setResponse([], ['message' => __('user.UserController.passwordReset')]))
                ->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Profile Details
     *
     * @return \Illuminate\Http\Response
     */
    public function getProfileDetails(Request $request)
    {
        try {
            $user = auth()->user();
            if ($user) {
                return response()->json(setResponse(new UserResource($user)))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setErrorResponse(__('user.UserController.notFound')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Update Profile Details.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateProfileDetails(Request $request)
    {
        try {
            $data = $request->only(['name', 'manager_email', 'unique_id', 'photo']);
            $rules = [
                'name' => 'sometimes|required|max:255',
                'manager_email' => 'email|max:255', //required_if:unique_id,users_manager_email|
                'unique_id' => 'max:10', //required_if:manager_email,users_unique_id|
            ];
            $messages = [
                'name.required' => 'Please enter name.',
                'manager_email.required_if' => 'Manager email is required.',
                'manager_email.email' => 'Email must be a valid email address (E.g.: <EMAIL>).',
                'unique_id.max' => 'Unique Id can not be greater than 10 digits.',
            ];
            $validator = Validator::make($data, $rules, $messages);
            if ($validator->fails()) {
                $err = implode('<br/>', $validator->errors()->all());
                return response()->json(setErrorResponse($err, Response::HTTP_BAD_REQUEST))->setStatusCode(Response::HTTP_BAD_REQUEST);
            }

            $user = User::where('id', auth()->id())->first();
            if ($request->hasFile('photo')) {
                $file = \Storage::disk('s3')->put(getUsersPath(), $request->file('photo'));
                $name = explode('/', $file);
                $data['photo'] = $name[count($name) - 1];
                if (!empty($user->photo)) {
                    deleteFileFromS3(getUsersPath() . '/' . $user->photo);
                }
            } else if ($request->has('photo')) {
                $file = convertBase64toImage($request->photo);
                \Storage::disk('s3')->put(getUsersPath() . '/' . $file['name'], $file['image']);
                $data['photo'] = $file['name'];
                if (!empty($user->photo)) {
                    deleteFileFromS3(getUsersPath() . '/' . $user->photo);
                }
            }
            if (!empty($request->manager_email)) {
                $operatorId = auth()->user()->user_relation->master_user_id;
                $operator = MasterUser::whereEmail($request->manager_email)->orWhere(function ($query) use ($operatorId) {
                    $query->where('parent_id', '=', $operatorId)
                        ->where('id', '=', $operatorId);
                })->first();
                if ($operator) {
                    $data['unique_id'] = $operator->unique_id;
                    $data['manager_id'] = $operator->id;
                } else {
                    $data['unique_id'] = isset($request->unique_id) ? $request->unique_id : null;
                }
            } else if (!empty($request->unique_id)) {
                $operatorId = auth()->user()->user_relation->master_user_id;
                $operator = MasterUser::where('unique_id', $request->unique_id)->orWhere(function ($query) use ($operatorId) {
                    $query->where('parent_id', '=', $operatorId)
                        ->where('id', '=', $operatorId);
                })->first();
                if ($operator) {
                    $data['manager_email'] = $operator->email;
                }
            }
            $user->update($data);
            unset($data['name']);
            unset($data['photo']);
            $user->user_relation()->update($data);
            return response()->json(setResponse(new UserResource($user), ['message' => __('user.UserController.profileUpdate')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Operator List
     *
     * @return \Illuminate\Http\Response
     */
    public function operatorList(Request $request)
    {
        try {
            $user = auth()->user();
            if ($user) {
                $operators = (new User)->operatorList($user->id);
                return new CustomCollection($operators, 'App\Http\Resources\V1\User\OperatorResource');
            }
            return response()->json(setErrorResponse(__('user.UserController.notFound')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Switch Operator
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function switchOperator(Request $request)
    {
        try {
            $user = auth()->user();
            if ($user) {
                $masterUser=UserRelation::where(['user_id' => $user->id, 'master_user_id' => $request->id])->value('id');
                if(!empty($masterUser)){
                UserRelation::where(['user_id' => $user->id, 'is_current_operator' => 1])->update(['is_current_operator' => 0]);
                UserRelation::where(['user_id' => $user->id, 'master_user_id' => $request->id])->update(['is_current_operator' => 1]);
                $operator = MasterUser::find($request->id);
                $operators = (new User)->operatorList($user->id);
                return new CustomCollection($operators, 'App\Http\Resources\V1\User\OperatorResource');
                }else{
                    return response()->json(setErrorResponse(__('user.UserController.MasterUserNotFound')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            }
            return response()->json(setErrorResponse(__('user.UserController.notFound')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }


    /**
     * Contact Us
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function contactUs()
    {
        try {
            if(isset(request()->user_id) && request()->current_operator_id)
            {
                $userId=request()->user_id;
                $operatorId=request()->current_operator_id;
            }else{
                $userId=Auth::user()->id;
                $operatorId=auth()->user()->user_relation->master_user_id;
            }
            if($userId){
                $contactInfo = [];
                $user = User::where('id',$userId)->whereNull('deleted_at')->first();
                $operator = MasterUser::where('id',$operatorId)->whereNull('deleted_at')->first();
                if(empty($user)){
                    return response()->json(setErrorResponse(__('user.UserController.notFound')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
                if(empty($operator)){
                    return response()->json(setErrorResponse(__('user.UserController.operatorNotFound')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
                if ($operator->enable_custom_contact_info == 1) {
                    $contactInfo['id'] = $user->user_relation->user_id ?? '';
                    $contactInfo['user_type'] = 'user';
                    $contactInfo['phone'] = $user->user_relation->contact_no ?? '';
                    $contactInfo['email'] = $user->user_relation->contact_email ?? '';
                    $contactInfo['alternate_email'] = '';
                    $contactInfo['address'] = $user->user_relation->contact_address ?? '';
                    $contactInfo['latitude'] = $user->user_relation->contact_lat ?? '';
                    $contactInfo['longitude'] = $user->user_relation->contact_long ?? '';
                    $contactInfo['website'] = $user->user_relation->contact_website ?? '';
                } else {
                    $contactInfo['id'] = $operator->id ?? '';
                    $contactInfo['user_type'] = 'operator';
                    $contactInfo['phone'] = $operator->contact_no ?? '';
                    $contactInfo['email'] = $operator->email ?? '';
                    $contactInfo['alternate_email'] = $operator->alternate_email ?? '';
                    $contactInfo['address'] = $operator->address ?? '';
                    $contactInfo['latitude'] = $operator->latitude ?? '';
                    $contactInfo['longitude'] = $operator->longitude ?? '';
                    $contactInfo['website'] = $operator->website ?? '';
                }
                return response()->json(setResponse($contactInfo, ['message' => __('user.UserController.contactInfo')]))->setStatusCode(Response::HTTP_OK);
            }else{
                return response()->json(setErrorResponse(__('user.UserController.notFound')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Submit Help Form
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function submitHelp(HelpFormRequest $request)
    {
        try {
            $name = !empty($request->name) ? $request->name : auth()->user()->name;
            $email = !empty($request->email) ? $request->email : auth()->user()->email;
            $phone = isset($request->phone) ? $request->phone : '-';
            $message = strip_tags($request->message);
            \DB::table('help')->insert([
                'user_id' => auth()->user()->id,
                'master_user_id' => auth()->user()->user_relation->master_user_id,
                'message' => $request->message,
                'phone' => isset($request->phone) ? $request->phone : '',
                'created_at' => date('Y-m-d h:i:s'),
            ]);
            //Checking for email address if smartawards contains only digits before @
            $smartAwardsMailSendFlag = smartAwardsEmailSendResctrict($email);
            if($smartAwardsMailSendFlag == 1){
                $helpJob = (new \App\Jobs\HelpSendMailJob(auth()->user(), $message, $phone, env('HELP_EMAIL'),$name,$email))->delay(env('QUEUE_JOB_DELAY_TIME'));
                dispatch($helpJob);
            }
            return response()->json(setResponse([], ['message' => __('user.UserController.submitContact')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Home Screen Detail.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function homeScreen($id)
    {
        try {
            $userRelation = auth()->user()->user_relation;
            $user_data=auth()->user();
            $trainingCourseIds = (new TrainingCourse)->getUserAssignCourseIds();
            $trainingCourseProgressIds = TrainingCourseProgress::where('user_id', auth()->user()->id)->where('course_progress', '>', 0)->where('course_progress', '<', 100)->whereIn('training_course_id', $trainingCourseIds)->pluck('training_course_id')->toArray();
            $trainingCourseProgIds = array_unique($trainingCourseProgressIds);
            $operatorId = $userRelation->master_user_id;
            if($user_data->assessor_role=='Yes'){
                $this->UpdateDeeplink($id, auth()->id());
            }
            $assessorData = $this->GetAssessorData(auth()->id(), $id);

            // checking for assessment portal feature setting
           
            $features = getAccessFeatures($operatorId, $operatorId); // operator id / parent id

            $assessorRole = 0;
            if(!empty($features)){
                // check for accessor potal access to user
                if((isset($features['assessment_portal']) && $features['assessment_portal'] == 0)){
                    $assessorRole = 0;
                }else{
                    $assessorRole = ($assessorData['assessor_role'] == "Yes") ? 1 : 0;
                }
            }

            if (!empty($assessorData['qr_code'])) {
                $QRCode = env('CDN_URL') . 'users/qrcode/' . auth()->id() . '/' . $assessorData['qr_code'];
            } else {
                $QRCode = '';
            }

            if (!empty($trainingCourseProgIds)) {
                $courseList = TrainingCourse::with('courseProgress:training_course_id,user_id,course_progress,is_reset',
                    'modules:training_course_id,name,id,module_lock,duration_unlock_datetime')
                    ->select('id', 'title', 'description', 'primary_image','single_module_course','single_submodule','total_modules')
                    ->whereIn('id', $trainingCourseProgIds)->where(['status' => 'Active', 'publish_now' => 1])->limit(config('constants.mobile_app.course'))->latest()->get();
                $list = new CustomCollection($courseList, 'App\Http\Resources\V1\OnGogingTrainingCourseResource');
                $info['user_id'] = auth()->id();
                $VertualBuddy=MasterUser::where('id',$userRelation->operator->id)->value('virtual_buddy');
                $get_stream['get_stream_user_id'] =$user_data->get_stream_user_id;
                $get_stream['get_stream_token'] = $user_data->get_stream_token;
                $get_stream['is_mfa_enable'] = $user_data->is_mfa_enable;
                $info['master_user_id'] = $id;
                $data['data']['user_info']=$get_stream;
                $data['data']['assessor']['assessor_id'] = $assessorData['assessor_id'];
                $data['data']['assessor']['assessor_role'] = $assessorRole;
                $data['data']['assessor']['qr_code'] = $QRCode;
                $data['data']['assessor']['qr_code_link'] = $assessorData['qr_code_link'];
                $data['data']['total_unread_count'] = Notifications::totalUnreadCount($info);
                $data['data']['current_operator']['id'] = $userRelation->operator->id;
                $data['data']['current_operator']['name'] = $userRelation->operator->name;
                $data['data']['current_operator']['email'] = $userRelation->operator->email;
                $data['data']['current_operator']['unique_id'] = $userRelation->operator->unique_id;
                $data['data']['current_operator']['image'] = $userRelation->operator->image_url;
                $data['data']['current_operator']['enable_manager_email'] = $userRelation->operator->enable_manager_email;
                $data['data']['current_operator']['enable_unique_id'] = $userRelation->operator->enable_unique_id;
                $data['data']['current_operator']['manager_email'] = $userRelation->manager_email ?? '';
                $data['data']['current_operator']['current_user_ouc'] = $userRelation->unique_id ?? '';
                $data['data']['current_operator']['product_type_grid_view'] = $userRelation->operator->set_product_type_grid_view ?? '';
                $data['data']['current_operator']['virtual_buddy'] = (int)$VertualBuddy;
                $data['data']['ongoing_course'] = $list;
                return $data;
            } else {
                $VertualBuddy=MasterUser::where('id',$userRelation->operator->id)->value('virtual_buddy');
                $info['user_id'] = auth()->id();
                $get_stream['get_stream_user_id'] =$user_data->get_stream_user_id;
                $get_stream['get_stream_token'] = $user_data->get_stream_token;
                $get_stream['is_mfa_enable'] = $user_data->is_mfa_enable;
                $info['master_user_id'] = $id;
                $total = Notifications::totalUnreadCount($info);
                $data['id'] = $userRelation->operator->id;
                $data['name'] = $userRelation->operator->name;
                $data['email'] = $userRelation->operator->email;
                $data['unique_id'] = $userRelation->operator->unique_id;
                $data['image'] = $userRelation->operator->image_url;
                $data['enable_manager_email'] = $userRelation->operator->enable_manager_email;
                $data['enable_unique_id'] = $userRelation->operator->enable_unique_id;
                $data['manager_email'] = $userRelation->manager_email ?? '';
                $data['current_user_ouc'] = $userRelation->unique_id ?? '';
                $data['product_type_grid_view'] = $userRelation->operator->set_product_type_grid_view ?? '';
                $data['virtual_buddy'] = (int)$VertualBuddy;
                $assessor['assessor_id'] = $assessorData['assessor_id'];
                $assessor['assessor_role'] = $assessorRole;
                $assessor['qr_code'] = $QRCode;
                $assessor['qr_code_link'] =  $assessorData['qr_code_link'];
                return response()->json(setResponse(['message' => __('user.TrainingCourse.notFound'), 'user_info' => $get_stream, 'total_unread_count' => $total, 'assessor' => $assessor, 'current_operator' => $data]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function UpdateDeeplink($operatorId, $userId)
    {
        $actualURL = url(route('users.AssessorAssign')). '?type=assessorAssign&operator_id=' . $operatorId . '&user_id=' . $userId;
        $androidactualURL = url(route('users.AssessorAssign')). '?type=assessorAssign&operator_id=' . $operatorId . '&user_id=' . $userId;
        // $AndroiddynamicUrl = generateFirebaseDeepLink($androidactualURL, $operatorId);
        $deeplinkUrl =$this->deeplinkService->generateDeeplink([
                                    'target_url' => $androidactualURL,
                                    'operator_id' => $operatorId,
                                    'type' => 'assessorAssign',
                                    'entity_id' => $operatorId,
                                    'entity_type' => 'assessorAssign',
                                ]);
        $AndroiddynamicUrl=$deeplinkUrl;
        // $dynamicUrl = generateFirebaseDeepLink($actualURL, $operatorId) . '?type=assessorAssign&operator_id=' . $operatorId . '&user_id=' . $data['id'];
        $deeplinkUrl =$this->deeplinkService->generateDeeplink([
                                    'target_url' => $actualURL,
                                    'operator_id' => $operatorId,
                                    'type' => 'assessorAssign',
                                    'entity_id' => $operatorId,
                                    'entity_type' => 'assessorAssign',
                                ]);
        $dynamicUrl=$deeplinkUrl;
        $qrCode = generateQRCodeAssessor($dynamicUrl);
        $file = storage_path('/qrcodes/') . $qrCode;
        \Storage::disk('s3')->put(getUsersPath() . '/qrcode/' . $userId . '/' . $qrCode, file_get_contents($file));
        User::where('id', $userId)->update(['qr_code' => $qrCode,'qr_code_link' => $AndroiddynamicUrl]);
    }

    /**
     * Get Managers from OUC
     *
     * @return \Illuminate\Http\Response
     */
    public function getOperatorsFromOuc(OperatosFromOucRequest $request)
    {
        $user = auth()->user();
        $key = $request->value;
        try {
            $operatorId = $user->user_relation->master_user_id;
            $operatorData = MasterUser::leftJoin('groups', 'groups.email', '=', 'master_users.email')->select('master_users.id', 'master_users.email', 'master_users.unique_id');
            if ($request->type == 'email') {
                $operatorData->where('master_users.email', '=', $key);
            } else {
                $operatorData->where('master_users.unique_id', '=', $key);
            }
            $operatorData = $operatorData->Where(function ($query) use ($operatorId) {
                $query->where('master_users.parent_id', $operatorId)
                    ->orWhere('master_users.id', '=', $operatorId);
            })->get();
            $message = (!is_null($operatorData)) ? __('operator.FrontUser.managerFound') : __('operator.FrontUser.managerNotFound');
            return response()->json(setResponse($operatorData, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get User Unread Notification count
     *
     * @return \Illuminate\Http\Response
     */
    public function notificationCount(Request $request)
    {
        try {
            $user = auth()->user();
            $data['user_id'] = $user->id;
            $data['master_user_id'] = $user->user_relation->master_user_id;
            $newNotificationCount = Notifications::totalUnreadCount($data);
            return response()->json(setResponse($newNotificationCount, ['message' => __('user.UserController.notificationList')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Assign User via Invite Link
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function assignUserInviteLink(AssignUserInviteLinkRequest $request)
    {
        try {
            $assignUser = $this->assignUserInviteLinksRepository->assignUserInviteLink($request->all());
            return response()->json(setResponse([], ['message' => __('user.UserController.assignUserInviteLink')]))
                ->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }


    /**
     * Assessor assign
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function assessorAssignByEmail(Request $request) {
        try {
            $user = auth()->user();
            if (User::where('email', '=', $request['email'])->exists()) {
            if ($user->assessor_role == 'No') {
                return response()->json(setErrorResponse(__('Not an assessor user.')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }
            if ($user->email == $request['email']) {
                return response()->json(setErrorResponse(__('user.UserController.notAssessorAssign')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else {
                $assessorMasterUserId = $user->user_relation->master_user_id;
                $userMasterUserId = $request->operator_id;

                if ($assessorMasterUserId == $userMasterUserId) {
                $record = $this->assignUserInviteLinksRepository->AssessorAssignByEmail($request->all());
                if ($record) {
                    return response()->json(setResponse([], ['message' => __('user.UserController.assessorAssigned')]))
                            ->setStatusCode(Response::HTTP_OK);
                } else {
                    return response()->json(setErrorResponse(__('user.UserController.alreadyAssignedAssessor')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
                } else {
                return response()->json(setErrorResponse(__('user.UserController.userNotAbleToAssign')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            }
            } else {
            return response()->json(setErrorResponse(__('user.UserController.EmailNotExits')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
        }

    /**
     * Assessor assign
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function assessorAssign(Request $request)
    {
        try {
            $user = auth()->user();
            if ($user->id == $request['user_id']) {
                    return response()->json(setError(['message' => __('user.UserController.notAssessorAssign')]))
                    ->setStatusCode(Response::HTTP_OK);
            }else{

                $assessorMasterUserId = $user->user_relation->master_user_id;
                $userMasterUserId = $request->operator_id;

                if($assessorMasterUserId == $userMasterUserId){
                    $record = $this->assignUserInviteLinksRepository->AssessorAssign($request->all());
                    if ($record) {
                        return response()->json(setResponse([], ['message' => __('user.UserController.assessorAssigned')]))
                            ->setStatusCode(Response::HTTP_OK);
                    } else {
                        return response()->json(setError(['message' => __('user.UserController.alreadyAssignedAssessor')]))
                            ->setStatusCode(Response::HTTP_OK);
                    }
                }else{
                    return response()->json(setResponse([], ['message' => __('user.UserController.userNotAbleToAssign')]))
                            ->setStatusCode(Response::HTTP_OK);
                }

            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get assessor assigned list
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getAssessorAssignUser(Request $request)
    {
        try {
            $record = $this->assignUserInviteLinksRepository->GetAssessorAssignUserList($request->isASC);
            $userlist = GetAssessorAssignUserListResource::collection($record);
            $message = (count($userlist) > 0) ? __('user.UserController.assessorListFound') : __('user.UserController.assessorListNotFound');
            return response()->json(setResponse($userlist, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Assessor Unassign
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function assessorUnassign($id)
    {
        try {
            AssessorAssignUser::where('id', $id)->delete();
            return response()->json(setResponse([], ['message' => __('user.UserController.assessorUnassigned')]))
                ->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    public function GetAssessorData($userid, $masterUserId)
    {
        $userId = UserRelation::where('user_id', $userid)->where('master_user_id', $masterUserId)->pluck('user_id');
        $UserDetail = User::where('id', $userid)->first();
        return $UserDetail;
    }
    public function getGetStreamDetails($id)
    {
        $data=app(GetStreamService::class)->Export($id);
        return $data;
    }
}
