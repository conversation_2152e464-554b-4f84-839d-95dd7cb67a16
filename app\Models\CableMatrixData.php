<?php

namespace App\Models;

use DBTableNames;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CableMatrixData extends Model
{
    use SoftDeletes;

    protected $table = 'cable_matrix_data';

    protected $fillable = [
        'question_id', 'master_user_id', 'cable_diameter', 'cable_type', 
        'cable_description', 'additional_data'
    ];

    protected $casts = [
        'additional_data' => 'array'
    ];

    // Relations
    public function question()
    {
        return $this->belongsTo(TrainingCourseSubModulePracticalAssessmentQuestion::class, 'question_id', 'id');
    }

    public function masterUser()
    {
        return $this->belongsTo(MasterUser::class, 'master_user_id', 'id');
    }

    // Scopes
    public function scopeByQuestion($query, $questionId)
    {
        return $query->where('question_id', $questionId);
    }

    public function scopeByDiameter($query, $diameter)
    {
        return $query->where('cable_diameter', $diameter);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('cable_type', $type);
    }

    public function scopeSearch($query, $searchTerm)
    {
        return $query->where(function ($q) use ($searchTerm) {
            $q->where('cable_diameter', 'LIKE', "%{$searchTerm}%")
              ->orWhere('cable_type', 'LIKE', "%{$searchTerm}%")
              ->orWhere('cable_description', 'LIKE', "%{$searchTerm}%")
              ->orWhereRaw("JSON_SEARCH(additional_data, 'all', ?) IS NOT NULL", ["%{$searchTerm}%"]);
        });
    }

    // Helper methods
    public static function getDistinctDiameters($questionId, $masterUserId)
    {
        return self::byQuestion($questionId)
            ->where('master_user_id', $masterUserId)
            ->whereNotNull('cable_diameter')
            ->where('cable_diameter', '!=', 'No Data')            
            ->distinct()
            ->orderBy('cable_diameter')
            ->pluck('cable_diameter');
    }

    public static function getDistinctTypes($questionId, $masterUserId, $diameter = null)
    {
        $query = self::byQuestion($questionId)
            ->where('master_user_id', $masterUserId)
            ->whereNotNull('cable_type')
            ->where('cable_type', '!=', '0');

        if ($diameter) {
            $query->byDiameter($diameter);
        }

        return $query->distinct()
            ->orderBy('cable_type')
            ->pluck('cable_type');
    }

    public static function getDescriptions($questionId, $masterUserId, $diameter = null, $type = null, $page = 1, $perPage = 20)
    {
        $query = self::byQuestion($questionId)
            ->where('master_user_id', $masterUserId);

        if ($diameter) {
            $query->byDiameter($diameter);
        }

        if ($type) {
            $query->byType($type);
        }

        return $query->select('cable_description', 'additional_data')
            ->orderBy('cable_description')
            ->paginate($perPage, ['*'], 'page', $page);
    }

    public static function searchPaginated($questionId, $masterUserId, $searchTerm, $page = 1, $perPage = 20)
    {
        return self::byQuestion($questionId)
            ->where('master_user_id', $masterUserId)
            ->search($searchTerm)
            ->select('cable_diameter', 'cable_type', 'cable_description', 'additional_data')
            ->orderBy('cable_description')
            ->paginate($perPage, ['*'], 'page', $page);
    }
}
