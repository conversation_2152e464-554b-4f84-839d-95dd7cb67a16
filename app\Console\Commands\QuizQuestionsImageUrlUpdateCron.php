<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\TrainingCourseSubModuleQuizQuestion;

class QuizQuestionsImageUrlUpdateCron extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'image:url-update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command is for updating quiz_questions table image names';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $quiz_images = TrainingCourseSubModuleQuizQuestion::select('id','question_image','answer_image')->get();

        foreach($quiz_images as $quiz_image){
            $question_image = explode("/", $quiz_image->question_image);
            $question_image_name = $question_image[count($question_image)-1];
           
            $answer_image = explode("/", $quiz_image->answer_image);
            $answer_image_name = $answer_image[count($answer_image)-1];

            TrainingCourseSubModuleQuizQuestion::where('id','=',$quiz_image->id)->update(['question_image' => $question_image_name, 'answer_image' => $answer_image_name]);

        }
       
    }
}
