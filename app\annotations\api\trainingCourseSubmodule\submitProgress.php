<?php
/**
     * @OA\Post(
     *     path="/api/trainingCourseSubmodule/submitProgress",
     *     tags={"Mobile - Training Course Submodule"},
     *     summary="Submit progress for all submodule",
     *     description="Submit progress for all submodule",
     *     operationId="submitProgress",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course ID",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="module_id",
     *                     description="Module ID",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="submodule_id",
     *                     description="Submodule ID",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="submodule_type_id",
     *                     description="Submodule Type ID",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="time_spent",
     *                     description="Time spent in seconds",
     *                     type="integer"
     *                 ),
     *                  @OA\Property(
     *                     property="touch_count",
     *                     description="Touch count in seconds",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="id",
     *                     description="Comma seprated id for confirmation boxes, video guide steps, image gallery, photo hotspot uploader and single id for happy unhappy facts, image gallery, product list, image with hotspot, photo hotspot uploader",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="is_happy_visited",
     *                     description="Value[0/1] for happy unhappy facts",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="is_unhappy_visited",
     *                     description="Value[0/1] for happy unhappy facts",
     *                     type="integer"
     *                 ), 
     *                 @OA\Property(
     *                     property="video_guide_id",
     *                     description="Video guide ID",
     *                     type="integer"
     *                 ),
     *                 example={"training_course_id": 1,"module_id": 1,"submodule_id": 16,"submodule_type_id":1,"time_spent": 5, "touch_count":5, "id": "5,6", "is_happy_visited":1, "is_unhappy_visited":1, "video_guide_id":20, "has_button_click":1}
     *              )
     *          )
     *      ),
     *     security={
     *         {"User": {}}
     *     },
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */