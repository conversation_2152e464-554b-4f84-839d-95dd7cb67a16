<?php
/**
     * @OA\Post(
     *     path="/api/trainingCourseSubmodule/uploadPracticalAssessmentMediaComment",
     *     tags={"Mobile - Training Course Submodule"},
     *     summary="Upload Practical Assessment Media Comment ",
     *     description="Upload Practical Assessment Media Comment",
     *     operationId="uploadPracticalAssessmentMediaComment",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="module_id",
     *                     description="Module Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="submodule_id",
     *                     description="Submodule Id",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="user_id",
     *                     description="User Id",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="question_id",
     *                     description="Question Id",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="comment",
     *                     description="Comment",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                      property="media",
     *                      description="Image/Video",
     *                      type="string"
     *                  ),
     *                 @OA\Property(
     *                     property="media_type",
     *                     description="Media Type : image/video",
     *                     type="string"
     *                 ),
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */