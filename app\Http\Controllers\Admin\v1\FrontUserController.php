<?php

namespace App\Http\Controllers\Admin\v1;

use DBTableNames;
use App\Models\User;
use App\Models\UserGroup;
use App\Models\MasterUser;
use App\Models\UserRelation;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\AssessorAssignUser;
use App\Services\GetStreamService;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Http\Resources\CustomCollection;
use Illuminate\Support\Facades\Validator;
use App\Http\Resources\V1\User\UserResource;
use Illuminate\Support\Facades\Notification;
use App\Http\Resources\Admin\v1\FrontUserResource;
use App\Repositories\Admin\v1\FrontUserRepository;
use App\Http\Requests\Admin\v1\CommonListingRequest;
use App\Http\Requests\Admin\v1\VerifyRegisteredUser;
use App\Http\Requests\Admin\v1\AssignOperatorRequest;
use App\Notifications\VerifyRegisteredUserNotification;
use App\Http\Resources\Admin\v1\FrontUserDetailResource;
use App\Http\Resources\Admin\v1\AssignOperatorListResource;
use App\Http\Resources\Admin\v1\FrontUserOperatorListResource;
use App\Http\Requests\Admin\v1\FrontUserBulkStatusChangeRequest;

class FrontUserController extends Controller
{

    /*
      |--------------------------------------------------------------------------
      | Front User Controller
      |--------------------------------------------------------------------------
      |
      | This controller handles user added by operator after admin login.
      |
    */
    protected $model;

    protected $user_repository;

    public function __construct() {
        $this->model = new User();
        $this->user_repository = new FrontUserRepository($this->model);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {

    }

    /**
     * Add User
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // return $request->all();
        try {
            $result = DB::transaction(function () use ($request) {
                $moduleData = $request;
                $this->user_repository->create($moduleData);
            });
            return response()->json(setResponse([], ['message' => __('User Added Successfully')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update User details
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            $this->user_repository->update($request, $id);
            return response()->json(setResponse([], ['message' => __('User updated successfully')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Delete User
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request) {
        try {
            $operatorId = auth()->guard('admin')->user()->id;
            $ids = is_array($request->ids)? $request->ids: [];
            $this->model->destroy($ids);
            foreach($ids as $id) {
                AssessorAssignUser::whereMasterUserId($operatorId)->whereUserId($id)->delete();
                }
            app(GetStreamService::class)->DeleteUserGetStream($ids);
            return response()->json(setResponse([], ['message' => __('admin.FrontUser.delete')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * User group detail found
     *
     * @return \Illuminate\Http\Response
     */
    public function getAllUserGroup(Request $request)
    {
        try {
            $masterUserId=auth()->guard('admin')->id();
            $allGroups=UserGroup::whereStatus('Active')->whereMasterUserId($masterUserId)->select('id','name')->get();
            $message = !empty($allGroups) ? __('User group detail found.') : __('User group not found.');
            return response()->json(setResponse($allGroups, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function getUserGroupDetailById($id)
    {
        try {
            $groupDetail=UserGroup::findOrFail($id, ['id','name', 'manager_email','unique_id','contact_no','email','latitude','longitude','website','status']);
            $message = !empty($groupDetail) ? __('User group detail found.') : __('User group not found.');
            return response()->json(setResponse($groupDetail, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * List All Users
     *
     * @return \Illuminate\Http\Response
     */
    public function getUsersList(CommonListingRequest $request)
    {
        try {
            $users = $this->user_repository->getUsersListing($request->all());
            if ($request->isExport) {
                return $this->user_repository->exportCsv($users->get(), $request->exportFields);
            }
            return FrontUserResource::collection($users->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get User profile details
     *
     * @return \Illuminate\Http\Response
     */
    public function getUserProfile($id)
    {
        try {
            $user = $this->model->find($id);
            return ($user) ?
                    (new FrontUserDetailResource($user)) :
                    response()->json(setErrorResponse(__('admin.FrontUser.NotFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * User Change Status
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function changeStatus(FrontUserBulkStatusChangeRequest $request)
    {
        try {
            $isDisabled = ($request->status === 'Active')? 0: 1;
            $this->model->whereIn('id', $request->ids)->update(['status' => $request->status]);
            app(GetStreamService::class)->UserChangeStatus($request->ids,$request->status);
            UserRelation::whereIn('user_id', $request->ids)->update(['is_disabled' => $isDisabled]);
            return response()->json(setResponse([], ['message' => __('admin.FrontUser.status')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * List All Users Operator
     *
     * @return \Illuminate\Http\Response
     */
    public function getUserOperatorList(CommonListingRequest $request)
    {
        try {
            $operatorList = $this->user_repository->getOperatorList($request->all());
            if($operatorList){
                return FrontUserOperatorListResource::collection($operatorList->paginate($request->per_page));
            }else{
                return response()->json(setErrorResponse(__('admin.FrontUser.operatorNotFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get All Operators
     *
     * @return \Illuminate\Http\Response
     */
    public function getAllOperators(Request $request)
    {
        try {
            $userId = $request->user_id ?? 0;
            if($userId != 0){
                $currentUser = User::find($userId);
                if($currentUser->user_relation){
                    $userCurrentOperatorId = $currentUser->user_relation->master_user_id;
                    $notRequireOperatorList = [1,2,$userCurrentOperatorId];
                }else{
                    $notRequireOperatorList = [1,2];
                }
            }else{
                $notRequireOperatorList = [1,2];
            }
            $operatorList = MasterUser::select('id','name','company_name','email', 'image', DB::raw("IF($userId = 0, '', $userId) AS user_id"))->whereNull('parent_id')->whereNotIn('id',$notRequireOperatorList)->whereUserType('Operator')->orderBy('name')->get();
            return AssignOperatorListResource::collection($operatorList);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Assigned Operators
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function assignOperators(AssignOperatorRequest $request)
    {
        try {
            $user = User::find($request->user_id);
            $oldSelectedOperators = $user->assignedOperators()->pluck('is_current_operator','master_user_id')->toArray();

            if(isset($request->operator_list) && count($request->operator_list) > 0){
                $oldSelectedOperatorIds = array_keys($oldSelectedOperators);
                $currentOperatorId = array_search(1,$oldSelectedOperators);
                $existedOperators = [];
                $cnt = 0;
                foreach($request->operator_list as $operatorId){

                    $existUser = UserRelation::whereUserId($request->user_id)->whereMasterUserId($operatorId)->first();
                    if(!$existUser){
                        $operatorData = MasterUser::whereId($operatorId)->first();
                        if(count($oldSelectedOperators) > 1){
                            $this->setOperatorData($operatorData, $request->user_id, 0);
                        }else{
                            if($cnt == 0){
                                $this->setOperatorData($operatorData, $request->user_id, 1);
                            }else{
                                $this->setOperatorData($operatorData, $request->user_id, 0);
                            }
                            $cnt++;
                        }
                    }
                    $existedOperators[] = $operatorId;
                }

                $deletedOperatorList = array_diff($oldSelectedOperatorIds, $request->operator_list);
                if(count($deletedOperatorList) > 0){
                    if (($key = array_search(1, $deletedOperatorList)) !== false) {
                        unset($deletedOperatorList[$key]);
                    }
                    if (($key = array_search($currentOperatorId, $deletedOperatorList)) !== false) {
                        unset($deletedOperatorList[$key]);
                    }
                    UserRelation::whereUserId($request->user_id)->whereIn('master_user_id', $deletedOperatorList)->delete();
                }
            }else{
                UserRelation::whereUserId($request->user_id)->where('master_user_id','!=',1)->delete();
            }
            return response()->json(setResponse([], ['message' => __('admin.FrontUser.assigned')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }


    public function setOperatorData($operatorData, $userId, $currentOperatorStatus){
        try {
            if($operatorData->enable_manager_email == 0 && $operatorData->enable_unique_id == 0){
                UserRelation::create([
                    'user_id' => $userId,
                    'master_user_id' => $operatorData->id,
                    'manager_id' => $operatorData->id,
                    'manager_email'=> $operatorData->email,
                    'unique_id'=> $operatorData->unique_id,
                    'is_current_operator' => $currentOperatorStatus
                ]);
            }elseif($operatorData->enable_manager_email == 0){
                UserRelation::create([
                    'user_id' => $userId,
                    'master_user_id' => $operatorData->id,
                    'manager_id' => $operatorData->id,
                    'manager_email'=> $operatorData->email,
                    'is_current_operator' => $currentOperatorStatus
                ]);
            }elseif($operatorData->enable_unique_id == 0){
                UserRelation::create([
                    'user_id' => $userId,
                    'master_user_id' => $operatorData->id,
                    'manager_id' => $operatorData->id,
                    'unique_id'=> $operatorData->unique_id,
                    'is_current_operator' => $currentOperatorStatus
                ]);
            }else{
                UserRelation::create([
                    'user_id' => $userId,
                    'master_user_id' => $operatorData->id,
                    'is_current_operator' => $currentOperatorStatus
                ]);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * verify Registered User By Admin Panel
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function verifyRegisteredUser(VerifyRegisteredUser $request) {
        try {
            $result = $this->model::where('id','=',$request->user_id)->update(['status'=>'Active', 'email_verified_at'=>date('Y-m-d h:i:s')]);
            if($result){
                $user = $this->model::find($request->user_id);
                //Checking for email address if smartawards contains only digits before @
                $smartAwardsMailSendFlag = smartAwardsEmailSendResctrict($user->email);
                if($smartAwardsMailSendFlag == 1){
                    Notification::send($user, new VerifyRegisteredUserNotification($user,'Admin'));
                }
            }
            return response()->json(setResponse([], ['message' => __('admin.FrontUser.verified')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
