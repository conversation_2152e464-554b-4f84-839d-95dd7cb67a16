<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\MasterUser;
use Illuminate\Console\Command;
use App\Services\GetStreamService;
use Illuminate\Support\Facades\DB;

class DeactivateUserOnGetStream extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'deactive:get-stream-user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Deactive Deleted User on Get Stream';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $record= DB::table('master_users')->select('get_stream_user_id')->where('get_stream_user_id','!=',"")->whereNotNull('deleted_at')->get()->toArray();
        foreach($record as $value){
            app(GetStreamService::class)->DeactivateUser($value->get_stream_user_id);
        }
        $users= DB::table('users')->select('get_stream_user_id')->where('get_stream_user_id','!=',"")->whereNotNull('deleted_at')->get()->toArray();
        foreach($users as $value){
            app(GetStreamService::class)->DeactivateUser($value->get_stream_user_id);
        }

    }
}
