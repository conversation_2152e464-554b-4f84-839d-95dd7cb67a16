<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\TrainingCourse;
use App\Http\Controllers\Controller;
use App\Models\PracticalAssessmentSubmoduleUser;
use App\Http\Resources\V1\AssessmentUsersResource;
use App\Repositories\PracticalAssessmentRepository;
use App\Http\Resources\V1\AssessmentHistoryResource;
use App\Http\Requests\V1\PracticalAssessmentUserRequest;
use App\Http\Resources\V1\AssessmentUserHistoryResource;
use App\Models\TrainingCourseSubModulePracticalAssessmentAnswer;
use App\Models\TrainingCourseSubModulePracticalAssessmentQuestion;
use App\Models\TrainingCourseSubModulePracticalAssessmentAnswersJson;
use App\Repositories\TrainingCourseSubModulePracticalAssessmentRepository;

class PracticalAssessmentSubModuleController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
     */

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    protected $model;

    protected $repository;

    public function __construct()
    {
        $this->model = new PracticalAssessmentSubmoduleUser();
        $this->repository = new PracticalAssessmentRepository($this->model);
        $this->PracticalAssessmentAnswer = new TrainingCourseSubModulePracticalAssessmentAnswersJson();
        $this->PracticalAssessmentRepository = new TrainingCourseSubModulePracticalAssessmentRepository($this->PracticalAssessmentAnswer);
    }

    /**
     * Practical Assessment Submodule User Add
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function addAssessmentUsers(PracticalAssessmentUserRequest $request)
    {
        try {
            $this->repository->startSessionUsersCreate($request->all());
            return response()->json(setResponse([], ['message' => __('user.PracticalAssessment.addedUser')]))
                ->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Assessor PQMS History
     *
     * @return \Illuminate\Http\Response
     */
    public function getAssessorHistory(Request $request)
    {
        try {
            $assessmentHistory = $this->repository->getAssessorHistory();
            $assessmentHistorylist = AssessmentHistoryResource::collection($assessmentHistory);
            $message = (count($assessmentHistorylist) > 0) ? __('user.PracticalAssessment.SubModuleListFound') : __('user.PracticalAssessment.SubModuleListNotFound');
            return response()->json(setResponse($assessmentHistorylist, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

      /**
     * Get Users With Result
     *
     * @return \Illuminate\Http\Response
     */
    public function getUsers($id)
    {
        try {
            $assessmentUserHistory = $this->repository->getAssessorUserHistory($id);
            $assessmentUserHistorylist = AssessmentUserHistoryResource::collection($assessmentUserHistory);
            $message = (count($assessmentUserHistorylist) > 0) ? __('user.PracticalAssessment.userListFound') : __('user.PracticalAssessment.userListNotFound');
            return response()->json(setResponse($assessmentUserHistorylist, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

     /**
     * Get Users With Result
     *
     * @return \Illuminate\Http\Response
     */
    public function getUserResult(Request $request,$id)
    {
        try {
            $submoduleId = $request->input('submoduleid');
            $assessmentUserHistory = $this->repository->getSelfAssessmentUserResult($id,$submoduleId);
            $assessmentUserHistorylist = AssessmentUserHistoryResource::collection($assessmentUserHistory);
            $message = (count($assessmentUserHistorylist) > 0) ? __('user.PracticalAssessment.userListFound') : __('user.PracticalAssessment.ResultNotGenerated');
            return response()->json(setResponse($assessmentUserHistorylist, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Selected Users to Start Session
     *
     * @return \Illuminate\Http\Response
     */
    public function getAssessmentUsers(Request $request)
    {
        try {
            $assessmentUsers = $this->repository->getAssessmentUsers($request->all());
            $assessmentUserslist = AssessmentUsersResource::collection($assessmentUsers);
            $message = (count($assessmentUserslist) > 0) ? __('user.PracticalAssessment.userListFound') : __('user.PracticalAssessment.userListNotFound');
            return response()->json(setResponse($assessmentUserslist, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get auto saved submodule detail
     *
     * @return \Illuminate\Http\Response
     */
    public function getAutoSaveSubmoduleDetail(Request $request)
    {
        try {
            $autoSaveDetail = $this->repository->getAutoSaveSubmoduleDetail($request->all());
            if (!empty($autoSaveDetail)) {
                $content = json_decode($autoSaveDetail->content);
                $message = 'user.PracticalAssessment.detailFound';
            } else {
                $content = [];
                $message = 'user.PracticalAssessment.detailNotFound';
            }
            return response()->json(setResponse($content, ['message' => __($message)]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
