<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;

class ImageRequest extends CustomFormRequest {

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'image' => 'required|mimes:jpeg,jpg,png',
            'type' => 'required|in:modules,submodules,courses,profile,users,notification'
        ];
    }
    
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'type.in' => __('Type must be modules, submodules, courses, profile, users'),
            'image.required' => __('Image is required'),
        ];
    }

}
