<?php

namespace Tests\Unit\Services;

use App\Models\CustomDeeplink;
use App\Models\MasterUser;
use App\Models\whiteLabelSetting;
use App\Services\DeeplinkService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class DeeplinkServiceTest extends TestCase
{
    use RefreshDatabase;

    private DeeplinkService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new DeeplinkService();
    }

    public function test_generates_unique_short_codes()
    {
        $code1 = $this->service->generateShortCode();
        $code2 = $this->service->generateShortCode();
        
        $this->assertNotEquals($code1, $code2);
        $this->assertEquals(8, strlen($code1));
        $this->assertEquals(8, strlen($code2));
        $this->assertTrue($this->service->isValidShortCode($code1));
        $this->assertTrue($this->service->isValidShortCode($code2));
    }

    public function test_validates_short_code_format()
    {
        $this->assertTrue($this->service->isValidShortCode('Abc123Xy'));
        $this->assertFalse($this->service->isValidShortCode('Abc123X')); // Too short
        $this->assertFalse($this->service->isValidShortCode('Abc123Xyz')); // Too long
        $this->assertFalse($this->service->isValidShortCode('Abc123O0')); // Contains confusing chars
    }

    public function test_creates_deeplink_successfully()
    {
        $params = [
            'target_url' => 'https://example.com/course/123',
            'operator_id' => null,
            'type' => 'training_course',
            'entity_id' => 123,
            'entity_type' => 'TrainingCourse'
        ];

        $deeplinkUrl = $this->service->generateDeeplink($params);
        
        $this->assertStringContains('/dl/', $deeplinkUrl);
        $this->assertDatabaseHas('custom_deeplinks', [
            'target_url' => $params['target_url'],
            'deeplink_type' => $params['type'],
            'entity_id' => $params['entity_id'],
            'entity_type' => $params['entity_type']
        ]);
    }

    public function test_creates_deeplink_with_operator_configuration()
    {
        // Create operator with white label settings
        $operator = MasterUser::factory()->create();
        $whiteLabelSettings = whiteLabelSetting::factory()->create([
            'operator_id' => $operator->id,
            'ios_package_name' => 'com.test.app',
            'android_package_name' => 'com.test.android',
            'ios_app_store_id' => '123456789',
            'custom_deeplink_domain' => 'https://custom.domain.com'
        ]);

        $params = [
            'target_url' => 'https://example.com/course/123',
            'operator_id' => $operator->id,
            'type' => 'training_course',
            'entity_id' => 123,
            'entity_type' => 'TrainingCourse'
        ];

        $deeplinkUrl = $this->service->generateDeeplink($params);
        
        $this->assertStringContains('custom.domain.com/dl/', $deeplinkUrl);
        $this->assertDatabaseHas('custom_deeplinks', [
            'operator_id' => $operator->id,
            'ios_package_name' => 'com.test.app',
            'android_package_name' => 'com.test.android',
            'ios_app_store_id' => '123456789'
        ]);
    }

    public function test_resolves_deeplink_successfully()
    {
        $deeplink = CustomDeeplink::factory()->create([
            'short_code' => 'test123',
            'target_url' => 'https://example.com/target',
            'is_active' => true
        ]);

        $resolved = $this->service->resolveDeeplink('test123');
        
        $this->assertNotNull($resolved);
        $this->assertEquals($deeplink->id, $resolved->id);
        $this->assertEquals('https://example.com/target', $resolved->target_url);
    }

    public function test_resolves_deeplink_with_caching()
    {
        $deeplink = CustomDeeplink::factory()->create([
            'short_code' => 'cached1',
            'target_url' => 'https://example.com/cached',
            'is_active' => true
        ]);

        // First call should cache the result
        $resolved1 = $this->service->resolveDeeplink('cached1');
        
        // Second call should use cache
        $resolved2 = $this->service->resolveDeeplink('cached1');
        
        $this->assertEquals($resolved1->id, $resolved2->id);
        
        // Verify cache was used
        $this->assertTrue(Cache::has("deeplink:cached1"));
    }

    public function test_returns_null_for_nonexistent_deeplink()
    {
        $resolved = $this->service->resolveDeeplink('notfound');
        
        $this->assertNull($resolved);
    }

    public function test_tracks_click_successfully()
    {
        $deeplink = CustomDeeplink::factory()->create([
            'short_code' => 'track123',
            'click_count' => 0
        ]);

        $metadata = [
            'user_agent' => 'Test Browser',
            'ip_address' => '127.0.0.1'
        ];

        $this->service->trackClick('track123', $metadata);

        $deeplink->refresh();
        $this->assertEquals(1, $deeplink->click_count);
        $this->assertNotNull($deeplink->last_clicked_at);
        $this->assertArrayHasKey('last_click', $deeplink->metadata);
    }

    public function test_bulk_generate_deeplinks()
    {
        $entityIds = [1, 2, 3];
        $operatorId = 1;

        $results = $this->service->bulkGenerateDeeplinks('training_course', $entityIds, $operatorId);

        $this->assertCount(3, $results);
        
        foreach ($entityIds as $entityId) {
            $this->assertArrayHasKey($entityId, $results);
            $this->assertStringContains('/dl/', $results[$entityId]);
        }

        // Verify database records
        $this->assertDatabaseCount('custom_deeplinks', 3);
    }

    public function test_handles_invalid_entity_type_in_bulk_generation()
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Unknown entity type: invalid_type');

        $this->service->bulkGenerateDeeplinks('invalid_type', [1], 1);
    }

    public function test_generates_different_codes_for_collision_avoidance()
    {
        // Create a deeplink with a specific short code
        CustomDeeplink::factory()->create(['short_code' => 'AAAAAAAA']);

        // Generate multiple codes to ensure no collision
        $codes = [];
        for ($i = 0; $i < 10; $i++) {
            $code = $this->service->generateShortCode();
            $this->assertNotEquals('AAAAAAAA', $code);
            $this->assertNotContains($code, $codes);
            $codes[] = $code;
        }
    }

    public function test_respects_expiration_in_resolution()
    {
        $expiredDeeplink = CustomDeeplink::factory()->create([
            'short_code' => 'expired1',
            'expires_at' => now()->subDay(),
            'is_active' => true
        ]);

        $resolved = $this->service->resolveDeeplink('expired1');
        
        $this->assertNull($resolved);
    }

    public function test_respects_inactive_status_in_resolution()
    {
        $inactiveDeeplink = CustomDeeplink::factory()->create([
            'short_code' => 'inactive',
            'is_active' => false
        ]);

        $resolved = $this->service->resolveDeeplink('inactive');
        
        $this->assertNull($resolved);
    }
}
