<?php
/**
     * @OA\Post(
     *     path="/api/trainingCourseSubmodule/storeQuizResult",
     *     tags={"Mobile - Training Course Submodule"},
     *     summary="Store Training Course Submodule Quiz Results",
     *     description="Store Training Course Submodule Quiz Results",
     *     operationId="storeQuizResult",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="submodule_id",
     *                     description="Submodule Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="duration",
     *                     description="duration",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="total_questions",
     *                     description="Total Questions",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="time_spent",
     *                     description="Time Spent",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="data",
     *                     description="Question and Answer id",
     *                     type="array",
     *                     @OA\Items(
     *                     type="object",
     *                          @OA\Property(
     *                              property="question_id",
     *                              description="Question Id",
     *                              type="integer"
     *                          ),
     *                          @OA\Property(
     *                              property="answer_id",
     *                              description="Answer Id",
     *                              type="integer"
     *                          )
     *                      )
     *                 ),
     *                 example={"submodule_id": 315, "duration": "05:30", "total_questions": 20, "time_spent": 20, "data":{{"question_id": 1, "answer_id": 1}, {"question_id": 10, "answer_id": 10}}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */