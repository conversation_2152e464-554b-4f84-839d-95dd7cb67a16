# Firebase to Custom Deeplink Migration - Technical Implementation Guide

## Overview

This document provides detailed technical specifications for migrating from Firebase Dynamic Links to a custom deeplink solution in the SkillBase API v2 application.

## Current Implementation State

### Firebase Dependencies

- **Package**: `kreait/laravel-firebase: ^4.2.0`
- **Main Function**: `generateFirebaseDeepLink()` in `app/Helpers/Helper.php`
- **Configuration**: `config/firebase.php`
- **Credentials**: Firebase JSON files stored in S3 and local storage

### Current Deeplink Usage Points

1. **Authentication**: Email verification, password reset
2. **Training Courses**: Course sharing, submodule access
3. **Products**: Product sharing and access
4. **Resources**: Resource sharing
5. **White Label**: Operator-specific deeplink generation

### Existing Database Tables

```sql
- white_label_settings (Firebase configuration)
- training_course_whitelabel_deeplink
- training_course_submodule_*_whitelabel_deeplink (multiple tables)
- products_whitelabel_deeplink
- resources_whitelabel_deeplink
```

## Custom Deeplink Architecture

### URL Structure

```
Base Format: {APP_URL}/dl/{short_code}
Example: https://skillsbase-api.com/dl/Kx9mP2nQ

Query Parameters:
- utm_source: Analytics tracking
- utm_medium: Medium identification
- utm_campaign: Campaign tracking
```

### Short Code Generation Algorithm

```php
// 8-character alphanumeric code
// Character set: A-Z, a-z, 0-9 (excluding confusing characters: 0, O, I, l)
// Total combinations: ~2.8 trillion
// Collision probability: <0.001% for 1M links
```

## Database Schema

### New Table: custom_deeplinks

```sql
CREATE TABLE `custom_deeplinks` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `short_code` varchar(8) NOT NULL,
    `operator_id` bigint(20) UNSIGNED NULL,
    `target_url` text NOT NULL,
    `deeplink_type` varchar(50) NOT NULL,
    `entity_id` bigint(20) UNSIGNED NULL,
    `entity_type` varchar(50) NULL,
    `ios_package_name` varchar(255) NULL,
    `android_package_name` varchar(255) NULL,
    `ios_app_store_id` varchar(100) NULL,
    `fallback_url` text NULL,
    `click_count` int(11) NOT NULL DEFAULT 0,
    `last_clicked_at` timestamp NULL,
    `is_active` tinyint(1) NOT NULL DEFAULT 1,
    `expires_at` timestamp NULL,
    `metadata` json NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `custom_deeplinks_short_code_unique` (`short_code`),
    KEY `custom_deeplinks_operator_id_index` (`operator_id`),
    KEY `custom_deeplinks_entity_index` (`entity_type`, `entity_id`),
    KEY `custom_deeplinks_type_index` (`deeplink_type`),
    KEY `custom_deeplinks_active_index` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### White Label Settings Updates

```sql
ALTER TABLE `white_label_settings` 
ADD COLUMN `custom_deeplink_domain` varchar(255) NULL AFTER `fallback_link`,
ADD COLUMN `custom_deeplink_enabled` tinyint(1) NOT NULL DEFAULT 0 AFTER `custom_deeplink_domain`;
```

## Implementation Components

### 1. DeeplinkService Class

**Location**: `app/Services/DeeplinkService.php`

**Key Methods**:

```php
public function generateDeeplink(array $params): string
public function resolveDeeplink(string $shortCode): ?CustomDeeplink
public function trackClick(string $shortCode, array $metadata = []): void
public function generateShortCode(): string
public function isValidShortCode(string $code): bool
```

**Configuration Support**:

- Operator-specific settings
- White label customization
- Fallback URL handling
- Analytics integration

### 2. DeeplinkController Class

**Location**: `app/Http/Controllers/Api/V1/DeeplinkController.php`

**Routes**:

```php
GET /dl/{shortCode} - Main deeplink resolution
GET /api/deeplinks/{shortCode}/stats - Analytics endpoint
POST /api/deeplinks - Create new deeplink (admin)
```

**User Agent Detection**:

```php
private function detectPlatform(Request $request): string
private function buildMobileRedirectUrl(CustomDeeplink $deeplink, string $platform): string
private function buildFallbackUrl(CustomDeeplink $deeplink): string
```

### 3. CustomDeeplink Model

**Location**: `app/Models/CustomDeeplink.php`

**Relationships**:

```php
public function operator(): BelongsTo
public function entity(): MorphTo
```

**Scopes**:

```php
public function scopeActive($query)
public function scopeByOperator($query, $operatorId)
public function scopeByType($query, $type)
```

## Migration Strategy

### Phase 1: Foundation Setup

1. Create database migration for `custom_deeplinks` table
2. Implement `DeeplinkService` class
3. Create `DeeplinkController` with basic resolution
4. Add routes for deeplink handling

### Phase 2: Service Integration

1. Create helper function `generateCustomDeeplink()`
2. Update white label settings schema
3. Implement operator-specific configuration
4. Add analytics and tracking capabilities

### Phase 3: Code Replacement

1. Replace `generateFirebaseDeepLink()` calls systematically
2. Update notification classes
3. Modify console commands
4. Update controller methods

### Phase 4: Data Migration

1. Create migration command for existing deeplinks
2. Update existing deeplink storage tables
3. Preserve analytics data
4. Implement cleanup procedures

## Code Examples

### DeeplinkService Implementation

```php
<?php

namespace App\Services;

use App\Models\CustomDeeplink;
use App\Models\whiteLabelSetting;
use Illuminate\Support\Str;

class DeeplinkService
{
    private const CHARSET = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789';
    private const CODE_LENGTH = 8;

    public function generateDeeplink(array $params): string
    {
        $shortCode = $this->generateShortCode();

        $deeplink = CustomDeeplink::create([
            'short_code' => $shortCode,
            'operator_id' => $params['operator_id'] ?? null,
            'target_url' => $params['target_url'],
            'deeplink_type' => $params['type'],
            'entity_id' => $params['entity_id'] ?? null,
            'entity_type' => $params['entity_type'] ?? null,
            'ios_package_name' => $this->getIosPackageName($params['operator_id']),
            'android_package_name' => $this->getAndroidPackageName($params['operator_id']),
            'fallback_url' => $params['fallback_url'] ?? $this->getDefaultFallback(),
            'metadata' => $params['metadata'] ?? null,
        ]);

        return $this->buildDeeplinkUrl($shortCode);
    }

    public function generateShortCode(): string
    {
        do {
            $code = '';
            for ($i = 0; $i < self::CODE_LENGTH; $i++) {
                $code .= self::CHARSET[random_int(0, strlen(self::CHARSET) - 1)];
            }
        } while (CustomDeeplink::where('short_code', $code)->exists());

        return $code;
    }

    private function buildDeeplinkUrl(string $shortCode): string
    {
        return env('APP_URL') . '/dl/' . $shortCode;
    }
}
```

### DeeplinkController Implementation

```php
<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\CustomDeeplink;
use App\Services\DeeplinkService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;

class DeeplinkController extends Controller
{
    private DeeplinkService $deeplinkService;

    public function __construct(DeeplinkService $deeplinkService)
    {
        $this->deeplinkService = $deeplinkService;
    }

    public function resolve(Request $request, string $shortCode): RedirectResponse
    {
        $deeplink = CustomDeeplink::where('short_code', $shortCode)
            ->where('is_active', true)
            ->first();

        if (!$deeplink || ($deeplink->expires_at && $deeplink->expires_at->isPast())) {
            return redirect()->to($this->getDefaultFallback());
        }

        // Track click
        $this->deeplinkService->trackClick($shortCode, [
            'user_agent' => $request->userAgent(),
            'ip_address' => $request->ip(),
            'referer' => $request->header('referer'),
        ]);

        $platform = $this->detectPlatform($request);

        if ($platform === 'ios' && $deeplink->ios_package_name) {
            return redirect()->to($this->buildIosUrl($deeplink));
        } elseif ($platform === 'android' && $deeplink->android_package_name) {
            return redirect()->to($this->buildAndroidUrl($deeplink));
        }

        return redirect()->to($deeplink->fallback_url ?: $deeplink->target_url);
    }

    private function detectPlatform(Request $request): string
    {
        $userAgent = strtolower($request->userAgent());

        if (strpos($userAgent, 'iphone') !== false || strpos($userAgent, 'ipad') !== false) {
            return 'ios';
        } elseif (strpos($userAgent, 'android') !== false) {
            return 'android';
        }

        return 'web';
    }

    private function buildIosUrl(CustomDeeplink $deeplink): string
    {
        $scheme = str_replace('.', '', $deeplink->ios_package_name);
        return $scheme . '://deeplink?url=' . urlencode($deeplink->target_url);
    }

    private function buildAndroidUrl(CustomDeeplink $deeplink): string
    {
        return 'intent://deeplink?url=' . urlencode($deeplink->target_url) .
               '#Intent;package=' . $deeplink->android_package_name . ';end';
    }
}
```

### Helper Function Replacement

```php
// Replace this function in app/Helpers/Helper.php
if (!function_exists('generateCustomDeeplink')) {
    function generateCustomDeeplink(string $url, string $operatorId = '', array $options = []): string
    {
        $deeplinkService = app(DeeplinkService::class);

        return $deeplinkService->generateDeeplink([
            'target_url' => $url,
            'operator_id' => $operatorId,
            'type' => $options['type'] ?? 'general',
            'entity_id' => $options['entity_id'] ?? null,
            'entity_type' => $options['entity_type'] ?? null,
            'fallback_url' => $options['fallback_url'] ?? null,
            'metadata' => $options['metadata'] ?? null,
        ]);
    }
}
```

## Database Migrations

### Create Custom Deeplinks Table

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCustomDeeplinksTable extends Migration
{
    public function up()
    {
        Schema::create('custom_deeplinks', function (Blueprint $table) {
            $table->id();
            $table->string('short_code', 8)->unique();
            $table->unsignedBigInteger('operator_id')->nullable();
            $table->text('target_url');
            $table->string('deeplink_type', 50);
            $table->unsignedBigInteger('entity_id')->nullable();
            $table->string('entity_type', 50)->nullable();
            $table->string('ios_package_name')->nullable();
            $table->string('android_package_name')->nullable();
            $table->string('ios_app_store_id', 100)->nullable();
            $table->text('fallback_url')->nullable();
            $table->integer('click_count')->default(0);
            $table->timestamp('last_clicked_at')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('expires_at')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['operator_id']);
            $table->index(['entity_type', 'entity_id']);
            $table->index(['deeplink_type']);
            $table->index(['is_active']);
            $table->index(['created_at']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('custom_deeplinks');
    }
}
```

### Update White Label Settings

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCustomDeeplinkFieldsToWhiteLabelSettings extends Migration
{
    public function up()
    {
        Schema::table('white_label_settings', function (Blueprint $table) {
            $table->string('custom_deeplink_domain')->nullable()->after('fallback_link');
            $table->boolean('custom_deeplink_enabled')->default(false)->after('custom_deeplink_domain');
        });
    }

    public function down()
    {
        Schema::table('white_label_settings', function (Blueprint $table) {
            $table->dropColumn(['custom_deeplink_domain', 'custom_deeplink_enabled']);
        });
    }
}
```

## Console Commands

### Data Migration Command

```php
<?php

namespace App\Console\Commands;

use App\Models\CustomDeeplink;
use App\Services\DeeplinkService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MigrateFirebaseToCustomDeeplinks extends Command
{
    protected $signature = 'deeplinks:migrate-firebase {--batch-size=100} {--dry-run}';
    protected $description = 'Migrate existing Firebase deeplinks to custom deeplinks';

    private DeeplinkService $deeplinkService;

    public function __construct(DeeplinkService $deeplinkService)
    {
        parent::__construct();
        $this->deeplinkService = $deeplinkService;
    }

    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        $batchSize = (int) $this->option('batch-size');

        $this->info('Starting Firebase to Custom Deeplinks migration...');

        if ($isDryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        $tables = [
            'training_course_whitelabel_deeplink' => [
                'type' => 'training_course',
                'entity_type' => 'TrainingCourse',
                'entity_id_field' => 'training_course_id'
            ],
            'products_whitelabel_deeplink' => [
                'type' => 'product',
                'entity_type' => 'Product',
                'entity_id_field' => 'product_id'
            ],
            'resources_whitelabel_deeplink' => [
                'type' => 'resource',
                'entity_type' => 'Resource',
                'entity_id_field' => 'resource_id'
            ]
        ];

        foreach ($tables as $tableName => $config) {
            $this->migrateTable($tableName, $config, $batchSize, $isDryRun);
        }

        $this->info('Migration completed successfully!');
    }

    private function migrateTable(string $tableName, array $config, int $batchSize, bool $isDryRun)
    {
        $this->info("Processing table: {$tableName}");

        $totalRecords = DB::table($tableName)->count();
        $this->info("Total records: {$totalRecords}");

        $bar = $this->output->createProgressBar($totalRecords);
        $processed = 0;

        DB::table($tableName)->orderBy('id')->chunk($batchSize, function ($records) use ($config, $isDryRun, &$processed, $bar) {
            foreach ($records as $record) {
                if (!$isDryRun) {
                    $this->createCustomDeeplink($record, $config);
                }
                $processed++;
                $bar->advance();
            }
        });

        $bar->finish();
        $this->newLine();
        $this->info("Processed {$processed} records from {$tableName}");
    }

    private function createCustomDeeplink($record, array $config)
    {
        try {
            $customDeeplink = $this->deeplinkService->generateDeeplink([
                'target_url' => $this->extractTargetUrl($record->share_url),
                'operator_id' => $record->operator_id,
                'type' => $config['type'],
                'entity_id' => $record->{$config['entity_id_field']},
                'entity_type' => $config['entity_type'],
                'metadata' => [
                    'migrated_from' => 'firebase',
                    'original_url' => $record->share_url,
                    'migration_date' => now()->toISOString()
                ]
            ]);

            // Update the original record with new custom deeplink
            DB::table($this->getTableName($record))
                ->where('id', $record->id)
                ->update([
                    'share_url' => $customDeeplink,
                    'updated_at' => now()
                ]);

        } catch (\Exception $e) {
            $this->error("Failed to migrate record ID {$record->id}: " . $e->getMessage());
        }
    }

    private function extractTargetUrl(string $firebaseUrl): string
    {
        // Extract the actual target URL from Firebase dynamic link
        // This may require parsing the Firebase URL structure
        if (preg_match('/link=([^&]+)/', $firebaseUrl, $matches)) {
            return urldecode($matches[1]);
        }

        return $firebaseUrl; // Fallback to original URL
    }
}
```

### Updated Generate Deeplinks Command

```php
<?php

namespace App\Console\Commands;

use App\Services\DeeplinkService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class GenerateCustomDeeplinks extends Command
{
    protected $signature = 'deeplinks:generate {--type=all} {--operator-id=}';
    protected $description = 'Generate custom deeplinks for existing content';

    private DeeplinkService $deeplinkService;

    public function __construct(DeeplinkService $deeplinkService)
    {
        parent::__construct();
        $this->deeplinkService = $deeplinkService;
    }

    public function handle()
    {
        $type = $this->option('type');
        $operatorId = $this->option('operator-id');

        $this->info('Generating custom deeplinks...');

        if ($type === 'all' || $type === 'training_courses') {
            $this->generateTrainingCourseDeeplinks($operatorId);
        }

        if ($type === 'all' || $type === 'products') {
            $this->generateProductDeeplinks($operatorId);
        }

        if ($type === 'all' || $type === 'resources') {
            $this->generateResourceDeeplinks($operatorId);
        }

        $this->info('Deeplink generation completed!');
    }

    private function generateTrainingCourseDeeplinks($operatorId = null)
    {
        $query = DB::table('training_courses');

        if ($operatorId) {
            $query->where('master_user_id', $operatorId);
        }

        $courses = $query->get();

        foreach ($courses as $course) {
            $targetUrl = url(route('trainingCourse.show', ['id' => $course->id]));

            $deeplink = $this->deeplinkService->generateDeeplink([
                'target_url' => $targetUrl,
                'operator_id' => $course->master_user_id,
                'type' => 'training_course',
                'entity_id' => $course->id,
                'entity_type' => 'TrainingCourse'
            ]);

            // Update or create deeplink record in storage table
            DB::table('training_course_whitelabel_deeplink')->updateOrInsert(
                [
                    'operator_id' => $course->master_user_id,
                    'training_course_id' => $course->id
                ],
                [
                    'share_url' => $deeplink,
                    'updated_at' => now()
                ]
            );
        }

        $this->info("Generated deeplinks for " . $courses->count() . " training courses");
    }
}
```

## Environment Configuration

### Required Environment Variables

```bash
# Custom Deeplink Configuration
CUSTOM_DEEPLINK_DOMAIN=https://skillsbase-api.com
CUSTOM_DEEPLINK_DEFAULT_FALLBACK=https://skillsbase.io
CUSTOM_DEEPLINK_CODE_LENGTH=8
CUSTOM_DEEPLINK_ANALYTICS_ENABLED=true

# Remove these Firebase variables after migration
# FIREBASE_CREDENTIALS=
# FIREBASE_DYNAMIC_LINKS_DEFAULT_DOMAIN=
```

### Configuration File Updates

```php
// config/deeplinks.php
<?php

return [
    'domain' => env('CUSTOM_DEEPLINK_DOMAIN', env('APP_URL')),
    'default_fallback' => env('CUSTOM_DEEPLINK_DEFAULT_FALLBACK', 'https://skillsbase.io'),
    'code_length' => env('CUSTOM_DEEPLINK_CODE_LENGTH', 8),
    'analytics_enabled' => env('CUSTOM_DEEPLINK_ANALYTICS_ENABLED', true),
    'cache_ttl' => env('CUSTOM_DEEPLINK_CACHE_TTL', 3600),

    'mobile_schemes' => [
        'ios' => [
            'default' => 'skillsbase',
            'fallback_store' => 'https://apps.apple.com/app/skillsbase'
        ],
        'android' => [
            'default' => 'skillsbase',
            'fallback_store' => 'https://play.google.com/store/apps/details?id=com.skillsbase.app'
        ]
    ]
];
```

## Testing Strategy

### Unit Tests

```php
<?php

namespace Tests\Unit\Services;

use App\Models\CustomDeeplink;
use App\Services\DeeplinkService;
use Tests\TestCase;

class DeeplinkServiceTest extends TestCase
{
    private DeeplinkService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new DeeplinkService();
    }

    public function test_generates_unique_short_codes()
    {
        $code1 = $this->service->generateShortCode();
        $code2 = $this->service->generateShortCode();

        $this->assertNotEquals($code1, $code2);
        $this->assertEquals(8, strlen($code1));
        $this->assertEquals(8, strlen($code2));
    }

    public function test_creates_deeplink_successfully()
    {
        $params = [
            'target_url' => 'https://example.com/course/123',
            'operator_id' => 1,
            'type' => 'training_course',
            'entity_id' => 123,
            'entity_type' => 'TrainingCourse'
        ];

        $deeplinkUrl = $this->service->generateDeeplink($params);

        $this->assertStringContains('/dl/', $deeplinkUrl);
        $this->assertDatabaseHas('custom_deeplinks', [
            'target_url' => $params['target_url'],
            'operator_id' => $params['operator_id'],
            'deeplink_type' => $params['type']
        ]);
    }
}
```

### Integration Tests

```php
<?php

namespace Tests\Feature;

use App\Models\CustomDeeplink;
use Tests\TestCase;

class DeeplinkResolutionTest extends TestCase
{
    public function test_resolves_deeplink_successfully()
    {
        $deeplink = CustomDeeplink::factory()->create([
            'short_code' => 'test123',
            'target_url' => 'https://example.com/target',
            'is_active' => true
        ]);

        $response = $this->get('/dl/test123');

        $response->assertRedirect('https://example.com/target');

        $deeplink->refresh();
        $this->assertEquals(1, $deeplink->click_count);
    }

    public function test_handles_mobile_user_agents()
    {
        $deeplink = CustomDeeplink::factory()->create([
            'short_code' => 'mobile1',
            'ios_package_name' => 'com.skillsbase.app',
            'target_url' => 'https://example.com/target'
        ]);

        $response = $this->withHeaders([
            'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)'
        ])->get('/dl/mobile1');

        $response->assertRedirect();
        $this->assertStringContains('skillsbaseapp://', $response->headers->get('Location'));
    }
}
```

## Performance Considerations

### Database Optimization

- Index on `short_code` for fast lookups
- Composite indexes for analytics queries
- Partitioning by date for large datasets
- Regular cleanup of expired deeplinks

### Caching Strategy

```php
// Cache deeplink resolution for performance
Cache::remember("deeplink:{$shortCode}", 3600, function () use ($shortCode) {
    return CustomDeeplink::where('short_code', $shortCode)->first();
});
```

### Rate Limiting

```php
// Apply rate limiting to deeplink creation
Route::middleware(['throttle:deeplinks'])->group(function () {
    Route::post('/api/deeplinks', [DeeplinkController::class, 'create']);
});
```

## Security Considerations

### Input Validation

- Validate target URLs to prevent open redirects
- Sanitize metadata inputs
- Implement CSRF protection for admin endpoints

### Access Control

- Operator-specific deeplink access
- Admin-only deeplink management
- Audit logging for deeplink operations

### URL Safety

```php
private function isValidTargetUrl(string $url): bool
{
    $allowedDomains = config('deeplinks.allowed_domains', []);
    $parsedUrl = parse_url($url);

    return in_array($parsedUrl['host'], $allowedDomains);
}
```

## Deployment Checklist

### Pre-deployment

- [ ] Run database migrations
- [ ] Update environment variables
- [ ] Deploy new code with feature flags disabled
- [ ] Run data migration in staging environment

### Deployment

- [ ] Enable custom deeplink feature flag
- [ ] Monitor error rates and performance
- [ ] Verify mobile app compatibility
- [ ] Test deeplink resolution across platforms

### Post-deployment

- [ ] Monitor analytics and click tracking
- [ ] Validate data migration accuracy
- [ ] Remove Firebase dependencies after validation period
- [ ] Update documentation and API specs

## Rollback Plan

### Immediate Rollback

1. Disable custom deeplink feature flag
2. Revert to Firebase deeplink generation
3. Monitor system stability

### Data Rollback

1. Restore original deeplink URLs from backup
2. Revert database schema changes if necessary
3. Re-enable Firebase service dependencies

This comprehensive technical document provides developers with all the necessary information to successfully implement the Firebase to Custom Deeplink migration while maintaining system reliability and performance.

```

```
