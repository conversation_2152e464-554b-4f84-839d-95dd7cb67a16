<?php

namespace App\Http\Requests\Admin\v1;
use DBTableNames;
use App\Http\Requests\CustomFormRequest;

class AuthRequest extends CustomFormRequest {

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'name' => 'required|max:150',
            'email' => 'required|email|max:255|unique:' . DBTableNames::MASTER_USERS,
            'user_type' => 'required|in:Admin,Operator,ContentProvider',
            'role_id' => 'required|exists:' . DBTableNames::ROLES . ',id',
            'password' => 'required|min:8|max:64|regex:/(?=.*([A-Z]))(?=.*([a-z]))(?=.*([~`\!-@#\$%\^&\*\(\)_\{\}\[\]]))/',
            'unique_id' => 'required|max:50|unique:' . DBTableNames::MASTER_USERS,
            'status' => 'required|in:Active,Inactive',
        ];
    }
    
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Please enter name.',
            'name.max' => 'Name may not be greater than 255 characters.',
            'user_type.required' => 'Please enter user type.',
            'user_type.in' => 'Invalid user type.',
            'role_id.required' => 'Please enter user role.',
            'role_id.exists' => 'Invalid user role.',
            'email.required' => 'Please enter email.',
            'email.email' => 'Email must be a valid email address (E.g.: <EMAIL> ).',
            'email.max' => 'Email may not be greater than 255 characters.',
            'email.unique' => 'Email has already been taken.',
            'password.required' => 'Please enter password.',
            'password.min' => 'Password must be at least 8 characters.',
            'password.max' => 'Password may not be greater than 20 characters.',
            'password.regex' => 'Password must have atleast one capital, small and special character.',
            'unique_id.required' => 'Please enter unique id.',
            'unique_id.max' => 'Unique ID may not be greater than 50 characters.',
            'unique_id.unique' => 'Unique ID already exists.',
            'status.required' => 'Please select status.',
            'status.in' => 'Invalid status.',
        ];
    }

}
