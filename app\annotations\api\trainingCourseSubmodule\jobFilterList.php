<?php
/**
     * @OA\Get(
     *     path="/api/trainingCourseSubmodule/jobFilterList",
     *     tags={"Mobile - Training Course Submodule"},
     *     summary="Get Job Filter List",
     *     description="Get Job Filter List",
     *     operationId="jobFilterList",
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */