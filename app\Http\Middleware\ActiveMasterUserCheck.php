<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Contracts\Auth\Factory as Auth;
use Illuminate\Http\Response;

class ActiveMasterUserCheck
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if(auth()->guard('operator')->user() && auth()->guard('operator')->user()->status == 'Active'){
                $response = $next($request);
        }else{
            return response()->json(setErrorResponse('Unauthorized, Your accout is inactivated.', Response::HTTP_UNAUTHORIZED))
                            ->setStatusCode(Response::HTTP_UNAUTHORIZED);
        }
        return $response;  
    }
}
