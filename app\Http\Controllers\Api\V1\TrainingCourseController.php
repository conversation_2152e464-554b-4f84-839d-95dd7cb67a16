<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\TrainingCourse;
use App\Models\TrainingCourseProgress;
use App\Models\UserAssignTrainingCourses;
use App\Models\User;
use App\Models\UserRelation;
use App\Models\TrainingCourseInvite;
use Illuminate\Support\Carbon;
use App\Repositories\TrainingCourseRepository;
use App\Http\Requests\V1\AccrediationListRequest;
use App\Http\Requests\V1\UserInviteLinkRequest;
use App\Http\Resources\V1\TrainingCourseResource;
use App\Http\Resources\V1\TrainingCourseDetailResource;
use App\Http\Resources\V2\TrainingCourseDetailResource as TrainingCourseDetailResourceV2;
use App\Http\Resources\CustomCollection;
use Illuminate\Support\Facades\Auth;
use App\Models\CustomDeeplink;

class TrainingCourseController extends Controller {

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    private $model;
    private $repository;

    public function __construct() {
	$this->model = new TrainingCourse();
	$this->repository = new TrainingCourseRepository($this->model);
    }

    /**
     * List Training Course
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $operatorId) {
		try {
			if (isset($request->user_id) && !empty($request->user_id)) {
				if (strpos($request->user_id, ',')) {
					$user_ids = explode(',', $request->user_id);
					foreach ($user_ids as $value) {
						$courseIds[] = UserAssignTrainingCourses::where('master_user_id', $operatorId)->where('user_id', $value)->pluck('training_course_id')->toArray();
					}
					$trainingCourseIds = call_user_func_array('array_intersect', $courseIds);
				} else {
					$user_ids[] = $request->user_id;
					$courseIds = UserAssignTrainingCourses::where('master_user_id', $operatorId)->whereIn('user_id', $user_ids)->pluck('training_course_id')->toArray();
					$trainingCourseIds = $courseIds;
				}

				$list = TrainingCourse::with('courseProgress:training_course_id,user_id,course_progress,is_new,is_reset')
					->whereIn('id', $trainingCourseIds)
					->where(['status' => 'Active', 'publish_now' => 1, 'master_user_id' => $operatorId])
					->orderBy('display_order')
					->paginate(config('constants.mobile_app.per_page'));
				return ($list) ? (new CustomCollection($list, 'App\Http\Resources\V1\TrainingCourseResource')) :
					response()->json(setErrorResponse(__('user.TrainingCourse.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
			} else {
				$trainingCourseIds = (new TrainingCourse)->getUserAssignCourseIds();
				$list = TrainingCourse::with('courseProgress:training_course_id,user_id,course_progress,is_new,is_reset')
					->whereIn('id', $trainingCourseIds)
					->where(['status' => 'Active', 'publish_now' => 1, 'master_user_id' => $operatorId])
					->orderBy('display_order')
					->paginate(config('constants.mobile_app.per_page'));
				return ($list) ? (new CustomCollection($list, 'App\Http\Resources\V1\TrainingCourseResource')) :
					response()->json(setErrorResponse(__('user.TrainingCourse.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
			}
		} catch (\Exception $e) {
			return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
		}
    }

    /**
     * Accreditation List Training Course
     *
     * @return \Illuminate\Http\Response
     */
    public function accreditationList(AccrediationListRequest $request) {
		try {
			$trainingCourseIds = (new TrainingCourse)->getUserAssignCourseIds();
			$list = TrainingCourse::whereHas('courseProgress', function ($query) use ($request) {
				$query->where('is_accreditation', 1);
					if ($request->filter_by === 'this_month') {
						$query->whereMonth('updated_at', Carbon::now()->month);
					} else if ($request->filter_by === 'six_months') {
						$query->where('updated_at', ">", Carbon::now()->subMonths(6));
					} else if ($request->filter_by === 'year') {
						$query->where('updated_at', ">", Carbon::now()->subMonths(12));
					}
				})
				->whereIn('id', $trainingCourseIds)
				->where(['status' => 'Active', 'publish_now' => 1, 'master_user_id' => $request->operator_id]);
			$list = ($request->sort_by === 'asc' || $request->sort_by === 'desc') ?
				$list->orderBy('title', $request->sort_by) :
				$list->{$request->sort_by}();
			$list = $list->paginate(config('constants.mobile_app.per_page'));
			return ($list) ? (new CustomCollection($list, 'App\Http\Resources\V1\TrainingCourseResource')) :
				response()->json(setErrorResponse(__('user.TrainingCourse.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
		} catch (\Exception $e) {
			return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
		}
    }

    /**
     * Display the specified training course detail.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id, Request $request) {
		try {
			$course = $this->model->with('courseProgress:training_course_id,user_id,course_progress,is_new,is_reset')->find($id);
			$user_relation = auth()->user()->user_relation;
			$auth_user = Auth::user();
			if (!$course) {
				return response()->json(setErrorResponse(__('user.TrainingCourse.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
			}
			if ($course->publish_now == 0 || $course->status == 'Inactive') {
				return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_OK);
			}

			if ($course->master_user_id != $user_relation->master_user_id && (isset($request->isAccessor) && $request->isAccessor == 0)) {
				return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_OK);
			}

			if ($course->master_user_id != $user_relation->master_user_id && (isset($request->isAccessor) && $request->isAccessor == 1)) {
				if (request()->header('api-version') == 'v2') {
					return ($course) ?
						(new TrainingCourseDetailResourceV2($course)) :
						response()->json(setErrorResponse(__('user.TrainingCourse.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
					} else {
					return ($course) ?
						(new TrainingCourseDetailResource($course)) :
						response()->json(setErrorResponse(__('user.TrainingCourse.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
					}
			}

			$trainingCourseIds = (new TrainingCourse)->getUserAssignCourseIds();

			$trainingCourseIds = (new TrainingCourse)->getUserAssignCourseIds();
			if (!in_array($id, $trainingCourseIds) && (isset($request->isAccessor) && $request->isAccessor == 0)) {
				return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_OK);
			}

			TrainingCourse::addDefaultCourseProgress($id);

			// If module not exist
			if ($course->total_modules == 0) {
			TrainingCourseProgress::whereTrainingCourseId($id)->whereUserId($auth_user->id)->whereMasterUserId($user_relation->master_user_id)->update(['course_progress' => 100]);
			}
			// Update Latest visited time to DB for report analytics
			TrainingCourseProgress::whereTrainingCourseId($id)->whereUserId($auth_user->id)->whereMasterUserId($user_relation->master_user_id)->update(['last_visited_at' => date('Y-m-d H:i:s')]);

			if (request()->header('api-version') == 'v2') {
				return ($course) ?
					(new TrainingCourseDetailResourceV2($course)) :
					response()->json(setErrorResponse(__('user.TrainingCourse.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
			} else {
				return ($course) ?
					(new TrainingCourseDetailResource($course)) :
					response()->json(setErrorResponse(__('user.TrainingCourse.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
			}
		} catch (\Exception $e) {
			return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
		}
    }

    /**
     * Reset Progress Of Training Course
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  request ID
     * @return \Illuminate\Http\Response
     */
    public function resetProgress(Request $request) {
		try {
			$trainingCourseIds = explode(',', $request->id);
			if (!empty($trainingCourseIds)) {
				TrainingCourseProgress::whereIn('training_course_id', $trainingCourseIds)->update(['is_reset' => 0]);
				return response()->json(setResponse([], ['message' => __('user.TrainingCourse.resetProgress')]))->setStatusCode(Response::HTTP_OK);
			} else {
				return response()->json(setErrorResponse(__('user.TrainingCourse.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
			}
		} catch (\Exception $e) {
			return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
		}
    }

    /**
     * Assign User via Invite Link
     */
    public function assignUserInviteLink(userInviteLinkRequest $request) {
		try {
			$link = trim($request['invite_link']);
			$user_id = $request['user_id'];
			$unique_id = $request['unique_id'];

			$shortLinkDetail = TrainingCourseInvite::where('unique_id', $unique_id)->orderBy('id', 'desc')->first();
			if (!empty($shortLinkDetail)) {

				$short_link = $shortLinkDetail->invite_link;
				$lastValue = substr($short_link, strrpos($short_link, '/') + 1);
				$getShotLinkDetail=CustomDeeplink::where('short_code', $lastValue)->first();
				if (!$getShotLinkDetail) {
					$errMessage = 'Invite link is invalid';
					return response()->json(setErrorResponse($errMessage))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
				}
				$url_components = parse_url($link);
				parse_str($url_components['query'], $params);
				$master_user_id = $getShotLinkDetail->operator_id ?? null;
				$courseId = $params['id'];

				$userData = UserRelation::whereMasterUserId($master_user_id)
					->where('user_id', $user_id)
					->first();
				$linkData = TrainingCourseInvite::select('id', 'master_user_id', 'course_id', 'invite_link', 'status')
					->whereMasterUserId($master_user_id)
					->whereCourseId($courseId)
					->where('invite_link', '=', $short_link)
					->whereNull('deleted_at')
					->first();

				if (isset($userData)) {
					if (isset($linkData['invite_link']) && $linkData['status'] == 'Active') {
						$courseName = $this->model->where('id', $courseId)->find($courseId);
						$userCourseExist = UserAssignTrainingCourses::where(['user_id' => $user_id, 'master_user_id' => $master_user_id, 'training_course_id' => $courseId])->first();
						if ($userCourseExist) {
							$errorMessage = 'Training Course already assigned';
							return response()->json(setErrorResponse($errorMessage))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
						} else {
							$linkAssignTrainingCourse = UserAssignTrainingCourses::create([
								'master_user_id' => $master_user_id,
								'user_id' => $user_id,
								'invite_link_id' => $linkData['id'],
								'training_course_id' => $courseId,
								'is_invite_link' => 1
							]);
							$message = $courseName['title'] . ' assigned successfully';
							// return response()->json(setResponse( ['message' => __($message)]))->setStatusCode(Response::HTTP_OK);
							return response()->json(setResponse([], ['message' => __($message)]))
									->setStatusCode(Response::HTTP_OK);
						}
					} else {
						$errMessage = 'Invite link is invalid';
						return response()->json(setErrorResponse($errMessage))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
					}
				} else {
					$errMessage = 'Invite link is invalid';
					return response()->json(setErrorResponse($errMessage))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
				}
			} else {
				$errMessage = 'Invite link not invalid';
				return response()->json(setErrorResponse($errMessage))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
			}
		} catch (\Exception $e) {
			return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
		}
    }
}
