<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCustomDeeplinksTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('custom_deeplinks', function (Blueprint $table) {
            $table->id();
            $table->string('short_code', 8)->unique();
            $table->unsignedBigInteger('operator_id')->nullable();
            $table->text('target_url');
            $table->string('deeplink_type', 50);
            $table->unsignedBigInteger('entity_id')->nullable();
            $table->string('entity_type', 50)->nullable();
            $table->string('ios_package_name')->nullable();
            $table->string('android_package_name')->nullable();
            $table->string('ios_app_store_id', 100)->nullable();
            $table->text('fallback_url')->nullable();
            $table->integer('click_count')->default(0);
            $table->timestamp('last_clicked_at')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('expires_at')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['operator_id']);
            $table->index(['entity_type', 'entity_id']);
            $table->index(['deeplink_type']);
            $table->index(['is_active']);
            $table->index(['created_at']);
            $table->index(['short_code', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('custom_deeplinks');
    }
}
