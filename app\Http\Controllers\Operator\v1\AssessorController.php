<?php

namespace App\Http\Controllers\Operator\v1;

use App\Http\Controllers\Controller;
use App\Http\Requests\Operator\v1\AssessorAssignFrontUsersRequest;
use App\Http\Requests\Operator\v1\AssessorUserUpdateRequest;
use App\Http\Requests\Operator\v1\AssignAssessorRequest;
use App\Http\Requests\Operator\v1\AssignedCourseDetailsRequest;
use App\Http\Requests\Operator\v1\CommonListingRequest;
use App\Http\Requests\Operator\v1\FrontUserDeleteRequest;
use App\Http\Requests\Operator\v1\FrontUserRequest;
use App\Http\Requests\Operator\v1\FrontUserStatusRequest;
use App\Http\Requests\Operator\v1\ImportFrontUsersRequest;
use App\Http\Requests\Operator\v1\SmartAward\AssignedCourseDetailsSmartAwardRequest;
use App\Http\Requests\Operator\v1\SmartAward\ChangePasswordSmartAwardRequest;
use App\Http\Requests\Operator\v1\SmartAward\FrontUserSmartAwardRequest;
use App\Http\Requests\Operator\v1\SmartAward\FrontUserUpdateSmartAwardRequest;
use App\Http\Requests\Operator\v1\SmartAward\StoreQuizResultSmartAwardRequest;
use App\Http\Requests\Operator\v1\UserManageDetailsRequest;
use App\Http\Requests\Operator\v1\VerifyRegisteredUser;
use App\Http\Resources\Operator\v1\AssessorAssignFrontUserResource;
use App\Http\Resources\Operator\v1\FrontUserResource;
use App\Http\Resources\Operator\v1\ShowAssessorUserResource;
use App\Http\Resources\Operator\v1\TrainingCourseSubModuleMiniQuizPdfResultResource;
use App\Http\Resources\Operator\v1\TrainingCourseSubModuleQuizPdfResultResource;
use App\Http\Resources\Operator\v1\UserCourseListResource;
use App\Models\AssessorAssignUser;
use App\Models\AssignTrainingCourses;
use App\Models\MasterUser;
use App\Models\PrerequisiteModules;
use App\Models\PrerequisiteSubModules;
use App\Models\TrainingCourse;
use App\Models\TrainingCourseModuleProgress;
use App\Models\TrainingCourseProgress;
use App\Models\TrainingCourseSubmoduleDetails;
use App\Models\TrainingCourseSubModuleFeedbackPDFResults;
use App\Models\TrainingCourseSubmoduleProgress;
use App\Models\TrainingCourseSubModuleQuiz;
use App\Models\TrainingCourseSubModuleQuizResults;
use App\Models\User;
use App\Models\UserAssignTrainingCourses;
use App\Models\UserGroup;
use App\Models\UserRelation;
use App\Notifications\VerifyRegisteredUserNotification;
use App\Repositories\Operator\v1\AssessorRepository;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Notification;

class AssessorController extends Controller {
    /*
      |---------------------------------------------------------------------------
      | Front User Controller
      |--------------------------------------------------------------------------
      |
      | This controller handles user added by operator after admin login.
      |
     */

    protected $model;
    protected $user_repository;

    public function __construct() {
        $this->model = new User();
        $this->user_repository = new AssessorRepository($this->model);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index() {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create() {
        
    }

    public function store(FrontUserRequest $request) {
        try {
            $moduleData = $request;
            $result = $this->user_repository->create($moduleData);
            if (isset($result['response_type']) && $result['response_type'] == "Error") {
                return response()->json(setErrorResponse($result['message']))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else {
                $retunData = array('id' => $result->id);
                return response()->json(setResponse($retunData, ['message' => __('operator.FrontUser.add')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/assessor/{id}",
     *     tags={"Assessor - Management"},
     *     summary="Show Assessor Details",
     *     description="Show Assessor Details",
     *     operationId="show",
     *     @OA\Parameter(
     *         description="Id of Assessor to fetch",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function show($id) {
        try {
            // checking for assessment portal feature setting
            $checkAssessment = checkAssessment();
            if ($checkAssessment == 1) {
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
            $dataId = [];
            $masterUserId = auth()->guard('operator')->id();
            $masterUserDet = MasterUser::find($masterUserId);
            if ($masterUserDet->parent_id != null) {
                $parent = $masterUserDet->parent_id;
            } else {
                $parent = $masterUserId;
            }
            $teamMembers = MasterUser::where('parent_id', $parent)->pluck('id')->toArray();
            array_push($teamMembers, $parent);
            if (in_array($masterUserId, $teamMembers)) {
                $allUsers = UserRelation::where('master_user_id', $parent)->pluck('user_id')->toArray();
                if (in_array($id, $allUsers)) {
                    $userDetail = $this->model::with(['user_relation' => function ($q) use ($masterUserId) {
                                    $q->where('master_user_id', $masterUserId)->first();
                                }])->whereId($id)->first();
                    if (!is_null($userDetail)) {

                        $userAssignCourse = UserAssignTrainingCourses::where('user_id', $userDetail->id)->whereMasterUserId(getOperatorId())->get();

                        if (!$userAssignCourse->isEmpty()) {
                            foreach ($userAssignCourse as $key => $course) {
                                $dataId[] = $course->training_course_id;
                            }
                            $userDetail->assign_course = $dataId;
                        }

                        if (!empty($userDetail->userOperator) && $userDetail->userOperator->user_group_id != '') {
                            $assignCourse = AssignTrainingCourses::where('user_group_id', $userDetail->userOperator->user_group_id)->whereMasterUserId($masterUserId)->get();

                            if (!$assignCourse->isEmpty()) {
                                foreach ($assignCourse as $key => $course) {
                                    $dataId[] = $course->training_course_id;
                                }

                                if (!is_null($userDetail->assign_course)) {
                                    $userDetail->assign_course = array_unique(array_merge($userDetail->assign_course, $dataId));
                                } else {
                                    $userDetail->assign_course = $dataId;
                                }
                            }
                        }
                        $userDetail = new ShowAssessorUserResource($userDetail);
                    }
                    $message = !empty($userDetail) ? __('operator.FrontUser.found') : __('operator.FrontUser.notFound');
                    return response()->json(setResponse($userDetail, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
                } else {
                    return response()->json(setErrorResponse(__('You cannot access this record')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id) {
        //
    }

    /**
     * @OA\Put(
     *     path="/operator/assessor/{id}",
     *     tags={"Assessor - Management"},
     *     summary="Update Assessor details",
     *     description="Update Assessor details",
     *     operationId="update",
     *     @OA\Parameter(
     *         description="Id of Assessor to update",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="Assessor Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 @OA\Property(
     *                     property="assignedUserIds",
     *                     description="Assign users to assessor",
     *                     type="object"
     *                 ),
     *                 example={"id": 826,"name": "us1","email": "<EMAIL>","status": "Active","assessor_id": "AID94809","assessor_role": "Yes","qr_code": "https://d1u80oeg1zk6yu.cloudfront.net/users/qrcode/826/fd2e968f978045a58be20416a889fa3a.png","assignedUserIds": {824, 825, 267, 808, 163},"assessorId": 826,"enable_password": true,"password": "Test@123","password_confirmation":"Test@123","step": 1}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="User Updated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function update(AssessorUserUpdateRequest $request, $id) {
        try {
            // checking for assessment portal feature setting
            $checkAssessment = checkAssessment();
            if ($checkAssessment == 1) {
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
            // main
            $response = $this->user_repository->update($request, $id);
            if (isset($response['response_type']) && $response['response_type'] == "Error") {
                return response()->json(setErrorResponse($response['message']))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else {
                $data['id'] = $id;
                return response()->json(setResponse($data, ['message' => __('operator.FrontUser.update')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function getAllUserGroup(Request $request) {
        try {
            $masterUserId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $allGroups = UserGroup::whereParentId($masterUserId)->select('id', 'name', 'status')->get();
            $message = !empty($allGroups) ? __('User group detail found.') : __('User group not found.');
            return response()->json(setResponse($allGroups, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function getUserGroupDetailById($id) {
        try {
            $groupDetail = UserGroup::find($id, ['id', 'manager_email', 'unique_id']);
            $message = !empty($groupDetail) ? __('operator.FrontUser.groupFound') : __('operator.FrontUser.dataNotFound');
            return response()->json(setResponse($groupDetail, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function getUserManagerDetailByField(UserManageDetailsRequest $request) {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $operatorUniqueIdName = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->unique_id_name;
            $operatorData = MasterUser::leftJoin('groups', 'groups.email', '=', 'master_users.email')->select('master_users.id', 'master_users.email', 'master_users.unique_id', 'groups.id as user_group_id');
            $term = $request->search_key;

            if ($request->search_type == 'email') {
                $operatorData->where('master_users.email', '=', $term);
            } else {
                $operatorData->where('master_users.unique_id', '=', $term);
            }
            $operatorData = $operatorData->Where(function ($query) use ($operatorId) {
                        $query->where('master_users.parent_id', $operatorId)
                                ->orWhere('master_users.id', '=', $operatorId);
                    })->get()->toArray();
            if (empty($operatorData)) {
                $message = $request->search_type == 'email' ? __('operator.FrontUser.managerEmail') : ($operatorUniqueIdName ? $operatorUniqueIdName . ' not found.' : 'Unique Id not found.');
            } else {
                $message = __('operator.FrontUser.managerFound');
            }
            return response()->json(setResponse($operatorData, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function destroy(FrontUserDeleteRequest $request) {
        try {
            $this->user_repository->delete($request);
            return response()->json(setResponse([], ['message' => __('operator.FrontUser.delete')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function changeStatus(FrontUserStatusRequest $request) {
        try {
            $this->user_repository->change_status($request);
            return response()->json(setResponse([], ['message' => __('operator.FrontUser.status')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function getFrontUsersList(CommonListingRequest $request) {
        try {
            $users = $this->user_repository->getUsersListing($request->all());
            if ($request->isExport) {
                return $this->user_repository->exportCsv($users->get(), $request->exportFields);
            }
            return FrontUserResource::collection($users->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function importFrontUsers(ImportFrontUsersRequest $request) {
        try {
            $isImported = $this->user_repository->importFrontUsers($request);
            if (isset($isImported) && $isImported[0]) {
                return response()->json(setResponse($isImported[1], ['message' => __('operator.FrontUser.import-success')]))->setStatusCode(Response::HTTP_OK);
            } else {
                return response()->json(setErrorResponse($isImported[1], ['message' => __('operator.FrontUser.import-error')]))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function getTrainingCourseList(CommonListingRequest $request) {
        try {
            $moduleData = $request;
            $courseTemp = $this->user_repository->trainingCourseList($moduleData);
            return UserCourseListResource::collection($courseTemp->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/assessor/getProfile/{id}",
     *     tags={"Assessor - Management"},
     *     summary="Get Assessor Profile Details",
     *     description="Get Assessor Profile Details",
     *     operationId="getProfile",
     *     @OA\Parameter(
     *         description="Id of User to fetch",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getProfile($id) {
        try {
            // checking for assessment portal feature setting
            $checkAssessment = checkAssessment();
            if ($checkAssessment == 1) {
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
            // main
            $masterUserId = auth()->guard('operator')->id();
            $masterUserDet = MasterUser::find($masterUserId);
            if ($masterUserDet->parent_id != null) {
                $parent = $masterUserDet->parent_id;
            } else {
                $parent = $masterUserId;
            }
            $teamMembers = MasterUser::where('parent_id', $parent)->pluck('id')->toArray();
            array_push($teamMembers, $parent);
            if (in_array($masterUserId, $teamMembers)) {
                $allUsers = UserRelation::where('master_user_id', $parent)->pluck('user_id')->toArray();
                if (in_array($id, $allUsers)) {
                    $userDetail = $this->model::with(['user_relation' => function ($q) use ($masterUserId) {
                                    $q->where('master_user_id', $masterUserId)->first();
                                }])->whereId($id)->first();
                    $assignedUserIds = AssessorAssignUser::where('assessor_id', $userDetail->id)->get();
                    $Count = count($assignedUserIds);
                    $userData = [];
                    if ($userDetail) {
                        $userData['id'] = $userDetail->id;
                        $userData['email'] = $userDetail->email;
                        $userData['name'] = $userDetail->name;
                        $userData['status'] = $userDetail->status;
                        $userData['photo'] = $userDetail->photo_url;
                        $userData['assessor_id'] = $userDetail->assessor_id;
                        $userData['assessor_role'] = $userDetail->assessor_role;
                        $userData['total_assigned_users'] = $Count;
                        $userData['center_name'] = $userDetail->centre_name ?? null;
                        $userData['center_id'] = $userDetail->centre_id ?? null;
                        $userData['qr_code'] = env('CDN_URL') . 'users/qrcode/' . $userDetail->id . '/' . $userDetail->qr_code;
                    }

                    $message = !empty($userDetail) ? __('operator.FrontUser.found') : __('operator.FrontUser.notFound');
                    return response()->json(setResponse($userData, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
                } else {
                    return response()->json(setErrorResponse(__('You cannot access this record')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function assignedCourses(AssignedCourseDetailsRequest $request) {
        try {
            $userId = $request->user_id;
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;

            // Get Assigned Courses
            $trainingCourseIds = [];
            $groupTrainingCourseIds = [];
            $manualTrainingCourseIds = [];
            $user = User::find($userId);

            if (!is_null($user->user_relation->user_group_id)) {
                $groupTrainingCourseIds = $user->userOperator->group->assignCourses()->pluck('training_course_id')->toArray();
            }
            $manualTrainingCourseIds = UserAssignTrainingCourses::whereMasterUserId($operatorId)
                            ->whereUserId($userId)->pluck('training_course_id')->toArray();
            $trainingCourseIds = array_unique(array_merge($groupTrainingCourseIds, $manualTrainingCourseIds));

            $trainingCourseList = [];
            $count = 0;
            if (count($trainingCourseIds) > 0) {

                $courseList = TrainingCourse::whereIn('id', $trainingCourseIds)->get();

                foreach ($courseList as $key => $course) {

                    $progressExist = TrainingCourseProgress::whereTrainingCourseId($course->id)
                            ->whereMasterUserId($operatorId)
                            ->whereUserId($userId)
                            ->first();
                    if ($progressExist && $progressExist->course) {

                        $trainingCourseList[$count]['course_id'] = $progressExist->course->id;
                        $trainingCourseList[$count]['course_name'] = $progressExist->course->title;
                        $trainingCourseList[$count]['image'] = $progressExist->course->primary_image_url;
                        $trainingCourseList[$count]['progress'] = $progressExist->course_progress;

                        // Get Modules
                        $moduleList = TrainingCourseModuleProgress::whereTrainingCourseId($course->id)
                                ->whereMasterUserId($operatorId)
                                ->whereUserId($userId)
                                ->get();
                        if ($moduleList) {
                            $trainingCourseList[$count]['total_modules'] = count($moduleList);
                            $totalCompletedModuleCount = 0;
                            foreach ($moduleList as $k => $module) {
                                if ($module->module_progress >= 100) {
                                    $totalCompletedModuleCount++;
                                }
                                $trainingCourseList[$count]['modules'][$k]['module_id'] = $module->modules->id;
                                $trainingCourseList[$count]['modules'][$k]['module_name'] = $module->modules->name;
                                $trainingCourseList[$count]['modules'][$k]['progress'] = $module->module_progress;
                                $trainingCourseList[$count]['modules'][$k]['unlock'] = $this->getUserModuleLock($course->id, $module->modules->id, $userId, $module->modules);
                                $trainingCourseList[$count]['modules'][$k]['open'] = ($module->date_time != $module->created_at) ? 1 : 0;
                                $trainingCourseList[$count]['modules'][$k]['completed'] = ($module->module_progress == 100) ? 1 : 0;

                                // Get Sub Modules
                                $submoduleList = TrainingCourseSubmoduleProgress::whereTrainingCourseId($course->id)
                                        ->whereMasterUserId($operatorId)
                                        ->whereUserId($userId)
                                        ->whereModuleId($module->module_id)
                                        ->get();
                                if ($submoduleList) {

                                    $trainingCourseList[$count]['modules'][$k]['total_submodules'] = count($submoduleList);
                                    $trainingCourseList[$count]['modules'][$k]['pdf_links'] = [];
                                    $totalCompletedSubModuleCount = 0;
                                    foreach ($submoduleList as $kk => $submodule) {
                                        if ($submodule->submodule_progress >= 100) {
                                            $totalCompletedSubModuleCount++;
                                        }

                                        if ($submodule->submodule_type_id == 12) {
                                            $quiz = TrainingCourseSubModuleQuizResults::where([
                                                        'training_course_id' => $submodule->training_course_id,
                                                        'module_id' => $submodule->module_id,
                                                        'submodule_id' => $submodule->submodule_id,
                                                        'user_id' => $userId,
                                                    ])->orderBy('id', 'DESC')->get();
                                            $result = TrainingCourseSubModuleQuizPdfResultResource::collection($quiz);
                                        }
                                        if ($submodule->submodule_type_id == 7) {
                                            $quiz2 = TrainingCourseSubModuleFeedbackPDFResults::where([
                                                        'training_course_id' => $submodule->training_course_id,
                                                        'module_id' => $submodule->module_id,
                                                        'submodule_id' => $submodule->submodule_id,
                                                        'user_id' => $userId,
                                                    ])->orderBy('id', 'DESC')->get();
                                            $result2 = TrainingCourseSubModuleMiniQuizPdfResultResource::collection($quiz2);
                                        }

                                        /** Max attempts check start */
                                        $quiz = TrainingCourseSubModuleQuiz::where(['submodule_id' => $submodule->submodule_id])->first();
                                        $maxAttemptsQuiz = false;
                                        $lastattempt = TrainingCourseSubModuleQuizResults::where(['submodule_id' => $submodule->submodule_id, 'user_id' => $userId])->orderBy('id', 'DESC')->first();
                                        if (!empty($lastattempt)) {
                                            if ($lastattempt->is_pass != 1) {
                                                $countResult = TrainingCourseSubModuleQuizResults::where(['submodule_id' => $submodule->submodule_id, 'user_id' => $userId])->count();
                                                $maxAttempts = $quiz->max_attempts_before_fail;
                                                if ($maxAttempts == 0) {
                                                    $maxAttemptsQuiz = false;
                                                } else if ($countResult < $maxAttempts) {
                                                    $maxAttemptsQuiz = false;
                                                } else {
                                                    $maxAttemptsQuiz = true;
                                                }
                                            }
                                        } else {
                                            $maxAttemptsQuiz = false;
                                        }
                                        /** Max attempts check end */
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['submodule_id'] = $submodule->subModule->id;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['submodule_type_id'] = $submodule->subModule->submodule_type_id;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['submodule_name'] = $submodule->subModule->submodule_name;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['progress'] = $submodule->submodule_progress;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['unlock'] = $this->getUserSubmoduleLock($course->id, $submodule->subModule->id, $userId, $submodule->subModule);
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['open'] = ($submodule->date_time != $submodule->created_at) ? 1 : 0;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['completed'] = ($submodule->submodule_progress == 100) ? 1 : 0;

                                        if ($submodule->submodule_type_id == 12) {
                                            if (count($result->pluck('pdf_url')) > 0) {
                                                $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]
                                                        ['pdf_links'] = $result->pluck('pdf_url');
                                                $trainingCourseList[$count]['modules'][$k]['pdf_links'] = array_merge($trainingCourseList[$count]['modules'][$k]['pdf_links'], $result->pluck('pdf_url')->toArray());
                                            }
                                            /** is_maximum_attempts true/false on the basis of max attempts set on quiz submodule */
                                            $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['is_maximum_attempts'] = $maxAttemptsQuiz;
                                        }

                                        if ($submodule->submodule_type_id == 7) {
                                            if (count($result2->pluck('pdf_url')) > 0) {
                                                $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]
                                                        ['pdf_links'] = $result2->pluck('pdf_url');
                                                $trainingCourseList[$count]['modules'][$k]['pdf_links'] = array_merge($trainingCourseList[$count]['modules'][$k]['pdf_links'], $result2->pluck('pdf_url')->toArray());
                                            }
                                        }
                                    }
                                    $trainingCourseList[$count]['modules'][$k]['total_completed_submodules'] = $totalCompletedSubModuleCount;
                                } else {
                                    $trainingCourseList[$count]['modules'][$k]['total_submodules'] = 0;
                                    $trainingCourseList[$count]['modules'][$k]['total_completed_submodules'] = 0;
                                    $trainingCourseList[$count]['modules'][$k]['submodules'] = [];
                                }
                            }
                            $trainingCourseList[$count]['total_completed_modules'] = $totalCompletedModuleCount;
                        } else {
                            $trainingCourseList[$count]['total_modules'] = 0;
                            $trainingCourseList[$count]['total_completed_modules'] = 0;
                            $trainingCourseList[$count]['modules'] = [];
                        }
                    } else {

                        $trainingCourseList[$count]['course_id'] = $course->id;
                        $trainingCourseList[$count]['course_name'] = $course->title;
                        $trainingCourseList[$count]['image'] = $course->primary_image_url;
                        $trainingCourseList[$count]['progress'] = 0;
                        if ($course->modules) {
                            $trainingCourseList[$count]['total_modules'] = count($course->modules);
                            $trainingCourseList[$count]['total_completed_modules'] = 0;
                            foreach ($course->modules as $k => $module) {
                                $trainingCourseList[$count]['modules'][$k]['module_id'] = $module->id;
                                $trainingCourseList[$count]['modules'][$k]['module_name'] = $module->name;
                                $trainingCourseList[$count]['modules'][$k]['progress'] = 0;
                                $trainingCourseList[$count]['modules'][$k]['unlock'] = 0;
                                $trainingCourseList[$count]['modules'][$k]['open'] = 0;
                                $trainingCourseList[$count]['modules'][$k]['completed'] = 0;

                                // Get Sub Modules
                                if ($module->subModules) {
                                    $trainingCourseList[$count]['modules'][$k]['total_submodules'] = count($module->subModules);
                                    $trainingCourseList[$count]['modules'][$k]['total_completed_submodules'] = 0;
                                    foreach ($module->subModules as $kk => $submodule) {
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['submodule_id'] = $submodule->id;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['submodule_type_id'] = $submodule->submodule_type_id;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['submodule_name'] = $submodule->submodule_name;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['progress'] = 0;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['unlock'] = 0;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['open'] = 0;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['completed'] = 0;
                                    }
                                } else {
                                    $trainingCourseList[$count]['modules'][$k]['total_submodules'] = 0;
                                    $trainingCourseList[$count]['modules'][$k]['submodules'] = [];
                                    $trainingCourseList[$count]['modules'][$k]['total_completed_submodules'] = 0;
                                }
                            }
                        } else {
                            $trainingCourseList[$count]['modules'][$k]['total_submodules'] = 0;
                            $trainingCourseList[$count]['modules'][$k]['submodules'] = [];
                            $trainingCourseList[$count]['modules'][$k]['total_completed_submodules'] = 0;
                        }
                    }
                    $count++;
                }

                $trainingCourseList = count($trainingCourseList) > 0 ? array_values($trainingCourseList) : $trainingCourseList;
                $message = (count($trainingCourseList) > 0) ? __('operator.FrontUser.assignedCourseFound') : __('operator.FrontUser.assignedCourseNotFound');
                return response()->json(setResponse($trainingCourseList, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
            } else {
                return response()->json(setErrorResponse(__('operator.FrontUser.assignedCourseNotFound')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function getUserModuleLock($trainingCourseId, $moduleId, $userId, $currentModule) {
        try {
            $moduleLock = 0;
            // Check Prerequisite Modules progress
            $prerequisiteModules = PrerequisiteModules::where(['training_course_id' => $trainingCourseId, 'module_id' => $moduleId])->get();

            if (count($prerequisiteModules)) {
                $moduleProgress = [];
                foreach ($prerequisiteModules as $modules) {
                    $moduleProgress[] = TrainingCourseModuleProgress::where(['training_course_id' => $modules->training_course_id, 'module_id' => $modules->prerequisite_module_id, 'user_id' => $userId])->where('module_progress', '>=', $modules->percentage)->count();
                }
                $moduleLock = in_array(0, $moduleProgress) ? 1 : 0;
            }

            if ($moduleLock == 0) {
                if ($currentModule->module_lock == 1) {
                    if ($currentModule->module_complete == 1) {
                        $data = ['duration' => $currentModule->duration, 'type' => $currentModule->duration_type];
                        $dateTime = TrainingCourseModuleProgress::where(['training_course_id' => $currentModule->training_course_id])->pluck('date_time')->first();

                        $data['date_time'] = (!is_null($dateTime) ? $dateTime : $currentModule->created_at);
                        $durationDate = addLockDurationToDate($data);
                        $moduleLock = ($durationDate <= Carbon::now() ? 0 : 1);
                    } else {
                        $moduleLock = ($currentModule->unlock_datetime <= Carbon::now() ? 0 : 1);
                    }
                } else {
                    $moduleLock = $currentModule->module_lock;
                }
            }
            return $moduleLock;
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function getUserSubmoduleLock($trainingCourseId, $subModuleId, $userId, $currentSubModule) {
        try {
            $subModuleLock = 0;
            // Check Prerequisite Submodules progress
            $prerequisiteSubModules = PrerequisiteSubModules::where(['training_course_id' => $trainingCourseId, 'submodule_id' => $subModuleId])->get();
            if (count($prerequisiteSubModules)) {
                $subModuleProgress = [];
                foreach ($prerequisiteSubModules as $subModules) {
                    $subModuleProgress[] = TrainingCourseSubmoduleProgress::where(['training_course_id' => $subModules->training_course_id, 'submodule_id' => $subModules->prerequisite_submodule_id, 'user_id' => $userId])->where('submodule_progress', '>=', $subModules->percentage)->count();
                }
                $subModuleLock = in_array(0, $subModuleProgress) ? 1 : 0;
            }
            if ($subModuleLock == 0) {
                if ($currentSubModule->submodule_lock == 1) {
                    if ($currentSubModule->submodule_complete == 1) {
                        $data = ['duration' => $currentSubModule->duration, 'type' => $currentSubModule->duration_type];
                        $dateTime = TrainingCourseProgress::where(['training_course_id' => $currentSubModule->training_course_id, 'user_id' => $userId])->pluck('date_time')->first();
                        $data['date_time'] = (!is_null($dateTime) ? $dateTime : $currentSubModule->created_at);
                        $durationDate = addLockDurationToDate($data);
                        $subModuleLock = ($durationDate <= Carbon::now() ? 0 : 1);
                    } else {
                        $subModuleLock = ($currentSubModule->unlock_datetime <= Carbon::now() ? 0 : 1);
                    }
                } else {
                    $subModuleLock = $currentSubModule->submodule_lock;
                }
            }
            return $subModuleLock;
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function verifyRegisteredUser(VerifyRegisteredUser $request) {
        try {
            $result = $this->model::where('id', '=', $request->user_id)->update(['status' => 'Active', 'email_verified_at' => date('Y-m-d h:i:s')]);
            if ($result) {
                $user = $this->model::find($request->user_id);
                //Checking for email address if smartawards contains only digits before @
                $smartAwardsMailSendFlag = smartAwardsEmailSendResctrict($user->email);
                if ($smartAwardsMailSendFlag == 1) {
                    Notification::send($user, new VerifyRegisteredUserNotification($user, 'Operator'));
                }
            }
            return response()->json(setResponse([], ['message' => __('operator.FrontUser.verified')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function storeSmartAwardUser(FrontUserSmartAwardRequest $request) {
        try {
            $moduleData = $request;
            $result = $this->user_repository->createSmartAwardUser($moduleData);
            if (isset($result['response_type']) && $result['response_type'] == "Error") {
                return response()->json(setErrorResponse($result['message']))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else {
                $retunData = array('id' => $result->id);
                return response()->json(setResponse($retunData, ['message' => __('operator.FrontUser.addUser')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function changeSmartAwardUserEmail(FrontUserUpdateSmartAwardRequest $request) {
        try {
            $response = $this->user_repository->changeSmartAwardUserEmail($request);
            if (isset($response['message']) && $response['message'] != "") {
                return response()->json(setErrorResponse($response['message']))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else {
                $data['email'] = $request->new_email_address;
                return response()->json(setResponse($data, ['message' => __('operator.FrontUser.updateEmail')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function setSmartAwardUserPassword(ChangePasswordSmartAwardRequest $request) {
        try {
            $response = $this->user_repository->setSmartAwardUserPassword($request);
            if (isset($response['message']) && $response['message'] != "") {
                return response()->json(setErrorResponse($response['message']))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else {
                $data['email'] = $request->email;
                return response()->json(setResponse($data, ['message' => __('operator.FrontUser.updatePassword')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function smartAwardsAssignedCourses(AssignedCourseDetailsSmartAwardRequest $request) {
        try {
            $trainingCourseIds = $request->training_course_ids;
            $master_user_id = config('constants.smart_award_operator_id');
            $userData = User::where('email', $request->user_email)->where('added_user_id', $master_user_id)->first();
            if (!empty($userData)) {
                $user_id = $userData->id;
                if (count($trainingCourseIds) > 0) {

                    /** Checking for the trainig courses not exists in table or belongs to another operator * */
                    $training_course_ids = TrainingCourse::whereIn('id', $trainingCourseIds)
                                    ->where('master_user_id', $master_user_id)
                                    ->where('status', 'Active')
                                    ->whereNull('deleted_at')
                                    ->pluck('id')->toArray();
                    $not_exist_temp_ids = [];
                    foreach ($trainingCourseIds as $key => $value) {
                        if (!in_array($value, $training_course_ids)) {
                            $not_exist_temp_ids[$key] = $value;
                        }
                    }

                    if (!empty($not_exist_temp_ids)) {
                        $not_exist_temp_ids = implode(',', $not_exist_temp_ids);
                        $errormessage = "Training Course ID " . $not_exist_temp_ids . " not found";
                        return response()->json(setErrorResponse($errormessage))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                    }
                    /** Checking for the trainig courses not exists in table or belongs to another operator * */
                    /** For default training course ids assigned to user * */
                    $default_training_course_ids = TrainingCourse::whereIn('id', $trainingCourseIds)
                                    ->where('master_user_id', $master_user_id)
                                    ->where('status', 'Active')
                                    ->where('is_default', 1)
                                    ->whereNull('deleted_at')
                                    ->pluck('id')->toArray();
                    $default_temp_ids = [];
                    foreach ($default_training_course_ids as $key => $default_ids) {
                        if (in_array($default_ids, $trainingCourseIds)) {
                            $default_temp_ids[$key] = $default_ids;
                        }
                    }

                    if (!empty($default_temp_ids)) {
                        $def_ids = implode(',', $default_temp_ids);
                        $message = "User Account already assigned to the default Training course ID " . $def_ids;
                        return response()->json(setErrorResponse($message))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                    }
                    /** For default training course ids assigned to user * */
                    /** Training course assignment process to user * */
                    $training_course_ids = TrainingCourse::whereIn('id', $trainingCourseIds)
                                    ->where('master_user_id', $master_user_id)
                                    ->where('status', 'Active')
                                    ->where('is_default', 0)
                                    ->whereNull('deleted_at')
                                    ->pluck('id')->toArray();

                    //delete existing assigned course
                    $oldCourse = UserAssignTrainingCourses::where(['user_id' => $user_id, 'master_user_id' => $master_user_id])->get();
                    UserAssignTrainingCourses::where(['user_id' => $user_id, 'master_user_id' => $master_user_id])->where('is_default_course', '!=', '1')->delete();
                    foreach ($training_course_ids as $key => $courseId) {
                        $newData = [];
                        $newData['master_user_id'] = $master_user_id;
                        $newData['user_id'] = $user_id;
                        $newData['training_course_id'] = $courseId;
                        $newData['is_manual_course'] = 1;
                        if (!is_null($user_id)) {
                            $courseExist = UserAssignTrainingCourses::where(['user_id' => $user_id, 'master_user_id' => $master_user_id, 'training_course_id' => $courseId])->first();
                            if (empty($courseExist)) {
                                UserAssignTrainingCourses::create($newData);
                            }
                        }
                    }
                    /** Training course assignment process to user * */
                    $userDetails = User::find($user_id);
                    // Send Push notifications

                    if (!$oldCourse->isEmpty()) {
                        $oldIds = $oldCourse->pluck('training_course_id')->toArray();
                        $newIds = $training_course_ids;
                        $notificationListIds = array_diff($newIds, array_values($oldIds));
                        if (count($notificationListIds) > 0) {
                            dispatch((new \App\Jobs\CourseNotificationJob($userDetails, $master_user_id, array_values($notificationListIds))));
                        }
                    } else {

                        $defaultCourse = UserAssignTrainingCourses::where(['user_id' => $user_id, 'master_user_id' => $master_user_id])->where('is_default_course', '=', '1')->get();

                        if (!$defaultCourse->isEmpty()) {
                            $arrayDiff = array_diff($training_course_ids, array_values($defaultCourse->pluck('training_course_id')->toArray()));
                            if (!empty($arrayDiff)) {
                                dispatch((new \App\Jobs\CourseNotificationJob($userDetails, $master_user_id, array_values($arrayDiff))));
                            }
                        } else {
                            dispatch((new \App\Jobs\CourseNotificationJob($userDetails, $master_user_id, $training_course_ids)));
                        }
                    }
                } else {
                    UserAssignTrainingCourses::where(['user_id' => $user_id, 'master_user_id' => $master_user_id]) /* ->where('is_default_course', '!=', '1') */->delete();
                }

                $tIds = implode(',', $training_course_ids);
                $data['user_email'] = $request->user_email;
                $message = "User Account assigned to Training course ID " . $tIds . " successfully.";

                return response()->json(setResponse($data, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
            } else {
                return response()->json(setErrorResponse(__('operator.FrontUser.notFound')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Store Training Course Submodule Quiz Results
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function smartAwardsStoreQuizResult(StoreQuizResultSmartAwardRequest $request) {
        try {
            $data = $request->all();
            $master_user_id = config('constants.smart_award_operator_id');
            $user = User::where('email', $data['email_address'])->where('added_user_id', $master_user_id)->first();
            if ($user) {
                if ($user->user_relation) {
                    if ($user->user_relation->is_disabled) {
                        return response()->json(setErrorResponse(__('operator.FrontUser.accountdisabled')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                    } elseif ($user->status == 'Active') {

                        $training_course = TrainingCourse::where(['id' => $request->training_course_id, 'master_user_id' => $master_user_id, 'status' => 'Active'])
                                ->whereNull('deleted_at')
                                ->first();

                        // Check training course exists
                        if ($training_course) {

                            $submoduleDetails = TrainingCourseSubmoduleDetails::where('training_course_id', $training_course->id)
                                    ->where('submodule_type_id', 12)
                                    ->where('status', 'Active')
                                    ->first();

                            // check submodule exists
                            if ($submoduleDetails) {

                                $userAssignTrainingCourse = UserAssignTrainingCourses::where('user_id', $user->id)->whereMasterUserId($master_user_id)->where('training_course_id', $training_course->id)->first();

                                //check training course assined to user
                                if ($userAssignTrainingCourse) {
                                    $quizResult = TrainingCourseSubModuleQuizResults::where(['submodule_id' => $submoduleDetails->id, 'user_id' => $user->id])->orderBy('attempts', 'DESC')->first();
                                    if (!is_null($quizResult) && $quizResult->passed_by_operator == 1) {
                                        return response()->json(setResponse([], ['status' => 1, 'message' => __('user.TrainingCourseSubmodule.quiz-result.passed-operator')]))->setStatusCode(Response::HTTP_OK);
                                    } else {
                                        $quiz = TrainingCourseSubModuleQuiz::where(['submodule_id' => $submoduleDetails->id])->where('created_by', $master_user_id)->first();

                                        // Check quiz exists
                                        if ($quiz) {
                                            $totalQuestions = $quiz->categories->count();

                                            if ($request->total_questions != $totalQuestions) {
                                                $message = "Total questions is incorrect. There is " . $totalQuestions . " questions in this quiz";
                                                return response()->json(setErrorResponse(__($message)))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                                            } else {
                                                $res['submodule_id'] = $submoduleDetails->id;
                                                $res['is_pass'] = $data['pass'];
                                                $res['correct_answers'] = $data['result'];
                                                $res['total_questions'] = $data['total_questions'];
                                                $res['training_course_id'] = $submoduleDetails->training_course_id;
                                                $res['module_id'] = $submoduleDetails->module_id;
                                                $res['user_id'] = $user->id;
                                                $res['master_user_id'] = $master_user_id;
                                                $res['time_spent'] = 0;

                                                $passingCriteria = $quiz->passing_criteria;

                                                $quizPassCount = (int) round(($totalQuestions * $passingCriteria) / 100);
                                                $totalCorrectAnswers = $res['correct_answers'];

                                                $previousAttempt = TrainingCourseSubModuleQuizResults::where(['training_course_id' => $submoduleDetails->training_course_id, 'submodule_id' => $submoduleDetails->id, 'user_id' => $user->id])->select('attempts', 'is_pass')->orderBy('attempts', 'desc')->first();
                                                $res['attempts'] = is_null($previousAttempt) ? 1 : $previousAttempt->attempts + 1;

                                                if (!empty($previousAttempt) && $previousAttempt->is_pass == 1) {
                                                    return response()->json(setResponse([], ["message" => "You have passed this quiz."]))->setStatusCode(Response::HTTP_OK);
                                                } else {
                                                    $result = TrainingCourseSubModuleQuizResults::create($res);

                                                    // Progress Calculation
                                                    if ($submoduleDetails->enable_time_spend == 1) {

                                                        // Check submodule progress
                                                        $isExist = TrainingCourseSubmoduleProgress::status()->where(['user_id' => $user->id, 'training_course_id' => $data['training_course_id'], 'module_id' => $res['module_id'], 'submodule_id' => $res['submodule_id'], 'submodule_type_id' => 12])->first();
                                                        $res['time_spent'] = (!is_null($isExist) ? ($isExist->time_spent + $res['time_spent']) : $res['time_spent']);

                                                        $res['completion_percentage'] = ($result->is_pass == 1 ? $submoduleDetails->completion_percentage : 0);

                                                        // Quiz Progress
                                                        if ($submoduleDetails->condition == 'and') {
                                                            if ($res['time_spent'] >= $submoduleDetails->time_spent && $data['completion_percentage'] == $submoduleDetails->completion_percentage) {
                                                                $progress = 100;
                                                            } elseif ($data['completion_percentage'] == 100 && $data['time_spent'] >= $submoduleDetails->time_spent) {
                                                                $progress = 100;
                                                            } else {
                                                                $timeSpent = (int) ((100 * $res['time_spent']) / $submoduleDetails->time_spent);
                                                                $progress = (int) (($timeSpent + $res['completion_percentage']) / 2);
                                                            }
                                                        } else {
                                                            $timeSpent = (int) ((100 * $res['time_spent']) / $submoduleDetails->time_spent);
                                                            $progress = max($res['completion_percentage'], $timeSpent);
                                                        }
                                                        $submoduleProgress = $progress;
                                                    } else {
                                                        $submoduleProgress = ($totalCorrectAnswers >= $quizPassCount ? 100 : (int) (($totalCorrectAnswers / $totalQuestions) * 100));
                                                    }

                                                    // Submodule Update progress
                                                    $progressData = ['user_id' => $user->id, 'training_course_id' => $res['training_course_id'], 'module_id' => $res['module_id'], 'submodule_id' => $res['submodule_id'], 'submodule_type_id' => 12, 'time_spent' => $res['time_spent'], 'submodule_progress' => 10];

                                                    (new TrainingCourseSubmoduleProgress)->updateSubmoduleData($progressData);

                                                    // Module progress
                                                    (new TrainingCourseModuleProgress)->calculateModuleProgress($res);

                                                    // Training course progress
                                                    (new TrainingCourseProgress)->calculateTrainingCourseProgress($res);

                                                    // Update New flag
                                                    (new TrainingCourseModuleProgress)->updateIsNew($progressData);
                                                    (new TrainingCourseProgress)->updateIsNew($progressData);

                                                    return response()->json(setResponse([], ["message" => "Quiz submitted successfully."]))->setStatusCode(Response::HTTP_OK);
                                                }
                                            }
                                        } else {
                                            return response()->json(setErrorResponse(__('operator.quiz.notFound')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                                        }
                                    }
                                } else {
                                    return response()->json(setErrorResponse(__('operator.TrainingCourse.notAssignCourse')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                                }
                            } else {
                                return response()->json(setErrorResponse(__('operator.TrainingCourse.moduleDosentExist')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                            }
                        } else {
                            return response()->json(setErrorResponse(__('operator.TrainingCourse.dosentExist')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                        }
                    } elseif ($user->status == 'Pending') {
                        return response()->json(setErrorResponse(__('operator.FrontUser.accountPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                    } elseif (is_null($user->email_verified_at)) {
                        return response()->json(setErrorResponse(__('operator.FrontUser.verificationPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                    } elseif ($user->status == 'Inactive') {
                        return response()->json(setErrorResponse(__('operator.FrontUser.accountInactive')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                    } elseif ($user->status == 'Rejected') {
                        return response()->json(setErrorResponse(__('operator.FrontUser.accountRejected')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                    }
                } else {
                    return response()->json(setErrorResponse(__('operator.FrontUser.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
            } else {
                return response()->json(setErrorResponse(__('operator.FrontUser.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Assessor User List
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getAssessorUsersList(CommonListingRequest $request) {
        try {
            // checking for assessment portal feature setting
            $checkAssessment = checkAssessment();
            if ($checkAssessment == 1) {
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
            // main
            $users = $this->user_repository->getAssessorUsersList($request->all());
            if ($request->isExport) {
                return $this->user_repository->AssessorExportCsv($users->get(), $request->exportFields);
            }
            return FrontUserResource::collection($users->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Assessor User List
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function AssignAssessor(AssignAssessorRequest $request) {
        try {
            // checking for assessment portal feature setting
            $checkAssessment = checkAssessment();
            if ($checkAssessment == 1) {
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
            // main
            $this->user_repository->AssignAssessor($request->all());
            return response()->json(setResponse($request->all(), ['message' => 'Assessor ID Assign Successfully']))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Assessor User List
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getAssessorAssignFrontUsersList(AssessorAssignFrontUsersRequest $request) {
        try {
            // checking for assessment portal feature setting
            $checkAssessment = checkAssessment();
            if ($checkAssessment == 1) {
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
            // main
            $users = $this->user_repository->getAssessorAssignFrontUsersList($request->all());
            return AssessorAssignFrontUserResource::collection($users->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function getOnlyAssessorAssignFrontUsersList(AssessorAssignFrontUsersRequest $request) {
        try {
            // checking for assessment portal feature setting
            $checkAssessment = checkAssessment();
            if ($checkAssessment == 1) {
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
            // main
            if(isset($request['user_id'])){
            $users = $this->user_repository->getOnlyAssessorAssignFrontUsersList($request->all());
            return AssessorAssignFrontUserResource::collection($users->paginate($request->per_page));
            }else{
                $users = [];
                return AssessorAssignFrontUserResource::collection($users->paginate($request->per_page));
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

}
