<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;
use Illuminate\Validation\Rule;

class PDFSettingRequest extends CustomFormRequest {

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array {
		$request = $this;
		$rules = [
			'operator_id' => 'required|int',
			'is_pdf_settings_on' => 'required|boolean'
		];
		if (isset($request->is_pdf_settings_on) && $request->is_pdf_settings_on) {
			$rules = array_merge($rules, [
				'short_logo' => [
					Rule::requiredIf(function ()use ($request) {
					$shortLogoName = \DB::table('white_label_settings')->where('operator_id', $request->operator_id)->value('short_logo');
					return $shortLogoName === null;
					}),
					'file',
					'mimes:png,jpg,jpeg',
				],
				'favicon_logo' => [
					Rule::requiredIf(function ()use ($request) {
					$faviconLogoName = \DB::table('white_label_settings')->where('operator_id', $request->operator_id)->value('favicon_logo');
					return $faviconLogoName === null;
					}),
					'file',
					'mimes:png,jpg,jpeg',
				],
				'sidebar_logo' => [
					Rule::requiredIf(function ()use ($request) {
					$sidebarLogoName = \DB::table('white_label_settings')->where('operator_id', $request->operator_id)->value('sidebar_logo');
					return $sidebarLogoName === null;
					}),
					'file',
					'mimes:png,jpg,jpeg',
				],
				'main_color_scheme' => 'required',
				'pass_color_scheme' => 'required',
				'fail_color_scheme' => 'required',
			]);
		}
		return $rules;
    }

    public function messages(): array {
		return [
			'operator_id.required' => __('Operator Id is required.'),
			'is_pdf_settings_on.required' => __('PDF Feature setting is required.'),
			'short_logo.required' => __('Short logo is required.'),
			'favicon_logo.required' => __('Favicon logo is required.'),
			'sidebar_logo.required' => __('Sidebar logo is required.'),
			'main_color_scheme.required' => __('Main color scheme is required.'),
			'pass_color_scheme.required' => __('Pass color scheme is required.'),
			'fail_color_scheme.required' => __('Fail color scheme is required.'),
		];
    }
}
