<?php
/**
     * @OA\Post(
     *     path="/admin/users/assignOperators",
     *     tags={"Admin - Users"},
     *     summary="Assigned Operators",
     *     description="Assigned Operators",
     *     operationId="assignOperators",
     *     @OA\Parameter(
     *         description="User Id",
     *         name="user_id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Parameter(
     *         description="Operator Ids",
     *         name="operator_list",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */