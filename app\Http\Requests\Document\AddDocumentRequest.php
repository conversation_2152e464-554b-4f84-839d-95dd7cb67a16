<?php

namespace App\Http\Requests\Document;

use App\Http\Requests\ValidationRequests;

class AddDocumentRequest extends ValidationRequests {

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array {
        return [
            'image' => "required|image|mimes:jpg,jpeg,png,svg|max:2048",
            'module' => "required|string|in:" . implode(',', array_keys(config('constants.module'))),
        ];
    }

}
