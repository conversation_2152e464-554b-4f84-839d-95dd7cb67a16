<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\UserCourseCertificate;
use App\Http\Resources\V1\UserCourseCertificateResource;

class UserCourseCertificateController extends Controller {

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    private $model;

    public function __construct() {
        $this->model = new UserCourseCertificate();
    }

    /**
     * Display the specified resource.
     * Product Details
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id) {
        try {
            $certificateNo = c_decrypt($id);
            $certificateData = $this->model->where('certificate_no', $certificateNo)->first();
            if ($certificateData) {
                $certificateData->save();
                return ($certificateData) ?
                        (new UserCourseCertificateResource($certificateData)) :
                        response()->json(setErrorResponse(__('Certificate not found')))->setStatusCode(Response::HTTP_NOT_FOUND);
            } else {
                return response()->json(setErrorResponse(__('Certificate not found')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function showDetail(Request $request) {
        try {
            $certificateData = $this->model->where('certificate_no', $request->query('certificate_no'))->first();
            if ($certificateData) {
                return ($certificateData) ?
                        (new UserCourseCertificateResource($certificateData)) :
                        response()->json(setErrorResponse(__('Certificate not found')))->setStatusCode(Response::HTTP_NOT_FOUND);
            } else {
                return response()->json(setErrorResponse(__('Certificate not found')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }


    /**
     * Display the specified resource.
     * Product Details
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function validateCertificate($id) {
        try {
            $certificateNo = c_decrypt($id);
            $certificateData = $this->model->where('certificate_no', $certificateNo)->first();
            if ($certificateData) {
                $certificateData->save();
                return response()->json(setResponse([], ['message' => __('Certificate verification completed successfully.')]))->setStatusCode(Response::HTTP_OK);
            } else {
                return response()->json(setErrorResponse(__('Certificate not found')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function validateCertificateNumber(Request $request) {
        try {
            $certificateData = $this->model->where('certificate_no', $request->query('certificate_number'))->first();
            if ($certificateData) {
                $certificateData->save();
                return response()->json(setResponse([], ['message' => __('Certificate verification completed successfully.')]))->setStatusCode(Response::HTTP_OK);
            } else {
                return response()->json(setErrorResponse(__('Certificate not found')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
