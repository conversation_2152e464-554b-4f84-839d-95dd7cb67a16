<?php

namespace App\Models;

use DBTableNames;
use App\Models\PQMSQuestionCategory;
use App\Models\CableMatrixData;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class TrainingCourseSubModulePracticalAssessmentQuestion extends Model
{
    use SoftDeletes;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_QUESTIONS;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'submodule_id', 'question', 'question_type', 'is_required', 'new_update', 'title', 'question_order','category_id','question_list_order','file_name','file_path'
    ];

    const QUESTION_TYPES = ['text', 'rating', 'single', 'multiple', 'calculated_single', 'location', 'image', 'video','cableMatrix','locationSelection','document'];

    public static function boot()
    {
        parent::boot();

        self::created(function ($model) {
            $subModuleUpdate = TrainingCourseSubmoduleDetails::find($model->submodule_id);
            $subModuleUpdate->total_subdata += 1;
            $subModuleUpdate->save();
            flushCacheTags([\cacheTag::APP_PRACTICAL_ASS . ':' . $model->submodule_id]);
        });

        self::deleted(function ($model) {
            $subModuleUpdate = TrainingCourseSubmoduleDetails::find($model->submodule_id);
            $subModuleUpdate->total_subdata = ($subModuleUpdate->total_subdata > 0) ? $subModuleUpdate->total_subdata - 1 : $subModuleUpdate->total_subdata;
            $subModuleUpdate->save();
        });

        self::deleting(function ($model) {
            $model->options()->delete();
        });

        self::updated(function ($model) {
            flushCacheTags([\cacheTag::APP_PRACTICAL_ASS . ':' . $model->submodule_id]);
        });
    }

    public function options()
    {
        return $this->hasMany(TrainingCourseSubModulePracticalAssessmentOption::class, 'question_id', 'id');
    }

    public function subModule()
    {
        return $this->belongsTo(TrainingCourseSubmoduleDetails::class, 'submodule_id', 'id');
    }

    public function answers()
    {
        return $this->hasMany(TrainingCourseSubModulePracticalAssessmentAnswer::class, 'question_id', 'id');
    }

    public function answer_data()
    {
        return $this->belongsTo(TrainingCourseSubModulePracticalAssessmentAnswer::class, 'question_id', 'id');
    }

    public function cableMatrixData()
    {
        return $this->hasMany(CableMatrixData::class, 'question_id', 'id');
    }

    // Helper methods for cable matrix
    public function hasCableMatrixData()
    {
        return $this->question_type === 'cableMatrix' && $this->cableMatrixData()->exists();
    }

    public function validateCableMatrixCsv($csvData)
    {
        $requiredHeaders = ['Cable Diameter (mm)', 'Cable Type', 'Cable Description'];
        $csvHeaders = array_keys($csvData[0] ?? []);

        $missingHeaders = array_diff($requiredHeaders, $csvHeaders);

        return [
            'valid' => empty($missingHeaders),
            'missing_headers' => $missingHeaders,
            'available_headers' => $csvHeaders
        ];
    }

    public function createData($data)
    {
        $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
        \Illuminate\Support\Facades\Log::info('Cable Matrix: createData called', [
            'operator_id' => $operatorId,
            'question_count' => count($data['question_list'] ?? [])
        ]);

        foreach ($data['question_list'] as $question) {
            if(isset($question['type'])){
                if($question['type'] == 'general'){
                    $title = null;
                    if (isset($question['title'])) {
                        $title = $question['title'];
                    }
                    $selfQuestion = self::create(array_merge($question, [
                        'submodule_id' => $data['submodule_id'],
                        'title' => $title
                    ]));

                    // Handle cable matrix CSV processing
                    if ($question['question_type'] === 'cableMatrix') {
                        \Illuminate\Support\Facades\Log::info('Cable Matrix: Processing cable matrix question', [
                            'question_id' => $selfQuestion->id,
                            'question_type' => $question['question_type'],
                            'has_file_path' => isset($question['file_path']),
                            'file_path' => $question['file_path'] ?? 'not set'
                        ]);

                        if (isset($question['file_path'])) {
                            $cableMatrixService = new \App\Services\CableMatrixService();
                            $csvResult = $cableMatrixService->processCsvFromFilePath($selfQuestion->id, $question['file_path'], $operatorId);

                            if (!$csvResult['success']) {
                                // Log error but don't fail the question creation
                                \Illuminate\Support\Facades\Log::error('Cable Matrix CSV Processing failed for question ' . $selfQuestion->id . ': ' . $csvResult['message']);
                            } else {
                                \Illuminate\Support\Facades\Log::info('Cable Matrix CSV Processing successful for question ' . $selfQuestion->id);
                            }
                        } else {
                            \Illuminate\Support\Facades\Log::warning('Cable Matrix question created without file_path', ['question_id' => $selfQuestion->id]);
                        }
                    }

                    // Handle location matrix CSV processing
                    if ($question['question_type'] === 'locationMatrix') {
                        \Illuminate\Support\Facades\Log::info('Location Matrix: Processing location matrix question', [
                            'question_id' => $selfQuestion->id,
                            'question_type' => $question['question_type'],
                            'has_file_path' => isset($question['file_path']),
                            'file_path' => $question['file_path'] ?? 'not set'
                        ]);

                        if (isset($question['file_path'])) {
                            $locationMatrixService = new \App\Services\LocationMatrixService();
                            $csvResult = $locationMatrixService->processCsvFromFilePath($selfQuestion->id, $question['file_path'], $operatorId);

                            if (!$csvResult['success']) {
                                // Log error but don't fail the question creation
                                \Illuminate\Support\Facades\Log::error('Location Matrix CSV Processing failed for question ' . $selfQuestion->id . ': ' . $csvResult['message']);
                            } else {
                                \Illuminate\Support\Facades\Log::info('Location Matrix CSV Processing successful for question ' . $selfQuestion->id);
                            }
                        } else {
                            \Illuminate\Support\Facades\Log::warning('Location Matrix question created without file_path', ['question_id' => $selfQuestion->id]);
                        }
                    }

                    if ($question['question_type'] === 'single' || $question['question_type'] === 'multiple' || $question['question_type'] === 'calculated_single') {
                        foreach ($question['options'] as $option) {
                            $severity_data = null;
                            if (isset($option['severity'])) {
                                $severity_data = json_encode($option['severity']);
                            }
                            TrainingCourseSubModulePracticalAssessmentOption::create(array_merge($option, [
                                'question_id' => $selfQuestion->id,
                                'severity' => $severity_data,
                            ]));
                        }
                    }
                }

                if($question['type'] == 'category'){
                    $Exits=PQMSQuestionCategory::where('submodule_id',$data['submodule_id'])->where('category_name',$question['category_name'])->first();
                    if(!empty($Exits)){
                        $CategoryId=$Exits->id;
                    }else{
                        $categoryRecord=new PQMSQuestionCategory;
                        $categoryRecord->master_user_id=$operatorId;
                        $categoryRecord->submodule_id=$data['submodule_id'];
                        $categoryRecord->category_name=$question['category_name'];
                        $categoryRecord->save();
                        $CategoryId=$categoryRecord->id;
                    }
                    foreach ($question['question'] as $cat_question) {
                        $title = null;
                        if (isset($cat_question['title'])) {
                            $title = $cat_question['title'];
                        }
                        $selfCat = self::create(array_merge($cat_question, [
                            'category_id' => $CategoryId,
                            'submodule_id' => $data['submodule_id'],
                            'title' => $title,
                            'question_list_order' => $question['question_list_order'],
                            'file_name' => $cat_question['file_name'] ?? null,
                            'file_path' => $cat_question['file_path'] ?? null,
                        ]));
                        if ($cat_question['question_type'] === 'single' || $cat_question['question_type'] === 'multiple' || $cat_question['question_type'] === 'calculated_single') {
                            foreach ($cat_question['options'] as $option) {
                                $severity_data = null;
                                if (isset($option['severity'])) {
                                    $severity_data = json_encode($option['severity']);
                                }
                                TrainingCourseSubModulePracticalAssessmentOption::create(array_merge($option, [
                                    'question_id' => $selfCat->id,
                                    'severity' => $severity_data,
                                ]));
                            }
                        }
            
                    }
                }
            }
        }
        if (isset($data['severities'])) {
            foreach ($data['severities'] as $severity) {
                Severity::create([
                    'master_user_id' => $operatorId,
                    'submodule_id' => $data['submodule_id'],
                    'severity_name' => $severity['severity_name'],
                    'value' => $severity['severity_occurrence'],
                    'severity_color_scheme' => $severity['severity_color_scheme'] ?? null,
                ]);
            }
        }

        // Upload Practical Assessment Media Comment
        $logsData = [
            'user_id' => $operatorId,
            'email' => auth()->guard('operator')->user()->email ?? null,
            'action' => 'Create',
            'action_name' => config('constants.system_logs.create_pa_submodule'),
            'extra_info' => get_submodule_name($data['submodule_id']) ?? null,
            'url' => str_replace(url('/v1/'), '', request()->fullUrl()) ?? null,
            'is_app_operator_admin' => 2,  
            'is_practical_assessment' => 1 , 
            'system_section' => 'Practical Assessment',
            'item_id' => $data['submodule_id'] ?? null,  
        ];
        custom_system_logs($logsData);
    }

    public function updateData($data, $id)
    {
        $subModule = TrainingCourseSubmoduleDetails::where('id', $id)->first();
        $existingQuestions = $subModule->allPracticalAssessmentQuestion()->pluck('id')->toArray();
        $existingSeverity = $subModule->severity()->pluck('id')->toArray();
        $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;

        $existingCategories = $subModule->practicalAssessmentCategory()->pluck('id')->toArray();
        foreach ($data['question_list'] as $question) {
            if(isset($question['type'])){
                if($question['type'] == 'general'){
                    if (isset($question['id'])) {
                        $self = self::where('id', $question['id'])->first();
                        $existingQuestionOptions = ($self->question_type === 'single' || $self->question_type === 'multiple' || $self->question_type === 'calculated_single') ? $self->options()->pluck('id')->toArray() : [];
                        $question['category_id'] = null;
                        $self->update(collect($question)->except('type')->toArray());

                        // Handle cable matrix CSV processing for updates
                        if ($question['question_type'] === 'cableMatrix' && isset($question['file_path'])) {
                            $cableMatrixService = new \App\Services\CableMatrixService();
                            $csvResult = $cableMatrixService->processCsvFromFilePath($self->id, $question['file_path'], $operatorId);

                            if (!$csvResult['success']) {
                                \Illuminate\Support\Facades\Log::error('Cable Matrix CSV Processing failed for question ' . $self->id . ': ' . $csvResult['message']);
                            }
                        }

                        // Handle location matrix CSV processing for updates
                        if ($question['question_type'] === 'locationMatrix' && isset($question['file_path'])) {
                            $locationMatrixService = new \App\Services\LocationMatrixService();
                            $csvResult = $locationMatrixService->processCsvFromFilePath($self->id, $question['file_path'], $operatorId);

                            if (!$csvResult['success']) {
                                \Illuminate\Support\Facades\Log::error('Location Matrix CSV Processing failed for question ' . $self->id . ': ' . $csvResult['message']);
                            }
                        }
                        if ($question['question_type'] === 'single' || $question['question_type'] === 'multiple' || $question['question_type'] === 'calculated_single') {
                            foreach ($question['options'] as $key => $option) {
                                if (isset($option['id'])) {
                                    $questionOption = TrainingCourseSubModulePracticalAssessmentOption::where('id', $option['id'])->first();
                                    $questionOption->update($option);
                                    unset($existingQuestionOptions[array_search($questionOption->id, $existingQuestionOptions)]);
                                } else {
                                    $option['question_id'] = $question['id'];
                                    if (isset($option['severity'])) {
                                        $option['severity'] = json_encode($option['severity']);
                                    }
                                    TrainingCourseSubModulePracticalAssessmentOption::create($option);
                                }
                            }
                        }
        
                        if (count($existingQuestionOptions) > 0) {
                            TrainingCourseSubModulePracticalAssessmentOption::whereIn('id', $existingQuestionOptions)->delete();
                        }

                        $generalQIndex = array_search($self->id, $existingQuestions);
                        if ($generalQIndex !== false) {
                            unset($existingQuestions[$generalQIndex]);
                        }
                        // unset($existingQuestions[array_search($self->id, $existingQuestions)]);
                    } else {
                        $ques = self::create(array_merge(collect($question)->except('type')->toArray(), [
                            'submodule_id' => $subModule->id, 'new_update' => 1,
                        ]));

                        // Handle cable matrix CSV processing for new questions
                        if ($question['question_type'] === 'cableMatrix' && isset($question['file_path'])) {
                            $cableMatrixService = new \App\Services\CableMatrixService();
                            $csvResult = $cableMatrixService->processCsvFromFilePath($ques->id, $question['file_path'], $operatorId);

                            if (!$csvResult['success']) {
                                \Illuminate\Support\Facades\Log::error('Cable Matrix CSV Processing failed for question ' . $ques->id . ': ' . $csvResult['message']);
                            }
                        }

                        // Handle location matrix CSV processing for new questions
                        if ($question['question_type'] === 'locationMatrix' && isset($question['file_path'])) {
                            $locationMatrixService = new \App\Services\LocationMatrixService();
                            $csvResult = $locationMatrixService->processCsvFromFilePath($ques->id, $question['file_path'], $operatorId);

                            if (!$csvResult['success']) {
                                \Illuminate\Support\Facades\Log::error('Location Matrix CSV Processing failed for question ' . $ques->id . ': ' . $csvResult['message']);
                            }
                        }

                        $subModule->new_update = 1;
                        $subModule->save();
                        if ($question['question_type'] === 'single' || $question['question_type'] === 'multiple' || $question['question_type'] === 'calculated_single') {
                            foreach ($question['options'] as $key => $option) {
                                if (isset($option['severity'])) {
                                    $severityOption = json_encode($option['severity']);
                                } else {
                                    $severityOption = '';
                                }
                                TrainingCourseSubModulePracticalAssessmentOption::create(array_merge($option, [
                                    'question_id' => $ques->id,
                                    'severity' => $severityOption,
                                ]));
                            }
                        }
                    }
                }
                if($question['type'] == 'category'){
                    if (isset($question['category_id']) && $question['category_id'] != '') {
                        $categoryRecord = PQMSQuestionCategory::whereId($question['category_id'])->first();
                    } else {
                        $categoryRecord = PQMSQuestionCategory::where('submodule_id', $data['submodule_id'])->where('category_name', $question['category_name'])->first();
                        if (empty($Exits)) {
                            $categoryRecord = new PQMSQuestionCategory;
                        }
                    }
                    $categoryRecord->master_user_id = $operatorId;
                    $categoryRecord->submodule_id = $data['submodule_id'];
                    $categoryRecord->category_name = $question['category_name'];
                    $categoryRecord->save();
                    $CategoryId = $categoryRecord->id;

                    $catIndex = array_search($CategoryId, $existingCategories);
                    if ($catIndex !== false) {
                        unset($existingCategories[$catIndex]);
                    }
                    // unset($existingCategories[array_search($CategoryId, $existingCategories)]);
                    $existingCatQuestions = $categoryRecord->categoryQuestions()->pluck('id')->toArray();
                    foreach ($question['question'] as $catQuestion) {
                        $title = null;
                        if (isset($catQuestion['title'])) {
                            $title = $catQuestion['title'];
                        }
                        $questionData = array_merge($catQuestion, [
                            'category_id' => $CategoryId,
                            'submodule_id' => $data['submodule_id'],
                            'title' => $title,
                            'question_list_order' => $question['question_list_order'],
                            'file_name' => $catQuestion['file_name'] ?? null,
                            'file_path' => $catQuestion['file_path'] ?? null,
                        ]);
                        unset($questionData['options']);
                        unset($questionData['question_media']);
                        unset($questionData['comment']);
                        if (isset($catQuestion['id']) && $catQuestion['id'] != '') {
                            $self_cat_ques = Self::whereId($catQuestion['id'])->first();
                            self::where('id', $catQuestion['id'])->update(collect($questionData)->except('type')->toArray());
                            
                            $generalCQIndex = array_search($catQuestion['id'], $existingCatQuestions);
                            if ($generalCQIndex !== false) {
                                unset($existingCatQuestions[$generalCQIndex]);
                            }

                            // unset($existingCatQuestions[array_search($catQuestion['id'], $existingCatQuestions)]);
                            $generalQIndex = array_search($catQuestion['id'], $existingQuestions);
                            if ($generalQIndex !== false) {
                                unset($existingQuestions[$generalQIndex]);
                            }
                            // unset($existingQuestions[array_search($catQuestion['id'], $existingQuestions)]);
                        } else {
                            unset($questionData['created_at']);
                            $self_cat_ques = self::create(collect($questionData)->except('type')->toArray());
                        }
                        // print_r($self_cat_ques->toArray());
                        // echo "<pre>";
                        // print_R($self_cat_ques->options()->pluck('id')->toArray());
                        $existingCatQuestionOptions = $self_cat_ques->options()->pluck('id')->toArray();
                        if ($catQuestion['question_type'] === 'single' || $catQuestion['question_type'] === 'multiple' || $catQuestion['question_type'] === 'calculated_single') {
                            foreach ($catQuestion['options'] as $option) {
                                $severity_data = null;
                                unset($option['created_at']);
                                if (isset($option['severity'])) {
                                    $severity_data = json_encode($option['severity']);
                                }
                                $optionData = array_merge($option, [
                                    'question_id' => $self_cat_ques->id,
                                    'severity' => $severity_data,
                                ]);
                                if (isset($option['id']) && $option['id'] != '') {
                                TrainingCourseSubModulePracticalAssessmentOption::whereId($option['id'])->update($optionData);
                                    unset($existingCatQuestionOptions[array_search($option['id'], $existingCatQuestionOptions)]);
                                } else {
                                    TrainingCourseSubModulePracticalAssessmentOption::create($optionData);
                                }
                            }
                        }
                       
                        if (count($existingCatQuestionOptions) > 0) {
                            TrainingCourseSubModulePracticalAssessmentOption::whereIn('id', $existingCatQuestionOptions)->delete();
                        }
                        
                    }
                }
                
            }
        }
        if(count($existingCategories) > 0) {
            $getCatQuestions = TrainingCourseSubModulePracticalAssessmentQuestion::whereIn('category_id',$existingCategories)->pluck('id')->toArray();
            TrainingCourseSubModulePracticalAssessmentOption::whereIn('id', $getCatQuestions)->delete();
            TrainingCourseSubModulePracticalAssessmentQuestion::whereIn('category_id',$existingCategories)->delete();
            PQMSQuestionCategory::whereIn('id', $existingCategories)->delete();
        }
	    if ($data['severities']) {
            foreach ($data['severities'] as $severity) {
                if (isset($severity['id'])) {
                    $severityData = Severity::where('id', $severity['id'])->first();
                    Severity::where('id', $severity['id'])->update([
                        'severity_name' => $severity['severity_name'],
                        'value' => $severity['severity_occurrence'],
                        'severity_color_scheme' => $severity['severity_color_scheme'] ?? null,
                    ]);
                    unset($existingSeverity[array_search($severityData->id, $existingSeverity)]);
                } else {
                    Severity::create([
                        'master_user_id' => $operatorId,
                        'submodule_id' => $id,
                        'severity_name' => $severity['severity_name'],
                        'value' => $severity['severity_occurrence'],
                        'severity_color_scheme' => $severity['severity_color_scheme'] ?? null,
                    ]);
                }
            }
        }
        if (count($existingQuestions) > 0) {
            self::whereIn('id', $existingQuestions)->delete();
        }
        if (count($existingSeverity) > 0) {
            Severity::whereIn('id', $existingSeverity)->delete();
        }  
        
        // Upload Practical Assessment Media Comment
        $logsData = [
            'user_id' => $operatorId,
            'email' => auth()->guard('operator')->user()->email ?? null,
            'action' => 'Update',
            'action_name' => config('constants.system_logs.update_pa_submodule'),
            'extra_info' => get_submodule_name($data['submodule_id']) ?? null,
            'url' => str_replace(url('/v1/'), '', request()->fullUrl()) ?? null,
            'is_app_operator_admin' => 2,  
            'is_practical_assessment' => 1 , 
            'system_section' => 'Practical Assessment',
            'item_id' => $data['submodule_id'] ?? null,  
        ];
        custom_system_logs($logsData);
    }

    public static function duplicatePracticalAssessment($newSubModuleChild, $oldCourseId)
    {
        $newSubmoduleId = $newSubModuleChild->id;
        $oldAssessmentQuestionsWithoutCat = self::whereSubmoduleId($newSubModuleChild->copy_from)->whereNull('category_id')->get();
        $oldSeverityList = Severity::whereSubmoduleId($newSubModuleChild->copy_from)->get();
        $oldCategories = PQMSQuestionCategory::where('submodule_id', $newSubModuleChild->copy_from)->get();
        if(count($oldCategories) > 0){
            foreach($oldCategories as $oldCat)
            {
                $newCat = $oldCat->replicate();
                $newCat->submodule_id = $newSubmoduleId;
                $newCat->save();
                $allCategoryQuestions = $oldCat->categoryQuestions($oldCat->submodule_id)->get();
                if ($allCategoryQuestions) {
                    foreach ($allCategoryQuestions as $catQuestion) {
                        $newAssessmentCatQuestion = $catQuestion->replicate();
                        $newAssessmentCatQuestion->submodule_id = $newSubmoduleId;
                        $newAssessmentCatQuestion->category_id = $newCat->id;
                        $newAssessmentCatQuestion->new_update = 0;
                        $newAssessmentCatQuestion->save();
                        if ($catQuestion->question_type === 'single' || $catQuestion->question_type === 'multiple' || $catQuestion->question_type === 'calculated_single') {
                            foreach ($catQuestion->options as $option) {
                                $option->replicate()->fill([
                                    'question_id' => $newAssessmentCatQuestion->id,
                                ])->save();
                            }
                        }
                    }
                }
            }
        }
    
        if ($oldAssessmentQuestionsWithoutCat) {
            foreach ($oldAssessmentQuestionsWithoutCat as $assessmentQuestion) {
                $newAssessmentQuestion = $assessmentQuestion->replicate();
                $newAssessmentQuestion->submodule_id = $newSubModuleChild->id;
                $newAssessmentQuestion->new_update = 0;
                $newAssessmentQuestion->save();
                if ($assessmentQuestion->question_type === 'single' || $assessmentQuestion->question_type === 'multiple' || $assessmentQuestion->question_type === 'calculated_single') {
                    foreach ($assessmentQuestion->options as $option) {
                        $option->replicate()->fill([
                            'question_id' => $newAssessmentQuestion->id,
                        ])->save();
                    }
                }
            }
        }
        if ($oldSeverityList) {
            foreach ($oldSeverityList as $severityList) {
                $newSeverityList = $severityList->replicate();
                $newSeverityList->submodule_id = $newSubModuleChild->id;
                $newSeverityList->save();
            }

        }
    }

    public static function copyPasteSubModuleData($fromSubModule, $toSubModule, $operator)
    {
        $oldAssessmentQuestions = self::whereSubmoduleId($fromSubModule->id)->whereNull('category_id')->get();
        $oldSeverityList = Severity::whereSubmoduleId($fromSubModule->id)->get();
        $oldCategories = PQMSQuestionCategory::where('submodule_id', $fromSubModule->id)->get();
        if(count($oldCategories) > 0){
            foreach($oldCategories as $oldCat)
            {
                $newCat = $oldCat->replicate();
                $newCat->submodule_id = $toSubModule->id;
                $newCat->save();
                $allCategoryQuestions = $oldCat->categoryQuestions($oldCat->submodule_id)->get();
                if ($allCategoryQuestions) {
                    foreach ($allCategoryQuestions as $catQuestion) {
                        $newAssessmentCatQuestion = $catQuestion->replicate();
                        $newAssessmentCatQuestion->submodule_id = $toSubModule->id;
                        $newAssessmentCatQuestion->category_id = $newCat->id;
                        $newAssessmentCatQuestion->new_update = 0;
                        $newAssessmentCatQuestion->save();
                        if ($catQuestion->question_type === 'single' || $catQuestion->question_type === 'multiple' || $catQuestion->question_type === 'calculated_single') {
                            foreach ($catQuestion->options as $option) {
                                $option->replicate()->fill([
                                    'question_id' => $newAssessmentCatQuestion->id,
                                ])->save();
                            }
                        }
                    }
                }
            }
        }

        if ($oldAssessmentQuestions) {
            foreach ($oldAssessmentQuestions as $assessmentQuestion) {
                $newAssessmentQuestion = $assessmentQuestion->replicate();
                $newAssessmentQuestion->submodule_id = $toSubModule->id;
                $newAssessmentQuestion->new_update = 0;
                $newAssessmentQuestion->save();
                if ($assessmentQuestion->question_type === 'single' || $assessmentQuestion->question_type === 'multiple' || $assessmentQuestion->question_type === 'calculated_single') {
                    foreach ($assessmentQuestion->options as $option) {
                        $option->replicate()->fill(['question_id' => $newAssessmentQuestion->id])->save();
                    }
                }
            }
        }
        if ($oldSeverityList) {
            foreach ($oldSeverityList as $severityList) {
                $newSeverityList = $severityList->replicate();
                $newSeverityList->submodule_id = $toSubModule->id;
                $newSeverityList->save();
            }

        }
    }
}
