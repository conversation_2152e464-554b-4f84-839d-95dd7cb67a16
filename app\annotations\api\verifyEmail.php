<?php

/**
     * @OA\Get(
     *     path="/api/verifyEmail/{key}",
     *     tags={"Mobile - Auth"},
     *     summary="Verify user email (for browser link)",
     *     description="Verify user email (for browser link)",
     *     operationId="viewVerifyEmail",
     *     @OA\Parameter(
     *         description="key",
     *         name="key",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     * )
     */

     /**
     * @OA\Post(
     *     path="/api/verifyEmail/{key}",
     *     tags={"Mobile - Auth"},
     *     summary="Verify user email (for mobile app)",
     *     description="Verify user email (for mobile app)",
     *     operationId="verifyUserEmail",
     *     @OA\Parameter(
     *         description="key",
     *         name="key",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     * )
     */