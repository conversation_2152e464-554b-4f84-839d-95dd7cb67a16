<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;
use DBTableNames;

class AdminProfileRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array
    {
        return [
            'company_name' => 'max:150',
            'contact_no' => 'max:20',
            'address' => 'max:255',
            'latitude' => ['regex:/^[-]?(([0-8]?[0-9])\.(\d+))|(90(\.0+)?)$/'],
            'longitude' => ['regex:/^[-]?((((1[0-7][0-9])|([0-9]?[0-9]))\.(\d+))|180(\.0+)?)$/'],
            'website' => ['max:150', 'regex:/^((?:http(s)?\:\/\/|www\.)(?:[-a-z0-9]+\.)*[-a-z0-9]+.*)$/'],
            'enable_manager_email' => 'in:1,0',
            'enable_unique_id' => 'in:1,0',
            'unique_id_name' => 'required_if:enable_unique_id,1|max:50',
            'enable_custom_contact_info' => 'in:1,0',
        ];
    }
    
    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'name.required' => __('Please enter name.'),
            'name.max' => __('Name may not be greater than 150 characters.'),
            'email.required' => __('Please enter email.'),
            'email.email' => __('Email must be a valid email address (E.g.: <EMAIL> ).'),
            'email.max' => __('Email may not be greater than 255 characters.'),
            'email.unique' => __('Email has already been taken.'),
        ];
    }
    
}
