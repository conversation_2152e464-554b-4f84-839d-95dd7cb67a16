<?php

namespace App\Http\Requests\Operator\v1;

use App\Http\Requests\CustomFormRequest;

class TrainingCourseSubmoduleJobMediaUpdateRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {
        return [
            'status' => 'required|in:Approved,Rejected',
            'comments' => 'required',
        ];
    }

    public function messages() : array
    {
        return [
            'status.required' => __('Status is required.'),
            'status.in' => __('Invalid status.'),
            'comments.required' => __('Comment is required.'),
        ];
    }
}
