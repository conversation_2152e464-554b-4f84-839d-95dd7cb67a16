<?php
 /**
     * @OA\Get(
     *     path="/admin/domainMapping/{id}",
     *     tags={"Admin - Domain Mapping"},
     *     summary="Domain Mapping List",
     *     description="Specific operators domains",
     *     operationId="index",
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */

/**
     * @OA\Post(
     *     path="/admin/domainMapping",
     *     tags={"Admin - Domain Mapping"},
     *     summary="Domain Mapping List",
     *     description="Add / update operators domains",
     *     operationId="store",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="domain",
     *                     description="Domain List",
     *                     type="string"
     *                 ),
     *                 example={"operator_id": 2,"domain": "['grr.la', 'gmail.com', 'yahoo.in']"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */