<?php

/**
     * @OA\Post(
     *     path="/api/oauth/logout",
     *     tags={"Mobile - Auth"},
     *     summary="User logout process",
     *     description="user logout process",
     *     operationId="logout",
     *       @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                  @OA\Property(
     *                     property="device_token",
     *                     description="device_token",
     *                     type="string"
     *                 ),
     *                 example={"device_token": "pfe9dgXujGVqDVypYOZb2b6TMrGWBP"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */