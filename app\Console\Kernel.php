<?php

namespace App\Console;

use App\Console\Commands\GetStreamToken;
use App\Console\Commands\ModulePermission;
use App\Console\Commands\UnlockModuleCron;
use App\Console\Commands\CertificateExpire;
use App\Console\Commands\CommanGetStreamId;
use App\Console\Commands\GeneratePAPDFCron;
use App\Console\Commands\PublishCourseCron;
use Illuminate\Console\Scheduling\Schedule;
use App\Console\Commands\ProgressCalculation;
use App\Console\Commands\UnlockSubModuleCron;
use App\Console\Commands\ProgressCalculationNew;
use App\Console\Commands\QuizAutoResetOnFailCron;
use App\Console\Commands\GenerateDashBoardStatics;
use Laravel\Lumen\Console\Kernel as ConsoleKernel;
use App\Console\Commands\DeactivateUserOnGetStream;
use App\Console\Commands\GenerateWhiteLabelDeeplink;
use App\Console\Commands\FlushAWSTempDirectoryCommand;
use App\Console\Commands\UpdateResourceTypeDisplayOrder;
use App\Console\Commands\QuizQuestionsImageUrlUpdateCron;
use App\Console\Commands\GenerateDeepLinkForExistingTable;
use App\Console\Commands\GenerateUserCourseCertificateCron;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        PublishCourseCron::class,
        FlushAWSTempDirectoryCommand::class,
        QuizAutoResetOnFailCron::class,
        GenerateDeepLinkForExistingTable::class,
        GetStreamToken::class,
        DeactivateUserOnGetStream::class,
        ModulePermission::class,
        CommanGetStreamId::class,
        UnlockModuleCron::class,
        UnlockSubModuleCron::class,  
        GenerateWhiteLabelDeeplink::class,
        GeneratePAPDFCron::class,
        GenerateUserCourseCertificateCron::class,
        UpdateResourceTypeDisplayOrder::class,
        GenerateDashBoardStatics::class,
        CertificateExpire::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('publish:course')->everyMinute();
        $schedule->command('aws:flush-temp-folder')->daily();
        $schedule->command('quiz:aut-reset-duration-on-fail')->everyMinute();
        $schedule->command('create:deeplink')->everyFifteenMinutes();
        // $schedule->command('image:url-update')->everyMinute();
        $schedule->command('unlock:module')->everyMinute();
        $schedule->command('unlock:submodule')->everyMinute();
        $schedule->command('create:whitelabel-deeplink')->everyFifteenMinutes();
        $schedule->command('aws:generate-pa-pdf-files')->everyMinute();
        $schedule->command('generate-user-course-certificate')->everyFiveMinutes();
        $schedule->command('dashboard:statics')->everyFifteenMinutes();
        $schedule->command('certificate:expire')->daily(); 
    }
}
