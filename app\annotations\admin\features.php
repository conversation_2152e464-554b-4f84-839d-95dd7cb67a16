<?php
/**
     * @OA\Post(
     *     path="/admin/features",
     *     tags={"Admin - Features"},
     *     summary="Features Status Change",
     *     description="Features Status Change",
     *     operationId="changeFeaturestatus",
     *      @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 type="object",
     *                  @OA\Property(
     *                     property="is_feature_on",
     *                     description="Status",
     *                     type="integer"
     *                 ),
     *           
     *                 @OA\Property(
     *                     property="feature_id",
     *                     description="Feature Id",
     *                     type="integer"
     *                 ),
     *                @OA\Property(
     *                     property="master_user_id",
     *                     description="Operator Id",
     *                     type="integer"
     *                 ),
     *                 example={"is_feature_on":"1", "feature_id":1, "master_user_id" : 50}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */