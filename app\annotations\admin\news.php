<?php
/**
     * @OA\Get(
     *     path="/admin/news/{id}",
     *     tags={"Admin - Moderate News"},
     *     summary="Get news",
     *     description="Get news",
     *     operationId="show",
     *     @OA\Parameter(
     *         description="Id of news to fetch",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */