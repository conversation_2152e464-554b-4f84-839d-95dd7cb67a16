<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterEnumValueInPracticalAssessmentMediaTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('practical_assessment_media', function (Blueprint $table) {
            DB::statement("ALTER TABLE `practical_assessment_media` CHANGE `media_type` `media_type` ENUM('Image', 'Video', 'Document') NOT NULL");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('practical_assessment_media', function (Blueprint $table) {
            DB::statement("ALTER TABLE `practical_assessment_media` CHANGE `media_type` `media_type` ENUM('Image', 'Video') NOT NULL");
        });
    }
}
