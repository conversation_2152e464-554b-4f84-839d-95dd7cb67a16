<?php
/**
     * @OA\Post(
     *     path="/api/trainingCourseSubmodule/allUploadJobList/",
     *     tags={"Mobile - Training Course Submodule"},
     *     summary="All Uploaded Jobs",
     *     description="All Uploaded Jobs",
     *     operationId="allUploadJobList",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                   @OA\Property(
     *                     property="id",
     *                     description="Operator Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="job_topic",
     *                     description="Job Topic",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="training_course",
     *                     description="Training Course",
     *                     type="string"
     *                 ),
     *                 example={"id":4, "job_topic": "MDU,CSP", "status": "Pending,Rejected", "training_course" : "MDU,CSP"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */