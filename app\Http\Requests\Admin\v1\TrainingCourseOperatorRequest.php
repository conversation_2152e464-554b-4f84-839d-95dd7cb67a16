<?php

namespace App\Http\Requests\Admin\v1;

use DBTableNames;
use App\Http\Requests\CustomFormRequest;

class TrainingCourseOperatorRequest extends CustomFormRequest {

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'training_course_id' => 'required|numeric|exists:'.\DBTableNames::TRAINING_COURSE.',id'
        ];
    }
    
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'training_course_id.required' => __('Course ID is required.'),
            'training_course_id.numeric' => __('Course ID is invalid.'),
        ];
    }

}
