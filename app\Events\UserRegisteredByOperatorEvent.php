<?php

namespace App\Events;

use Illuminate\Foundation\Events\Dispatchable;

class UserRegisteredByOperatorEvent extends Event
{
    use Dispatchable;
    
    public $user;
    public $password;
    
    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($user, $password)
    {
        $this->user = $user;
        $this->password = $password;
    }
}
