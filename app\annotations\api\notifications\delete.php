<?php 
/**
     * @OA\Delete(
     *     path="/api/notifications/delete",
     *     tags={"Mobile - Notifications"},
     *     summary="Delete Notifications",
     *     description="Delete Notifications",
     *     operationId="delete",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="ids",
     *                     description="Notification Ids",
     *                     type="string"
     *                 ),
     *                 example={"id": 9}
     *              )
     *          )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */