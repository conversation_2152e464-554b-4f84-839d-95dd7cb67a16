<?php

namespace App\Http\Requests\Operator\v1;

use App\Http\Requests\CustomFormRequest;

class TrainingCourseSubModuleJobsListingRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array
    {
        return [
            'submodule_id' => 'required|numeric|exists:'.\DBTableNames::TRAINING_COURSE_SUBMODULE_DETAILS.',id',
            'per_page' => 'numeric',
            'page' => 'numeric',
            'order_by' => "in:ASC,DESC,asc,desc",
            'filters' => 'array',
        ];
    }
    
    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'submodule_id.required' => __('Sub module Id is required.'),
            'submodule_id.numeric' => __('Sub module Id is invalid.'),
            'submodule_id.exists' => __('Sub module not found.'),
            'per_page.numeric' => __('Please enter valid per page limit'),
            'page.numeric' => __('Please enter valid page number'),
            'order_by.in' => __('Please enter valid sort order'),
        ];
    }
    
}
