<?php
/**
     * @OA\Post(
     *     path="/api/oauth/web/forgotPassword",
     *     tags={"Web - Auth"},
     *     summary="Web user forgot password process",
     *     description="Web user forgot password process",
     *     operationId="webForgotPassword",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="email",
     *                     description="Email",
     *                     type="string"
     *                 ),
     *                 example={"email": "<EMAIL>"}
     *              )
     *          )
     *      ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     * )
     */