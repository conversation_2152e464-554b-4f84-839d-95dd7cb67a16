<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Artisan;

class AlterTableForDeeplinks extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $tables = [
            'training_course_submodule_happy_unhappy',
            'training_course_submodule_video_guide',
            'training_course_submodule_image_gallery',
            'training_course_submodule_product_list',
            'training_course_submodule_details',
            'training_course',
            'resources',
            'products'
        ];
        foreach ($tables as $table) {
        // Make 'share_url' nullable (set to null) where it currently has a value
        \DB::table($table)->whereNotNull('share_url')->update(['share_url' => null]);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
