<?php

namespace App\Http\Controllers\Api\V1;

use DBTableNames;
use VideoThumbnail;
use App\Models\User;
use App\Models\MasterUser;
use App\Models\AdjustResult;
use Illuminate\Http\Request;
use App\Models\ScormProgress;
use Illuminate\Http\Response;
use App\Models\TrainingCourse;
use App\Models\JobProgressMedia;
use App\Models\WebNotifications;
use App\Models\whiteLabelSetting;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\HotspotUploaderMedia;
use Illuminate\Support\Facades\Auth;
use App\Models\SelfAssessmentAttempt;
use App\Models\TrainingCourseModules;
use App\Models\TrainingCourseProgress;
use App\Notifications\KitOverviewEmail;
use App\Http\Resources\CustomCollection;
use App\Models\PracticalAssessmentMedia;
use App\Http\Requests\V1\ProgressRequest;
use App\Models\TrainingCourseEmailDetail;
use App\Http\Requests\V1\UploadJobRequest;
use App\Http\Resources\V1\AttemptResource;
use App\Models\TrainingCourseSubModuleJob;
use App\Http\Requests\V1\SubmitScormRequest;
use App\Models\TrainingCourseModuleProgress;
use App\Models\TrainingCourseSubmoduleDetails;
use App\Http\Requests\V1\CourseFeedbackRequest;
use App\Models\TrainingCourseSubmoduleProgress;
use App\Notifications\UserJobUploadNotification;
use App\Http\Requests\V1\SendEmailProductRequest;
use App\Models\TrainingCourseSubModuleTitleSlide;
use App\Models\TrainingCourseSubModuleVideoGuide;
use App\Models\TrainingCourseSubmoduleJobProgress;
use App\Models\TrainingCourseSubModuleProductList;
use App\Models\TrainingCourseSubModuleQuizResults;
use App\Models\TrainingCourseSubModuleUploadVideo;
use App\Notifications\UserPhotoUploadNotification;
use App\Http\Requests\V1\UploadImageHotspotRequest;
use App\Models\TrainingCourseSubModuleConfirmation;
use App\Models\TrainingCourseSubModuleHappyUnhappy;
use App\Models\TrainingCourseSubModuleImageGallery;
use App\Models\TrainingCourseSubModuleImageHotspot;
use App\Models\TrainingCourseSubModulePhotoHotspot;
use App\Http\Requests\V1\PracticalAssessmentRequest;
use App\Models\TrainingCourseSubModuleDocumentViewer;
use App\Http\Resources\V1\TrainingCourseScormResource;
use App\Models\TrainingCourseSubModuleConfirmationBox;
use App\Models\TrainingCourseSubModuleVideoGuideSteps;
use App\Models\TrainingCourseSubModuleFeedbackQuestion;
use App\Http\Requests\V1\PracticalAssessmentMediaRequest;
use App\Notifications\TrainingCourseFeedbackNotification;
use App\Http\Resources\V1\PracticalAssessmentMediaResource;
use App\Http\Resources\V1\TrainingCourseThreeSixtyResource;
use App\Http\Resources\V1\TrainingCourseTitleSlideResource;
use App\Http\Resources\V1\TrainingCourseProductListResource;
use App\Http\Resources\V1\TrainingCourseUploadVideoResource;
use App\Http\Resources\V1\TrainingCourseConfirmationResource;
use App\Models\TrainingCourseSubModuleFeedbackQuestionAnswer;
use App\Http\Resources\V1\TrainingCourseMicroLearningResource;
use App\Http\Resources\V1\TrainingCourseSubModuleListResource;
use App\Models\TrainingCourseSubmoduleHotspotUploaderProgress;
use App\Http\Resources\V1\TrainingCourseDocumentViewerResource;
use App\Models\TrainingCourseSubModulePracticalAssessmentMedia;
use App\Http\Requests\V1\PracticalAssessmentMediaCommentRequest;
use App\Http\Resources\V1\TrainingCourseUploadJobDetailResource;
use App\Models\TrainingCourseSubModulePracticalAssessmentAnswer;
use App\Http\Resources\V1\TrainingCourseHappyUnhappyListResource;
use App\Http\Resources\V1\TrainingCourseImageGalleryListResource;
use App\Models\TrainingCourseSubModulePracticalAssessmentResults;
use App\Http\Resources\V1\TrainingCourseSubmoduleProgressResource;
use App\Models\TrainingCourseSubModulePracticalAssessmentQuestion;
use App\Models\TrainingCourseSubModuleSelfAssessmentAnswersAttempt;
use App\Http\Resources\V1\TrainingCourseVideoGuideStepsListResource;
use App\Http\Resources\V1\TrainingCourseVideoGuideStepDetailResource;
use App\Models\TrainingCourseSubModulePracticalAssessmentAnswersJson;
use App\Repositories\TrainingCourseSubModulePracticalAssessmentRepository;
use App\Http\Resources\V1\TrainingCourseSubModulePracticalAssessmentCategoryQuestionResourceMobile;

class TrainingCourseSubModuleController extends Controller
{

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    private $model;

    public function __construct()
    {
        $this->model = new TrainingCourseSubmoduleDetails();
        $this->PracticalAssessmentAnswer = new TrainingCourseSubModulePracticalAssessmentAnswersJson();
        $this->PracticalAssessmentRepository = new TrainingCourseSubModulePracticalAssessmentRepository($this->PracticalAssessmentAnswer);
    }

    /**
     * Get Training Course Submodule Title Slide Detail
     *
     * @return \Illuminate\Http\Response
     */
    public function getTitleSlide($id)
    {
        try {
            $subModule = $this->model::with('titleSlide:submodule_id,title,subtitle,button_text')
                                        ->where('submodule_type_id',config('constants.submodule_types.title_slide'))
                                        ->find($id);
            if($subModule){
                $checkTrainingCourse = checkTrainingCourse($subModule);
                if($checkTrainingCourse == 1){
                    return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
                if($checkTrainingCourse == 2){
                    return response()->json(setErrorResponse(__('user.TrainingCourseModule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                if($subModule->status != 'Active'){
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                return (new TrainingCourseTitleSlideResource($subModule));
            }else{
                return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule ThreeSixty Detail
     *
     * @return \Illuminate\Http\Response
     */
    public function getThreeSixtyUrl($id)
    {
        try {
            $subModule = $this->model->where('submodule_type_id',config('constants.submodule_types.web_view'))->find($id);
            if($subModule){
                $touchCount = TrainingCourseSubmoduleProgress::where(['submodule_id' => $id, 'user_id' => auth()->user()->id])->select('touch_count', 'total_touch_count')->first();
                $userTouchCount = (!is_null($touchCount) ? $touchCount->user_touch_count : 0);
                $subModule->user_touch_count = $userTouchCount;

                $checkTrainingCourse = checkTrainingCourse($subModule);

                if($checkTrainingCourse == 1){
                    return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
                if($checkTrainingCourse == 2){
                    return response()->json(setErrorResponse(__('user.TrainingCourseModule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                if($subModule->status != 'Active'){
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                return (new TrainingCourseThreeSixtyResource($subModule));
            }else{
                return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule upload video Detail
     *
     * @return \Illuminate\Http\Response
     */
    public function getUploadVideo($id)
    {
        try {
            $subModule = $this->model::with('uploadVideo:submodule_id,title,resource_type,resource_id,is_autoplay,start_time,end_time')->where('submodule_type_id',config('constants.submodule_types.upload_video'))->find($id);
            if($subModule){
                $checkTrainingCourse = checkTrainingCourse($subModule);
                if($checkTrainingCourse == 1){
                    return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
                if($checkTrainingCourse == 2){
                    return response()->json(setErrorResponse(__('user.TrainingCourseModule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                if($subModule->status != 'Active'){
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                return (new TrainingCourseUploadVideoResource($subModule));
            }else{
                return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule Document Viewer Detail
     *
     * @return \Illuminate\Http\Response
     */
    public function getDocumentViewer($id)
    {
        try {
            $subModule = $this->model::with('documentViewer:submodule_id,resource_type,resource_id')
                                    ->where('submodule_type_id',config('constants.submodule_types.document_viewer'))
                                    ->find($id);
            if($subModule){
                $checkTrainingCourse = checkTrainingCourse($subModule);
                if($checkTrainingCourse == 1){
                    return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
                if($checkTrainingCourse == 2){
                    return response()->json(setErrorResponse(__('user.TrainingCourseModule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                if($subModule->status != 'Active'){
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                return (new TrainingCourseDocumentViewerResource($subModule));
            }else{
                return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule Detail
     *
     * @return \Illuminate\Http\Response
     */
    public function productList($id)
    {
        try {
            $subModule = $this->model::where('submodule_type_id',config('constants.submodule_types.itemised_list'))->find($id);
            if($subModule){
                $checkTrainingCourse = checkTrainingCourse($subModule);
                if($checkTrainingCourse == 1){
                     return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_OK);
                }
                if($checkTrainingCourse == 2){
                    return response()->json(setErrorResponse(__('user.TrainingCourseModule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                if($subModule->status != 'Active'){
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }

                $productList = TrainingCourseSubModuleProductList::with('productProgress:user_id,product_id,is_new,is_visited')->whereSubmoduleId($id)->get();
                if (count($productList) > 0) {
                    $list = (new CustomCollection($productList, 'App\Http\Resources\V1\TrainingCourseProductListResource'));
                    $submoduleProgress = (new TrainingCourseSubmoduleProgress)->getProgress(['user_id' => auth()->user()->id, 'submodule_id' => $id]);
                    $userTimeSpent = (new TrainingCourseSubmoduleProgress)->getUserTimeSpent(['user_id' => auth()->user()->id, 'submodule_id' => $id]);
                    $data['data']['submodule_progress'] = $submoduleProgress;
                    $data['data']['user_time_spent'] = $userTimeSpent;

                    /* Deeplinking White-Label START */
                    $operatorId = $productList[0]->subModule->trainingCourse->master_user_id;
                    $shareParameters = [
                        'submoduleId' => $id,
                        'operatorId' => $operatorId,
                        'trainingCourseId' => $productList[0]->id,
                        'shareURL' => $productList[0]->share_url,
                        'tableName' => 'training_course_submodule_product_list_whitelabel_deeplink',
                        'submoduleIdKey' => 'submodule_id',
                        'linkRoute' => 'trainingCourseSubmodule.productList',
                        'type' => 'productList',
                    ];
                    $deeplink = getDeeplinkShareURL($shareParameters);
                    $data['data']['share_url'] = $deeplink;
                    /* Deeplinking White-Label START */

                    $data['data']['list'] = $list;
                    return $data;
                } else {
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
            }else{
                return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }

        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule Product Detail
     *
     * @return \Illuminate\Http\Response
     */
    public function productDetail($id)
    {
        try {
            $product = TrainingCourseSubModuleProductList::find($id);
            return ($product) ? (new TrainingCourseProductListResource($product)) :
                response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Send Kit Overview - Product Email
     *
     * @return \Illuminate\Http\Response
     */
    public function sendEmailProduct(SendEmailProductRequest $request)
    {
        try {
            $result = DB::transaction(function () use ($request) {
                $data = $request->all();
                $user_data = auth()->user();
                dispatch(new \App\Jobs\SendEmailProductJob($data, $user_data));
            });
            return response()->json(setResponse([], ['message' => __('user.TrainingCourseSubmodule.sentEmail')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule Happy-Unhappy List
     *
     * @return \Illuminate\Http\Response
     */
    public function happyUnhappyList($id)
    {
        try {
            $subModule = $this->model::where('submodule_type_id',config('constants.submodule_types.happy_unhappy'))->find($id);
            if($subModule){
                $checkTrainingCourse = checkTrainingCourse($subModule);
                if($checkTrainingCourse == 1){
                     return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_OK);
                }
                if($checkTrainingCourse == 2){
                    return response()->json(setErrorResponse(__('user.TrainingCourseModule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                if($subModule->status != 'Active'){
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }

                $happyUnhappyList = TrainingCourseSubModuleHappyUnhappy::with('happyUnhappyProgress:user_id,happy_unhappy_id,is_new,is_happy_visited,is_unhappy_visited')->whereSubmoduleId($id)->get();
                if (count($happyUnhappyList) > 0) {
                    $list = (new CustomCollection($happyUnhappyList, 'App\Http\Resources\V1\TrainingCourseHappyUnhappyListResource'));
                    $submoduleProgress = (new TrainingCourseSubmoduleProgress)->getProgress(['user_id' => auth()->user()->id, 'submodule_id' => $id]);
                    $userTimeSpent = (new TrainingCourseSubmoduleProgress)->getUserTimeSpent(['user_id' => auth()->user()->id, 'submodule_id' => $id]);
                    $data['data']['submodule_progress'] = $submoduleProgress;
                    $data['data']['user_time_spent'] = $userTimeSpent;

                    /* Deeplinking White-Label START */
                    $operatorId = $happyUnhappyList[0]->subModule->trainingCourse->master_user_id;
                    $shareParameters = [
                        'submoduleId' => $id,
                        'operatorId' => $operatorId,
                        'trainingCourseId' => $happyUnhappyList[0]->id,
                        'shareURL' => $happyUnhappyList[0]->share_url,
                        'tableName' => 'training_course_submodule_happy_unhappy_whitelabel_deeplink',
                        'submoduleIdKey' => 'submodule_id',
                        'linkRoute' => 'trainingCourseSubmodule.happyUnhappyList',
                        'type' => 'happyUnhappyList',
                    ];
                    $deeplink = getDeeplinkShareURL($shareParameters);
                    $data['data']['share_url'] = $deeplink;
                    /* Deeplinking White-Label START */

                    $data['data']['list'] = $list;
                    return $data;
                } else {
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
            }else{
                return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule Happy-Unhappy Detail
     *
     * @return \Illuminate\Http\Response
     */
    public function happyUnhappyDetail($id)
    {
        try {
            $happyUnhappy = TrainingCourseSubModuleHappyUnhappy::find($id);
            return ($happyUnhappy) ? (new TrainingCourseHappyUnhappyListResource($happyUnhappy)) :
                response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule Video Guide List
     *
     * @return \Illuminate\Http\Response
     */
    public function videoGuideList($id)
    {
        try {
            $subModule = $this->model::where('submodule_type_id',config('constants.submodule_types.step_by_step_video_guide'))->find($id);
            if($subModule){
                $checkTrainingCourse = checkTrainingCourse($subModule);
                if($checkTrainingCourse == 1){
                     return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_OK);
                }
                if($checkTrainingCourse == 2){
                    return response()->json(setErrorResponse(__('user.TrainingCourseModule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                if($subModule->status != 'Active'){
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }

                $videoGuideList = TrainingCourseSubModuleVideoGuide::with('videoProgress:video_guide_id,user_id,video_progress,is_new')->whereSubmoduleId($id)->get();
                if (count($videoGuideList) > 0) {
                    $list = (new CustomCollection($videoGuideList, 'App\Http\Resources\V1\TrainingCourseVideoGuideListResource'));
                    $submoduleProgress = (new TrainingCourseSubmoduleProgress)->getProgress(['user_id' => auth()->user()->id, 'submodule_id' => $id]);
                    $userTimeSpent = (new TrainingCourseSubmoduleProgress)->getUserTimeSpent(['user_id' => auth()->user()->id, 'submodule_id' => $id]);
                    $data['data']['submodule_progress'] = $submoduleProgress;
                    $data['data']['user_time_spent'] = $userTimeSpent;

                    /* Deeplinking White-Label START */
                    $operatorId = $videoGuideList[0]->subModule->trainingCourse->master_user_id;
                    $shareParameters = [
                        'submoduleId' => $id,
                        'operatorId' => $operatorId,
                        'trainingCourseId' => $videoGuideList[0]->id,
                        'shareURL' => $videoGuideList[0]->share_url,
                        'tableName' => 'training_course_submodule_video_guide_whitelabel_deeplink',
                        'submoduleIdKey' => 'submodule_id',
                        'linkRoute' => 'trainingCourseSubmodule.videoGuideList',
                        'type' => 'videoGuideList',
                    ];
                    $deeplink = getDeeplinkShareURL($shareParameters);
                    $data['data']['share_url'] = $deeplink;
                    /* Deeplinking White-Label END */

                    $data['data']['list'] = $list;
                    return $data;
                } else {
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
            }else{
                return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule Video Guide Steps List
     *
     * @return \Illuminate\Http\Response
     */
    public function videoGuideStepsList($id)
    {
        try {
            $videoGuideSteps = TrainingCourseSubModuleVideoGuide::with('videoProgress:video_guide_id,user_id,video_progress')->with('steps', 'subModule:id,training_course_id,description', 'steps.stepVisited:user_id,video_guide_steps_id,is_visited,is_new')->find($id);
            if ($videoGuideSteps) {
                $list = (new TrainingCourseVideoGuideStepsListResource($videoGuideSteps));
                $submoduleProgress = (new TrainingCourseSubmoduleProgress)->getProgress(['user_id' => auth()->user()->id, 'submodule_id' => $videoGuideSteps->subModule->id]);
                $userTimeSpent = (new TrainingCourseSubmoduleProgress)->getUserTimeSpent(['user_id' => auth()->user()->id, 'submodule_id' => $videoGuideSteps->subModule->id]);
                $data['data']['submodule_progress'] = $submoduleProgress;
                $data['data']['user_time_spent'] = $userTimeSpent;

                /* Deeplinking White-Label START */
                $operatorId = $videoGuideSteps->subModule->trainingCourse->master_user_id;
                $shareParameters = [
                    'submoduleId' => $videoGuideSteps->submodule_id,
                    'operatorId' => $operatorId,
                    'shareURL' => $videoGuideSteps->share_url,
                    'tableName' => 'training_course_submodule_video_guide_whitelabel_deeplink',
                    'submoduleIdKey' => 'submodule_id',
                    'linkRoute' => 'trainingCourseSubmodule.videoGuideList',
                    'type' => 'videoGuideList',
                ];
                $deeplink = getDeeplinkShareURL($shareParameters);
                $data['data']['share_url'] = $deeplink;
                /* Deeplinking White-Label END */

                $data['data']['list'] = $list;
                return $data;
            } else {
                return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule Video Guide Step Detail
     *
     * @return \Illuminate\Http\Response
     */
    public function videoGuideStepDetail($id)
    {
        try {
            $videoGuideStep = TrainingCourseSubModuleVideoGuideSteps::with('videoGuide:id,submodule_id,title,resource_type,resource_id')->find($id);
            return ($videoGuideStep) ? (new TrainingCourseVideoGuideStepDetailResource($videoGuideStep)) :
                response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule image gallery List
     *
     * @return \Illuminate\Http\Response
     */
    public function imageGalleryList($id)
    {
        try {
            $subModule = $this->model::where('submodule_type_id',config('constants.submodule_types.image_gallery'))->find($id);
            if($subModule){
                $checkTrainingCourse = checkTrainingCourse($subModule);
                if($checkTrainingCourse == 1){
                     return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_OK);
                }
                if($checkTrainingCourse == 2){
                    return response()->json(setErrorResponse(__('user.TrainingCourseModule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                if($subModule->status != 'Active'){
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }

                $imageGalleryList = TrainingCourseSubModuleImageGallery::with('subModule:id,image_direction_type,training_course_id', 'galleryProgress:image_id,user_id,is_visited,is_new')->whereSubmoduleId($id)->get();
                if (count($imageGalleryList) > 0) {
                    $list = (new CustomCollection($imageGalleryList, 'App\Http\Resources\V1\TrainingCourseImageGalleryListResource'));
                    $submoduleProgress = (new TrainingCourseSubmoduleProgress)->getProgress(['user_id' => auth()->user()->id, 'submodule_id' => $id]);
                    $userTimeSpent = (new TrainingCourseSubmoduleProgress)->getUserTimeSpent(['user_id' => auth()->user()->id, 'submodule_id' => $id]);
                    $data['data']['submodule_progress'] = $submoduleProgress ?? 0;
                    $data['data']['user_time_spent'] = $userTimeSpent;

                    /* Deeplinking White-Label START */
                    $operatorId = $imageGalleryList[0]->subModule->trainingCourse->master_user_id;
                    $shareParameters = [
                        'submoduleId' => $id,
                        'operatorId' => $operatorId,
                        'trainingCourseId' => $imageGalleryList[0]->id,
                        'shareURL' => $imageGalleryList[0]->share_url,
                        'tableName' => 'training_course_submodule_image_gallery_whitelabel_deeplink',
                        'submoduleIdKey' => 'submodule_id',
                        'linkRoute' => 'trainingCourseSubmodule.imageGalleryList',
                        'type' => 'imageGalleryList',
                    ];
                    $deeplink = getDeeplinkShareURL($shareParameters);
                    $data['data']['share_url'] = $deeplink;
                    /* Deeplinking White-Label END */

                    $data['data']['list'] = $list;
                    return $data;
                } else {
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
            }else{
                return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule image gallery Detail
     *
     * @return \Illuminate\Http\Response
     */
    public function imageGalleryDetail($id)
    {
        try {
            $gallery = TrainingCourseSubModuleImageGallery::with('subModule:id,image_direction_type,training_course_id')->find($id);
            return ($gallery) ? (new TrainingCourseImageGalleryListResource($gallery)) :
                response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule course feedback form
     *
     * @return \Illuminate\Http\Response
     */
    public function courseFeedbackForm($id)
    {
        try {
            $subModule = $this->model::where('submodule_type_id',config('constants.submodule_types.mini_quiz'))->find($id);
            if($subModule){
                $checkTrainingCourse = checkTrainingCourse($subModule);
                if($checkTrainingCourse == 1){
                     return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_OK);
                }
                if($checkTrainingCourse == 2){
                    return response()->json(setErrorResponse(__('user.TrainingCourseModule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                if($subModule->status != 'Active'){
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }

                $courseFeedback = TrainingCourseSubModuleFeedbackQuestion::with('options:id,question_id,name', 'answers:question_id,user_id,answer,is_new')->whereSubmoduleId($id)->get();
                return ($courseFeedback) ? (new CustomCollection($courseFeedback, 'App\Http\Resources\V1\TrainingCourseCourseFeedbackResource')) :
                    response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }else{
                return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Submit Training Course Feedback
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function submitCourseFeedback(CourseFeedbackRequest $request)
    {
        try {
	    $course=TrainingCourse::whereId($request->training_course_id)->first();

	    if(empty($course)){
		return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_OK);
	    }
	    if(!in_array($request->training_course_id,$course->getUserAssignCourseIds())){
		return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_OK);
	    }
        
            $result = DB::transaction(function () use ($request,$course) {
                $submoduleName = TrainingCourseSubmoduleDetails::select('submodule_name','remove_progress_calculation')->whereId($request->submodule_id)->first();
		        $loginUser=auth()->user();
                $data = $request->all();
                $answerJson = json_decode($data['answer_json'], true);
                if (count($answerJson)) {
//                    $masterUserId = TrainingCourse::whereId($data['training_course_id'])->pluck('master_user_id')->first();
                    $masterUserId = $course->master_user_id;
                    foreach ($answerJson as $answer) {
                        TrainingCourseSubModuleFeedbackQuestionAnswer::updateOrCreate(
                            [
                                'training_course_id' => $data['training_course_id'],
                                'module_id' => $data['module_id'],
                                'submodule_id' => $data['submodule_id'],
                                'user_id' => $loginUser->id,
                                'master_user_id' => $masterUserId,
                                'question_id' => $answer['question_id'],
                            ],
                            [
                                'training_course_id' => $data['training_course_id'],
                                'module_id' => $data['module_id'],
                                'submodule_id' => $data['submodule_id'],
                                'user_id' => $loginUser->id,
                                'master_user_id' => $masterUserId,
                                'question_id' => $answer['question_id'],
                                'answer' => $answer['answer'],
                            ]
                        );
                    }
                }
                #TODO Progress Calculation
                $data['time_spent'] = (!isset($data['time_spent']) ? 0 : $data['time_spent']);
                $data['submodule_type_id'] = 7;
                (new TrainingCourseSubModuleFeedbackQuestionAnswer)->calculateProgress($data);

                $data['user_id'] = $loginUser->id;
                $overRideResult = TrainingCourseSubmoduleProgress::select('is_result_override')->where([
                    'user_id' => $data['user_id'],
                    'training_course_id' => $data['training_course_id'],
                    'module_id' => $data['module_id'],
                    'submodule_id' => $data['submodule_id']
                ])->first();
                if($overRideResult->is_result_override == 'Default'){

                    if($submoduleName->remove_progress_calculation != 1){
                    // Module progress
                    (new TrainingCourseModuleProgress)->calculateModuleProgress($data);

                    // Training course progress
                    (new TrainingCourseProgress)->calculateTrainingCourseProgress($data);
                    }
                }
                // Update is_new flag
                TrainingCourseSubModuleFeedbackQuestionAnswer::where(['training_course_id' => $data['training_course_id'], 'module_id' => $data['module_id'], 'submodule_id' => $data['submodule_id'], 'user_id' => auth()->user()->id, 'master_user_id' => $masterUserId, 'is_new' => 1])->update(['is_new' => 0]);

                if($overRideResult->is_result_override == 'Default'){
                    if($submoduleName->remove_progress_calculation == 1){
                        (new TrainingCourseSubmoduleProgress)->updateIsNew($data);
                    }else{
                        (new TrainingCourseModuleProgress)->updateIsNew($data);
                        (new TrainingCourseProgress)->updateIsNew($data);
                    }
                }
                // Send web notification while submitting feedback
                if (auth()->user()->user_relation->operator->courseNotificationCheck->is_on == 1) {
                    $webNotification['type'] = 'Feedback';
                    $webNotification['type_id'] = $data['user_id'];
                    $webNotification['user_id'] = $data['user_id'];
                    $webNotification['master_user_id'] = $loginUser->user_relation->master_user_id;
                    $webNotification['training_course_id'] = $data['training_course_id'];
                    $webNotification['module_id'] = $data['module_id'];
                    $webNotification['submodule_id'] = $data['submodule_id'];
                    WebNotifications::storeNotification($webNotification);
                }
                // Send email to manager
//                $tName = TrainingCourse::whereId($data['training_course_id'])->pluck('title')->first();
                $tName = $course->title;
                $fdata = ['email' => $loginUser->email, 'tname' => $tName];
                $qdata = TrainingCourseSubModuleFeedbackQuestion::with('answers', 'options')->where('submodule_id', $data['submodule_id'])->get();

                // Getting data for email
                $feedbackData = User::whereHas('courseFeedback', function ($qu) use ($data) {
                    $qu->where(['training_course_id' => $data['training_course_id'], 'module_id' => $data['module_id'], 'submodule_id' => $data['submodule_id']]);
                })
                    ->select('id', 'name', 'email')
                    ->with(['courseFeedback' => function ($qu) use ($data) {
                        $qu->where(['training_course_id' => $data['training_course_id'], 'module_id' => $data['module_id'], 'submodule_id' => $data['submodule_id']]);
                    }, 'courseFeedback.feedbackQuestion:id,question,question_type', 'courseFeedback.trainingCourse:id,title', 'courseFeedback.module:id,name', 'courseFeedback.feedbackQuestion.options'])
                    ->get()->toArray();

                $temp[0][] = 'Name';
                $temp[0][] = 'Email';
                $temp[0][] = 'Course Name';
                foreach ($feedbackData as $feedback) {
                    foreach ($feedback['course_feedback'] as $sfeedback) {
                        if (in_array($sfeedback['feedback_question']['question'], $temp[0])) {
                            $temp[0][] = $sfeedback['feedback_question']['question'] . "-copy";
                        } else {
                            $temp[0][] = $sfeedback['feedback_question']['question'];
                        }
                    }
                    break;
                }

                foreach ($feedbackData as $k => $feedback) {
                    foreach ($feedback['course_feedback'] as $kk => $sfeedback) {
                        if (isset($sfeedback['feedback_question']['question_type']) && ($sfeedback['feedback_question']['question_type'] == 'single' || $sfeedback['feedback_question']['question_type'] == 'multiple')) {
                            if ($sfeedback['feedback_question']['question_type'] == 'single') {
                                foreach ($sfeedback['feedback_question']['options'] as $option) {
                                    if ($option['id'] == $sfeedback['answer']) {
                                        $feedbackData[$k]['course_feedback'][$kk]['answer'] = $option['name'];
                                    }
                                }
                            } else {
                                $multipleNames = [];
                                if (!empty($sfeedback['answer'])) {
                                    $allIds = explode(",", $sfeedback['answer']);
                                    foreach ($sfeedback['feedback_question']['options'] as $option) {
                                        if (in_array($option['id'], $allIds)) {
                                            $multipleNames[] = $option['name'];
                                        }
                                    }
                                    if (count($multipleNames) > 0) {
                                        $feedbackData[$k]['course_feedback'][$kk]['answer'] = implode(", ", $multipleNames);
                                        $multipleNames = [];
                                    }
                                }
                            }
                        }
                    }
                }
                $counter = 1;
                foreach ($feedbackData as $k => $feedback) {
                    $temp[$counter][] = $feedback['name'];
                    $temp[$counter][] = $feedback['email'];
                    $temp[$counter][] = $feedback['course_feedback'][0]['training_course']['title'];

                    foreach ($feedback['course_feedback'] as $kk => $sfeedback) {
                        $temp[$counter][] = (!empty($sfeedback['answer']) ? $sfeedback['answer'] : 'Not Given');
                    }
                    $counter++;
                }

                if (count($temp) > 0) {
                    $fileName = "Feedback-" . time() . ".xls";
                    $export = fopen(public_path($fileName), "w");
                    foreach ($temp as $row) {
                        fputcsv($export, $row, ",");
                    }
                    fclose($export);
                }

                /** Swati 23-09-2022 Start **/
                $feedbackPdfData = User::whereHas('courseFeedback', function ($qu) use ($data) {
                    $qu->where(['user_id' => $data['user_id'], 'training_course_id' => $data['training_course_id'], 'module_id' => $data['module_id'], 'submodule_id' => $data['submodule_id']]);
                })
                    ->select('id', 'name', 'email')
                    ->with(['courseFeedback' => function ($qu) use ($data) {
                        $qu->where(['training_course_id' => $data['training_course_id'], 'module_id' => $data['module_id'], 'submodule_id' => $data['submodule_id']]);
                    }, 'courseFeedback.feedbackQuestion:id,question,question_type', 'courseFeedback.trainingCourse:id,title', 'courseFeedback.module:id,name', 'courseFeedback.feedbackQuestion.options'])
                    ->first()->toArray();

                //Mini quiz generate pdf in result
                dispatch(new \App\Jobs\MiniQuizResultPdfJob($feedbackPdfData, $loginUser->user_relation->master_user_id, $data['training_course_id'], $data['module_id'], $data['submodule_id'], $data['user_id']));
                /** Swati 23-09-2022 End **/
                if (!is_null($loginUser->user_relation->manager_email)) {
                    $manager = MasterUser::where('email', $loginUser->user_relation->manager_email)->first();
                    if (!is_null($manager)) {
                        if ($loginUser->user_relation->operator->courseNotificationCheck->is_on == 1) {
                            $manager->notify(new TrainingCourseFeedbackNotification($fdata, $qdata, public_path($fileName)));
                        }
                    } else {
                        if ($loginUser->user_relation->operator->courseNotificationCheck->is_on == 1) {
                            (new MasterUser)->forceFill([
                                'email' => $loginUser->user_relation->manager_email,
                            ])->notify(new TrainingCourseFeedbackNotification($fdata, $qdata, public_path($fileName)));
                        }
                    }
                } else {
                    if ($loginUser->user_relation->operator->courseNotificationCheck->is_on == 1) {
                        $manager = MasterUser::find($loginUser->user_relation->master_user_id);
                        $manager->notify(new TrainingCourseFeedbackNotification($fdata, $qdata, public_path($fileName)));
                    }
                }
            });
            $submoduleName = TrainingCourseSubmoduleDetails::whereId($request->submodule_id)->pluck('submodule_name')->first();
            return response()->json(setResponse([], ['message' => __($submoduleName . ' has been submitted successfully.')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule Confirmation Detail
     *
     * @return \Illuminate\Http\Response
     */
    public function getConfirmation($id)
    {
        try {
            $subModule = $this->model::with('confirmation:submodule_id,title,button1_text,button2_text,confirmation_lock,button_text')->where('submodule_type_id',config('constants.submodule_types.simple_question'))->find($id);
            if($subModule){
                $checkTrainingCourse = checkTrainingCourse($subModule);
                if($checkTrainingCourse == 1){
                     return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_OK);
                }
                if($checkTrainingCourse == 2){
                    return response()->json(setErrorResponse(__('user.TrainingCourseModule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                if($subModule->status != 'Active'){
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                return (new TrainingCourseConfirmationResource($subModule));
            }else{
                return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule Confirmation Boxes List
     *
     * @return \Illuminate\Http\Response
     */
    public function confirmationBoxesList($id)
    {
        try {
            $subModule = $this->model::where('submodule_type_id',config('constants.submodule_types.checklist'))->find($id);
            if($subModule){
                $checkTrainingCourse = checkTrainingCourse($subModule);
                if($checkTrainingCourse == 1){
                     return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_OK);
                }
                if($checkTrainingCourse == 2){
                    return response()->json(setErrorResponse(__('user.TrainingCourseModule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                if($subModule->status != 'Active'){
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                $boxList = TrainingCourseSubModuleConfirmationBox::with('confirmationBoxProgress:confirmation_box_id,user_id,is_visited,is_new')->whereSubmoduleId($id)->get();
                return ($boxList) ? (new CustomCollection($boxList, 'App\Http\Resources\V1\TrainingCourseConfirmationBoxListResource')) :
                    response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }else{
                return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule image hotspot List
     *
     * @return \Illuminate\Http\Response
     */
    public function imageHotspotList($id)
    {
        try {
            $subModule = $this->model::where('submodule_type_id',config('constants.submodule_types.hotspot_image'))->find($id);
            if($subModule){
                $checkTrainingCourse = checkTrainingCourse($subModule);
                if($checkTrainingCourse == 1){
                     return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_OK);
                }
                if($checkTrainingCourse == 2){
                    return response()->json(setErrorResponse(__('user.TrainingCourseModule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                if($subModule->status != 'Active'){
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }

                $imageHotspotList = TrainingCourseSubModuleImageHotspot::with('imageProgress:image_hotspot_id,user_id,is_visited,is_new')->whereSubmoduleId($id)->get();
                if (count($imageHotspotList) > 0) {
                    $list = (new CustomCollection($imageHotspotList, 'App\Http\Resources\V1\TrainingCourseImageHotspotListResource'));
                    $subModule = TrainingCourseSubmoduleDetails::select('training_course_id', 'hotspot_image', 'hotspot_image_height', 'hotspot_image_width')->find($id);
                    $data['data']['image'] = ['url' => $subModule->hotspot_image_url, 'width' => $subModule->hotspot_image_width, 'height' => $subModule->hotspot_image_height];
                    $submoduleProgress = (new TrainingCourseSubmoduleProgress)->getProgress(['user_id' => auth()->user()->id, 'submodule_id' => $id]);
                    $userTimeSpent = (new TrainingCourseSubmoduleProgress)->getUserTimeSpent(['user_id' => auth()->user()->id, 'submodule_id' => $id]);
                    $data['data']['submodule_progress'] = $submoduleProgress;
                    $data['data']['user_time_spent'] = $userTimeSpent;
                    $data['data']['list'] = $list;
                    return $data;
                } else {
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
            }else{
                return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Upload Image Hotspot Media
     *
     * @return \Illuminate\Http\Response
     */
    public function uploadImageHotspot(UploadImageHotspotRequest $request)
    {
        try {
            $data = $request->all();
            DB::transaction(function () use ($request, $data) {
                $submodule = TrainingCourseSubmoduleDetails::find($data['submodule_id']);
                //$manager = MasterUser::where('email', auth()->user()->user_relation->manager_email)->first();
                $data['user_id'] = auth()->id();
                $data['master_user_id'] = auth()->user()->user_relation->master_user_id;
                $exists = ['training_course_id' => $data['training_course_id'], 'module_id' => $data['module_id'], 'submodule_id' => $data['submodule_id'], 'user_id' => $data['user_id']];
                $previousHotspotProgress = TrainingCourseSubmoduleHotspotUploaderProgress::where($exists)->orderBy('attempts', 'DESC')->first();
                if ($previousHotspotProgress) { // Check if previous attempt exists
                    if ($previousHotspotProgress->status === 'Rejected') { // Create new progress data if previous attempt is rejected
                        $data['attempts'] = $previousHotspotProgress->attempts + 1;
                        $hotspotProgress = TrainingCourseSubmoduleHotspotUploaderProgress::create($data);
                    } else {
                        $hotspotProgress = $previousHotspotProgress;
                    }
                } else { // Create new progress data for first time
                    $data['attempts'] = 1;
                    $hotspotProgress = TrainingCourseSubmoduleHotspotUploaderProgress::create($data);
                }

                foreach ($request->hotspots as $hotspot) {
                    $photoHotspot = TrainingCourseSubModulePhotoHotspot::find($hotspot['photo_hotspot_id']);
                    $hotspot['attempts'] = $hotspotProgress->attempts;
                    if ($hotspot['media_type'] === 'image') {
                        $file = \Storage::disk('s3')->put(getTrainingCourseSubmodulePath($data['training_course_id']), $hotspot['media']);
                        $name = explode('/', $file);
                        $hotspot['media'] = $name[count($name) - 1];
                    } else {
                        $file = \Storage::disk('s3')->put(getTrainingCourseSubmoduleVideoPath($data['training_course_id']), $hotspot['media']);
                        $name = explode('/', $file);
                        $uploadedImageName = $name[count($name) - 1];
                        $hotspot['media'] = $uploadedImageName;

                        $videoUrl = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCourseSubmoduleVideoPath($data['training_course_id']), '/') . '/' . $hotspot['media'];
                        $storageUrl = base_path('storage/thumb');
                        if (!file_exists($storageUrl)) {
                            mkdir($storageUrl);
                            chmod($storageUrl, 0777);
                        }

                        $thumb_name = explode('.', $uploadedImageName);
                        $thumb_name = $thumb_name[0] . '.jpg';
                        $imgSizes = config('constants.videoguide.thumbnail');
                        VideoThumbnail::createThumbnail($videoUrl, $storageUrl, $thumb_name, 0, $imgSizes['width'], $imgSizes['height']);

                        chmod(base_path('storage/thumb/' . $thumb_name), 0777);
                        $file = base_path('storage/thumb/' . $thumb_name);

                        $s3 = \Storage::disk('s3')->put(getTrainingCourseSubmoduleThumbnailPath($data['training_course_id']) . '/' . $thumb_name, file_get_contents($file));
                        if (file_exists($file)) {
                            unlink($file);
                        }
                        $hotspot['thumbnail'] = $thumb_name;
                    }

                    $hotspot['hotspot_uploader_id'] = $hotspotProgress->id;
                    $hotspot['status'] = (!$submodule->poc_enable) ? 'Approved' : 'Pending';
                    HotspotUploaderMedia::create($hotspot);
                }
                if (!$submodule->poc_enable) { // To check all hotspots add this condition - ($totalHotspots == count($request->hotspots))
                    $hotspotProgress->status = 'Approved';
                    $hotspotProgress->save();
                }
                if (!$submodule->poc_enable && $hotspotProgress->status !== 'Approved') {
                    $photoHotspotIds = $submodule->photoHotspots()->pluck('id')->toArray();
                    $approvedHotspotIds = $hotspotProgress->hotspotUploader()->where('status', 'Approved')->pluck('photo_hotspot_id')->toArray();
                    if (count(array_diff($photoHotspotIds, $approvedHotspotIds)) === 0) { // Check if all hotspots are Approved then mark the progress as Approved
                        $hotspotProgress->status = 'Approved';
                        $hotspotProgress->save();
                    }
                }
                if ($submodule->poc_enable) { // If all regions are uploaded then mark status as Pending else it will be Start
                    $photoHotspotIds = $submodule->photoHotspots()->pluck('id')->toArray();
                    $uploadedHotspotIds = $hotspotProgress->hotspotUploader()->pluck('photo_hotspot_id')->toArray();
                    if (count(array_diff($photoHotspotIds, $uploadedHotspotIds)) === 0) { // Check if all hotspots are uploaded then mark the progress as Pending
                        $hotspotProgress->status = 'Pending';
                        $hotspotProgress->save();
                    }
                }

                // Update New flag
                (new TrainingCourseSubmoduleProgress)->updateIsNew($data);
                (new TrainingCourseModuleProgress)->updateIsNew($data);
                (new TrainingCourseProgress)->updateIsNew($data);

                // Poc Web Notification
                $operatorPocCheck = auth()->user()->user_relation->operator->pocNotificationCheck->is_on ?? 0;
                if ($submodule->poc_enable == 1 && $operatorPocCheck == 1) {
                    $webNotification['type'] = 'Poc';
                    $webNotification['type_id'] = $hotspotProgress->id;
                    $webNotification['user_id'] = auth()->user()->id;
                    $webNotification['master_user_id'] = auth()->user()->user_relation->master_user_id;
                    $webNotification['training_course_id'] = $submodule->training_course_id;
                    $webNotification['module_id'] = $submodule->module_id;
                    $webNotification['submodule_id'] = $submodule->id;
                    WebNotifications::storeNotification($webNotification);

                    // Send email to manager
                    $tName = TrainingCourse::whereId($hotspotProgress->training_course_id)->pluck('title')->first();
                    $mName = TrainingCourseModules::whereId($hotspotProgress->module_id)->pluck('name')->first();
                    $sName = TrainingCourseSubmoduleDetails::whereId($hotspotProgress->submodule_id)->pluck('submodule_name')->first();
                    $pData = ['email' => auth()->user()->email, 'tname' => $tName, 'time_stamp' => format_datetime($hotspotProgress->created_at, 'Y-m-d H:i:s'), 'link' => env('OPERATOR_URL') . "#/courses/view-response/photo-hotspot-uploader/" . $tName . "/" . $hotspotProgress->training_course_id . "/" . $mName . "/" . $hotspotProgress->module_id . "/" . $sName . "/" . $hotspotProgress->submodule_id];
                    if (!is_null(auth()->user()->user_relation->manager_email)) {
                        $manager = MasterUser::where('email', auth()->user()->user_relation->manager_email)->first();
                        if (!is_null($manager)) {
                            $manager->notify(new UserPhotoUploadNotification($pData));
                        } else {
                            (new MasterUser)->forceFill([
                                'email' => auth()->user()->user_relation->manager_email,
                            ])->notify(new UserPhotoUploadNotification($pData));
                        }
                    } else {
                        $manager = MasterUser::find(auth()->user()->user_relation->master_user_id);
                        $manager->notify(new UserPhotoUploadNotification($pData));
                    }
                }
            });

            // Send photo region data in response same as photoHotspotList/{id} API
            $id = $request->submodule_id;
            $photoHotspot = TrainingCourseSubmoduleHotspotUploaderProgress::select('id', 'comment', 'ratings', 'attempts')->where(['submodule_id' => $data['submodule_id'], 'user_id' => auth()->user()->id])->orderBy('attempts', 'DESC')->first();

            $photoHotspotList = DB::table(DBTableNames::TRAINING_COURSE_SUBMODULE_PHOTO_HOTSPOTS . ' as tsp')
                ->leftJoin(DBTableNames::TRAINING_COURSE_SUBMODULE_HOTSPOT_UPLOADER_PROGRESS . ' as tcshp', function ($join) use ($id, $photoHotspot) {
                    $join->where('tcshp.submodule_id', '=', $id);
                    $join->where('tcshp.user_id', '=', auth()->user()->id);
                    $join->where('tcshp.attempts', '=', $photoHotspot->attempts);
                })->leftJoin(DBTableNames::HOTSPOT_UPLOADER_MEDIA . ' as hum', function ($join) {
                    $join->on('hum.hotspot_uploader_id', '=', 'tcshp.id');
                    $join->on('hum.photo_hotspot_id', '=', 'tsp.id');
                    $join->on('hum.id', '=', DB::raw('(select id from `hotspot_uploader_media` where hotspot_uploader_id = `hum`.`hotspot_uploader_id` AND photo_hotspot_id = `hum`.`photo_hotspot_id` order by id DESC limit 1)'));
                })->select('tsp.*', 'tcshp.submodule_id', 'tcshp.user_id', 'tcshp.training_course_id as training_course_id', 'hum.id as hid', 'hum.media as media', 'hum.thumbnail as thumbnail', 'hum.description as comment')->where('tsp.submodule_id', $id)->get();

            if (count($photoHotspotList) > 0) {
                $pData = [];
                $list = (new CustomCollection($photoHotspotList, 'App\Http\Resources\V1\TrainingCoursePhotoHotspotListResource'));
                $subModule = TrainingCourseSubmoduleDetails::select('training_course_id', 'hotspot_photo', 'hotspot_photo_height', 'hotspot_photo_width', 'total_subdata')->find($id);

                $isPhotoUploaded = 0;
                if ($photoHotspot->attempts != 0) {
                    $hotspotUploadCnt = HotspotUploaderMedia::where(['hotspot_uploader_id' => $photoHotspot->id, 'attempts' => $photoHotspot->attempts])->count();
                    $isPhotoUploaded = ($hotspotUploadCnt == $subModule->total_subdata ? 1 : 0);
                }
                $pData['image'] = ['url' => $subModule->hotspot_photo_url, 'width' => $subModule->hotspot_photo_width, 'height' => $subModule->hotspot_photo_height, 'comment' => $photoHotspot->comment ?? '', 'ratings' => $photoHotspot->ratings ?? '', 'attempt' => $photoHotspot->attempts];
                $submoduleProgress = (new TrainingCourseSubmoduleProgress)->getProgress(['user_id' => auth()->user()->id, 'submodule_id' => $data['submodule_id']]);
                $userTimeSpent = (new TrainingCourseSubmoduleProgress)->getUserTimeSpent(['user_id' => auth()->user()->id, 'submodule_id' => $data['submodule_id']]);
                $pData['submodule_progress'] = $submoduleProgress;
                $pData['user_time_spent'] = $userTimeSpent;
                $pData['is_photo_uploaded'] = $isPhotoUploaded;
                $pData['list'] = $list;

                return response()->json(setResponse($pData, ['message' => __('user.TrainingCourseSubmodule.uploadHotspot')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule job List
     *
     * @return \Illuminate\Http\Response
     */
    public function jobList($id)
    {
        try {
            $subModule = $this->model::where('submodule_type_id',config('constants.submodule_types.upload_media'))->find($id);
            if($subModule){
                $checkTrainingCourse = checkTrainingCourse($subModule);
                if($checkTrainingCourse == 1){
                     return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_OK);
                }
                if($checkTrainingCourse == 2){
                    return response()->json(setErrorResponse(__('user.TrainingCourseModule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                if($subModule->status != 'Active'){
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }

                $jobList = TrainingCourseSubModuleJob::whereSubmoduleId($id)->get();
                if ($jobList) {
                    $list = (new CustomCollection($jobList, 'App\Http\Resources\V1\TrainingCourseJobListResource'));
                    $subModule = TrainingCourseSubmoduleDetails::select('job_topic', 'job_no_required')->find($id);
                    $data['data']['job'] = ['job_topic' => $subModule->job_topic, 'job_no_required' => $subModule->job_no_required];
                    $data['data']['list'] = $list;
                    return $data;
                } else {
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
            }else{
                return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule photo hotspot uploader List
     *
     * @return \Illuminate\Http\Response
     */
    public function photoHotspotList($id, $attempt)
    {
        try {
            $submodule_type_id=DB::table('training_course_submodule_types')->where('name','Hotspot Photo Uploader')->value('id');
            if(isset($submodule_type_id) && !empty($submodule_type_id)){
                $submodule_type_id=$submodule_type_id;
            }else{
                $submodule_type_id=config('constants.submodule_types.hotspot_photo_uploader');
            }
            $subModule = TrainingCourseSubmoduleDetails::select('training_course_id', 'module_id','status', 'hotspot_photo', 'hotspot_photo_height', 'hotspot_photo_width', 'total_subdata', 'poc_enable')
                                        ->where('submodule_type_id',$submodule_type_id)
                                        ->find($id);
            if($subModule){
                $checkTrainingCourse = checkTrainingCourse($subModule);
                if($checkTrainingCourse == 1){
                     return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_OK);
                }
                if($checkTrainingCourse == 2){
                    return response()->json(setErrorResponse(__('user.TrainingCourseModule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                if($subModule->status != 'Active'){
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }

                $photoHotspotList = DB::table(DBTableNames::TRAINING_COURSE_SUBMODULE_PHOTO_HOTSPOTS . ' as tsp')
                    ->leftJoin(DBTableNames::TRAINING_COURSE_SUBMODULE_HOTSPOT_UPLOADER_PROGRESS . ' as tcshp', function ($join) use ($id, $attempt, $subModule) {
                        $join->where('tcshp.submodule_id', '=', $id);
                        $join->where('tcshp.user_id', '=', auth()->user()->id);
                        if ($subModule->poc_enable == 1) {
                            $join->where('tcshp.attempts', '=', $attempt);
                        }
                    })->leftJoin(DBTableNames::HOTSPOT_UPLOADER_MEDIA . ' as hum', function ($join) use ($attempt, $subModule) {
                        $join->on('hum.hotspot_uploader_id', '=', 'tcshp.id');
                        $join->on('hum.photo_hotspot_id', '=', 'tsp.id');
                        $join->on('hum.id', '=', DB::raw('(select id from `hotspot_uploader_media` where hotspot_uploader_id = `hum`.`hotspot_uploader_id` AND photo_hotspot_id = `hum`.`photo_hotspot_id` order by id DESC limit 1)'));
                        if ($subModule->poc_enable == 1) {
                            $join->where('hum.attempts', '=', $attempt);
                        }
                    })->select('tsp.*', 'tsp.submodule_id', 'tcshp.user_id', 'tcshp.training_course_id as training_course_id', 'hum.id as hid', 'hum.media as media', 'hum.thumbnail as thumbnail', 'hum.description as comment', 'hum.status')->where('tsp.submodule_id', $id)->whereNull('tsp.deleted_at')->get();
                if (count($photoHotspotList) > 0) {
                    $list = (new CustomCollection($photoHotspotList, 'App\Http\Resources\V1\TrainingCoursePhotoHotspotListResource'));
                    $isPhotoUploaded = 0;
                    if ($subModule->poc_enable == 1) {
                        $photoHotspot = TrainingCourseSubmoduleHotspotUploaderProgress::select('id', 'comment', 'ratings')->where(['submodule_id' => $id, 'user_id' => auth()->user()->id, 'attempts' => $attempt])->first();
                        if ($attempt != 0) {
                            $hotspotUploadCnt = HotspotUploaderMedia::where(['hotspot_uploader_id' => $photoHotspot->id, 'attempts' => $attempt])->count();
                            $isPhotoUploaded = ($hotspotUploadCnt == $subModule->total_subdata ? 1 : 0);
                        }
                    }
                    $data['data']['image'] = ['url' => $subModule->hotspot_photo_url, 'width' => $subModule->hotspot_photo_width, 'height' => $subModule->hotspot_photo_height, 'comment' => $photoHotspot->comment ?? '', 'ratings' => $photoHotspot->ratings ?? ''];
                    $submoduleProgress = (new TrainingCourseSubmoduleProgress)->getProgress(['user_id' => auth()->user()->id, 'submodule_id' => $id]);
                    $userTimeSpent = (new TrainingCourseSubmoduleProgress)->getUserTimeSpent(['user_id' => auth()->user()->id, 'submodule_id' => $id]);
                    $data['data']['submodule_progress'] = $submoduleProgress;
                    $data['data']['user_time_spent'] = $userTimeSpent;
                    $data['data']['is_photo_uploaded'] = $isPhotoUploaded;
                    $data['data']['list'] = $list;
                    return $data;
                } else {
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
            } else {
                return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Submit progress for all submodule
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function submitProgress(ProgressRequest $request)
    {
        try {
            $data = $request->all();
            $result = DB::transaction(function () use ($data) {
                $data['time_spent'] = (!isset($data['time_spent']) ? 0 : $data['time_spent']);
                $data['user_id'] = auth()->user()->id;
                if ($data['submodule_type_id'] == 1) {
                    (new TrainingCourseSubModuleTitleSlide)->calculateProgress($data);
                } else if ($data['submodule_type_id'] == 2) {
                    (new TrainingCourseSubmoduleDetails)->calculateProgress($data);
                } else if ($data['submodule_type_id'] == 3) {
                    (new TrainingCourseSubModuleUploadVideo)->calculateProgress($data);
                } else if ($data['submodule_type_id'] == 4) {
                    (new TrainingCourseSubModuleImageGallery)->calculateProgress($data);
                } else if ($data['submodule_type_id'] == 5) {
                    (new TrainingCourseSubModuleProductList)->calculateProgress($data);
                } else if ($data['submodule_type_id'] == 6) {
                    (new TrainingCourseSubModuleHappyUnhappy)->calculateProgress($data);
                } else if ($data['submodule_type_id'] == 7) {
                    (new TrainingCourseSubModuleFeedbackQuestionAnswer)->calculateProgress($data);
                } else if ($data['submodule_type_id'] == 8) {
                    (new TrainingCourseSubModuleVideoGuide)->calculateProgress($data);
                } else if ($data['submodule_type_id'] == 9) {
                    (new TrainingCourseSubModuleImageHotspot)->calculateProgress($data);
                } else if ($data['submodule_type_id'] == 10) {
                    (new TrainingCourseSubModuleConfirmation)->calculateProgress($data);
                } else if ($data['submodule_type_id'] == 11) {
                    (new TrainingCourseSubModulePhotoHotspot)->calculateProgress($data);
                } else if ($data['submodule_type_id'] == 12) {
                    (new TrainingCourseSubModuleQuizResults)->calculateProgress($data);
                } else if ($data['submodule_type_id'] == 13) {
                    (new TrainingCourseSubModuleJob)->calculateProgress($data);
                } else if ($data['submodule_type_id'] == 14) {
                    (new TrainingCourseSubModuleConfirmationBox)->calculateProgress($data);
                } else if ($data['submodule_type_id'] == 15) {
                    (new TrainingCourseSubModuleDocumentViewer)->calculateProgress($data);
                }
                $submoduleData = TrainingCourseSubmoduleDetails::select('remove_progress_calculation')->find($data['submodule_id']);
                $overRideResult = TrainingCourseSubmoduleProgress::select('is_result_override')->where([
                    'user_id' => $data['user_id'],
                    'training_course_id' => $data['training_course_id'],
                    'module_id' => $data['module_id'],
                    'submodule_id' => $data['submodule_id']
                ])->first();
                if($overRideResult->is_result_override =='Default'){
                    if($submoduleData->remove_progress_calculation == 0){
                    // Module progress
                    (new TrainingCourseModuleProgress)->calculateModuleProgress($data);
                    // Training course progress
                    (new TrainingCourseProgress)->calculateTrainingCourseProgress($data);
                    }
                }
            });

            #TODO - get response of training course, module and sub module progress
            $submoduleProgress = TrainingCourseSubmoduleProgress::where(['training_course_id' => $data['training_course_id'], 'submodule_id' => $data['submodule_id'], 'user_id' => auth()->user()->id])->select('submodule_progress', 'time_spent', 'total_spent', 'touch_count', 'total_touch_count')->first();

            return (new TrainingCourseSubmoduleProgressResource($submoduleProgress));
        } catch (\Exception $e) {
            dd($e);
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule photo hotspot uploader POC List
     *
     * @return \Illuminate\Http\Response
     */
    public function pocList($id)
    {
        try {
            $subModule = $this->model::where('submodule_type_id',config('constants.submodule_types.hotspot_photo_uploader'))->find($id);
            if($subModule){
                $checkTrainingCourse = checkTrainingCourse($subModule);
                if($checkTrainingCourse == 1){
                     return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_OK);
                }
                if($checkTrainingCourse == 2){
                    return response()->json(setErrorResponse(__('user.TrainingCourseModule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                if($subModule->status != 'Active'){
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                $pData['user_id'] = auth()->user()->id;
                $photoData = TrainingCourseSubmoduleHotspotUploaderProgress::where(['submodule_id' => $id, 'user_id' => $pData['user_id']])->orderBy('attempts', 'DESC')->get();
                if (count($photoData) > 0) {
                    $i = 0;
                    foreach ($photoData as $photo) {
                        if ($photo->status == 'Rejected') {
                            if ($i == 0) {
                                $pocList[] = ["status" => "Start", "date_completed" => "N/A", "attempt" => 0, "ratings" => 0];
                            }
                            $pocList[] = ["status" => $photo->status, "date_completed" => format_datetime($photo->created_at, 'Y-m-d.h:i:s'), "attempt" => $photo->attempts, "ratings" => $photo->ratings ?? 0];
                        } else {
                            $pocList[] = ["status" => $photo->status, "date_completed" => format_datetime($photo->created_at, 'Y-m-d.h:i:s'), "attempt" => $photo->attempts, "ratings" => $photo->ratings ?? 0];
                        }
                        $i++;
                    }
                } else {
                    $pocList[] = ["status" => "Start", "date_completed" => "N/A", "attempt" => 0];
                }
                $submoduleProgress = (new TrainingCourseSubmoduleProgress)->getProgress(['user_id' => auth()->user()->id, 'submodule_id' => $id]);
                $userTimeSpent = (new TrainingCourseSubmoduleProgress)->getUserTimeSpent(['user_id' => auth()->user()->id, 'submodule_id' => $id]);
                $data['data']['submodule_progress'] = $submoduleProgress;
                $data['data']['user_time_spent'] = $userTimeSpent;
                $data['data']['remove_progress_calculation'] = $subModule->remove_progress_calculation;
                $data['data']['list'] = $pocList;
                return $data;
            }else{
                return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule  Upload Job List
     *
     * @return \Illuminate\Http\Response
     */
    public function uploadJobList($id)
    {
        try {
            $jData['user_id'] = auth()->user()->id;
            $jobData = TrainingCourseSubmoduleJobProgress::where(['submodule_id' => $id, 'user_id' => $jData['user_id']])->orderBy('attempts', 'DESC')->get();
            $subModule = TrainingCourseSubmoduleDetails::with('trainingCourse:id,title')
                                                ->select('id', 'training_course_id', 'job_topic', 'multiple_job_enable','module_id','status','remove_progress_calculation')
                                                ->whereId($id)
                                                ->where('submodule_type_id',config('constants.submodule_types.upload_media'))
                                                ->first();
            if($subModule){
                $checkTrainingCourse = checkTrainingCourse($subModule);
                if($checkTrainingCourse == 1){
                     return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_OK);
                }
                if($checkTrainingCourse == 2){
                    return response()->json(setErrorResponse(__('user.TrainingCourseModule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                if($subModule->status != 'Active'){
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }

                if ($subModule->multiple_job_enable == 1) {
                    $cntPendingStatus = $jobData->where('status', 'Pending')->count();
                }

                if ($subModule->multiple_job_enable == 1) {
                    $cntPendingStatus = $jobData->where('status', 'Pending')->count();
                }

                if (count($jobData) > 0) {
                    $i = 0;
                    foreach ($jobData as $job) {
                        if ($subModule->multiple_job_enable == 1) {
                            if ($i == 0) {
                                if ($cntPendingStatus != config('constants.pending_limit')) {
                                    $jobList[] = ["status" => "Start", "job_no" => "N/A", "last_updated" => "N/A", "training_course" => $subModule->trainingCourse->title, "job_topic" => $subModule->job_topic, "job_id" => 0, "attempt" => 0];
                                }
                            }
                            $jobList[] = ["status" => $job->status, "job_no" => (!is_null($job->job_no) ? $job->job_no : "N/A"), "last_updated" => format_datetime($job->updated_at, 'Y-m-d\TH:i:sO'), "training_course" => $subModule->trainingCourse->title, "job_topic" => $subModule->job_topic, "job_id" => $job->id, "attempt" => $job->attempts];
                        } else {
                            if ($job->status == 'Rejected') {
                                if ($i == 0) {
                                    $jobList[] = ["status" => "Start", "job_no" => "N/A", "last_updated" => "N/A", "training_course" => $subModule->trainingCourse->title, "job_topic" => $subModule->job_topic, "job_id" => 0, "attempt" => 0];
                                }
                                $jobList[] = ["status" => $job->status, "job_no" => (!is_null($job->job_no) ? $job->job_no : "N/A"), "last_updated" => format_datetime($job->updated_at, 'Y-m-d\TH:i:sO'), "training_course" => $subModule->trainingCourse->title, "job_topic" => $subModule->job_topic, "job_id" => $job->id, "attempt" => $job->attempts];
                            } else {
                                $jobList[] = ["status" => $job->status, "job_no" => (!is_null($job->job_no) ? $job->job_no : "N/A"), "last_updated" => format_datetime($job->updated_at, 'Y-m-d\TH:i:sO'), "training_course" => $subModule->trainingCourse->title, "job_topic" => $subModule->job_topic, "job_id" => $job->id, "attempt" => $job->attempts];
                            }
                        }
                        $i++;
                    }
                } else {
                    $jobList[] = ["status" => "Start", "job_no" => "N/A", "last_updated" => "N/A", "training_course" => $subModule->trainingCourse->title, "job_topic" => $subModule->job_topic, "job_id" => 0, "attempt" => 0];
                }
                $submoduleProgress = (new TrainingCourseSubmoduleProgress)->getProgress(['user_id' => auth()->user()->id, 'submodule_id' => $id]);
                $userTimeSpent = (new TrainingCourseSubmoduleProgress)->getUserTimeSpent(['user_id' => auth()->user()->id, 'submodule_id' => $id]);
                $data['data']['submodule_progress'] = $submoduleProgress;
                $data['data']['user_time_spent'] = $userTimeSpent;
                $data['data']['remove_progress_calculation'] = $subModule->remove_progress_calculation;
                $data['data']['list'] = $jobList;
                return $data;
            }else{
                return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Upload Job
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function uploadJob(UploadJobRequest $request)
    {
        try {
            DB::transaction(function () use ($request) {
                $data = $request->all();
                $data['user_id'] = auth()->id();
                $data['master_user_id'] = auth()->user()->user_relation->master_user_id;
                $overRideResult = TrainingCourseSubmoduleProgress::select('is_result_override')->where([
                    'user_id' => $data['user_id'],
                    'training_course_id' => $data['training_course_id'],
                    'module_id' => $data['module_id'],
                    'submodule_id' => $data['submodule_id']
                ])->first();
                $exists = ['training_course_id' => $data['training_course_id'], 'module_id' => $data['module_id'], 'submodule_id' => $data['submodule_id'], 'user_id' => $data['user_id']];
                $previousJobProgress = TrainingCourseSubmoduleJobProgress::select('status', 'attempts')->where($exists)->orderBy('attempts', 'DESC')->first();

                if ($previousJobProgress) { // Check if previous attempt exists

                    $multipleJobEnable = TrainingCourseSubmoduleDetails::whereId($data['submodule_id'])->pluck('multiple_job_enable')->first();

                    if ($multipleJobEnable == 0) {
                        if ($previousJobProgress->status == 'Rejected') { // Create new progress data if previous attempt is rejected
                            $data['attempts'] = $previousJobProgress->attempts + 1;
                            $jobProgress = TrainingCourseSubmoduleJobProgress::create($data);
                        } else {
                            $jobProgress = $previousJobProgress;
                        }
                    } else {
                        $data['attempts'] = $previousJobProgress->attempts + 1;
                        $jobProgress = TrainingCourseSubmoduleJobProgress::create($data);
                    }
                } else { // Create new progress data for first time
                    $data['attempts'] = 1;
                    $jobProgress = TrainingCourseSubmoduleJobProgress::create($data);
                }
                foreach ($request->jobs as $job) {
                    if ($job['media_type'] === 'image') {
                        $file = \Storage::disk('s3')->put(getTrainingCourseSubmodulePath($data['training_course_id']), $job['media']);
                        $name = explode('/', $file);
                        $job['media'] = $name[count($name) - 1];
                    } else {
                        $file = \Storage::disk('s3')->put(getTrainingCourseSubmoduleVideoPath($data['training_course_id']), $job['media']);
                        $name = explode('/', $file);
                        $uploadedImageName = $name[count($name) - 1];
                        $job['media'] = $uploadedImageName;

                        $videoUrl = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCourseSubmoduleVideoPath($data['training_course_id']), '/') . '/' . $job['media'];
                        $storageUrl = base_path('storage/thumb');
                        if (!file_exists($storageUrl)) {
                            mkdir($storageUrl);
                            chmod($storageUrl, 0777);
                        }

                        $thumb_name = explode('.', $uploadedImageName);
                        $thumb_name = $thumb_name[0] . '.jpg';
                        $imgSizes = config('constants.videoguide.thumbnail');
                        VideoThumbnail::createThumbnail($videoUrl, $storageUrl, $thumb_name, 0, $imgSizes['width'], $imgSizes['height']);

                        chmod(base_path('storage/thumb/' . $thumb_name), 0777);
                        $file = base_path('storage/thumb/' . $thumb_name);

                        $s3 = \Storage::disk('s3')->put(getTrainingCourseSubmoduleThumbnailPath($data['training_course_id']) . '/' . $thumb_name, file_get_contents($file));
                        if (file_exists($file)) {
                            unlink($file);
                        }
                        $job['thumbnail'] = $thumb_name;
                    }

                    $job['job_progress_id'] = $jobProgress->id;
                    JobProgressMedia::create($job);
                }

                if($overRideResult->is_result_override == 'Default'){
                // Update New flag
                (new TrainingCourseSubmoduleProgress)->updateIsNew($data);
                (new TrainingCourseModuleProgress)->updateIsNew($data);
                (new TrainingCourseProgress)->updateIsNew($data);
                }

                $auth = Auth::user();
                // Push Notification
                $mutable = $auth->jobNotificationCheck->is_on ?? 0;
                $uploadJobNotificationJob = (new \App\Jobs\UploadJobNotificationJob($auth, $mutable, $jobProgress, $jobProgress->subModule->submodule_type_id, $auth->user_relation->master_user_id))->delay(env('QUEUE_JOB_DELAY_TIME'));
                dispatch($uploadJobNotificationJob);

                // Web Notification
                if ($auth->user_relation->operator->jobNotificationCheck->is_on == 1) {
                    $webNotification['type'] = 'Job';
                    $webNotification['type_id'] = $jobProgress->id;
                    $webNotification['user_id'] = $auth->id;
                    $webNotification['master_user_id'] = $auth->user_relation->master_user_id;
                    $webNotification['training_course_id'] = $jobProgress->subModule->training_course_id;
                    $webNotification['module_id'] = $jobProgress->module_id;
                    $webNotification['submodule_id'] = $jobProgress->subModule->id;
                    WebNotifications::storeNotification($webNotification);
                    // Email Code Pending
                }

                // Send email to manager
                $master_user = MasterUser::find(auth()->user()->user_relation->master_user_id);
                $managerName = MasterUser::where('email', auth()->user()->user_relation->manager_email)->pluck('name')->first();
                $tName = TrainingCourse::whereId($jobProgress->subModule->training_course_id)->pluck('title')->first();
                //   $mName = TrainingCourseModules::whereId($jobProgress->module_id)->pluck('name')->first();
                $sName = TrainingCourseSubmoduleDetails::whereId($jobProgress->subModule->id)->pluck('submodule_name')->first();
                $sTopic = TrainingCourseSubmoduleDetails::whereId($jobProgress->subModule->id)->pluck('job_topic')->first();
                $jobDet = TrainingCourseSubmoduleJobProgress::select('job_no', 'comments')->whereId($jobProgress->id)->first();
                $jobMedia = JobProgressMedia::where('job_progress_id', $jobProgress->id)->get();
                $video = [];
                $photo = [];
                if ($jobMedia) {
                    foreach ($jobMedia as $key => $jobm) {
                        if ($jobm->thumbnail) {
                            $videolink = env('CDN_URL') . trim(getTrainingCourseSubmoduleVideoPath($jobProgress->subModule->training_course_id), '/') . '/' . $jobm->media;
                            $video[$key] = '<a width:"100%" style="display:block;" href="' . $videolink . '">' . $videolink . '</a></br>';
                        } else {
                            $medialink = env('CDN_URL') . trim(getTrainingCourseSubmodulePath($jobProgress->subModule->training_course_id), '/') . '/' . $jobm->media;
                            $photo[$key] = '<img style= "height:100px;width:100px;margin-right:10px;" src ="' . $medialink . '">';
                        }
                        $videos = implode(' ', $video);
                        $photos = implode(' ', $photo);
                    }
                    // dd($string);

                }

                $joblink = env('OPERATOR_URL') . '#/jobs/view/' . $jobProgress->id;
                $pData = ['email' => (auth()->user()->user_relation->manager_email) ? auth()->user()->user_relation->manager_email : $master_user->email, 'tname' => $tName, 'sname' => $sName, 'stopic' => $sTopic, 'jobno' => $jobDet->job_no, 'comments' => $jobDet->comments, 'video' => $videos, 'photo' => $photos, 'joblink' => $joblink, 'user_name' => (!empty(auth()->user()->name) ? auth()->user()->name : 'User'), 'manager_name' => (auth()->user()->user_relation->manager_email) ? $managerName : $master_user->name];
                if (!is_null(auth()->user()->user_relation->manager_email)) {
                    $manager = MasterUser::where('email', auth()->user()->user_relation->manager_email)->first();
                    if (!is_null($manager)) {
                        $manager->notify(new UserJobUploadNotification($pData));
                    } else {
                        (new MasterUser)->forceFill([
                            'email' => auth()->user()->user_relation->manager_email,
                        ])->notify(new UserJobUploadNotification($pData));
                    }
                } else {
                    $manager = MasterUser::find(auth()->user()->user_relation->master_user_id);
                    $manager->notify(new UserJobUploadNotification($pData));
                }

                $submodule = $jobProgress['submodule_id'];
                $submodulesData = TrainingCourseSubmoduleDetails::select('*')->whereId($submodule)->first();
                $jobData = TrainingCourseSubmoduleJobProgress::find($jobProgress->id);
                if ($submodulesData['auto_approve'] == 1) {
                    $statusUpdate = TrainingCourseSubmoduleJobProgress::where(['id' => $jobData->id])->update(['status' => "Approved"]);
                    $auth = Auth::user();
                    $mutable = $auth->jobStatusNotificationCheck->is_on ?? 0;
                    $jobStatusNotificationJob = (new \App\Jobs\JobStatusNotificationJob($auth, $mutable, $jobProgress, $jobProgress->subModule->submodule_type_id, $auth->user_relation->master_user_id, "Approved"))->delay(env('QUEUE_JOB_DELAY_TIME'));
                    dispatch($jobStatusNotificationJob);
                    $submoduleData = TrainingCourseSubmoduleDetails::select('enable_time_spend', 'condition', 'time_spent')->find($jobProgress->submodule_id);
                    $progressData = TrainingCourseSubmoduleProgress::where(['submodule_id' => $jobProgress->submodule_id, 'user_id' => $jobProgress->user_id])->first();
                    if ($submoduleData['enable_time_spend'] == 1) {
                        if ($submoduleData['condition'] == 'and') {
                            if ($progressData->time_spent == $progressData->total_spent) {
                                $progress = 100;
                            } else {
                                $timeSpent = (int) ((100 * $progressData->time_spent) / $progressData->total_spent);
                                $progress = (int) (($timeSpent + 100) / 2);
                            }
                        } else {
                            $progress = 100;
                        }
                    } else {
                        $progress = 100;
                    }

                    $progress = (int) ($progress > 100 ? 100 : $progress);
                    if($overRideResult->is_result_override == 'Default'){
                    TrainingCourseSubmoduleProgress::where(['submodule_id' => $jobProgress->submodule_id, 'user_id' => $jobProgress->user_id])->update(['submodule_progress' => $progress]);
                    }
                    $submoduleDataRecord = TrainingCourseSubmoduleDetails::select('remove_progress_calculation')->where('id',$data['submodule_id'])->first();
                    
                    if($overRideResult->is_result_override == 'Default'){
                        if($submoduleDataRecord->remove_progress_calculation == 0){
                        $mData = ['training_course_id' => $jobProgress->training_course_id, 'module_id' => $jobProgress->module_id, 'user_id' => $jobProgress->user_id];
                        (new TrainingCourseModuleProgress)->calculateModuleProgress($mData);
                        // Training course progress
                        $tData = ['training_course_id' => $jobProgress->training_course_id, 'user_id' => $jobProgress->user_id];
                        (new TrainingCourseProgress)->calculateTrainingCourseProgress($tData);
                        }
                    }
                }
            });
            return response()->json(setResponse([], ['message' => __('user.TrainingCourseSubmodule.uploadJob')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule  Upload Job Detail
     *
     * @return \Illuminate\Http\Response
     */
    public function uploadJobDetail($id)
    {
        try {
            $uploadJob = TrainingCourseSubmoduleJobProgress::with('jobProgressMedia')->where(['id' => $id, 'user_id' => auth()->id()])->first();
            return ($uploadJob) ? (new TrainingCourseUploadJobDetailResource($uploadJob)) :
                response()->json(setErrorResponse(__('Training Course Job doesn\'t exists.')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * All Uploaded Jobs
     *
     * @return \Illuminate\Http\Response
     */
    public function allUploadJobList(Request $request)
    {
        try {
            $data = $request->all();

            // Find training course id
            $trainingCourseIds = (new TrainingCourse)->getUserAssignCourseIds();

            // Default entry in Progress table
            if (!empty($trainingCourseIds)) {
                foreach ($trainingCourseIds as $id) {
                    TrainingCourse::addDefaultCourseProgress($id);
                }
            }

            $query = TrainingCourseSubmoduleJobProgress::select('training_course_submodule_job_progress.id', 'training_course_submodule_job_progress.submodule_id', 'training_course_submodule_job_progress.module_id', 'training_course_submodule_job_progress.training_course_id', 'training_course_submodule_job_progress.user_id', 'job_no', 'training_course_submodule_job_progress.status', 'attempts', 'job_no', 'job_topic', 'tc.title', 'training_course_submodule_job_progress.updated_at', 'tcsd.enable_time_spend', 'tcsd.condition', 'tcsd.time_spent', 'tcsd.submodule_name', 'tcmd.name', 'tcsd.multiple_job_enable')
                ->join(DBTableNames::TRAINING_COURSE . ' as tc', 'tc.id', 'training_course_submodule_job_progress.training_course_id')
                ->join(DBTableNames::TRAINING_COURSE_MODULES . ' as tcmd', 'training_course_submodule_job_progress.module_id', 'tcmd.id')
                ->join(DBTableNames::TRAINING_COURSE_SUBMODULE_DETAILS . ' as tcsd', 'training_course_submodule_job_progress.submodule_id', 'tcsd.id')
                ->where('training_course_submodule_job_progress.user_id', auth()->id())
                ->whereIn('training_course_submodule_job_progress.training_course_id', $trainingCourseIds);

            if (!empty($data['job_topic'])) {
                $jobTopic = explode(',', $data['job_topic']);
                $query = $query->whereIn('job_topic', $jobTopic);
            }

            if (!empty($data['status'])) {
                $status = explode(',', $data['status']);
                $query = $query->whereIn('training_course_submodule_job_progress.status', $status);
            }

            if (!empty($data['training_course'])) {
                $trainingCourse = explode(',', $data['training_course']);
                $query = $query->whereIn('tc.title', $trainingCourse);
            }

            $jobsList = $query->orderBy('id', 'DESC')->get();
            $jobList = [];

            // Start Status Filter
            if (!empty($data['status'])) {
                $status = explode(',', $data['status']);
            }

            if (empty($data['status']) && empty($data['job_topic']) && empty($data['training_course'])) {
                $jobList[] = ["status" => "Start", "job_no" => "N/A", "last_updated" => "N/A", "training_course" => "", "job_topic" => "N/A", "job_id" => 0, "training_course_id" => 0, "module_id" => 0, "module_name" => "N/A", "submodule_id" => 0, "submodule_name" => "N/A", "submodule_type_id" => 0, "attempt" => 0, "multiple_job_enable" => 1, "enable_time_spend" => 0, "condition" => "N/A", "submodule_progress" => 0, "user_time_spent" => 0];
                foreach ($jobsList as $job) {
                    $submoduleProgress = (new TrainingCourseSubmoduleProgress)->getProgress(['user_id' => auth()->user()->id, 'submodule_id' => $job->submodule_id]);
                    $userTimeSpent = (new TrainingCourseSubmoduleProgress)->getUserTimeSpent(['user_id' => auth()->user()->id, 'submodule_id' => $job->submodule_id]);

                    $jobList[] = ["status" => $job->status, "job_no" => (!is_null($job->job_no) ? $job->job_no : "N/A"), "last_updated" => format_datetime($job->updated_at, 'Y-m-d\TH:i:sO'), "training_course" => $job->title, "job_topic" => $job->job_topic, "job_id" => $job->id, "training_course_id" => $job->training_course_id, "module_id" => $job->module_id, "module_name" => $job->name, "submodule_id" => $job->submodule_id, "submodule_name" => $job->submodule_name, "submodule_type_id" => 13, "attempt" => $job->attempts, "multiple_job_enable" => $job->multiple_job_enable, "enable_time_spend" => $job->enable_time_spend, "condition" => (!is_null($job->condition) ? $job->condition : ""), "submodule_progress" => $submoduleProgress, "user_time_spent" => $userTimeSpent];
                }
            } else if ((!empty($data['status']) && strpos($data['status'], ',') !== true && $data['status'] != 'Start') || !empty($data['job_topic']) || !empty($data['training_course'])) {
                foreach ($jobsList as $job) {
                    $submoduleProgress = (new TrainingCourseSubmoduleProgress)->getProgress(['user_id' => auth()->user()->id, 'submodule_id' => $job->submodule_id]);
                    $userTimeSpent = (new TrainingCourseSubmoduleProgress)->getUserTimeSpent(['user_id' => auth()->user()->id, 'submodule_id' => $job->submodule_id]);

                    $jobList[] = ["status" => $job->status, "job_no" => (!is_null($job->job_no) ? $job->job_no : "N/A"), "last_updated" => format_datetime($job->updated_at, 'Y-m-d\TH:i:sO'), "training_course" => $job->title, "job_topic" => $job->job_topic, "job_id" => $job->id, "training_course_id" => $job->training_course_id, "module_id" => $job->module_id, "module_name" => $job->name, "submodule_id" => $job->submodule_id, "submodule_name" => $job->submodule_name, "submodule_type_id" => 13, "attempt" => $job->attempts, "multiple_job_enable" => $job->multiple_job_enable, "enable_time_spend" => $job->enable_time_spend, "condition" => (!is_null($job->condition) ? $job->condition : ""), "submodule_progress" => $submoduleProgress, "user_time_spent" => $userTimeSpent];
                }
            }

            // Sorting by Start
            $jobColumn = array_column($jobList, 'status');
            array_multisort($jobColumn, SORT_DESC, $jobList);

            $jData = [];
            $jData['data']['list'] = $jobList;
            return $jData;
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Job Filter List
     *
     * @return \Illuminate\Http\Response
     */
    public function jobFilterList()
    {
        try {
            // Find training course id
            $trainingCourseIds = (new TrainingCourse)->getUserAssignCourseIds();
            $courseData = TrainingCourseSubmoduleDetails::select('training_course_id', 'job_topic')->where(['submodule_type_id' => 13, 'status' => 'Active'])->whereIn('training_course_id', $trainingCourseIds)->get()->toArray();
            $trainingCourse = TrainingCourse::whereIn('id', array_column($courseData, 'training_course_id'))->pluck('title')->toArray();
            $filters = ['job_topic' => array_column($courseData, 'job_topic'), 'training_course' => array_unique($trainingCourse), 'status' => ['Start', 'Pending', 'Rejected', 'Approved']];
            return $filters;
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Course Job List
     *
     * @return \Illuminate\Http\Response
     */
    public function courseJobList(Request $request)
    {
        try {
            $data = $request->all();
            $trainingCourseIds = (new TrainingCourse)->getUserAssignCourseIds();

            $jobList = TrainingCourseSubmoduleDetails::with(
                'trainingCourse:id,title',
                'module:id,name',
                'jobProgress:id,created_at,submodule_id,status'
            )->where(['status' => 'Active', 'submodule_type_id' => 13])->select('id', 'training_course_id', 'module_id', 'submodule_name', 'enable_time_spend', 'multiple_job_enable', 'condition')->whereIn('training_course_id', $trainingCourseIds)->get();

            foreach ($jobList as $key => $job) {
                $userTimeSpent = (new TrainingCourseSubmoduleProgress)->getUserTimeSpent(['user_id' => auth()->id(), 'submodule_id' => $job->id]);

                $jobList[$key]['user_time_spent'] = $userTimeSpent;

                $pendingJobCnt = TrainingCourseSubmoduleJobProgress::where(['submodule_id' => $job->id, 'user_id' => auth()->id(), 'status' => 'Pending'])->count();
                if ($pendingJobCnt == config('constants.pending_limit')) {
                    unset($jobList[$key]);
                }

                if ($job->multiple_job_enable == 0) {
                    $uploadJobCnt = TrainingCourseSubmoduleJobProgress::where(['submodule_id' => $job->id, 'user_id' => auth()->id()])->where('status', '!=', 'Rejected')->count();
                    if ($uploadJobCnt > 0) {
                        unset($jobList[$key]);
                    }
                }
            }
            return ($jobList) ? (new CustomCollection($jobList, 'App\Http\Resources\V1\TrainingCourseJobsListResource')) :
                response()->json(setErrorResponse(__('Training Course Jobs doesn\'t exists.')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            dd($e);
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule Practical Assessment
     *
     * @return \Illuminate\Http\Response
     */
    public function getPracticalAssessment($id) {
        try {
            $subModule = $this->model
                ->where('submodule_type_id', config('constants.submodule_types.practical_assessment'))
                ->orWhere('submodule_type_id', config('constants.submodule_types.self_assessment'))
                ->find($id);
            if ($subModule) {
                $result_override=AdjustResult::where('submodule_id',$id)->where('assessor_id',auth()->user()->id)->where('user_id',request()->user_id)->count();
                    if($result_override>0){
                        $override=TRUE;
                    }else{
                        $override=FALSE;
                    }
                $allowOptionalMedia = TrainingCourseSubmoduleDetails::whereId($id)->pluck('allow_optional_media')->first();
                $restrict_gallery = TrainingCourseSubmoduleDetails::whereId($id)->pluck('restrict_gallery')->first();
                $allowOfflineSaSubmission = TrainingCourseSubmoduleDetails::whereId($id)->pluck('allow_offline_sa_submission')->first();
                $OptionalMedia = PracticalAssessmentMedia::where('assessor_id', auth()->user()->id)->whereSubmoduleId($id)->whereNull('question_id')->get();
                if (strtolower(trim(request()->api_version)) == 'v2') {
                    if (request()->history && request()->history == 'true') {
                        $attempt=0;
                        //will show question attepmted by user even deleted from sub-modules and also same for question related data where history=true
                        $practicalAssessment = TrainingCourseSubModulePracticalAssessmentQuestion::
                                        select('training_course_submodule_practical_assessment_questions.*',
                                                DB::raw("IF(ans.question_name is null,training_course_submodule_practical_assessment_questions.question,ans.question_name) as question"),
                                                DB::raw("IF(ans.question_type is null,training_course_submodule_practical_assessment_questions.question_type,ans.question_type) as question_type"))
                                        ->with('options:id,question_id,name,severity', 'subModule:id,allow_optional_media', 'answers:question_id,user_id,answer,is_new')
                                        ->join('training_course_submodule_practical_assessment_answers as ans', 'ans.question_id', 'training_course_submodule_practical_assessment_questions.id')
                                        ->where('training_course_submodule_practical_assessment_questions.submodule_id', $id)
                                        ->where('assessor_id', auth()->user()->id)
                                         ->groupBy(DB::raw('IFNULL(training_course_submodule_practical_assessment_questions.category_id, training_course_submodule_practical_assessment_questions.id)'))
                                        ->orderBy('training_course_submodule_practical_assessment_questions.question_list_order', 'ASC')
                                ->orderBy('training_course_submodule_practical_assessment_questions.question_order', 'ASC')
                                ->orderBy('training_course_submodule_practical_assessment_questions.id', 'ASC')
                                ->withTrashed()->get();
                    $answeredQuestion=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id',$id)->where('assessor_id',auth()->user()->id)->where('user_id',request()->user_id)->whereNotNull('question_id')->count();
                    } else {
                        if(!isset(request()->attempt)){
                        $attempt = $this->SubModuleAttempt($id,request()->user_id);
                        }else{
                            $attempt = (int) request()->attempt;
                        }
                        $practicalAssessment = TrainingCourseSubModulePracticalAssessmentQuestion::with('options:id,question_id,name,severity', 'subModule:id,allow_optional_media', 'answers:question_id,user_id,answer,is_new')
                                ->whereSubmoduleId($id)
                                ->orderBy('question_list_order', 'ASC')
                                ->orderBy('question_order', 'ASC')
                                ->orderBy('id', 'ASC')
                                ->groupBy(DB::raw('IFNULL(category_id, id)'))
                                ->get();
                    $answeredQuestion=null;
                    }
                    $TotalSubModuleQuestion=TrainingCourseSubModulePracticalAssessmentQuestion::where('submodule_id',$id)->where('is_required','1')->pluck('id');
                    $TotalDeletedQuestion=TrainingCourseSubModulePracticalAssessmentQuestion::where('submodule_id',$id)->where('is_required','1')->onlyTrashed()->pluck('id');
                    $DeleteQuestionCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $id)->where('assessor_id',auth()->user()->id)->where('user_id',request()->user_id)->whereIn('question_id',$TotalDeletedQuestion)->distinct('question_id')->count('id');
                    $AnsweredCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $id)->where('assessor_id',auth()->user()->id)->where('user_id',request()->user_id)->whereIn('question_id',$TotalSubModuleQuestion)->distinct('question_id')->count('id');
                    $AnsweredDeleteCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $id)->where('assessor_id',auth()->user()->id)->where('user_id',request()->user_id)->whereIn('question_id',$TotalDeletedQuestion)->distinct('question_id')->count('id');
                    $list = ($practicalAssessment) ? (new CustomCollection($practicalAssessment, 'App\Http\Resources\V1\TrainingCourseCoursePracticalAssessmentResource')) :
                            response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                    $data['data']['allow_optional_media'] = $allowOptionalMedia;
                    $data['data']['attempt'] = $attempt;
                    $data['data']['restrict_gallery'] = $restrict_gallery;
                    $data['data']['allow_offline_sa_submission'] = $allowOfflineSaSubmission;
                    $data['data']['total_question'] = count($TotalSubModuleQuestion)+ $DeleteQuestionCount;
                    $data['data']['total_answered_question'] = $this->GetAnsweredQuestionCount($id,auth()->user()->id,request()->user_id);
                    $data['data']['is_result_override'] = $override;
                    $data['data']['optional_media'] = PracticalAssessmentMediaResource::collection($OptionalMedia);
                    $data['data']['question_list'] = $list;
                } else {
                    if (request()->history && request()->history == 'true') {
                        $attempt=0;
                        //will show question attepmted by user even deleted from sub-modules and also same for question related data where history=true
                        $practicalAssessment = TrainingCourseSubModulePracticalAssessmentQuestion::
                                        select('training_course_submodule_practical_assessment_questions.*',
                                                DB::raw("IF(ans.question_name is null,training_course_submodule_practical_assessment_questions.question,ans.question_name) as question"),
                                                DB::raw("IF(ans.question_type is null,training_course_submodule_practical_assessment_questions.question_type,ans.question_type) as question_type"))
                                        ->with('options:id,question_id,name,severity', 'subModule:id,allow_optional_media', 'answers:question_id,user_id,answer,is_new')
                                        ->join('training_course_submodule_practical_assessment_answers as ans', 'ans.question_id', 'training_course_submodule_practical_assessment_questions.id')
                                        ->where('training_course_submodule_practical_assessment_questions.submodule_id', $id)
                                        ->where('assessor_id', auth()->user()->id)->whereNull('category_id')
                                        ->groupBy('training_course_submodule_practical_assessment_questions.id')
                                        ->orderBy('question_order', 'ASC')->withTrashed()->get();
                        $answeredQuestion=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id',$id)->where('assessor_id',auth()->user()->id)->where('user_id',request()->user_id)->whereNotNull('question_id')->count();
                    } else {
                        if(!isset(request()->attempt)){
                        $attempt = $this->SubModuleAttempt($id,request()->user_id);
                        }else{
                            $attempt =(int) request()->attempt;
                        }
                        $practicalAssessment = TrainingCourseSubModulePracticalAssessmentQuestion::with('options:id,question_id,name,severity', 'subModule:id,allow_optional_media', 'answers:question_id,user_id,answer,is_new')->whereSubmoduleId($id)->whereNull('category_id')->orderBy('question_order', 'ASC')->get();
                        $answeredQuestion=null;
                    }
                    $TotalSubModuleQuestion=TrainingCourseSubModulePracticalAssessmentQuestion::where('submodule_id',$id)->where('is_required','1')->pluck('id');
                    $TotalDeletedQuestion=TrainingCourseSubModulePracticalAssessmentQuestion::where('submodule_id',$id)->where('is_required','1')->onlyTrashed()->pluck('id');
                    $DeleteQuestionCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $id)->where('assessor_id',auth()->user()->id)->where('user_id',request()->user_id)->whereIn('question_id',$TotalDeletedQuestion)->distinct('question_id')->count('id');
                    $AnsweredCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $id)->where('assessor_id',auth()->user()->id)->where('user_id',request()->user_id)->whereIn('question_id',$TotalSubModuleQuestion)->distinct('question_id')->count('id');
                    $AnsweredDeleteCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $id)->where('assessor_id',auth()->user()->id)->where('user_id',request()->user_id)->whereIn('question_id',$TotalDeletedQuestion)->distinct('question_id')->count('id');
                    $list = ($practicalAssessment) ? (new CustomCollection($practicalAssessment, 'App\Http\Resources\V1\TrainingCourseCoursePracticalAssessmentResource')) :
                            response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                    $data['data']['allow_optional_media'] = $allowOptionalMedia;
                    $data['data']['attempt'] = $attempt;
                    $data['data']['is_result_override'] = $override;
                    $data['data']['total_question'] = count($TotalSubModuleQuestion)+ $DeleteQuestionCount;
                    // $data['data']['total_answered_question'] = $AnsweredCount+$AnsweredDeleteCount;
                    $data['data']['total_answered_question'] = $this->GetAnsweredQuestionCount($id,auth()->user()->id,request()->user_id);
                    $data['data']['optional_media'] = PracticalAssessmentMediaResource::collection($OptionalMedia);
                    $data['data']['form'] = $list;
                    $data['data']['categories'] = new TrainingCourseSubModulePracticalAssessmentCategoryQuestionResourceMobile($id);
                }
                return $data;
            } else {
                return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    public function SubModuleAttempt($submodule_id,$user_id)
    {
        $submodule = TrainingCourseSubmoduleDetails::find($submodule_id);
        if($submodule->submodule_type_id == 19){
        $attempt = SelfAssessmentAttempt::where('submodule_id',$submodule_id)->where('user_id',$user_id)->count();
        SelfAssessmentAttempt::create([
            'submodule_id' => $submodule_id,
            'user_id' => $user_id,
            'attempt' => $attempt+1, // You may want to increment this if tracking multiple attempts
            'attempt_date' => now(),
        ]);
        return $attempt+1;
        }
        return 0;
    }
    public function getAttemptHistory($id)
    {
        $attempt = SelfAssessmentAttempt::where('submodule_id',$id)->where('user_id',auth()->user()->id)->orderBy('attempt_date','desc')->get();
        return AttemptResource::collection($attempt);
    }
    public function deleteMediaPracticalAssessment($id)
    {
        $media = PracticalAssessmentMedia::where('id',$id)->first();
        if($media){
            $media->delete();
            return response()->json(setResponse([], ['message' => __('Media deleted successfully')]))->setStatusCode(Response::HTTP_OK);
        }
        return response()->json(setErrorResponse(__('Media not found')))->setStatusCode(Response::HTTP_NOT_FOUND);
    }
    public function GetAnsweredQuestionCount($submodule_id,$assessor_id,$user_id)
    {
        $Count=0;
        $TotalSubModuleQuestion=TrainingCourseSubModulePracticalAssessmentQuestion::where('submodule_id',$submodule_id)->where('is_required','1')->pluck('id');
        foreach($TotalSubModuleQuestion as $question){
            $questionType=TrainingCourseSubModulePracticalAssessmentQuestion::where('id',$question)->value('question_type');
            if($questionType=='text')
            {
                $AnsweredCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submodule_id)->where('assessor_id',$assessor_id)->whereIn('user_id',explode(',',$user_id))->where('question_id',$question)->where('answer','!=',"")->count('id');
                if($AnsweredCount==count(explode(',',$user_id))){
                $Count+=1;
                }
            }
            if($questionType=='calculated_single')
            {
                $AnsweredCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submodule_id)->where('assessor_id',$assessor_id)->whereIn('user_id',explode(',',$user_id))->where('question_id',$question)->where('option_id','!=',"")->count('id');
                if($AnsweredCount==count(explode(',',$user_id))){
                $Count+=1;
                }
            }
            if($questionType=='rating')
            {
                $AnsweredCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submodule_id)->where('assessor_id',$assessor_id)->whereIn('user_id',explode(',',$user_id))->where('question_id',$question)->where('rating','!=',"")->count('id');
                if($AnsweredCount==count(explode(',',$user_id))){
                $Count+=1;
                }
            }
            if($questionType=='multiple')
            {
                $AnsweredCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submodule_id)->where('assessor_id',$assessor_id)->whereIn('user_id',explode(',',$user_id))->where('question_id',$question)->where('answer','!=',"")->count('id');
                if($AnsweredCount==count(explode(',',$user_id))){
                $Count+=1;
                }
            }
            if($questionType=='location')
            {
                $AnsweredCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submodule_id)->where('assessor_id',$assessor_id)->whereIn('user_id',explode(',',$user_id))->where('question_id',$question)->where('latitude','!=',"")->count('id');
                if($AnsweredCount==count(explode(',',$user_id))){
                $Count+=1;
                }
            }
            if($questionType=='single')
            {
                $AnsweredCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submodule_id)->where('assessor_id',$assessor_id)->whereIn('user_id',explode(',',$user_id))->where('question_id',$question)->where('option_id','!=',"")->count('id');
                if($AnsweredCount==count(explode(',',$user_id))){
                $Count+=1;
                }
            }
            if($questionType=='multiple')
            {
                $AnsweredCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submodule_id)->where('assessor_id',$assessor_id)->whereIn('user_id',explode(',',$user_id))->where('question_id',$question)->where('option_id','!=',"")->count('id');
                if($AnsweredCount==count(explode(',',$user_id))){
                $Count+=1;
                }
            }
            if($questionType=='image')
            {
                $AnsweredCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submodule_id)->where('assessor_id',$assessor_id)->whereIn('user_id',explode(',',$user_id))->where('question_id',$question)->where('media_data','!=',"")->count('id');
                if($AnsweredCount==count(explode(',',$user_id))){
                $Count+=1;
                }
            }
            if($questionType=='video')
            {
                $AnsweredCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submodule_id)->where('assessor_id',$assessor_id)->whereIn('user_id',explode(',',$user_id))->where('question_id',$question)->where('media_data','!=',"")->count('id');
                if($AnsweredCount==count(explode(',',$user_id))){
                $Count+=1;
                }
            }
        }
        $TotalDeletedQuestion=TrainingCourseSubModulePracticalAssessmentQuestion::where('submodule_id',$submodule_id)->where('is_required','1')->onlyTrashed()->pluck('id');
        return $Count;
    }
    /**
     * Submit Training Course Practical Assessment
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function submitPracticalAssessment(PracticalAssessmentRequest $request)
    {
        try {
            $data = $request->all();
            $resultdata = $data['result'];
            if (count($resultdata) && isset($data['is_progress']) && $data['is_progress']!=1) {
                $this->PracticalAssessmentRepository->saveGeoLocation($data);
                $jsonRequest = $this->PracticalAssessmentRepository->saveJsonRequest($data);
                $masterUserId = TrainingCourse::whereId($data['training_course_id'])->pluck('master_user_id')->first();
                foreach ($data['result'] as $result) {
                    if(isset($result['question_id'])){
                        foreach ($result['answer_json'] as $answer) {
                            if (strpos($answer['user_id'], ',')) {
                                $user_ids = explode(',', $answer['user_id']);
                            } else {
                                $user_ids = $answer['user_id'];
                            }
                            if (is_array($user_ids) && count($user_ids) > 1) {
                                //added by kishor start
                                if (request()->history && request()->history == 'true') {
                                    //getting attempted data related to question after finally submited by user where history=true
                                    $attemtedA = TrainingCourseSubModulePracticalAssessmentAnswer::whereTrainingCourseId($data['training_course_id'])->whereModuleId($data['module_id'])->whereSubmoduleId($data['submodule_id'])->whereAssessorId(auth()->user()->id)->whereMasterUserId($masterUserId)->whereQuestionId($result['question_id'])->whereIn('user_id', $user_ids)->first();
                                    $question_name = $attemtedA->question_name;
                                    $question_type = $attemtedA->question_type;
                                    $question_options = $attemtedA->question_options;
                                    $total_questions = $attemtedA->total_questions;
                                } else {
                                    $attemtedQ = TrainingCourseSubModulePracticalAssessmentQuestion::find($result['question_id']);
                                    $question_name = $attemtedQ->question ?? null;
                                    $question_type = $attemtedQ->question_type ?? null;
                                    $question_options = isset($attemtedQ->options) ? json_encode($attemtedQ->options) : null;
                                    $total_questions = TrainingCourseSubModulePracticalAssessmentQuestion::whereSubmoduleId($data['submodule_id'])->where('is_required', 1)->count('id');
                                }
                                //end
                                TrainingCourseSubModulePracticalAssessmentAnswer::where('training_course_id',$data['training_course_id'])->where('module_id',$data['module_id'])->where('submodule_id',$data['submodule_id'])->where('assessor_id',auth()->user()->id)->where('master_user_id',$masterUserId)->where('question_id',$result['question_id'])->whereIn('user_id',$user_ids)->delete();
                                foreach ($user_ids as $key => $value) {
                                                    $insert_record[]= ['training_course_id' => $data['training_course_id'],
                                    'module_id' => $data['module_id'],
                                    'submodule_id' => $data['submodule_id'],
                                    'user_id' => $value,
                                    'assessor_id' => auth()->user()->id,
                                                        'assessed_by_assessor' =>$data['assessed_by_assessor'],
                                    'master_user_id' => $masterUserId,
                                    'question_id' => isset($result['question_id']) ? $result['question_id'] : null,
                                    'option_id' => isset($answer['option_id']) ? $answer['option_id'] : null,
                                    'answer' => isset($answer['answer']) ? $answer['answer'] : null,
                                    'rating' => isset($answer['rating']) ? $answer['rating'] : null,
                                    'latitude' => isset($answer['latitude']) ? $answer['latitude'] : null,
                                    'longitude' => isset($answer['longitude']) ? $answer['longitude'] : null,
                                    'media_data' => isset($answer['media_data']) ? json_encode($answer['media_data']) : null,
                                    'created_at' => date('Y-m-d H:i:s'),
                                    'updated_at' => date('Y-m-d H:i:s'),
                                    //added by kishor start
                                    'question_name' => $question_name,
                                    'question_type' => $question_type,
                                    'question_options' => $question_options,
                                    'total_questions' => $total_questions,
                                        //end
                                    ];
                                }
                                TrainingCourseSubModulePracticalAssessmentAnswer::insert($insert_record);
                            } else {
                            //added by kishor start
                            if (request()->history && request()->history == 'true') {
                                //getting attempted data related to question after finally submited by user where history=true
                                $attemtedA = TrainingCourseSubModulePracticalAssessmentAnswer::whereTrainingCourseId($data['training_course_id'])->whereModuleId($data['module_id'])->whereSubmoduleId($data['submodule_id'])->whereAssessorId(auth()->user()->id)->whereMasterUserId($masterUserId)->whereQuestionId($result['question_id'])->whereUserId($answer['user_id'])->first();
                                $question_name = $attemtedA->question_name;
                                $question_type = $attemtedA->question_type;
                                $question_options = $attemtedA->question_options;
                                $total_questions = $attemtedA->total_questions;
                            } else {
                                $attemtedQ = TrainingCourseSubModulePracticalAssessmentQuestion::find($result['question_id']);
                                $question_name = $attemtedQ->question ?? null;
                                $question_type = $attemtedQ->question_type ?? null;
                                $question_options = isset($attemtedQ->options) ? json_encode($attemtedQ->options) : null;
                                $total_questions = TrainingCourseSubModulePracticalAssessmentQuestion::whereSubmoduleId($data['submodule_id'])->where('is_required', 1)->count('id');
                            }
                            //end
                            TrainingCourseSubModulePracticalAssessmentAnswer::where('training_course_id',$data['training_course_id'])->where('module_id',$data['module_id'])->where('submodule_id',$data['submodule_id'])->where('assessor_id',auth()->user()->id)->where('master_user_id',$masterUserId)->where('question_id',$result['question_id'])->where('user_id',$answer['user_id'])->delete();
                                                $insert_record=['training_course_id' => $data['training_course_id'],
                                                'module_id' =>$data['module_id'],
                                                'submodule_id' =>$data['submodule_id'],
                                'user_id' => $answer['user_id'],
                                'assessor_id' => auth()->user()->id,
                                'assessed_by_assessor' => $data['assessed_by_assessor'],
                                'master_user_id' => $masterUserId,
                                'question_id' => isset($result['question_id']) ? $result['question_id'] : null,
                                'option_id' => isset($answer['option_id']) ? $answer['option_id'] : null,
                                'answer' => isset($answer['answer']) ? $answer['answer'] : null,
                                'rating' => isset($answer['rating']) ? $answer['rating'] : null,
                                'latitude' => isset($answer['latitude']) ? $answer['latitude'] : null,
                                'longitude' => isset($answer['longitude']) ? $answer['longitude'] : null,
                                'media_data' => isset($answer['media_data']) ? json_encode($answer['media_data']) : null,
                                'created_at' => date('Y-m-d H:i:s'),
                                'updated_at' => date('Y-m-d H:i:s')
                            ];
                            //added by kishor start
                            $insert_record['question_name'] = $question_name;
                            $insert_record['question_type'] = $question_type;
                            $insert_record['question_options'] = $question_options;
                            $insert_record['total_questions'] = $total_questions;
                            //end
                            TrainingCourseSubModulePracticalAssessmentAnswer::insert($insert_record);
                            }
                        }
                    }
                }
            }
            if(isset($data['is_progress']) && $data['is_progress']==1){
                $this->PracticalAssessmentRepository->saveGeoLocation($data);
                $is_Update=TrainingCourseSubModulePracticalAssessmentAnswersJson::where('training_course_id',$data['training_course_id'])->where('module_id',$data['module_id'])->where('submodule_id',$data['submodule_id'])->where('assessor_id',auth()->user()->id)->value('required');
                if($is_Update==1){
                    $masterUserId = TrainingCourse::whereId($data['training_course_id'])->pluck('master_user_id')->first();
                    if (request()->history && request()->history == 'true') {
                        //calc progress for what question attempted by user where history=true
                        $this->PracticalAssessmentRepository->progressCalculationForHistory($data, auth()->user()->id, $masterUserId);
                    } else {
                        $this->PracticalAssessmentRepository->progressCalculation($data, auth()->user()->id, $masterUserId);
                    }
                    TrainingCourseSubModulePracticalAssessmentAnswersJson::where('training_course_id',$data['training_course_id'])->where('module_id',$data['module_id'])->where('submodule_id',$data['submodule_id'])->where('assessor_id',auth()->user()->id)->update(['required'=>0]);
                }
            }
            $submoduleName = TrainingCourseSubmoduleDetails::whereId($request->submodule_id)->pluck('submodule_name')->first();
            // Submit Practical Assessment Log
            $logsData = [
                'user_id' => auth()->user()->id,
                'email' => auth()->user()->email ?? null,
                'action' => 'Create',
                'action_name' => config('constants.system_logs.submit_practical_assessment'),
                'url' => str_replace(url('/v1/'), '', request()->fullUrl()) ?? null,
                'is_app_operator_admin' => 1,
                'is_practical_assessment' => 1 ,
                'system_section' => 'Practical Assessment',
                'item_id' => $data['submodule_id'] ?? null,
            ];
            custom_system_logs($logsData);
            return response()->json(setResponse($data, ['message' => __($submoduleName . ' has been submitted successfully.Result will be reflected after 2-3 minutes.')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    /**
     * Submit Training Course Practical Assessment
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function submitSelfAssessment(PracticalAssessmentRequest $request)
    {
        try {
            $data = $request->all();
            $data['type'] = 'Self';
            $resultdata = $data['result'];
            if (count($resultdata) && isset($data['is_progress']) && $data['is_progress']!=1) {
                $this->PracticalAssessmentRepository->saveGeoLocation($data);
                $jsonRequest = $this->PracticalAssessmentRepository->saveJsonRequest($data);
                $masterUserId = TrainingCourse::whereId($data['training_course_id'])->pluck('master_user_id')->first();
                foreach ($data['result'] as $result) {
                    if(isset($result['question_id'])){
                        foreach ($result['answer_json'] as $answer) {
                            if (strpos($answer['user_id'], ',')) {
                                $user_ids = explode(',', $answer['user_id']);
                            } else {
                                $user_ids = $answer['user_id'];
                            }
                            if (is_array($user_ids) && count($user_ids) > 1) {
                                //added by kishor start
                                if (request()->history && request()->history == 'true') {
                                    //getting attempted data related to question after finally submited by user where history=true
                                    $attemtedA = TrainingCourseSubModulePracticalAssessmentAnswer::whereTrainingCourseId($data['training_course_id'])->whereModuleId($data['module_id'])->whereSubmoduleId($data['submodule_id'])->whereAssessorId(auth()->user()->id)->whereMasterUserId($masterUserId)->whereQuestionId($result['question_id'])->whereIn('user_id', $user_ids)->first();
                                    $question_name = $attemtedA->question_name;
                                    $question_type = $attemtedA->question_type;
                                    $question_options = $attemtedA->question_options;
                                    $total_questions = $attemtedA->total_questions;
                                } else {
                                    $attemtedQ = TrainingCourseSubModulePracticalAssessmentQuestion::find($result['question_id']);
                                    $question_name = $attemtedQ->question ?? null;
                                    $question_type = $attemtedQ->question_type ?? null;
                                    $question_options = isset($attemtedQ->options) ? json_encode($attemtedQ->options) : null;
                                    $total_questions = TrainingCourseSubModulePracticalAssessmentQuestion::whereSubmoduleId($data['submodule_id'])->where('is_required', 1)->count('id');
                                }
                                //end
                                TrainingCourseSubModulePracticalAssessmentAnswer::where('training_course_id',$data['training_course_id'])->where('module_id',$data['module_id'])->where('submodule_id',$data['submodule_id'])->where('assessor_id',auth()->user()->id)->where('master_user_id',$masterUserId)->where('question_id',$result['question_id'])->whereIn('user_id',$user_ids)->delete();
                                foreach ($user_ids as $key => $value) {
                                                    $insert_record[]= ['training_course_id' => $data['training_course_id'],
                                    'module_id' => $data['module_id'],
                                    'submodule_id' => $data['submodule_id'],
                                    'user_id' => $value,
                                    'assessor_id' => auth()->user()->id,
                                    'assessed_by_assessor' =>$data['assessed_by_assessor'],
                                    'master_user_id' => $masterUserId,
                                    'question_id' => isset($result['question_id']) ? $result['question_id'] : null,
                                    'option_id' => isset($answer['option_id']) ? $answer['option_id'] : null,
                                    'answer' => isset($answer['answer']) ? $answer['answer'] : null,
                                    'rating' => isset($answer['rating']) ? $answer['rating'] : null,
                                    'latitude' => isset($answer['latitude']) ? $answer['latitude'] : null,
                                    'longitude' => isset($answer['longitude']) ? $answer['longitude'] : null,
                                    'media_data' => isset($answer['media_data']) ? json_encode($answer['media_data']) : null,
                                    'created_at' => date('Y-m-d H:i:s'),
                                    'updated_at' => date('Y-m-d H:i:s'),
                                    //added by kishor start
                                    'question_name' => $question_name,
                                    'question_type' => $question_type,
                                    'question_options' => $question_options,
                                    'total_questions' => $total_questions,
                                    'type' => (isset($data['type']) && !empty($data['type'])) ? $data['type'] : 'PA',
                                        //end
                                    ];
                                }
                                TrainingCourseSubModulePracticalAssessmentAnswer::insert($insert_record);
                                TrainingCourseSubModuleSelfAssessmentAnswersAttempt::updateOrCreate(
                                    [
                                        'training_course_id' => $data['training_course_id'],
                                        'module_id' => $data['module_id'],
                                        'submodule_id' => $data['submodule_id'],
                                        'user_id' => $user_ids[0],
                                        'assessor_id' => auth()->user()->id,
                                        'master_user_id' => $masterUserId,
                                        'question_id' => isset($result['question_id']) ? $result['question_id'] : null,
                                        'attempt' => $data['attempt'] ?? 1,
                                    ],[
                                        'attempt_date' => date('Y-m-d H:i:s'),
                                        'assessed_by_assessor' => $data['assessed_by_assessor'],
                                        'answer' => isset($answer['answer']) ? $answer['answer'] : null,
                                        'option_id' => isset($answer['option_id']) ? $answer['option_id'] : null,
                                        'rating' => isset($answer['rating']) ? $answer['rating'] : null,
                                        'latitude' => isset($answer['latitude']) ? $answer['latitude'] : null,
                                        'longitude' => isset($answer['longitude']) ? $answer['longitude'] : null,
                                        'media_data' => isset($answer['media_data']) ? json_encode($answer['media_data']) : null,
                                        'question_name' => $question_name ?? null,
                                        'question_type' => $question_type ?? null,
                                        'question_options' => $question_options ?? null,
                                        'total_questions' => $total_questions ?? null,
                                    ]
                                );
                            } else {
                            //added by kishor start
                            if (request()->history && request()->history == 'true') {
                                //getting attempted data related to question after finally submited by user where history=true
                                $attemtedA = TrainingCourseSubModulePracticalAssessmentAnswer::whereTrainingCourseId($data['training_course_id'])->whereModuleId($data['module_id'])->whereSubmoduleId($data['submodule_id'])->whereAssessorId(auth()->user()->id)->whereMasterUserId($masterUserId)->whereQuestionId($result['question_id'])->whereUserId($answer['user_id'])->first();
                                $question_name = $attemtedA->question_name;
                                $question_type = $attemtedA->question_type;
                                $question_options = $attemtedA->question_options;
                                $total_questions = $attemtedA->total_questions;
                            } else {
                                $attemtedQ = TrainingCourseSubModulePracticalAssessmentQuestion::find($result['question_id']);
                                $question_name = $attemtedQ->question ?? null;
                                $question_type = $attemtedQ->question_type ?? null;
                                $question_options = isset($attemtedQ->options) ? json_encode($attemtedQ->options) : null;
                                $total_questions = TrainingCourseSubModulePracticalAssessmentQuestion::whereSubmoduleId($data['submodule_id'])->where('is_required', 1)->count('id');
                            }
                            //end
                            TrainingCourseSubModulePracticalAssessmentAnswer::where('training_course_id',$data['training_course_id'])->where('module_id',$data['module_id'])->where('submodule_id',$data['submodule_id'])->where('assessor_id',auth()->user()->id)->where('master_user_id',$masterUserId)->where('question_id',$result['question_id'])->where('user_id',$answer['user_id'])->delete();
                                                $insert_record=['training_course_id' => $data['training_course_id'],
                                                'module_id' =>$data['module_id'],
                                                'submodule_id' =>$data['submodule_id'],
                                'user_id' => $answer['user_id'],
                                'assessor_id' => auth()->user()->id,
                                'assessed_by_assessor' => $data['assessed_by_assessor'],
                                'master_user_id' => $masterUserId,
                                'question_id' => isset($result['question_id']) ? $result['question_id'] : null,
                                'option_id' => isset($answer['option_id']) ? $answer['option_id'] : null,
                                'answer' => isset($answer['answer']) ? $answer['answer'] : null,
                                'rating' => isset($answer['rating']) ? $answer['rating'] : null,
                                'latitude' => isset($answer['latitude']) ? $answer['latitude'] : null,
                                'longitude' => isset($answer['longitude']) ? $answer['longitude'] : null,
                                'media_data' => isset($answer['media_data']) ? json_encode($answer['media_data']) : null,
                                'created_at' => date('Y-m-d H:i:s'),
                                'updated_at' => date('Y-m-d H:i:s'),
                                'type' => (isset($data['type']) && !empty($data['type'])) ? $data['type'] : 'PA',
                            ];

                            // Handle cable matrix answers
                            if (isset($answer['question_type']) && $answer['question_type'] === 'cableMatrix') {
                                $cableMatrixSelection = [
                                    'diameter' => $answer['cable_diameter'] ?? null,
                                    'type' => $answer['cable_type'] ?? null,
                                    'description' => $answer['cable_description'] ?? null,
                                    'additional_data' => $answer['additional_data'] ?? null
                                ];

                                $insert_record['cable_matrix_selection'] = json_encode($cableMatrixSelection);
                                $insert_record['answer'] = $answer['cable_description'] ?? '';
                            }
                            //added by kishor start
                            $insert_record['question_name'] = $question_name;
                            $insert_record['question_type'] = $question_type;
                            $insert_record['question_options'] = $question_options;
                            $insert_record['total_questions'] = $total_questions;
                            //end
                            TrainingCourseSubModulePracticalAssessmentAnswer::insert($insert_record);       
                            TrainingCourseSubModuleSelfAssessmentAnswersAttempt::updateOrCreate(
                                [
                                    'training_course_id' => $data['training_course_id'],
                                    'module_id' => $data['module_id'],
                                    'submodule_id' => $data['submodule_id'],
                                    'user_id' => $answer['user_id'],
                                    'assessor_id' => auth()->user()->id,
                                    'master_user_id' => $masterUserId,
                                    'question_id' => isset($result['question_id']) ? $result['question_id'] : null,
                                    'attempt' => $data['attempt'] ?? 1,
                                ],[
                                    'attempt_date' => date('Y-m-d H:i:s'),
                                    'assessed_by_assessor' => $data['assessed_by_assessor'],
                                    'answer' => isset($answer['answer']) ? $answer['answer'] : null,
                                    'option_id' => isset($answer['option_id']) ? $answer['option_id'] : null,
                                    'rating' => isset($answer['rating']) ? $answer['rating'] : null,
                                    'latitude' => isset($answer['latitude']) ? $answer['latitude'] : null,
                                    'longitude' => isset($answer['longitude']) ? $answer['longitude'] : null,
                                    'media_data' => isset($answer['media_data']) ? json_encode($answer['media_data']) : null,
                                    'question_name' => $question_name ?? null,
                                    'question_type' => $question_type ?? null,
                                    'question_options' => $question_options ?? null,
                                    'total_questions' => $total_questions ?? null,
                                ]
                            );
                            }
                        }
                    }
                }
            }
            if(isset($data['is_progress']) && $data['is_progress']==1){
                $this->PracticalAssessmentRepository->saveGeoLocation($data);
                $is_Update=TrainingCourseSubModulePracticalAssessmentAnswersJson::where('training_course_id',$data['training_course_id'])->where('module_id',$data['module_id'])->where('submodule_id',$data['submodule_id'])->where('assessor_id',auth()->user()->id)->value('required');
                if($is_Update==1){
                    $masterUserId = TrainingCourse::whereId($data['training_course_id'])->pluck('master_user_id')->first();
                    if (request()->history && request()->history == 'true') {
                        //calc progress for what question attempted by user where history=true
                        $this->PracticalAssessmentRepository->progressCalculationForHistory($data, auth()->user()->id, $masterUserId);
                    } else {
                        $this->PracticalAssessmentRepository->progressCalculation($data, auth()->user()->id, $masterUserId);
                    }
                    TrainingCourseSubModulePracticalAssessmentAnswersJson::where('training_course_id',$data['training_course_id'])->where('module_id',$data['module_id'])->where('submodule_id',$data['submodule_id'])->where('assessor_id',auth()->user()->id)->update(['required'=>0]);
                }
            }
            $submoduleName = TrainingCourseSubmoduleDetails::whereId($request->submodule_id)->pluck('submodule_name')->first();
            // Submit Practical Assessment Log
            $logsData = [
                'user_id' => auth()->user()->id,
                'email' => auth()->user()->email ?? null,
                'action' => 'Create',
                'action_name' => config('constants.system_logs.submit_practical_assessment'),
                'url' => str_replace(url('/v1/'), '', request()->fullUrl()) ?? null,
                'is_app_operator_admin' => 1,
                'is_practical_assessment' => 1 ,
                'system_section' => 'Practical Assessment',
                'item_id' => $data['submodule_id'] ?? null,
            ];
            custom_system_logs($logsData);
            return response()->json(setResponse($data, ['message' => __($submoduleName . ' has been submitted successfully.Result will be reflected after 2-3 minutes.')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function uploadMediaPracticalAssessment(PracticalAssessmentMediaRequest $request)
    {
        try {
            $data = $request->all();
            $masterUserId = TrainingCourse::whereId($data['training_course_id'])->pluck('master_user_id')->first();
            if (isset($data['user_id']) && $data['media_content'] == 'Main' && strpos($data['user_id'], ',')) {
                $user_ids = explode(',', $data['user_id']);
            } else {
                if (isset($data['user_id'])) {
                    $user_ids[] = $data['user_id'];
                } else {
                    $user_ids = [];
                }
            }
            // $media_data = $answer['media_data'][0];
            if (isset($data['media'])) {
                if ($data['media_type'] === 'image') {
                    $file = \Storage::disk('s3')->put(getTrainingCourseSubmodulePath($data['training_course_id']), $data['media']);
                    $name = explode('/', $file);
                    $data['media'] = $name[count($name) - 1];
                }
                else if ($data['media_type'] === 'document') { 
                    $file = \Storage::disk('s3')->put(getTrainingCourseSubmodulePath($data['training_course_id']), $data['media']);
                    $name = explode('/', $file);
                    $data['media'] = $name[count($name) - 1];
                } else {
                    $video = \Storage::disk('s3')->put(getTrainingCourseSubmoduleVideoPath($data['training_course_id']), $data['media']);
                    $name = explode('/', $video);
                    $uploadedImageName = $name[count($name) - 1];
                    $data['media'] = $uploadedImageName;

                    $videoUrl = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCourseSubmoduleVideoPath($data['training_course_id']), '/') . '/' . $data['media'];
                    $storageUrl = base_path('storage/thumb');
                    if (!file_exists($storageUrl)) {
                        mkdir($storageUrl);
                        chmod($storageUrl, 0777);
                    }

                    $thumb_name = explode('.', $uploadedImageName);
                    $thumb_name = $thumb_name[0] . '.jpg';
                    $imgSizes = config('constants.videoguide.thumbnail');
                    VideoThumbnail::createThumbnail($videoUrl, $storageUrl, $thumb_name, 0, $imgSizes['width'], $imgSizes['height']);

                    chmod(base_path('storage/thumb/' . $thumb_name), 0777);
                    $file = base_path('storage/thumb/' . $thumb_name);

                    $s3 = \Storage::disk('s3')->put(getTrainingCourseSubmoduleThumbnailPath($data['training_course_id']) . '/' . $thumb_name, file_get_contents($file));
                    if (file_exists($file)) {
                        unlink($file);
                    }
                    $media_data['thumbnail'] = $thumb_name;
                }

                // $media_data['media_content'] = 'Main';
                if ($data['media_content'] == 'Main') {
                    foreach ($user_ids as $value) {
                        $record = PracticalAssessmentMedia::updateOrCreate(
                            [
                                // 'answer_id' => $data['answer_id'],
                                'training_course_id' => $data['training_course_id'],
                                'module_id' => $data['module_id'],
                                'submodule_id' => $data['submodule_id'],
                                'user_id' => $value,
                                'assessor_id' => $data['assessor_id'],
                                'question_id' => isset($data['question_id']) ? $data['question_id'] : null,
                            ],
                            [
                                'media_type' => $data['media_type'],
                                'media' => $data['media'],
                                'media_content' => $data['media_content'],
                                'thumbnail' => isset($media_data['thumbnail']) ? $media_data['thumbnail'] : null,
                                'type' => ($data['assessor_id'] == $value) ? 'Self' : 'PA',
                                'tag' => isset($data['tag']) ? $data['tag'] : null,
                            ]
                        );
                        $new_data['id'] = $record->id;
                    }
                } else {
                    $record = new PracticalAssessmentMedia;
                    $record->training_course_id = $data['training_course_id'];
                    $record->module_id = $data['module_id'];
                    $record->submodule_id = $data['submodule_id'];
                    $record->assessor_id = $data['assessor_id'];
                    if (isset($data['user_id'])) {
                        $record->user_id = $data['user_id'];
                    }
                    if (isset($data['question_id'])) {
                        $record->question_id = $data['question_id'];
                    }
                    if (isset($data['comment'])) {
                        $record->comment = $data['comment'];
                    }
                    $record->media_type = $data['media_type'];
                    $record->media = $data['media'];
                    $record->media_content = $data['media_content'];
                    if (isset($media_data['thumbnail'])) {
                        $record->thumbnail = $media_data['thumbnail'];
                    }
                    $record->tag = isset($data['tag']) ? $data['tag'] : null;
                    $record->save();
                }
            }
            if ($data['media_type'] == 'image') {
                $new_data['media'] = env('CDN_URL') . $file;
            }
            else if ($data['media_type'] == 'document') {
                $new_data['media'] = env('CDN_URL') . $file;
            } else {
                $new_data['thumbnail'] = env('CDN_URL') . 'training_course/' . $data['training_course_id'] . '/submodules/thumbnail/' . $media_data['thumbnail'];
                $new_data['media'] = env('CDN_URL') . 'training_course/' . $data['training_course_id'] . '/submodules/video/' . $uploadedImageName;
            }

            $new_data['media_type'] = $data['media_type'];
            if (isset($data['media_content'])) {
                $new_data['media_content'] = $data['media_content'];
            }
            if (isset($data['mimes'])) {
                $new_data['mimes'] = $data['mimes'];
            }
            $new_data['training_course_id'] = intval($data['training_course_id']);
            $new_data['module_id'] = intval($data['module_id']);
            $new_data['submodule_id'] = intval($data['submodule_id']);
            if (isset($data['user_id'])) {
                $new_data['user_id'] = $data['user_id'];
            }
            if (isset($data['comment'])) {
                $new_data['comment'] = $data['comment'];
            }
            $new_data['assessor_id'] = $data['assessor_id'];
            $new_data['tag'] = $data['tag'] ?? null;
            $new_data['id'] = $record->id ?? null;
            if (isset($data['question_id'])) {
                $new_data['question_id'] = intval($data['question_id']);
            }

            // Upload Practical Assessment media logs
            $logsData = [
                'user_id' => auth()->user()->id,
                'email' => auth()->user()->email ?? null,
                'action' => 'Create',
                'action_name' => config('constants.system_logs.upload_practical_assessment_media'),
                'url' => str_replace(url('/v1/'), '', request()->fullUrl()) ?? null,
                'is_app_operator_admin' => 1,
                'is_practical_assessment' => 1 ,
                'system_section' => 'Practical Assessment',
                'item_id' => intval($data['submodule_id']) ?? null,
            ];
            custom_system_logs($logsData);

            return response()->json(setResponse($new_data))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function uploadPracticalAssessmentMediaComment(PracticalAssessmentMediaCommentRequest $request) {
        try {
            $data = $request->all();
            $masterUserId = TrainingCourse::whereId($data['training_course_id'])->pluck('master_user_id')->first();
            if (isset($data['user_id']) && strpos($data['user_id'], ',')) {
                $user_ids = explode(',', $data['user_id']);
            } else {
                if (isset($data['user_id'])) {
                    $user_ids[] = $data['user_id'];
                } else {
                    $user_ids = [];
                }
            }
            if (isset($data['media'])) {
                if ($data['media_type'] === 'image') {
                    $file = \Storage::disk('s3')->put(getTrainingCourseSubmodulePath($data['training_course_id']), $data['media']);
                    $name = explode('/', $file);
                    $data['media'] = $name[count($name) - 1];
                } else {
                    $video = \Storage::disk('s3')->put(getTrainingCourseSubmoduleVideoPath($data['training_course_id']), $data['media']);
                    $name = explode('/', $video);
                    $uploadedImageName = $name[count($name) - 1];
                    $data['media'] = $uploadedImageName;

                    $videoUrl = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCourseSubmoduleVideoPath($data['training_course_id']), '/') . '/' . $data['media'];
                    $storageUrl = base_path('storage/thumb');
                    if (!file_exists($storageUrl)) {
                        mkdir($storageUrl);
                        chmod($storageUrl, 0777);
                    }

                    $thumb_name = explode('.', $uploadedImageName);
                    $thumb_name = $thumb_name[0] . '.jpg';
                    $imgSizes = config('constants.videoguide.thumbnail');
                    VideoThumbnail::createThumbnail($videoUrl, $storageUrl, $thumb_name, 0, $imgSizes['width'], $imgSizes['height']);
                    chmod(base_path('storage/thumb/' . $thumb_name), 0777);
                    $file = base_path('storage/thumb/' . $thumb_name);

                    $s3 = \Storage::disk('s3')->put(getTrainingCourseSubmoduleThumbnailPath($data['training_course_id']) . '/' . $thumb_name, file_get_contents($file));
                    if (file_exists($file)) {
                        unlink($file);
                    }
                    $media_data['thumbnail'] = $thumb_name;
                }
                $mediaCommentData['media'] = $data['media']??null;
                $mediaCommentData['media_type'] = $data['media_type']??'';
            }
            $assessorId = auth()->user()->id;

            $mediaCommentData['training_course_id'] = $data['training_course_id'];
            $mediaCommentData['module_id'] = $data['module_id'];
            $mediaCommentData['submodule_id'] = $data['submodule_id'];
            $mediaCommentData['assessor_id'] = $assessorId;
            $mediaCommentData['question_id'] = $data['question_id'];
            if (isset($data['comment'])) {
                $mediaCommentData['comment'] = $data['comment'];
            }

            $mediaCommentData['media_content'] = '';
            $mediaCommentData['type'] = ($assessorId == $data['user_id']) ? 'Self' : 'PA';
            $mediaCommentData['is_question_comment'] = true;
            if (isset($media_data['thumbnail'])) {
                $mediaCommentData['thumbnail'] = $media_data['thumbnail'];
            }
            foreach ($user_ids as $key => $value) {
                $mediaCommentData['user_id'] = $value;
                $mediaComment = PracticalAssessmentMedia::updateOrCreate(
                    [
                        'training_course_id' => $mediaCommentData['training_course_id'],
                        'module_id' => $mediaCommentData['module_id'],
                        'submodule_id' => $mediaCommentData['submodule_id'],
                        'user_id' => $mediaCommentData['user_id'],
                        'assessor_id' => $assessorId,
                        'question_id' => $mediaCommentData['question_id'],
                    ],
                    $mediaCommentData
                );
                $mediaComment->media_type = ($mediaComment->media == '' ? '' : $mediaComment->media_type);
                $mediaComment->save();
                $new_data[$key]['media']='';
                if (isset($mediaComment->media_type) && $mediaComment->media_type != ''&& $mediaComment->media!='') {
                    if (strtoupper($mediaComment->media_type) == 'IMAGE') {
                    $new_data[$key]['media'] = env('CDN_URL') . 'training_course/' . $mediaComment->training_course_id . '/submodules/' . $mediaComment->media;
                    } else {
                    $new_data[$key]['thumbnail'] = env('CDN_URL') . 'training_course/' . $data['training_course_id'] . '/submodules/thumbnail/' . $mediaComment->thumbnail;
                    $new_data[$key]['media'] = env('CDN_URL') . 'training_course/' . $data['training_course_id'] . '/submodules/video/' . $mediaComment->media;
                    }
                }

                $new_data[$key]['media_type'] = $mediaComment->media_type;
                $new_data[$key]['training_course_id'] = $mediaComment->training_course_id;
                $new_data[$key]['module_id'] = $mediaComment->module_id;
                $new_data[$key]['submodule_id'] = $mediaComment->submodule_id;
                $new_data[$key]['user_id'] = (int)$mediaComment->user_id;
                $new_data[$key]['comment'] = $mediaComment->comment;
                $new_data[$key]['assessor_id'] = $mediaComment->assessor_id;
                $new_data[$key]['question_id'] = $mediaComment->question_id;
                

                // Set PDF null in the case of question media and comment add (For regenerate PDF)
                TrainingCourseSubModulePracticalAssessmentResults::where('training_course_id', $mediaComment->training_course_id)
                                                                ->where('module_id', $mediaComment->module_id)
                                                                ->where('submodule_id', $mediaComment->submodule_id)
                                                                ->where('assessor_id', $mediaComment->assessor_id)
                                                                ->where('user_id', $mediaComment->user_id)
                                                                ->update(['pdf'=>null]);

            }

            // Upload Practical Assessment Media Comment
            $logsData = [
                'user_id' => auth()->user()->id,
                'email' => auth()->user()->email ?? null,
                'action' => 'Create',
                'action_name' => config('constants.system_logs.upload_practical_assessment_media_comment'),
                'url' => str_replace(url('/v1/'), '', request()->fullUrl()) ?? null,
                'is_app_operator_admin' => 1,
                'is_practical_assessment' => 1 ,
                'system_section' => 'Practical Assessment',
                'item_id' => $data['submodule_id'] ?? null,
            ];
            custom_system_logs($logsData);

            return response()->json(setResponse($new_data))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule SCORM
     *
     * @return \Illuminate\Http\Response
     */
    public function getScorm($id)
    {
        try {
            $subModule = $this->model->where('submodule_type_id',config('constants.submodule_types.scorm'))->find($id);
            if($subModule){
                $submoduleProgress = ScormProgress::where(['user_id' => auth()->user()->id, 'submodule_id' => $id])->first();

                $checkTrainingCourse = checkTrainingCourse($subModule);

                if($checkTrainingCourse == 1){
                    return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
                if($checkTrainingCourse == 2){
                    return response()->json(setErrorResponse(__('user.TrainingCourseModule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                if($subModule->status != 'Active'){
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                $subModule->scorm_registration_id = $submoduleProgress->scorm_registration_id ?? null;
                $subModule->submodule_progress = $submoduleProgress->submodule_progress ?? 0;
                return (new TrainingCourseScormResource($subModule));
            }else{
                return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    /**
     * Get Training Course Submodule ThreeSixty Detail
     *
     * @return \Illuminate\Http\Response
     */
    public function submitScorm(SubmitScormRequest $request)
    {
        $progressDataJson = json_encode($request['progressData']);

        if($request['progressData']['registrationCompletion']=='COMPLETED' && $request['progressData']['registrationSuccess']=='PASSED')
        {
            $progress=100;
        }else{
            $progress=0;
        }
        $newUser = ScormProgress::updateOrCreate([
            'user_id'   => auth()->user()->id,
            'master_user_id'   => auth()->user()->user_relation->master_user_id,
            'submodule_id'   => $request['userData']['id'],
            'training_course_id'   => $request['userData']['training_course_id'],
            'module_id'   => $request['userData']['module_id'],
            'scorm_registration_id'   => $request['userData']['scorm_registration_id'],
        ],[
            'scorm_course_id'     => $request['userData']['scorm_course_id'],
            'submodule_progress' => $progress,
            'thumbnail'    => $request['userData']['thumbnail'],
            'scorm_zip_name'   => $request['userData']['scorm_zip_name'],
            'scorm_zip_name_url'       => $request['userData']['scorm_zip_name_url'],
            'progress_data'       => $progressDataJson, // Assign JSON string
        ]);

        $submoduleProgress = TrainingCourseSubmoduleProgress::where('user_id',auth()->user()->id)
                                                                ->where('submodule_id',$request['userData']['id'])
                                                                ->where('module_id',$request['userData']['module_id'])
                                                                ->where('training_course_id',$request['userData']['training_course_id'])->first();

            if(!empty($submoduleProgress)){
                $overRideResult = TrainingCourseSubmoduleProgress::select('is_result_override')->where([
                    'user_id' => auth()->user()->id,
                    'training_course_id' => $request['userData']['training_course_id'],
                    'module_id' => $request['userData']['module_id'],
                    'submodule_id' => $request['userData']['id']
                ])->first();
                if($overRideResult->is_result_override == 'Default'){
                // Submodule Progress
                $submoduleProgress->update(['submodule_progress'=>$progress]);
                // Module progress
                $mData = ['training_course_id' => $submoduleProgress->training_course_id, 'module_id' => $submoduleProgress->module_id, 'user_id' => $submoduleProgress->user_id];
                    (new TrainingCourseModuleProgress)->calculateModuleProgress($mData);
                    // Training course progress
                $tData = ['training_course_id' =>  $submoduleProgress->training_course_id, 'user_id' => $submoduleProgress->user_id];
                    (new TrainingCourseProgress)->calculateTrainingCourseProgress($tData);
                }
            }

        return response()->json(setResponse([], ['message' => __('user.TrainingCourseSubmodule.submitProgress')]))->setStatusCode(Response::HTTP_OK);
    }

    /**
     * Get Training Course Submodule SCORM
     *
     * @return \Illuminate\Http\Response
     */
    public function getMicroLearning($id)
    {
        try {
            $subModule = $this->model->where('submodule_type_id',config('constants.submodule_types.micro_learning'))->find($id);
            if($subModule){
                $touchCount = TrainingCourseSubmoduleProgress::where(['submodule_id' => $id, 'user_id' => auth()->user()->id])->select('touch_count', 'total_touch_count')->first();
                $userTouchCount = (!is_null($touchCount) ? $touchCount->user_touch_count : 0);
                $subModule->user_touch_count = $userTouchCount;

                $checkTrainingCourse = checkTrainingCourse($subModule);

                if($checkTrainingCourse == 1){
                    return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
                if($checkTrainingCourse == 2){
                    return response()->json(setErrorResponse(__('user.TrainingCourseModule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                if($subModule->status != 'Active'){
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                return (new TrainingCourseMicroLearningResource($subModule));
            }else{
                return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Self Assessment SubModule Listing
     *
     * @return \Illuminate\Http\Response
     */
    public function SelfAssessmentSubModuleListing(Request $request)
    {
        try {
            $trainingCourseIds = (new TrainingCourse)->getUserAssignCourseIds();
            if (empty($trainingCourseIds)) {
                return response()->json(setErrorResponse(__('user.TrainingCourse.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
            $query = TrainingCourseSubmoduleDetails::where('submodule_type_id', 19)
                ->whereIn('training_course_id', $trainingCourseIds)
                ->where(['status' => 'Active'])
                ->whereNull('deleted_at');

            // Filter by name if provided
            if ($request->has('name') && !empty($request->name)) {
                $query->where('submodule_name', 'like', '%' . $request->name . '%');
            }

            // Sorting
            $sortOrder = $request->get('sort', 'desc'); // default desc
            if (!in_array(strtolower($sortOrder), ['asc', 'desc'])) {
                $sortOrder = 'desc';
            }
            $query->orderBy('submodule_name', $sortOrder);
            $subModules = $query->paginate(config('constants.mobile_app.per_page'));
                return ($subModules) ? (new CustomCollection($subModules, 'App\Http\Resources\V1\TrainingCourseSubModuleListResource')) :
					response()->json(setErrorResponse(__('user.TrainingCourse.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Cable Matrix Filter Options
     */
    public function getCableMatrixFilters($questionId)
    {
        try {
            $cableMatrixService = new \App\Services\CableMatrixService();
            $options = $cableMatrixService->getFilterOptions($questionId);

            return response()->json(setResponse($options))->setStatusCode(200);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse($e->getMessage()))->setStatusCode(500);
        }
    }

    /**
     * Unified Cable Matrix API
     *
     * Supports multiple result types with flexible filtering and search capabilities
     *
     * Query Parameters:
     * - resultType: diameter|type|description (default: diameter)
     * - diameter: Filter by cable diameter
     * - type: Filter by cable type
     * - search: Free text search across all fields
     * - page: Page number (default: 1)
     * - pageSize: Items per page (default: 20, max: 100)
     * - distinct: Return unique values only (default: true)
     *
     * @param int $questionId
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCableMatrixData($questionId, Request $request)
    {
        try {
            // Validate request parameters
            // $request->validate([
            //     'resultType' => 'nullable|in:diameter,type,description',
            //     'diameter' => 'nullable|string|max:50',
            //     'type' => 'nullable|string|max:100',
            //     'search' => 'nullable|string|min:2|max:255',
            //     'page' => 'nullable|integer|min:1',
            //     'pageSize' => 'nullable|integer|min:1|max:100',
            //     'distinct' => 'nullable|boolean'
            // ]);

            // Extract parameters with defaults
            $resultType = $request->query('resultType', 'diameter');
            $diameter = $request->query('diameter');
            $type = $request->query('type');
            $search = $request->query('search');
            $page = (int) $request->query('page', 1);
            $pageSize = (int) $request->query('pageSize', 20);
            $distinct = $request->query('distinct', true);

            // Convert string boolean to actual boolean
            if (is_string($distinct)) {
                $distinct = filter_var($distinct, FILTER_VALIDATE_BOOLEAN);
            }

            $cableMatrixService = new \App\Services\CableMatrixService();

            // Route to appropriate method based on resultType
            switch ($resultType) {
                case 'diameter':
                    $result = $cableMatrixService->getDiameters($questionId, $search, $distinct);
                    break;

                case 'type':
                    $result = $cableMatrixService->getTypes($questionId, $diameter, $search, $distinct);
                    break;

                case 'description':
                    $result = $cableMatrixService->getDescriptions($questionId, $diameter, $type, $search, $page, $pageSize, $distinct);
                    break;

                default:
                    return response()->json(setErrorResponse('Invalid resultType. Must be: diameter, type, or description'))->setStatusCode(400);
            }

            // Prepare response
            $response = [
                'resultType' => $resultType,
                'filters' => [
                    'diameter' => $diameter,
                    'type' => $type,
                    'search' => $search
                ],
                'pagination' => [
                    'page' => $page,
                    'pageSize' => $pageSize,
                    'distinct' => $distinct
                ],
                'data' => $result
            ];

            return response()->json(setResponse($response))->setStatusCode(200);

        } catch (\Exception $e) {
            return response()->json(setErrorResponse($e->getMessage()))->setStatusCode(500);
        }
    }
}
