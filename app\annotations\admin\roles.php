<?php
/**
     * @OA\Post(
     *     path="/admin/roles",
     *     tags={"Admin - Roles"},
     *     summary="Add Roles",
     *     description="Add Roles",
     *     operationId="store",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="Role Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Role Status",
     *                     type="string",
     *                      enum={"Active", "Inactive"}
     *                 ),
     *                 @OA\Property(
     *                     property="permission",
     *                     description="1=View All Users Data, 0=View Only Assigned Users Data",
     *                     type="string",
                           enum={"1", "0"}
     *                 ),
     *                 example={"name":"Editor",
                      "status":"Active",
                      "permission":"1",
                      "module_permission": {
                            "dashboard": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "courses": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "user_groups": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "users": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "analytics": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "products": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "resources": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "training_course_directory": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "product_directory": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "news": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "news_library": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "feedback": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "reports": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            }
                      }
                    }
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Role Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */ 

/**
     * @OA\Get(
     *     path="/admin/roles/{id}",
     *     tags={"Admin - Roles"},
     *     summary="Show Role Details",
     *     description="Show Role Details",
     *     operationId="show",
     *     @OA\Parameter(
     *         description="Id of role to fetch",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */

/**
     * @OA\Put(
     *     path="/admin/roles/{id}",
     *     tags={"Admin - Roles"},
     *     summary="Update Role details",
     *     description="Update Role details",
     *     operationId="update",
     *     @OA\Parameter(
     *         description="Id of role to update",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
    *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="Role Name",
     *                     type="string"
     *                 ),
    *                 @OA\Property(
     *                     property="status",
     *                     description="Role Status",
     *                     type="string",
                           enum={"Active", "Inactive"}
     *                 ),
     *                 @OA\Property(
     *                     property="permission",
     *                     description="1=View All Users Data, 0=View Only Assigned Users Data",
     *                     type="string",
                           enum={"1", "0"}
     *                 ),
     *                 example={"name":"Editor",
                      "status":"Active",
                      "permission":"1",
                      "module_permission": {
                            "dashboard": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "courses": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "user_groups": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "users": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "analytics": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "products": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "resources": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "training_course_directory": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "product_directory": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "news": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "news_library": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "feedback": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            },
                            "reports": {
                                "view":true,
                                "add":false,
                                "delete":false,
                                "edit":false
                            }
                      }
                    }
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Role Updated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */ 

