<?php

namespace App\Repositories\Operator\v1;

use App\Repositories\Repository;
use App\Models\TrainingCourseProgress;
use App\Models\UserCourseCertificate;
use DBTableNames;
use Illuminate\Database\Eloquent\Builder;

class GenerateReportRepository extends Repository
{
    protected $model;
    protected $table;
    protected $baseRepository;
    
    public function __construct(TrainingCourseProgress $model) {
        $this->model = $model;
        $this->table = $model->getTable();
        $this->baseRepository = new Repository($this->model);
    }
    
    public function getAllCoursesListing($data) {
        $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
        $selectColumns = [
            $this->table . '.id',
            $this->table . '.training_course_id',
            $this->table . '.user_id',
            $this->table . '.course_progress',
            $this->table . '.is_certificate_generated',
            $this->table . '.updated_at',
            $this->table . '.complete_date',
            'users.name as name',
            'users.email as email',
            'training_course.title as training_course_name',
        ];
        $query = TrainingCourseProgress::whereHas('user', function (Builder $query) {
                                                $query->whereNull('deleted_at');
                                            })->whereHas('course', function (Builder $query) {
                                                $query->whereNull('deleted_at');
                                            });
        $query->leftJoin(DBTableNames::USERS, $this->table . '.user_id', '=', 'users.id');
        $query->leftJoin(DBTableNames::TRAINING_COURSE, $this->table . '.training_course_id', '=', 'training_course.id');
        $query->where($this->table .'.master_user_id',$operatorId);      
        if(request()->isExport){
            if(isset(request()->ids) && !empty(request()->ids)){
                $query->whereIn($this->table.'.id', request()->ids);
            }
        }
        $query->select($selectColumns);
        $query->distinct(); // Ensure uniqueness
        $query->groupBy([
            $this->table . '.user_id',
            $this->table . '.training_course_id',
        ]);

        // Searching
        $search = isset($data['search_key']) ? $data['search_key'] : "";
        $searchFields = ['users.name','users.email','training_course.title',$this->table.'.course_progress'];
        if (!empty($search)) {
            $query = $query->where(function($query) use ($searchFields, $search) {
                foreach ($searchFields as $key => $field) {
                    $query->orWhere($field, 'LIKE', '%'.$search.'%');
                }
            });
        }

        /* Advance Filters */
        if (isset($data['filters']) && count($data['filters']) > 0) {
            if (isset($data['filters']['name']) && $data['filters']['name'] != "") {
                $query = $query->where('users.name', 'LIKE', '%' . $data['filters']['name'] . '%');
            }
            if (isset($data['filters']['email']) && $data['filters']['email'] != "") {
                $query = $query->where('users.email', 'LIKE', '%' . $data['filters']['email'] . '%');
            }
            if (isset($data['filters']['training_course_name']) && $data['filters']['training_course_name'] != "") {
                $query = $query->where('training_course.title', 'LIKE', '%' . $data['filters']['training_course_name'] . '%');
            }
            if (isset($data['filters']['course_progress']) && $data['filters']['course_progress'] != "") {
                $query = $query->where($this->table.'.course_progress', $data['filters']['course_progress']);
            }
            if (isset($data['filters']['updated_at']) && $data['filters']['updated_at'] != "") {
                $query = $query->where($this->table.'.course_progress', 100)
                       ->whereDate($this->table.'.updated_at', '=', $data['filters']['updated_at']);
            }
            if (isset($data['filters']['complete_date']) && $data['filters']['complete_date'] != "") {
                $query = $query->where($this->table.'.course_progress', 100)
                       ->whereDate($this->table.'.complete_date', '=', $data['filters']['complete_date']);
            }
        }

        // Sorting
        $sort = $this->table . '.id'; // Default sort by ID
        $sortingKeys = [
            'id' => $this->table .'.id',
            'name' => 'name',
            'email' => 'email',
            'training_course_name' => 'training_course_name',
            'course_progress' => $this->table .'.course_progress',
            'updated_at' => $this->table .'.updated_at',
            'complete_date' => $this->table .'.complete_date',
        ];
        if (isset($data['sort_by']) && $data['sort_by'] != "") {
            if (array_key_exists($data['sort_by'], $sortingKeys)) {
                $sort = $sortingKeys[$data['sort_by']];
            }
        }

        if (isset($data['sort_by']) && $data['sort_by'] != "") {
            if (array_key_exists($data['sort_by'], $sortingKeys)) {
                $sort = $sortingKeys[$data['sort_by']];
            }
        }
        // Ordering (Default DESC)
        $order = isset($data['order_by']) ? $data['order_by'] : 'DESC';
        // Apply Sorting and Ordering
        if (in_array($sort, $sortingKeys)) {
            $query = $query->orderBy($sort, $order);
        }
        return $query;
    }
    /**
     * Export Selected and All Course Progress records in CSV
     */
    public function exportAllCoursesReportCsv($data, $fields = null) {
        $columns = ['name' => 'NAME','email' => 'EMAIL','training_course_name'=> 'TRAINING COURSE','course_progress'=> 'PROGRESS', 'updated_at' => 'COMPLETION DATE'];
        $exportFields = array_flip((empty($fields))? array_keys($columns): $fields);
        $fileName = '../storage/' . time() . '-' . randomString() . '.csv';
        $fp = fopen($fileName, 'w');
        fputcsv($fp, array_intersect_key($columns, $exportFields));
        foreach ($data as $k=>$raw) {
            $raw = (object) $raw;
            $progressData = json_decode($raw->progress_data);
            $output = [
                "name"                  => $raw->name,
                "email"                  => $raw->email,
                "training_course_name"  => $raw->training_course_name,
                "course_progress"       => $raw->course_progress,
                'updated_at'            => ($raw->course_progress == 100) ? date_format($raw->updated_at, 'Y-m-d H:i:s') : '',
            ];
            fputcsv($fp, array_intersect_key($output, $exportFields));
        }
        return response()->download($fileName, time() . '-AllCourseProgressReport.csv', csvHeaders())->deleteFileAfterSend(true);
    }

    /**
     * Export user course certificate PDF
     */
    public function pdfExport() {
        $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
        // Get the training course progress records
        $query = TrainingCourseProgress::query();
        // Check if specific IDs are provided in the requests
        if(isset(request()->ids)){
            $query->whereIn('id',request()->ids);
        }
        // Filter for records where certificate is generated and match the operator ID
        $query->where('is_certificate_generated',1);
        $query->where('master_user_id', $operatorId);
        $progressData = $query->orderBy('id','DESC')->get();
        // Collect certificate names based on training course ID and user ID
        $certificateURLs = [];
        // Count the training course progress records with the generated certificate flag set to 1
        if($progressData->count() > 0){
            foreach ($progressData as $progress) {
                // Fetch the certificates for the current progress record
                $certificates = UserCourseCertificate::where('training_course_id', $progress->training_course_id)
                    ->where('user_id', $progress->user_id) // use the appropriate user ID if different
                    ->where('master_user_id', $progress->master_user_id) // or use the appropriate master user ID
                    ->pluck('certificate_name'); // Adjust 'certificate_name' to your actual column name
                // Generate URLs for each certificate and merge into the array
                foreach ($certificates as $certificateName) {
                    $certificateURL = getUserCourseCertificatePath($certificateName, $progress->user_id, $progress->training_course_id);
                    $certificateURLs[] = $certificateURL; // Add URL to the array
                }
            }
            return $certificateURLs;
        }else{
            return false;
        }
    }

    public function allCoursesExportUploadedEmails($userIds){
        $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
        $selectColumns = [
            $this->table . '.id',
            $this->table . '.training_course_id',
            $this->table . '.user_id',
            $this->table . '.course_progress',
            $this->table . '.is_certificate_generated',
            $this->table . '.updated_at',
            'users.name as name',
            'users.email as email',
            'training_course.title as training_course_name',
        ];
        $query = TrainingCourseProgress::whereHas('user', function (Builder $query) {
                                                $query->whereNull('deleted_at');
                                            })->whereHas('course', function (Builder $query) {
                                                $query->whereNull('deleted_at');
                                            });
        $query->leftJoin(DBTableNames::USERS, $this->table . '.user_id', '=', 'users.id');
        $query->leftJoin(DBTableNames::TRAINING_COURSE, $this->table . '.training_course_id', '=', 'training_course.id');
        $query->where($this->table .'.master_user_id',$operatorId);      
        if(!empty($userIds)){
            $query->whereIn($this->table.'.user_id', $userIds);
        }
        $query->orderBy('id','DESC');
        $query->select($selectColumns);
        return $query;
    }
}
