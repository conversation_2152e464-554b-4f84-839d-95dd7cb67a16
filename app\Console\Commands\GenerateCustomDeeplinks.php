<?php

namespace App\Console\Commands;

use App\Services\DeeplinkService;
use App\Models\TrainingCourse;
use App\Models\Product;
use App\Models\Resource;
use App\Models\TrainingCourseSubmoduleDetails;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GenerateCustomDeeplinks extends Command
{
    protected $signature = 'deeplinks:generate {--type=all} {--operator-id=} {--batch-size=100} {--force}';
    protected $description = 'Generate custom deeplinks for existing content';

    private DeeplinkService $deeplinkService;

    public function __construct(DeeplinkService $deeplinkService)
    {
        parent::__construct();
        $this->deeplinkService = $deeplinkService;
    }

    public function handle()
    {
        $type = $this->option('type');
        $operatorId = $this->option('operator-id');
        $batchSize = (int) $this->option('batch-size');
        $force = $this->option('force');

        $this->info('Generating custom deeplinks...');

        if ($type === 'all' || $type === 'training_courses') {
            $this->generateTrainingCourseDeeplinks($operatorId, $batchSize, $force);
        }

        if ($type === 'all' || $type === 'products') {
            $this->generateProductDeeplinks($operatorId, $batchSize, $force);
        }

        if ($type === 'all' || $type === 'resources') {
            $this->generateResourceDeeplinks($operatorId, $batchSize, $force);
        }

        if ($type === 'all' || $type === 'submodules') {
            $this->generateSubmoduleDeeplinks($operatorId, $batchSize, $force);
        }

        $this->info('Deeplink generation completed!');
        return 0;
    }

    private function generateTrainingCourseDeeplinks($operatorId = null, int $batchSize = 100, bool $force = false)
    {
        $this->info('Generating training course deeplinks...');
        
        $query = TrainingCourse::query();
        
        if ($operatorId) {
            $query->where('master_user_id', $operatorId);
        }

        $totalCourses = $query->count();
        $this->info("Processing {$totalCourses} training courses");

        $bar = $this->output->createProgressBar($totalCourses);
        $generated = 0;
        $skipped = 0;

        $query->chunk($batchSize, function ($courses) use (&$generated, &$skipped, $bar, $force) {
            foreach ($courses as $course) {
                try {
                    // Check if deeplink already exists
                    $existingDeeplink = DB::table('training_course_whitelabel_deeplink')
                        ->where('operator_id', $course->master_user_id)
                        ->where('training_course_id', $course->id)
                        ->first();

                    if ($existingDeeplink && !$force) {
                        $skipped++;
                        $bar->advance();
                        continue;
                    }

                    $targetUrl = url(route('trainingCourse.show', ['id' => $course->id]));
                    
                    $deeplinkUrl = $this->deeplinkService->generateDeeplink([
                        'target_url' => $targetUrl,
                        'operator_id' => $course->master_user_id,
                        'type' => 'training_course',
                        'entity_id' => $course->id,
                        'entity_type' => 'TrainingCourse'
                    ]);

                    // Update or create deeplink record in storage table
                    DB::table('training_course_whitelabel_deeplink')->updateOrInsert(
                        [
                            'operator_id' => $course->master_user_id,
                            'training_course_id' => $course->id
                        ],
                        [
                            'share_url' => $deeplinkUrl,
                            'updated_at' => now(),
                            'created_at' => $existingDeeplink ? $existingDeeplink->created_at : now()
                        ]
                    );

                    $generated++;

                } catch (\Exception $e) {
                    Log::error('Failed to generate training course deeplink', [
                        'course_id' => $course->id,
                        'operator_id' => $course->master_user_id,
                        'error' => $e->getMessage()
                    ]);
                }

                $bar->advance();
            }
        });

        $bar->finish();
        $this->newLine();
        $this->info("Generated: {$generated}, Skipped: {$skipped}");
    }

    private function generateProductDeeplinks($operatorId = null, int $batchSize = 100, bool $force = false)
    {
        $this->info('Generating product deeplinks...');
        
        $query = Product::query();
        
        if ($operatorId) {
            $query->where('master_user_id', $operatorId);
        }

        $totalProducts = $query->count();
        $this->info("Processing {$totalProducts} products");

        $bar = $this->output->createProgressBar($totalProducts);
        $generated = 0;
        $skipped = 0;

        $query->chunk($batchSize, function ($products) use (&$generated, &$skipped, $bar, $force) {
            foreach ($products as $product) {
                try {
                    // Check if deeplink already exists
                    $existingDeeplink = DB::table('products_whitelabel_deeplink')
                        ->where('operator_id', $product->master_user_id)
                        ->where('product_id', $product->id)
                        ->first();

                    if ($existingDeeplink && !$force) {
                        $skipped++;
                        $bar->advance();
                        continue;
                    }

                    $targetUrl = url(route('products.show', ['id' => $product->id]));
                    
                    $deeplinkUrl = $this->deeplinkService->generateDeeplink([
                        'target_url' => $targetUrl,
                        'operator_id' => $product->master_user_id,
                        'type' => 'product',
                        'entity_id' => $product->id,
                        'entity_type' => 'Product'
                    ]);

                    // Update or create deeplink record in storage table
                    DB::table('products_whitelabel_deeplink')->updateOrInsert(
                        [
                            'operator_id' => $product->master_user_id,
                            'product_id' => $product->id
                        ],
                        [
                            'share_url' => $deeplinkUrl,
                            'updated_at' => now(),
                            'created_at' => $existingDeeplink ? $existingDeeplink->created_at : now()
                        ]
                    );

                    $generated++;

                } catch (\Exception $e) {
                    Log::error('Failed to generate product deeplink', [
                        'product_id' => $product->id,
                        'operator_id' => $product->master_user_id,
                        'error' => $e->getMessage()
                    ]);
                }

                $bar->advance();
            }
        });

        $bar->finish();
        $this->newLine();
        $this->info("Generated: {$generated}, Skipped: {$skipped}");
    }

    private function generateResourceDeeplinks($operatorId = null, int $batchSize = 100, bool $force = false)
    {
        $this->info('Generating resource deeplinks...');
        
        $query = Resource::query();
        
        if ($operatorId) {
            $query->where('master_user_id', $operatorId);
        }

        $totalResources = $query->count();
        $this->info("Processing {$totalResources} resources");

        $bar = $this->output->createProgressBar($totalResources);
        $generated = 0;
        $skipped = 0;

        $query->chunk($batchSize, function ($resources) use (&$generated, &$skipped, $bar, $force) {
            foreach ($resources as $resource) {
                try {
                    // Check if deeplink already exists
                    $existingDeeplink = DB::table('resources_whitelabel_deeplink')
                        ->where('operator_id', $resource->master_user_id)
                        ->where('resource_id', $resource->id)
                        ->first();

                    if ($existingDeeplink && !$force) {
                        $skipped++;
                        $bar->advance();
                        continue;
                    }

                    $targetUrl = url(route('resources.show', ['id' => $resource->id]));
                    
                    $deeplinkUrl = $this->deeplinkService->generateDeeplink([
                        'target_url' => $targetUrl,
                        'operator_id' => $resource->master_user_id,
                        'type' => 'resource',
                        'entity_id' => $resource->id,
                        'entity_type' => 'Resource'
                    ]);

                    // Update or create deeplink record in storage table
                    DB::table('resources_whitelabel_deeplink')->updateOrInsert(
                        [
                            'operator_id' => $resource->master_user_id,
                            'resource_id' => $resource->id
                        ],
                        [
                            'share_url' => $deeplinkUrl,
                            'updated_at' => now(),
                            'created_at' => $existingDeeplink ? $existingDeeplink->created_at : now()
                        ]
                    );

                    $generated++;

                } catch (\Exception $e) {
                    Log::error('Failed to generate resource deeplink', [
                        'resource_id' => $resource->id,
                        'operator_id' => $resource->master_user_id,
                        'error' => $e->getMessage()
                    ]);
                }

                $bar->advance();
            }
        });

        $bar->finish();
        $this->newLine();
        $this->info("Generated: {$generated}, Skipped: {$skipped}");
    }

    private function generateSubmoduleDeeplinks($operatorId = null, int $batchSize = 100, bool $force = false)
    {
        $this->info('Generating submodule deeplinks...');
        
        $submoduleTables = [
            'training_course_submodule_details_whitelabel_deeplink' => [
                'route' => 'trainingCourseSubmodule.getUploadVideo',
                'type' => 'training_course_submodule',
                'entity_type' => 'TrainingCourseSubmoduleDetails'
            ]
        ];

        foreach ($submoduleTables as $tableName => $config) {
            $this->generateSubmoduleTypeDeeplinks($tableName, $config, $operatorId, $batchSize, $force);
        }
    }

    private function generateSubmoduleTypeDeeplinks(string $tableName, array $config, $operatorId = null, int $batchSize = 100, bool $force = false)
    {
        $this->info("Generating {$config['type']} deeplinks...");
        
        $query = TrainingCourseSubmoduleDetails::query();
        
        if ($operatorId) {
            $query->whereHas('trainingCourse', function($q) use ($operatorId) {
                $q->where('master_user_id', $operatorId);
            });
        }

        $totalSubmodules = $query->count();
        $this->info("Processing {$totalSubmodules} submodules");

        $bar = $this->output->createProgressBar($totalSubmodules);
        $generated = 0;
        $skipped = 0;

        $query->with('trainingCourse')->chunk($batchSize, function ($submodules) use (&$generated, &$skipped, $bar, $force, $tableName, $config) {
            foreach ($submodules as $submodule) {
                try {
                    $operatorId = $submodule->trainingCourse->master_user_id;

                    // Check if deeplink already exists
                    $existingDeeplink = DB::table($tableName)
                        ->where('operator_id', $operatorId)
                        ->where('training_course_submodule_details_id', $submodule->id)
                        ->first();

                    if ($existingDeeplink && !$force) {
                        $skipped++;
                        $bar->advance();
                        continue;
                    }

                    $targetUrl = url(route($config['route'], ['id' => $submodule->id]));
                    
                    $deeplinkUrl = $this->deeplinkService->generateDeeplink([
                        'target_url' => $targetUrl,
                        'operator_id' => $operatorId,
                        'type' => $config['type'],
                        'entity_id' => $submodule->id,
                        'entity_type' => $config['entity_type']
                    ]);

                    // Update or create deeplink record in storage table
                    DB::table($tableName)->updateOrInsert(
                        [
                            'operator_id' => $operatorId,
                            'training_course_submodule_details_id' => $submodule->id,
                            'training_course_id' => $submodule->training_course_id,
                            'module_id' => $submodule->module_id
                        ],
                        [
                            'share_url' => $deeplinkUrl,
                            'updated_at' => now(),
                            'created_at' => $existingDeeplink ? $existingDeeplink->created_at : now()
                        ]
                    );

                    $generated++;

                } catch (\Exception $e) {
                    Log::error('Failed to generate submodule deeplink', [
                        'submodule_id' => $submodule->id,
                        'training_course_id' => $submodule->training_course_id,
                        'error' => $e->getMessage()
                    ]);
                }

                $bar->advance();
            }
        });

        $bar->finish();
        $this->newLine();
        $this->info("Generated: {$generated}, Skipped: {$skipped}");
    }
}
