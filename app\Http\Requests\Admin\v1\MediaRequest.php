<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;

class MediaRequest extends CustomFormRequest {

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'file' => 'required|mimes:mp4',
            'type' => 'required|in:submodules',
            'media_type' => 'required|in:video'
        ];
    }
    
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'type.in' => __('Type must be submodules'),
            'file.required' => __('File is required'),
            'media_type.required' => __('Media type must be Video'),
        ];
    }

}
