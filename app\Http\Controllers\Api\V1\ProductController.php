<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Jobs\ProductFeedbackNotificationJob;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\Product;
use App\Models\Industry;
use App\Models\ProductType;
use App\Models\ProductFeedback;
use App\Models\ProductFeedbackFiles;
use App\Http\Requests\V1\ProductFeedbackRequest;
use App\Http\Resources\V1\ProductListingResource;
use App\Http\Resources\V1\ProductDetailResource;
use App\Notifications\ProductFeedbackNotification;
use App\Models\MasterUser;

class ProductController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */

    public function __construct() {

    }

    /**
     * Filters
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getFilters() {
        try {
            $user = auth()->user();
            return response()->json(setResponse([
                'types' => ProductType::active()->get(['id', 'name'])->where('master_user_id',$user->user_relation->master_user_id)->toArray(),
                'industries' => Industry::active()->get(['id', 'name'])->where('master_user_id',$user->user_relation->master_user_id)->toArray()
            ]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Products List
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {
        try {
            $user = auth()->user();
            if(isset($request['category_id']) && !empty($request['category_id'])){
            $products = Product::active()->whereProductTypeId($request['category_id'])->where('master_user_id', $user->user_relation->master_user_id);
            }else{
                $products = Product::active()->where('master_user_id', $user->user_relation->master_user_id);
            }
            if (is_array($request->types) && count($request->types) > 0) {
                $products = $products->whereIn('product_type_id', $request->types);
            }
            if (is_array($request->industries) && count($request->industries) > 0) {
                $products = $products->whereIn('industry_id', $request->industries);
            }
            if (in_array($request->sort_by, ['asc', 'desc', 'latest', 'oldest'])) {
                $products = ($request->sort_by === 'asc' || $request->sort_by === 'desc')?
                        $products->orderBy('title', $request->sort_by):
                        $products->{$request->sort_by}();
            }else{
                $products->orderBy('display_order', 'DESC');
            }
            return ProductListingResource::collection($products->paginate(config('constants.mobile_app.per_page')));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Display the specified resource.
     * Product Details
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id) {
        try {
            $product = Product::find($id);
            if($product->status == 'Inactive'){
                return response()->json(setErrorResponse(__('user.Product.unAuthorized')))->setStatusCode(Response::HTTP_OK);
            }
            return ($product)?
                    (new ProductDetailResource($product)):
                    response()->json(setErrorResponse(__('operator.products.not-found')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Store feedback of products.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function feedback(ProductFeedbackRequest $request) {
        try{
            $data = $request->all();

            $feedback = ProductFeedback::create($data);

            // Upload and Store Media Files
            foreach($request->media as $media) {
                $fileURL = \Storage::disk('s3')->put(getProductFeedbackPath(), $media);
                $name = explode('/', $fileURL);
                ProductFeedbackFiles::create([
                    'media' => $name[count($name)-1],
                    'feedback_id' => $feedback->id
                ]);
            }

            dispatch(new ProductFeedbackNotificationJob(auth()->user()->user_relation->manager_email, auth()->user()->user_relation->master_user_id, auth()->user()->email));
            return response()->json(setResponse([], ['message' => 'Feedback added successfully']))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            dd($e);
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    /**
     * Products Type List
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getProductTypes(Request $request) {
        try {
            $perPage = isset($request->per_page) ? $request->per_page : config('constants.mobile_app.per_page');
            $user = auth()->user();
            $productTypes = ProductType::active()->where('master_user_id', $user->user_relation->master_user_id);
            $productTypes->orderBy('display_order', 'ASC');
            return \App\Http\Resources\V1\ProductTypeListingResource::collection($productTypes->paginate($perPage));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    /**
     * Products List By Category/Product Type Id
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getProductsByCategory(Request $request,$id) {
	try {
	    $perPage = isset($request->per_page) ? $request->per_page : config('constants.mobile_app.per_page');
	    $user = auth()->user();
	    $products = Product::active()->whereProductTypeId($id);
	    $products->orderBy('id', 'DESC');
	    return ProductListingResource::collection($products->paginate($perPage));
	} catch (\Exception $e) {
	    return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
	}
    }

}
