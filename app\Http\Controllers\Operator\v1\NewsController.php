<?php

namespace App\Http\Controllers\Operator\v1;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\News;
use App\Repositories\Operator\v1\NewsRepository;
use App\Http\Requests\Operator\v1\NewsRequest;
use App\Http\Requests\Operator\v1\CommonListingRequest;
use App\Http\Requests\Operator\v1\CommonBulkStatusChangeRequest;
use App\Http\Resources\Operator\v1\NewsDetailResource;
use App\Http\Resources\Operator\v1\NewsListingResource;
use App\Http\Resources\Operator\v1\AssignNewsListingResource;

class NewsController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    protected $model;

    protected $repository;
    
    public function __construct() {
        $this->model = new News();
        $this->repository = new NewsRepository($this->model);
    }

    /**
     * @OA\Post(
     *     path="/operator/news/getAssignNewsListing",
     *     tags={"Operator - News Management"},
     *     summary="Listing of news assign to Resources",
     *     description="Listing of news assign to Resources",
     *     operationId="getAssignNewsListing",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 example={"per_page": 10, "page": 1, "search_key": "", "sort_by": "name", "order_by": "asc"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getAssignNewsListing(CommonListingRequest $request) {
        try {
            $news = $this->repository->getListing($request->all());
            return AssignNewsListingResource::collection($news->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * @OA\Post(
     *     path="/operator/news/getListing",
     *     tags={"Operator - News Management"},
     *     summary="List news",
     *     description="List news",
     *     operationId="getListing",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 example={"per_page": 10, "page": 1, "search_key": "", "sort_by": "title", "order_by": "asc", "filters": {"title": "", "updated_at": "", "status": ""}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getListing(CommonListingRequest $request) {
        try {
            $news = $this->repository->getListing($request->all());
            return NewsListingResource::collection($news->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * @OA\Post(
     *     path="/operator/news",
     *     tags={"Operator - News Management"},
     *     summary="Store news",
     *     description="Store news",
     *     operationId="store",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="title",
     *                     description="Title",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="description",
     *                     description="Description",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="primary_image",
     *                     description="Primary Image",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="images",
     *                     description="Images",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="tags",
     *                     description="Tags",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 @OA\Property(
     *                     property="resources",
     *                     description="Resource IDs",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 example={"title": "Box Connection 20", "description": "Description of News", "primary_image": "filename.jpg", "images": {"image1.jpg", "image2.jpg"}, "tags": {"tag1", "tag2"}, "status": "Active", "resources": {1, 2}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function store(NewsRequest $request) {   
        try {
            $news = $this->repository->create($request->all());
            return (new NewsDetailResource($news));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * @OA\Get(
     *     path="/operator/news/{id}",
     *     tags={"Operator - News Management"},
     *     summary="Get news",
     *     description="Get news",
     *     operationId="show",
     *     @OA\Parameter(
     *         description="Id of news to fetch",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function show($id) {
        try {
            $news = $this->model->find($id);
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $newsList = $this->model->where('master_user_id', $operatorId)->pluck('id')->toArray();
            if(in_array($id, $newsList)){
                return ($news) ?
                    (new NewsDetailResource($news)) :
                    response()->json(setErrorResponse(__('operator.news.not-found')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }else{
                return response()->json(setErrorResponse(__('You cannot access this record')))->setStatusCode(Response::HTTP_NOT_FOUND); 
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * @OA\Put(
     *     path="/operator/news/{id}",
     *     tags={"Operator - News Management"},
     *     summary="Update news",
     *     description="Update news",
     *     operationId="update",
     *     @OA\Parameter(
     *         description="Id of news to update",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="title",
     *                     description="Title",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="description",
     *                     description="Description",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="primary_image",
     *                     description="Primary Image",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="images",
     *                     description="Images",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="tags",
     *                     description="Tags",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 @OA\Property(
     *                     property="resources",
     *                     description="Resource IDs",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 example={"title": "Box Connection 20", "description": "Description of News", "primary_image": "filename.jpg", "images": {"image1.jpg", "image2.jpg"}, "tags": {"tag1", "tag2"}, "status": "Active", "resources": {1, 2}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function update(NewsRequest $request, $id) {   
        try {
            $news = $this->model->find($id);
            if ($news) {
                $this->repository->update($request->all(), $news);
                return response()->json(setResponse([], ['message' => __('operator.news.updated')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setErrorResponse(__('operator.news.not-found')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * @OA\Post(
     *     path="/operator/news/delete",
     *     tags={"Operator - News Management"},
     *     summary="Delete news",
     *     description="Delete news",
     *     operationId="destroy",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="ids",
     *                     description="Ids of Resources",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 example={"ids": {1, 2}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function destroy(Request $request) {
        try {
            $ids = is_array($request->ids)? $request->ids: [];
            $this->model->destroy($ids);
            return response()->json(setResponse([], ['message' => __('operator.news.deleted')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * @OA\Post(
     *     path="/operator/news/changeStatus",
     *     tags={"Operator - News Management"},
     *     summary="Change news status",
     *     description="Change news status",
     *     operationId="changeStatus",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="ids",
     *                     description="ids",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={"ids": {"1","2"}, "status": "Active"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function changeStatus(CommonBulkStatusChangeRequest $request) {
        try {
            $this->model->whereIn('id', $request->ids)->update(['status' => $request->status]);
            if($request->status == 'Active'){
                foreach($request->ids as $id){
                    $news = News::find($id);
                    if($news->status == 'Active' && $news->notify_user == 1){
                        News::SendNewsNotifications($news);
                    }
                }
            }
            return response()->json(setResponse([], ['message' => __('operator.news.status-changed')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
