<?php

namespace App\Http\Resources\V1;

use App\Models\TrainingCourse;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\TrainingCourseSubModulePracticalAssessmentAnswer;
use App\Models\TrainingCourseSubModulePracticalAssessmentResults;

class TrainingCourseSubModuleListResource extends JsonResource
{

    public function toArray($request)
    {
        $trainingCourseName = TrainingCourse::where('id', $this->training_course_id)->value('title');
        $user = auth()->user();
        $userId = $user ? $user->id : null;
        $masterUserId = $user && isset($user->user_relation) ? $user->user_relation->master_user_id : null;
        $UserCount = TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $this->id)->where('assessor_id',$userId)->where('user_id',$userId)->count('id');
        $result = TrainingCourseSubModulePracticalAssessmentResults::where('submodule_id', $this->id)
            ->where('user_id', $userId)
            ->where('master_user_id', $masterUserId)
            ->where('type', 'Self')
            ->first();
        if($UserCount>0){
        if ($result && isset($result->is_pass) && ($result->is_pass == 0 || $result->is_pass == 1)) {
            $status = $result->is_pass;
        } else {
        $status = 2;
        }
        }else {
            $status = 3;
        }
        
        $LastActivity = TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $this->id)
        ->where('assessor_id',$userId)
        ->where('user_id',$userId)
        ->where('type', 'Self')
        ->orderBy('id', 'desc')
        ->first();
        return [
            "id" => $this->id,
            "training_course_id" => $this->training_course_id,
            "training_course_name" => $trainingCourseName,
            "module_id" => $this->module_id,
            "submodule_name" => $this->submodule_name,
            "thumbnail" => $this->thumbnail_url ?? null,
            "file" => $this->file,
            "description" => $this->description ?? 0,
            "image_direction_type" => $this->image_direction_type,
            "submodule_lock" => $this->submodule_lock,
            "submodule_complete" => $this->submodule_complete,
            "duration" => $this->duration,
            "duration_type" => $this->duration_type,
            "unlock_datetime" => $this->unlock_datetime,
            "enable_time_spend" => $this->enable_time_spend,
            "completion_percentage" => $this->completion_percentage,
            "condition" => $this->condition,
            "time_spent" => $this->time_spent,
            "time_spent_type" => $this->time_spent_type,
            "is_retake_all_question" => $this->is_retake_all_question,
            "disable_result_pdf" => $this->disable_result_pdf,
            "how_assessment_summary" => $this->how_assessment_summary,
            "disable_user_device" => $this->disable_user_device,
            "allow_optional_media" => $this->allow_optional_media,
            "share_url" => $this->share_url,
            "allow_sa_override" => $this->allow_sa_override,
            "allow_sa_fail" => $this->allow_sa_fail,
            "restrict_gallery" => $this->restrict_gallery,
            "allow_offline_sa_submission" => $this->allow_offline_sa_submission,
            "result" => $status,
            "assessment_result" => isset($result) && $result ? $result->status : null,
            "last_activity" => $LastActivity && !empty($LastActivity->updated_at) ? date_format($LastActivity->updated_at, 'Y-m-d H:i:s') : null,
        ];
    }
}
