I'll reanalyze the codebase more thoroughly and create a comprehensive technical plan for implementing the Cable Matrix selection feature.

Now let me check the routes to understand the current API structure.

Now let me check the current implementation status by looking at recent migrations and models.

Based on my comprehensive analysis of the codebase and Jira ticket SBD-1648, I can now create a detailed technical implementation plan.

# Technical Implementation Plan: SBD-1648 - Cable Matrix Selection Feature

## Executive Summary

This document outlines the complete technical implementation strategy for the Cable Matrix selection feature in the self-assessment module. The feature enables hierarchical filtering of large datasets (1800+ records) through CSV uploads with two-level dropdown filtering and search functionality.

## Current Implementation Status

**✅ Already Implemented:**
- Database schema updates (migrations completed)
- Question type enums updated (`cableMatrix`, `locationSelection`)
- File storage fields added (`file_name`, `file_path`)
- Offline submission toggle (`allow_offline_sa_submission`)

**🔄 Partially Implemented:**
- Basic model constants updated
- Sample CSV file exists

**❌ Not Yet Implemented:**
- API endpoints for CSV processing
- Hierarchical filtering logic
- Frontend integration
- Mobile app support

---

## 1. Database Schema Implementation

### 1.1 Required New Table: `cable_matrix_data`

```sql
CREATE TABLE `cable_matrix_data` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `question_id` bigint(20) UNSIGNED NOT NULL,
  `master_user_id` bigint(20) UNSIGNED NOT NULL,
  `filter_column_1` varchar(255) NOT NULL COMMENT 'First filter column (e.g., Cable Diameter)',
  `filter_column_2` varchar(255) NOT NULL COMMENT 'Second filter column (e.g., Cable Type)',
  `selection_column` varchar(255) NOT NULL COMMENT 'Final selection column (e.g., Cable Description)',
  `filter_value_1` varchar(255) NOT NULL,
  `filter_value_2` varchar(255) NOT NULL,
  `selection_value` text NOT NULL,
  `additional_data` json NULL COMMENT 'Store additional CSV columns',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `cable_matrix_data_question_id_foreign` (`question_id`),
  KEY `cable_matrix_data_master_user_id_foreign` (`master_user_id`),
  KEY `idx_filter_values` (`filter_value_1`, `filter_value_2`),
  CONSTRAINT `cable_matrix_data_question_id_foreign` FOREIGN KEY (`question_id`) REFERENCES `training_course_submodule_practical_assessment_questions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `cable_matrix_data_master_user_id_foreign` FOREIGN KEY (`master_user_id`) REFERENCES `master_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

**Migration File:** `database/migrations/2025_07_30_100000_create_cable_matrix_data_table.php`

### 1.2 Update Practical Assessment Answers Table

```sql
ALTER TABLE `training_course_submodule_practical_assessment_answers` 
ADD COLUMN `cable_matrix_selection` json NULL COMMENT 'Store cable matrix selection data' 
AFTER `question_options`;
```

**Migration File:** `database/migrations/2025_07_30_100001_add_cable_matrix_selection_to_practical_assessment_answers.php`

---

## 2. Model Implementation

### 2.1 New Model: `CableMatrixData`

**File:** `app/Models/CableMatrixData.php`

```php
<?php

namespace App\Models;

use DBTableNames;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CableMatrixData extends Model
{
    use SoftDeletes;

    protected $table = 'cable_matrix_data';

    protected $fillable = [
        'question_id', 'master_user_id', 'filter_column_1', 'filter_column_2', 
        'selection_column', 'filter_value_1', 'filter_value_2', 'selection_value', 
        'additional_data'
    ];

    protected $casts = [
        'additional_data' => 'array'
    ];

    // Relations
    public function question()
    {
        return $this->belongsTo(TrainingCourseSubModulePracticalAssessmentQuestion::class, 'question_id', 'id');
    }

    public function masterUser()
    {
        return $this->belongsTo(MasterUser::class, 'master_user_id', 'id');
    }

    // Scopes
    public function scopeByQuestion($query, $questionId)
    {
        return $query->where('question_id', $questionId);
    }

    public function scopeByFilters($query, $filter1 = null, $filter2 = null)
    {
        if ($filter1) {
            $query->where('filter_value_1', $filter1);
        }
        if ($filter2) {
            $query->where('filter_value_2', $filter2);
        }
        return $query;
    }
}
```

### 2.2 Update Existing Model

**File:** `app/Models/TrainingCourseSubModulePracticalAssessmentQuestion.php`

```php
// Add to existing model
public function cableMatrixData()
{
    return $this->hasMany(CableMatrixData::class, 'question_id', 'id');
}

public function getCsvFilePath()
{
    if ($this->file_path) {
        return env('CDN_URL') . $this->file_path;
    }
    return null;
}
```

---

## 3. API Routes Implementation

### 3.1 Operator Panel Routes

**File:** `routes/operator.php`

```php
// Add to existing practical assessment routes
$router->group(['prefix' => 'practicalAssessment/cableMatrix'], function () use ($router) {
    $router->post('/uploadCsv', 'TrainingCourseSubmodulePracticalAssessmentController@uploadCableMatrixCsv');
    $router->get('/downloadSample', 'TrainingCourseSubmodulePracticalAssessmentController@downloadCableMatrixSample');
    $router->post('/validateCsv', 'TrainingCourseSubmodulePracticalAssessmentController@validateCableMatrixCsv');
    $router->delete('/clearData/{questionId}', 'TrainingCourseSubmodulePracticalAssessmentController@clearCableMatrixData');
});
```

### 3.2 Mobile API Routes

**File:** `routes/api.php`

```php
// Add to existing training course submodule routes
$router->group(['prefix' => 'trainingCourseSubmodule/cableMatrix'], function () use ($router) {
    $router->get('/filters/{questionId}', ['middleware' => ['replica.db'], 'uses' => 'TrainingCourseSubModuleController@getCableMatrixFilters']);
    $router->get('/options/{questionId}', ['middleware' => ['replica.db'], 'uses' => 'TrainingCourseSubModuleController@getCableMatrixOptions']);
    $router->post('/search/{questionId}', ['middleware' => ['replica.db'], 'uses' => 'TrainingCourseSubModuleController@searchCableMatrix']);
});
```

---

## 4. Controller Implementation

### 4.1 Operator Controller Methods

**File:** `app/Http/Controllers/Operator/v1/TrainingCourseSubmodulePracticalAssessmentController.php`

```php
/**
 * Upload Cable Matrix CSV
 */
public function uploadCableMatrixCsv(CableMatrixCsvUploadRequest $request)
{
    try {
        $questionId = $request->question_id;
        $question = TrainingCourseSubModulePracticalAssessmentQuestion::findOrFail($questionId);
        
        // Validate question type
        if ($question->question_type !== 'cableMatrix') {
            return response()->json(setErrorResponse('Invalid question type for cable matrix upload'))->setStatusCode(422);
        }

        // Process CSV upload
        $result = $this->cableMatrixService->processCsvUpload($request, $question);
        
        if ($result['success']) {
            return response()->json(setResponse($result['data'], ['message' => 'CSV uploaded successfully']))->setStatusCode(200);
        } else {
            return response()->json(setErrorResponse($result['message'], $result['errors']))->setStatusCode(422);
        }
    } catch (\Exception $e) {
        return response()->json(setErrorResponse($e->getMessage()))->setStatusCode(500);
    }
}

/**
 * Download Sample CSV
 */
public function downloadCableMatrixSample()
{
    try {
        $samplePath = public_path('images/Sample_Cable_Matrix.csv');
        return response()->download($samplePath, 'Cable_Matrix_Sample.csv');
    } catch (\Exception $e) {
        return response()->json(setErrorResponse('Sample file not found'))->setStatusCode(404);
    }
}

/**
 * Validate CSV before upload
 */
public function validateCableMatrixCsv(CableMatrixCsvValidationRequest $request)
{
    try {
        $result = $this->cableMatrixService->validateCsv($request);
        return response()->json(setResponse($result))->setStatusCode(200);
    } catch (\Exception $e) {
        return response()->json(setErrorResponse($e->getMessage()))->setStatusCode(500);
    }
}

/**
 * Clear Cable Matrix Data
 */
public function clearCableMatrixData($questionId)
{
    try {
        $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
        
        CableMatrixData::where('question_id', $questionId)
            ->where('master_user_id', $operatorId)
            ->delete();
            
        // Clear file references
        $question = TrainingCourseSubModulePracticalAssessmentQuestion::findOrFail($questionId);
        $question->update(['file_name' => null, 'file_path' => null]);
        
        return response()->json(setResponse([], ['message' => 'Cable matrix data cleared successfully']))->setStatusCode(200);
    } catch (\Exception $e) {
        return response()->json(setErrorResponse($e->getMessage()))->setStatusCode(500);
    }
}
```

### 4.2 Mobile API Controller Methods

**File:** `app/Http/Controllers/Api/V1/TrainingCourseSubModuleController.php`

```php
/**
 * Get Cable Matrix Filter Options
 */
public function getCableMatrixFilters($questionId)
{
    try {
        $question = TrainingCourseSubModulePracticalAssessmentQuestion::findOrFail($questionId);
        $masterUserId = $question->subModule->trainingCourse->master_user_id;
        
        $filters = CableMatrixData::byQuestion($questionId)
            ->where('master_user_id', $masterUserId)
            ->select('filter_column_1', 'filter_column_2', 'selection_column')
            ->distinct()
            ->first();
            
        $filter1Options = CableMatrixData::byQuestion($questionId)
            ->where('master_user_id', $masterUserId)
            ->select('filter_value_1')
            ->distinct()
            ->pluck('filter_value_1')
            ->filter()
            ->values();
            
        return response()->json(setResponse([
            'filter_columns' => $filters,
            'filter_1_options' => $filter1Options
        ]))->setStatusCode(200);
    } catch (\Exception $e) {
        return response()->json(setErrorResponse($e->getMessage()))->setStatusCode(500);
    }
}

/**
 * Get Cable Matrix Options based on filters
 */
public function getCableMatrixOptions(CableMatrixOptionsRequest $request, $questionId)
{
    try {
        $question = TrainingCourseSubModulePracticalAssessmentQuestion::findOrFail($questionId);
        $masterUserId = $question->subModule->trainingCourse->master_user_id;
        
        $query = CableMatrixData::byQuestion($questionId)
            ->where('master_user_id', $masterUserId);
            
        if ($request->filter_1) {
            $query->where('filter_value_1', $request->filter_1);
            
            // Get filter 2 options
            $filter2Options = $query->select('filter_value_2')
                ->distinct()
                ->pluck('filter_value_2')
                ->filter()
                ->values();
                
            if ($request->filter_2) {
                $query->where('filter_value_2', $request->filter_2);
                
                // Get final selection options
                $selectionOptions = $query->select('selection_value', 'additional_data')
                    ->get()
                    ->map(function ($item) {
                        return [
                            'value' => $item->selection_value,
                            'additional_data' => $item->additional_data
                        ];
                    });
                    
                return response()->json(setResponse([
                    'selection_options' => $selectionOptions
                ]))->setStatusCode(200);
            }
            
            return response()->json(setResponse([
                'filter_2_options' => $filter2Options
            ]))->setStatusCode(200);
        }
        
        return response()->json(setErrorResponse('Filter 1 is required'))->setStatusCode(422);
    } catch (\Exception $e) {
        return response()->json(setErrorResponse($e->getMessage()))->setStatusCode(500);
    }
}

/**
 * Search Cable Matrix Data
 */
public function searchCableMatrix(CableMatrixSearchRequest $request, $questionId)
{
    try {
        $question = TrainingCourseSubModulePracticalAssessmentQuestion::findOrFail($questionId);
        $masterUserId = $question->subModule->trainingCourse->master_user_id;
        $searchTerm = $request->search_term;
        
        $results = CableMatrixData::byQuestion($questionId)
            ->where('master_user_id', $masterUserId)
            ->where(function ($query) use ($searchTerm) {
                $query->where('filter_value_1', 'LIKE', "%{$searchTerm}%")
                      ->orWhere('filter_value_2', 'LIKE', "%{$searchTerm}%")
                      ->orWhere('selection_value', 'LIKE', "%{$searchTerm}%")
                      ->orWhereRaw("JSON_SEARCH(additional_data, 'all', ?) IS NOT NULL", ["%{$searchTerm}%"]);
            })
            ->select('filter_value_1', 'filter_value_2', 'selection_value', 'additional_data')
            ->limit(50)
            ->get();
            
        return response()->json(setResponse([
            'search_results' => $results
        ]))->setStatusCode(200);
    } catch (\Exception $e) {
        return response()->json(setErrorResponse($e->getMessage()))->setStatusCode(500);
    }
}
```

---

## 5. Service Implementation

### 5.1 Cable Matrix Service

**File:** `app/Services/CableMatrixService.php`

```php
<?php

namespace App\Services;

use App\Models\CableMatrixData;
use App\Models\TrainingCourseSubModulePracticalAssessmentQuestion;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

class CableMatrixService
{
    /**
     * Process CSV Upload
     */
    public function processCsvUpload($request, $question)
    {
        try {
            DB::beginTransaction();
            
            // Clear existing data
            CableMatrixData::where('question_id', $question->id)->delete();
            
            // Upload file to S3
            $file = $request->file('csv_file');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = 'cable_matrix/' . $question->submodule_id . '/' . $fileName;
            
            Storage::disk('s3')->put($filePath, file_get_contents($file));
            
            // Update question with file info
            $question->update([
                'file_name' => $fileName,
                'file_path' => $filePath
            ]);
            
            // Process CSV data
            $csvData = $this->parseCsv($file);
            $result = $this->storeCsvData($csvData, $question, $request);
            
            DB::commit();
            
            return [
                'success' => true,
                'data' => [
                    'total_rows' => $result['total_rows'],
                    'processed_rows' => $result['processed_rows'],
                    'filter_columns' => $result['filter_columns']
                ]
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Parse CSV File
     */
    private function parseCsv($file)
    {
        $csvData = [];
        $handle = fopen($file->getPathname(), 'r');
        
        // Get headers
        $headers = fgetcsv($handle);
        
        // Process rows
        while (($row = fgetcsv($handle)) !== false) {
            if (count($row) === count($headers)) {
                $csvData[] = array_combine($headers, $row);
            }
        }
        
        fclose($handle);
        return $csvData;
    }
    
    /**
     * Store CSV Data
     */
    private function storeCsvData($csvData, $question, $request)
    {
        $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
        $filterColumn1 = $request->filter_column_1;
        $filterColumn2 = $request->filter_column_2;
        $selectionColumn = $request->selection_column;
        
        $processedRows = 0;
        
        foreach ($csvData as $row) {
            if (isset($row[$filterColumn1]) && isset($row[$filterColumn2]) && isset($row[$selectionColumn])) {
                // Prepare additional data (exclude filter and selection columns)
                $additionalData = array_diff_key($row, array_flip([$filterColumn1, $filterColumn2, $selectionColumn]));
                
                CableMatrixData::create([
                    'question_id' => $question->id,
                    'master_user_id' => $operatorId,
                    'filter_column_1' => $filterColumn1,
                    'filter_column_2' => $filterColumn2,
                    'selection_column' => $selectionColumn,
                    'filter_value_1' => $row[$filterColumn1],
                    'filter_value_2' => $row[$filterColumn2],
                    'selection_value' => $row[$selectionColumn],
                    'additional_data' => $additionalData
                ]);
                
                $processedRows++;
            }
        }
        
        return [
            'total_rows' => count($csvData),
            'processed_rows' => $processedRows,
            'filter_columns' => [
                'filter_1' => $filterColumn1,
                'filter_2' => $filterColumn2,
                'selection' => $selectionColumn
            ]
        ];
    }
    
    /**
     * Validate CSV
     */
    public function validateCsv($request)
    {
        $file = $request->file('csv_file');
        $handle = fopen($file->getPathname(), 'r');
        
        // Get headers
        $headers = fgetcsv($handle);
        fclose($handle);
        
        return [
            'columns' => $headers,
            'total_columns' => count($headers),
            'suggested_filters' => $this->suggestFilterColumns($headers)
        ];
    }
    
    /**
     * Suggest Filter Columns
     */
    private function suggestFilterColumns($headers)
    {
        $suggestions = [];
        
        foreach ($headers as $header) {
            $lowerHeader = strtolower($header);
            
            if (strpos($lowerHeader, 'diameter') !== false || strpos($lowerHeader, 'size') !== false) {
                $suggestions['filter_1'] = $header;
            } elseif (strpos($lowerHeader, 'type') !== false || strpos($lowerHeader, 'category') !== false) {
                $suggestions['filter_2'] = $header;
            } elseif (strpos($lowerHeader, 'description') !== false || strpos($lowerHeader, 'name') !== false) {
                $suggestions['selection'] = $header;
            }
        }
        
        return $suggestions;
    }
}
```

---

## 6. Request Validation

### 6.1 CSV Upload Request

**File:** `app/Http/Requests/Operator/v1/CableMatrixCsvUploadRequest.php`

```php
<?php

namespace App\Http\Requests\Operator\v1;

use App\Http\Requests\CustomFormRequest;

class CableMatrixCsvUploadRequest extends CustomFormRequest
{
    public function rules(): array
    {
        return [
            'question_id' => 'required|exists:training_course_submodule_practical_assessment_questions,id',
            'csv_file' => 'required|file|mimes:csv,txt|max:10240', // 10MB max
            'filter_column_1' => 'required|string|max:255',
            'filter_column_2' => 'required|string|max:255',
            'selection_column' => 'required|string|max:255',
        ];
    }

    public function messages(): array
    {
        return [
            'question_id.required' => 'Question ID is required.',
            'question_id.exists' => 'Invalid question ID.',
            'csv_file.required' => 'CSV file is required.',
            'csv_file.mimes' => 'Only CSV files are allowed.',
            'csv_file.max' => 'File size cannot exceed 10MB.',
            'filter_column_1.required' => 'First filter column is required.',
            'filter_column_2.required' => 'Second filter column is required.',
            'selection_column.required' => 'Selection column is required.',
        ];
    }
}
```

### 6.2 Mobile API Requests

**File:** `app/Http/Requests/V1/CableMatrixOptionsRequest.php`

```php
<?php

namespace App\Http\Requests\V1;

use App\Http\Requests\CustomFormRequest;

class CableMatrixOptionsRequest extends CustomFormRequest
{
    public function rules(): array
    {
        return [
            'filter_1' => 'nullable|string|max:255',
            'filter_2' => 'nullable|string|max:255',
        ];
    }
}
```

**File:** `app/Http/Requests/V1/CableMatrixSearchRequest.php`

```php
<?php

namespace App\Http\Requests\V1;

use App\Http\Requests\CustomFormRequest;

class CableMatrixSearchRequest extends CustomFormRequest
{
    public function rules(): array
    {
        return [
            'search_term' => 'required|string|min:2|max:255',
        ];
    }

    public function messages(): array
    {
        return [
            'search_term.required' => 'Search term is required.',
            'search_term.min' => 'Search term must be at least 2 characters.',
        ];
    }
}
```

---

## 7. Frontend Integration Points

### 7.1 Operator Panel Integration

**Required Changes in Question Creation Form:**

```javascript
// Add to question type selection
if (questionType === 'cableMatrix') {
    // Show CSV upload section
    showCsvUploadSection();
    
    // Add validation for offline submission
    if (allowOfflineSubmission) {
        showError('Cable Matrix questions cannot be used with offline submission');
        disableQuestionType('cableMatrix');
    }
}

// CSV Upload Handler
function handleCsvUpload() {
    const formData = new FormData();
    formData.append('csv_file', csvFile);
    formData.append('question_id', questionId);
    formData.append('filter_column_1', selectedFilterColumn1);
    formData.append('filter_column_2', selectedFilterColumn2);
    formData.append('selection_column', selectedSelectionColumn);
    
    fetch('/operator/practicalAssessment/cableMatrix/uploadCsv', {
        method: 'POST',
        body: formData
    }).then(response => response.json())
      .then(data => handleUploadResponse(data));
}
```

### 7.2 Mobile App Integration

**Required API Calls:**

```javascript
// Get initial filter options
async function getCableMatrixFilters(questionId) {
    const response = await fetch(`/api/trainingCourseSubmodule/cableMatrix/filters/${questionId}`);
    return response.json();
}

// Get hierarchical options
async function getCableMatrixOptions(questionId, filter1, filter2) {
    const params = new URLSearchParams();
    if (filter1) params.append('filter_1', filter1);
    if (filter2) params.append('filter_2', filter2);
    
    const response = await fetch(`/api/trainingCourseSubmodule/cableMatrix/options/${questionId}?${params}`);
    return response.json();
}

// Search functionality
async function searchCableMatrix(questionId, searchTerm) {
    const response = await fetch(`/api/trainingCourseSubmodule/cableMatrix/search/${questionId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ search_term: searchTerm })
    });
    return response.json();
}
```

---

## 8. Testing Strategy

### 8.1 Unit Tests

**File:** `tests/Unit/CableMatrixServiceTest.php`

```php
<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\CableMatrixService;
use App\Models\CableMatrixData;
use Illuminate\Http\UploadedFile;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CableMatrixServiceTest extends TestCase
{
    use RefreshDatabase;

    public function test_csv_upload_processing()
    {
        // Create test CSV content
        $csvContent = "Cable Diameter,Cable Type,Cable Description\n25mm,Heavy,Test Cable 1\n30mm,Light,Test Cable 2";
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);
        
        $service = new CableMatrixService();
        // Add test implementation
    }

    public function test_hierarchical_filtering()
    {
        // Test filter logic
    }

    public function test_search_functionality()
    {
        // Test search across all columns
    }
}
```

### 8.2 Feature Tests

**File:** `tests/Feature/CableMatrixApiTest.php`

```php
<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\CableMatrixData;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CableMatrixApiTest extends TestCase
{
    use RefreshDatabase;

    public function test_get_cable_matrix_filters()
    {
        // Test API endpoint
    }

    public function test_hierarchical_options_retrieval()
    {
        // Test filter-based options
    }

    public function test_search_functionality()
    {
        // Test search API
    }
}
```

---

## 9. Performance Optimization

### 9.1 Database Indexing

```sql
-- Add indexes for performance
CREATE INDEX idx_cable_matrix_question_filters ON cable_matrix_data(question_id, filter_value_1, filter_value_2);
CREATE INDEX idx_cable_matrix_search ON cable_matrix_data(filter_value_1, filter_value_2, selection_value);
CREATE FULLTEXT INDEX idx_cable_matrix_fulltext ON cable_matrix_data(filter_value_1, filter_value_2, selection_value);
```

### 9.2 Caching Strategy

```php
// Add to CableMatrixService
public function getCachedFilters($questionId)
{
    return Cache::remember("cable_matrix_filters_{$questionId}", 3600, function () use ($questionId) {
        return $this->getFilters($questionId);
    });
}
```

---

## 10. Deployment Checklist

### 10.1 Pre-deployment

- [ ] Run database migrations
- [ ] Update model constants
- [ ] Deploy service classes
- [ ] Update API routes
- [ ] Test CSV upload functionality
- [ ] Verify offline submission toggle

### 10.2 Post-deployment

- [ ] Test hierarchical filtering
- [ ] Verify search functionality
- [ ] Check mobile app integration
- [ ] Monitor performance metrics
- [ ] Validate data integrity

---

## 11. Monitoring and Maintenance

### 11.1 Key Metrics

- CSV upload success rate
- API response times for filtering
- Search query performance
- Mobile app usage analytics

### 11.2 Error Handling

- CSV parsing errors
- Large dataset performance
- Network timeout handling
- Data validation failures

---

## 12. Security Considerations

### 12.1 File Upload Security

- Validate CSV file format
- Limit file size (10MB max)
- Scan for malicious content
- Store files in secure S3 bucket

### 12.2 Data Access Control

- Ensure operator-level data isolation
- Validate question ownership
- Implement proper authentication
- Log all data access attempts

---

This comprehensive technical plan provides a complete roadmap for implementing the Cable Matrix selection feature. The implementation follows existing codebase patterns while introducing the necessary new functionality for hierarchical filtering and CSV data management.
