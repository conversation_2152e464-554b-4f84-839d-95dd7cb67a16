<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\News;
use App\Models\Product;
use App\Models\Resources;
use App\Models\TrainingCourse;
use App\Http\Resources\V1\SearchProductResource;
use App\Http\Resources\V1\SearchNewsResource;
use App\Http\Resources\V1\SearchResourceResource;
use App\Http\Resources\V1\SearchTrainingCourseResource;

class SearchController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    
    public function __construct() {
        
    }
    
    /**
     * Search among Products, News, Resources and Training Course.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {
        try {
            $user = auth()->user();
            $userRelation = $user->user_relation;
            $filterBy = ["products", "news", "resources", "training"];
            if (is_array($request->filter_by) && count($request->filter_by) > 0) {
                $filterBy = $request->filter_by;
            }
            $data = [];
            if (in_array('news', $filterBy)) {
                $news = News::select('id', 'title', 'primary_image')->active()
                        ->where(function($query) use($request) {
                            return $query->where('title', 'LIKE', '%' . $request->search . '%')->orWhere('tags', 'LIKE', '%' . $request->search . '%');
                        })
                        ->where('master_user_id', $userRelation->master_user_id)->get();
                $data['News'] = SearchNewsResource::collection($news);
            }
            if (in_array('resources', $filterBy)) {
                $resources = Resources::select('id', 'name','resource_type', 'resource_id','thumbnail')->active()
                            ->where(function($query) use($request) {
                                return $query->where('name', 'LIKE', '%' . $request->search . '%')->orWhere('tags', 'LIKE', '%' . $request->search . '%');
                            })
                            ->where('master_user_id', $userRelation->master_user_id)->get();
                $data['Resources'] = SearchResourceResource::collection($resources);
            }
            if (in_array('products', $filterBy)) {
                $products = Product::select('id', 'title', 'primary_image')->active()
                            ->where(function($query) use($request) {
                                return $query->where('title', 'LIKE', '%' . $request->search . '%')->orWhere('tags', 'LIKE', '%' . $request->search . '%');
                            })
                            ->where('master_user_id', $userRelation->master_user_id)->get();
                $data['Products'] = SearchProductResource::collection($products);
            }
            if (in_array('training', $filterBy)) {
                $assignedCourseIds =(new TrainingCourse)->getUserAssignCourseIds();
                $training = TrainingCourse::whereIn('id', $assignedCourseIds)
                                    ->select('id', 'title', 'primary_image')
                                    ->where(function($query) use($request) {
                                        return $query->where('title', 'LIKE', '%' . $request->search . '%')->orWhere('keywords', 'LIKE', '%' . $request->search . '%');
                                    })
                                    ->where(['status' => 'Active', 'publish_now' => 1, 'master_user_id' => $userRelation->master_user_id])
                                    ->get();
                $data['Training'] = SearchTrainingCourseResource::collection($training);
            }
            return response()->json(setResponse($data))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
