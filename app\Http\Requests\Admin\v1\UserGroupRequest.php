<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;

class UserGroupRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {
        return [
            "name" => 'sometimes|required',
            "manager_email" => 'sometimes|required|email',
            'groupId' => 'sometimes|required|int',
            'website' => 'sometimes|required|url',
            'address'=>'sometimes|required',
            'address'=>'sometimes|required',
            'contact_no'=>'sometimes|required',
            'latitude'=>'sometimes|required',
            'longitude'=>'sometimes|required'
        ];
    }

    public function messages() : array
    {
        return [
            'name.required' => __('The name field is required.'),
            'manager_email.email' => __('Invalid manager email.'),
            'groupId.required' => __('User group id is required.'),
            'groupId.int' => __('User group id must be integer.'),
            'website.url' => __('Invalid website url.'),
            'contact_no.required' => __('contact no. is required.'),
        ];
    }
}
