<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;

class ResetPasswordRequest extends CustomFormRequest {

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'email' => 'required|email|exists:master_users,email',
            'token' => 'required|min:6|max:6',
            'password' => [
                'required',
                'min:6',
                'max:64',
                'regex:/[a-z]/', // must contain at least one lowercase letter
                'regex:/[A-Z]/', // must contain at least one uppercase letter
                'regex:/[0-9]/', // must contain at least one digit
                'regex:/[-@$!%*#?&]/',
            ],
            'password_confirmation' => 'required|same:password',
        ];
    }
    
    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'email.required' => __('Email is required.'),
            'email.email' => __('Email must be a valid email address (E.g.: <EMAIL>).'),
            'email.exists' => __("You have entered an incorrect email address."),
            'token.required' => __('Token is required.'),
            'token.min' => __('Token must be consist of 6 digits.'),
            'token.max' => __('Token must be consist of 6 digits.'),
        ];
    }

}
