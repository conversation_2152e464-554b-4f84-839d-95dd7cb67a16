<?php

namespace App\Http\Controllers\Admin\v1;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\DomainMapping;
use App\Models\UserRelation;
use App\Models\TrainingCourseProgress;
use App\Repositories\Admin\v1\DomainMappingRepository;
use App\Http\Resources\Admin\v1\DomainMappingResource;
use App\Http\Requests\Admin\v1\DomainMappingRequest;
use DB;
class DomainMappingController extends Controller
{
    protected $model;

    protected $repository;
    
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->model = new DomainMapping();
        $this->repository = new DomainMappingRepository($this->model);
    }

    /**
     * Domain Mapping List
     *
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request) {
        try {
            $allDomains = DomainMapping::whereMasterUserId($request->id)->get();
            $message = $allDomains->count() > 0 ? 'operator.Domain.found' : 'operator.Domain.notFound';
            return response()->json(setResponse($allDomains, ['message' => __($message)]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Add / update operators domains
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(DomainMappingRequest $request) {
        try {
            $result = DB::transaction(function () use ($request) {
                $domainList = $request->domain;
                $existedDomains = [];
                $masterUserId = $request->operator_id;
                $oldDomains = DomainMapping::whereMasterUserId($masterUserId)->get()->pluck('domain_name')->toArray();
                
                if(isset($request->domain) && count($domainList) > 0){
                    foreach($domainList as $domain){
                        if(!empty(trim($domain))){
                            $alreadyExist = DomainMapping::whereMasterUserId($masterUserId)->whereDomainName(trim($domain))->first();
                            if(!$alreadyExist){
                                DomainMapping::create([
                                    'master_user_id' => $masterUserId,
                                    'domain_name' => trim($domain)
                                ]);
                            }
                        }
                        $existedDomains[] = trim($domain);
                    }
                    $deletedDomainList = array_diff($oldDomains, $domainList);
                    if(count($deletedDomainList) > 0){
                        DomainMapping::whereMasterUserId($masterUserId)->whereIn('domain_name', $deletedDomainList)->delete();
                        
                        // delete domain users
                        $totalUsers = UserRelation::whereMasterUserId($masterUserId)->whereIn('mapped_domain', $deletedDomainList)->get()->pluck('user_id')->toArray();
                        if(count($totalUsers) > 0){
                            // Unmapped deleted mapped domain users
                            $unmappedDomainUsersJob = (new \App\Jobs\UnmappedDomainUsersJob($masterUserId, $totalUsers))->delay(env('QUEUE_JOB_DELAY_TIME'));
                            dispatch($unmappedDomainUsersJob);
                            // TrainingCourseProgress::whereMasterUserId($masterUserId)->whereIn('user_id', $totalUsers)->delete();
                        }
                        UserRelation::whereMasterUserId($masterUserId)->whereIn('mapped_domain', $deletedDomainList)->delete();
                    }
                }else{
                    if(count($oldDomains) > 0){
                        DomainMapping::whereMasterUserId($masterUserId)->delete();
            
                        // delete domain users
                        $totalUsers = UserRelation::whereMasterUserId($masterUserId)->whereIn('mapped_domain', $oldDomains)->get()->pluck('user_id')->toArray();
                        if(count($totalUsers) > 0){
                            // Unmapped deleted mapped domain users
                            $unmappedDomainUsersJob = (new \App\Jobs\UnmappedDomainUsersJob($masterUserId, $totalUsers))->delay(env('QUEUE_JOB_DELAY_TIME'));
                            dispatch($unmappedDomainUsersJob);
                            // TrainingCourseProgress::whereMasterUserId($masterUserId)->whereIn('user_id', $totalUsers)->delete();
                        }
                        UserRelation::whereMasterUserId($masterUserId)->whereIn('mapped_domain', $oldDomains)->delete();
                    }
                }
                return response()->json(setResponse([], ['message' => __('operator.Domain.updated')]))->setStatusCode(Response::HTTP_OK);
            });
            return $result;
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
