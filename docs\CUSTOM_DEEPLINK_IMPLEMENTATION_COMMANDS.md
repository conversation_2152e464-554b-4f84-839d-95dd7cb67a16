# Firebase to Custom Deeplink Migration - Implementation Commands

## Overview
This document contains all the commands needed to implement the Firebase to Custom Deeplink migration in the correct order.

## Prerequisites
- Ensure you have a backup of your database
- Test in a staging environment first
- Verify all dependencies are installed

## Phase 1: Database Setup

### 1. Run Database Migrations
```bash
# Create custom deeplinks table
php artisan migrate --path=database/migrations/2024_12_19_120000_create_custom_deeplinks_table.php

# Add custom deeplink fields to white label settings
php artisan migrate --path=database/migrations/2024_12_19_120001_add_custom_deeplink_fields_to_white_label_settings.php
```

### 2. Verify Database Schema
```bash
# Check if tables were created successfully
php artisan tinker
>>> Schema::hasTable('custom_deeplinks')
>>> Schema::hasColumns('white_label_settings', ['custom_deeplink_domain', 'custom_deeplink_enabled'])
>>> exit
```

## Phase 2: Configuration Setup

### 3. Update Environment Variables
```bash
# Copy the new environment variables from .env.example to your .env file
# Add these variables to your .env:

CUSTOM_DEEPLINKS_ENABLED=false
CUSTOM_DEEPLINKS_MIGRATION_MODE=true
CUSTOM_DEEPLINK_DOMAIN="${APP_URL}"
CUSTOM_DEEPLINK_DEFAULT_FALLBACK="https://skillsbase.io"
CUSTOM_DEEPLINK_CODE_LENGTH=8
CUSTOM_DEEPLINK_ANALYTICS_ENABLED=true
CUSTOM_DEEPLINK_CACHE_TTL=3600
CUSTOM_DEEPLINK_VALIDATE_URLS=true
CUSTOM_DEEPLINK_MAX_REDIRECTS_PER_IP=100
CUSTOM_DEEPLINK_RATE_LIMIT_WINDOW=3600
CUSTOM_DEEPLINKS_DETAILED_ANALYTICS=false
CUSTOM_DEEPLINKS_AUTO_CLEANUP=true
CUSTOM_DEEPLINKS_CLEANUP_AFTER_DAYS=365
CUSTOM_DEEPLINKS_CLEANUP_BATCH_SIZE=1000
```

### 4. Clear Configuration Cache
```bash
php artisan config:clear
php artisan config:cache
```

## Phase 3: Testing (Optional but Recommended)

### 5. Run Unit Tests
```bash
# Run the deeplink service tests
php artisan test tests/Unit/Services/DeeplinkServiceTest.php

# Run all tests to ensure nothing is broken
php artisan test
```

### 6. Test Deeplink Generation
```bash
# Test generating a few deeplinks
php artisan tinker
>>> $service = app(\App\Services\DeeplinkService::class);
>>> $url = $service->generateDeeplink(['target_url' => 'https://example.com', 'type' => 'test']);
>>> echo $url;
>>> exit
```

## Phase 4: Data Migration (Staging Environment First)

### 7. Dry Run Migration
```bash
# Test migration without making changes
php artisan deeplinks:migrate-firebase --dry-run

# Test specific table migration
php artisan deeplinks:migrate-firebase --dry-run --table=training_course_whitelabel_deeplink

# Test for specific operator
php artisan deeplinks:migrate-firebase --dry-run --operator-id=1
```

### 8. Actual Migration (After Dry Run Success)
```bash
# Migrate all Firebase deeplinks to custom deeplinks
php artisan deeplinks:migrate-firebase --batch-size=50

# Or migrate specific tables one by one for better control
php artisan deeplinks:migrate-firebase --table=training_course_whitelabel_deeplink --batch-size=50
php artisan deeplinks:migrate-firebase --table=products_whitelabel_deeplink --batch-size=50
php artisan deeplinks:migrate-firebase --table=resources_whitelabel_deeplink --batch-size=50
```

## Phase 5: Generate New Deeplinks

### 9. Generate Deeplinks for Existing Content
```bash
# Generate deeplinks for all content types
php artisan deeplinks:generate --type=all --batch-size=100

# Or generate by type
php artisan deeplinks:generate --type=training_courses --batch-size=100
php artisan deeplinks:generate --type=products --batch-size=100
php artisan deeplinks:generate --type=resources --batch-size=100

# Generate for specific operator
php artisan deeplinks:generate --type=all --operator-id=1 --batch-size=100

# Force regeneration (overwrite existing)
php artisan deeplinks:generate --type=all --force
```

## Phase 6: Enable Custom Deeplinks

### 10. Enable Custom Deeplinks Feature
```bash
# Update environment variable
# Change CUSTOM_DEEPLINKS_ENABLED=true in .env file

# Clear cache
php artisan config:clear
php artisan config:cache
```

### 11. Test Deeplink Resolution
```bash
# Test a generated deeplink in browser or with curl
curl -I "http://your-domain.com/dl/ABC123XY"

# Should return a 302 redirect
```

## Phase 7: Verification and Monitoring

### 12. Verify Migration Success
```bash
# Check deeplink counts
php artisan tinker
>>> \App\Models\CustomDeeplink::count()
>>> \App\Models\CustomDeeplink::where('metadata->migrated_from', 'firebase')->count()
>>> exit

# Test deeplink resolution
php artisan tinker
>>> $service = app(\App\Services\DeeplinkService::class);
>>> $deeplink = $service->resolveDeeplink('YOUR_SHORT_CODE');
>>> var_dump($deeplink);
>>> exit
```

### 13. Monitor Logs
```bash
# Monitor application logs for any errors
tail -f storage/logs/laravel.log

# Check for deeplink-related errors
grep -i "deeplink" storage/logs/laravel.log
```

## Phase 8: Cleanup (After Successful Migration)

### 14. Disable Firebase Deeplinks (Optional)
```bash
# Update environment variables to disable Firebase fallback
# Change CUSTOM_DEEPLINKS_MIGRATION_MODE=false in .env file

# Clear cache
php artisan config:clear
php artisan config:cache
```

### 15. Setup Cleanup Cron Job
```bash
# Add to your crontab or scheduler
# Run cleanup daily at 2 AM
0 2 * * * cd /path/to/your/app && php artisan deeplinks:cleanup

# Or run manually
php artisan deeplinks:cleanup --dry-run
php artisan deeplinks:cleanup
```

## Phase 9: Performance Optimization

### 16. Setup Cache Warming (Optional)
```bash
# Warm up frequently accessed deeplinks
php artisan tinker
>>> $popularDeeplinks = \App\Models\CustomDeeplink::orderBy('click_count', 'desc')->limit(100)->get();
>>> foreach($popularDeeplinks as $dl) { \Cache::put("deeplink:{$dl->short_code}", $dl, 3600); }
>>> exit
```

### 17. Database Optimization
```bash
# Analyze table performance
php artisan tinker
>>> \DB::statement('ANALYZE TABLE custom_deeplinks');
>>> exit
```

## Rollback Plan (If Needed)

### 18. Emergency Rollback
```bash
# Disable custom deeplinks
# Change CUSTOM_DEEPLINKS_ENABLED=false in .env file

# Clear cache
php artisan config:clear
php artisan config:cache

# Restore original Firebase deeplinks from backup if needed
# (This would require restoring the original share_url values)
```

## Monitoring Commands

### 19. Health Check Commands
```bash
# Check deeplink service health
curl "http://your-domain.com/api/deeplinks/health"

# Get deeplink statistics
php artisan tinker
>>> \App\Models\CustomDeeplink::selectRaw('deeplink_type, count(*) as count')->groupBy('deeplink_type')->get();
>>> \App\Models\CustomDeeplink::selectRaw('DATE(created_at) as date, count(*) as count')->groupBy('date')->orderBy('date', 'desc')->limit(7)->get();
>>> exit
```

### 20. Performance Monitoring
```bash
# Check cache hit rates
php artisan tinker
>>> \Cache::get('deeplink_cache_stats', []);
>>> exit

# Monitor database query performance
# Enable query logging in config/database.php and monitor slow queries
```

## Notes

1. **Always test in staging first** before running in production
2. **Monitor logs closely** during and after migration
3. **Keep Firebase configuration** until you're confident the migration is successful
4. **Backup your database** before starting the migration
5. **Coordinate with mobile app teams** for any URL scheme changes
6. **Set up monitoring alerts** for deeplink resolution failures

## Support Commands

### Debug Commands
```bash
# Debug specific deeplink
php artisan tinker
>>> $deeplink = \App\Models\CustomDeeplink::where('short_code', 'YOUR_CODE')->first();
>>> var_dump($deeplink->toArray());
>>> exit

# Check operator configuration
php artisan tinker
>>> $operator = \App\Models\MasterUser::with('whiteLabelSettings')->find(OPERATOR_ID);
>>> var_dump($operator->whiteLabelSettings->toArray());
>>> exit
```

This completes the implementation commands for the Firebase to Custom Deeplink migration.
