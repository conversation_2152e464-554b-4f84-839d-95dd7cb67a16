<?php

namespace Database\Factories;

use App\Models\CustomDeeplink;
use Illuminate\Database\Eloquent\Factories\Factory;

class CustomDeeplinkFactory extends Factory
{
    protected $model = CustomDeeplink::class;

    public function definition()
    {
        return [
            'short_code' => $this->generateShortCode(),
            'operator_id' => $this->faker->optional()->numberBetween(1, 100),
            'target_url' => $this->faker->url(),
            'deeplink_type' => $this->faker->randomElement([
                'training_course',
                'product',
                'resource',
                'training_course_submodule',
                'general'
            ]),
            'entity_id' => $this->faker->optional()->numberBetween(1, 1000),
            'entity_type' => $this->faker->optional()->randomElement([
                'TrainingCourse',
                'Product',
                'Resource',
                'TrainingCourseSubmoduleDetails'
            ]),
            'ios_package_name' => $this->faker->optional()->regexify('com\.[a-z]+\.[a-z]+'),
            'android_package_name' => $this->faker->optional()->regexify('com\.[a-z]+\.[a-z]+'),
            'ios_app_store_id' => $this->faker->optional()->numerify('#########'),
            'fallback_url' => $this->faker->optional()->url(),
            'click_count' => $this->faker->numberBetween(0, 1000),
            'last_clicked_at' => $this->faker->optional()->dateTimeBetween('-1 year', 'now'),
            'is_active' => $this->faker->boolean(90), // 90% chance of being active
            'expires_at' => $this->faker->optional(30)->dateTimeBetween('now', '+1 year'), // 30% chance of having expiration
            'metadata' => $this->faker->optional()->randomElement([
                null,
                ['source' => 'api'],
                ['campaign' => 'email', 'source' => 'newsletter'],
                ['migrated_from' => 'firebase', 'migration_date' => now()->toISOString()]
            ]),
        ];
    }

    /**
     * Generate a valid short code using the same character set as the service.
     */
    private function generateShortCode(): string
    {
        $charset = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789';
        $code = '';
        
        for ($i = 0; $i < 8; $i++) {
            $code .= $charset[random_int(0, strlen($charset) - 1)];
        }
        
        return $code;
    }

    /**
     * Create an active deeplink.
     */
    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => true,
                'expires_at' => null,
            ];
        });
    }

    /**
     * Create an inactive deeplink.
     */
    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => false,
            ];
        });
    }

    /**
     * Create an expired deeplink.
     */
    public function expired()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => true,
                'expires_at' => $this->faker->dateTimeBetween('-1 year', '-1 day'),
            ];
        });
    }

    /**
     * Create a deeplink with no clicks.
     */
    public function unused()
    {
        return $this->state(function (array $attributes) {
            return [
                'click_count' => 0,
                'last_clicked_at' => null,
            ];
        });
    }

    /**
     * Create a deeplink with many clicks.
     */
    public function popular()
    {
        return $this->state(function (array $attributes) {
            return [
                'click_count' => $this->faker->numberBetween(100, 10000),
                'last_clicked_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
            ];
        });
    }

    /**
     * Create a training course deeplink.
     */
    public function trainingCourse()
    {
        return $this->state(function (array $attributes) {
            return [
                'deeplink_type' => 'training_course',
                'entity_type' => 'TrainingCourse',
                'entity_id' => $this->faker->numberBetween(1, 1000),
                'target_url' => url('/api/trainingCourses/' . $this->faker->numberBetween(1, 1000)),
            ];
        });
    }

    /**
     * Create a product deeplink.
     */
    public function product()
    {
        return $this->state(function (array $attributes) {
            return [
                'deeplink_type' => 'product',
                'entity_type' => 'Product',
                'entity_id' => $this->faker->numberBetween(1, 1000),
                'target_url' => url('/api/products/' . $this->faker->numberBetween(1, 1000)),
            ];
        });
    }

    /**
     * Create a resource deeplink.
     */
    public function resource()
    {
        return $this->state(function (array $attributes) {
            return [
                'deeplink_type' => 'resource',
                'entity_type' => 'Resource',
                'entity_id' => $this->faker->numberBetween(1, 1000),
                'target_url' => url('/api/resources/' . $this->faker->numberBetween(1, 1000)),
            ];
        });
    }

    /**
     * Create a deeplink for a specific operator.
     */
    public function forOperator(int $operatorId)
    {
        return $this->state(function (array $attributes) use ($operatorId) {
            return [
                'operator_id' => $operatorId,
                'ios_package_name' => 'com.operator' . $operatorId . '.app',
                'android_package_name' => 'com.operator' . $operatorId . '.android',
            ];
        });
    }

    /**
     * Create a deeplink with mobile app configuration.
     */
    public function withMobileConfig()
    {
        return $this->state(function (array $attributes) {
            return [
                'ios_package_name' => 'com.skillsbase.app',
                'android_package_name' => 'com.skillsbase.android',
                'ios_app_store_id' => '123456789',
                'fallback_url' => 'https://skillsbase.io',
            ];
        });
    }

    /**
     * Create a deeplink with metadata.
     */
    public function withMetadata(array $metadata)
    {
        return $this->state(function (array $attributes) use ($metadata) {
            return [
                'metadata' => $metadata,
            ];
        });
    }
}
