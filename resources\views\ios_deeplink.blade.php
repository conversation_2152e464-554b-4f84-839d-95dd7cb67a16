<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <title>Open in {{$appName}}</title>
    
    <!-- iOS Smart App Banner (native fallback) -->
    <meta name="apple-itunes-app" content="app-id=YOUR_APP_STORE_ID">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            background-image: url('{{$backgroundImage}}');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            height: 100%;
            width: 100%;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            max-width: 400px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .app-icon {
            width: 80px;
            height: 80px;
            background: #007AFF;
            border-radius: 18px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            box-shadow: 0 4px 12px rgba(0,122,255,0.3);
        }
        
        h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #1d1d1f;
        }
        
        .subtitle {
            color: #86868b;
            font-size: 16px;
            margin-bottom: 30px;
            line-height: 1.4;
        }
        
        .btn {
            display: block;
            width: 100%;
            padding: 16px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 12px;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
        }
        
        .btn-primary {
            background: {{$buttonColor}};
            color: white;
        }
        
        .btn-primary:hover {
            background: {{$buttonColor}};
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #f2f2f7;
            color: {{$buttonColor}};
        }
        
        .btn-secondary:hover {
            background: #e5e5ea;
        }
        
        .status {
            margin-top: 20px;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
            display: none;
        }
        
        .status.show {
            display: block;
        }
        
        .status.trying {
            background: #fff3cd;
            color: #856404;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.failed {
            background: #f8d7da;
            color: #721c24;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid {{$buttonColor}};
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .footer {
            margin-top: 30px;
            font-size: 12px;
            color: #86868b;
            line-height: 1.4;
        }
        
    </style>
</head>
<body>
    
    <div class="container">
        <img src="{{$shortLogo}}" alt="{{$appName}} Logo" class="skillsbase-logo" style="width:80px;height:80px;margin-bottom:10px;">
        <h1 style="font-size:1.2rem;">Open in {{$appName}}</h1>
                
        <button onclick="openApp()" class="btn btn-primary" id="open-btn">
            <span class="loading" id="loading" style="display: none;"></span>
            <span id="btn-text">Open {{$appName}}</span>
        </button>
        
        <a href="{{$appStoreUrl}}" class="btn btn-secondary" id="store-btn">
            Get from App Store
        </a>
        
        <div id="status" class="status">
            <span id="status-text"></span>
        </div>
        
        <div class="footer">
            If the app doesn't open automatically, tap "Get from App Store" to download {{$appName}}.
        </div>
    </div>

    <script>
        let attemptCount = 0;
        let opened = false;
        
        function updateStatus(message, type) {
            const status = document.getElementById('status');
            const statusText = document.getElementById('status-text');
            
            status.className = 'status show ' + type;
            statusText.textContent = message;
        }
        
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'inline-block' : 'none';
            document.getElementById('btn-text').textContent = show ? 'Opening...' : 'Open {{$appName}}';
        }
        
        function openApp() {
            if (attemptCount > 0) return; // Prevent multiple attempts
            
            attemptCount++;
            opened = false;
            showLoading(true);
            updateStatus('Trying to open {{$appName}}...', 'trying');
            
            // Method 1: Try iframe approach
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = '{{$deeplinkUrl}}';
            document.body.appendChild(iframe);
            
            // Method 2: Also try direct window.location as backup
            setTimeout(() => {
                if (!opened) {
                    window.location.href = '{{$deeplinkUrl}}';
                }
            }, 100);
            
            // Cleanup iframe
            setTimeout(() => {
                if (iframe.parentNode) {
                    iframe.parentNode.removeChild(iframe);
                }
            }, 1000);
            
            // Fallback timer
            const fallbackTimer = setTimeout(() => {
                if (!opened) {
                    showLoading(false);
                    updateStatus('App not installed? Download from App Store', 'failed');
                    
                    // Auto-redirect to App Store after showing message
                    setTimeout(() => {
                        window.location.href = '{{$appStoreUrl}}';
                    }, 2000);
                }
            }, 2500);
            
            // Success detection
            function handleSuccess() {
                if (!opened) {
                    opened = true;
                    clearTimeout(fallbackTimer);
                    showLoading(false);
                    updateStatus('{{$appName}} opened successfully!', 'success');
                }
            }
            
            // Listen for app opening indicators
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) handleSuccess();
            });
            
            window.addEventListener('blur', handleSuccess);
            window.addEventListener('pagehide', handleSuccess);
        }
        
        // Auto-attempt on page load
        // window.addEventListener('load', () => {
        //     setTimeout(openApp, 500);
        // });
        
        // Prevent accidental navigation
        window.addEventListener('beforeunload', (e) => {
            if (opened) {
                e.preventDefault();
                e.returnValue = '';
            }
        });
    </script>
</body>
</html>