<?php

namespace App\Http\Controllers\Admin\v1;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\Configuration;
use App\Http\Requests\Admin\v1\MiantenaceModeRequest;
use App\Jobs\MaintenanceModeEmailNotificationJob;
use App\Jobs\MaintenanceModePushNotificationJob;

class ConfigurationController extends Controller {

    protected $model;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
	$this->model = new Configuration();
    }

    public function maintenanceMode(MiantenaceModeRequest $request) {
	try {
	    $configuration = $this->model->find(1); //id 1 for getting configuratin settings
	    if ($configuration) {
		$configuration->is_maintenance_mode = $request->maintenance_mode;
		$configuration->content = json_encode($request->all());
		if ($configuration->save()) {
		    if ($request->push_notification) {
			/* Send push notification to all mobile users */
			dispatch(new MaintenanceModePushNotificationJob($request->mobile_title, $request->mobile_message)); //->onConnection('sync');
		    }
		    if ($request->email_notification) {
			/* Send email notification to all operator users */
			dispatch(new MaintenanceModeEmailNotificationJob($request->email_title, $request->email_message)); //->onConnection('sync');
		    }
		    return response()->json(setResponse([], ['message' => __('admin.configuration.maintenance_mode')]))->setStatusCode(Response::HTTP_OK);
		}
	    }
	    return response()->json(setErrorResponse(__('admin.configuration.not_found')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
	} catch (\Exception $e) {
	    return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
	}
    }

}
