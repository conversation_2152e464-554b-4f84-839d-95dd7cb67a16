<?php

namespace App\Http\Controllers\Operator\v1;

use DB;
use Carbon\Carbon;
use App\Models\User;
use App\Models\Roles;
use App\Models\UserGroup;
use App\Models\MasterUser;
use App\Models\UserRelation;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\TrainingCourse;
use App\Services\GetStreamService;
use App\Models\PrerequisiteModules;
use App\Http\Controllers\Controller;
use App\Models\AssignTrainingCourses;
use App\Models\TrainingCourseModules;
use App\Models\UserCourseCertificate;
use App\Jobs\QuizResultPdfSendMailJob;
use App\Models\PrerequisiteSubModules;
use App\Models\TrainingCourseProgress;
use App\Models\UserAssignTrainingCourses;
use App\Models\TrainingCourseSubModuleQuiz;
use App\Models\TrainingCourseModuleProgress;
use Illuminate\Support\Facades\Notification;
use App\Models\TrainingCourseSubmoduleDetails;
use App\Models\TrainingCourseSubmoduleProgress;
use App\Models\TrainingCourseSubModuleQuizResults;
use App\Http\Requests\Operator\v1\FrontUserRequest;
use App\Http\Resources\Operator\v1\ShowUserResource;
use App\Http\Resources\Operator\v1\FrontUserResource;
use App\Repositories\Operator\v1\FrontUserRepository;
use App\Http\Requests\Operator\v1\CommonListingRequest;
use App\Http\Requests\Operator\v1\VerifyRegisteredUser;
use App\Notifications\VerifyRegisteredUserNotification;
use App\Http\Requests\Operator\v1\AssignAssessorRequest;
use App\Http\Requests\Operator\v1\FrontUserDeleteRequest;
use App\Http\Requests\Operator\v1\FrontUserStatusRequest;
use App\Http\Requests\Operator\v1\FrontUserUpdateRequest;
use App\Models\TrainingCourseSubModuleFeedbackPDFResults;
use App\Http\Requests\Operator\v1\ImportFrontUsersRequest;
use App\Http\Resources\Operator\v1\UserCourseListResource;
use App\Http\Requests\Operator\v1\UserManageDetailsRequest;
use App\Http\Requests\Operator\v1\AssignedCourseDetailsRequest;
use App\Http\Requests\Operator\v1\OverRideCourseProgressRequest;
use App\Models\TrainingCourseSubModulePracticalAssessmentResults;
use App\Http\Requests\Operator\v1\AssessorAssignFrontUsersRequest;
use App\Http\Resources\Operator\v1\AssessorAssignFrontUserResource;
use App\Http\Requests\Operator\v1\SmartAward\FrontUserSmartAwardRequest;
use App\Http\Requests\Operator\v1\SmartAward\ChangePasswordSmartAwardRequest;
use App\Http\Requests\Operator\v1\SmartAward\FrontUserUpdateSmartAwardRequest;
use App\Http\Requests\Operator\v1\SmartAward\StoreQuizResultSmartAwardRequest;
use App\Http\Resources\Operator\v1\TrainingCourseSubModuleQuizPdfResultResource;
use App\Http\Requests\Operator\v1\SmartAward\AssignedCourseDetailsSmartAwardRequest;
use App\Http\Resources\Operator\v1\TrainingCourseSubModuleMiniQuizPdfResultResource;

class FrontUserController extends Controller
{

    /*
    |---------------------------------------------------------------------------
    | Front User Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles user added by operator after admin login.
    |
     */
    protected $model;

    protected $user_repository;

    public function __construct()
    {
        // ini_set('max_execution_time', '300');
        $this->model = new User();
        $this->user_repository = new FrontUserRepository($this->model);
        // $quiz_pdf_link         = new TrainingCourseSubModuleQuizResults();
        // $miniquiz_pdf_link         = new TrainingCourseSubModuleFeedbackPDFResults();
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    /**
     * @OA\Post(
     *     path="/operator/users",
     *     tags={"Operator - Users"},
     *     summary="Add User",
     *     description="Add User",
     *     operationId="store",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="User Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="User Status",
     *                     type="string",
    enum={"Active", "Inactive"}
     *                 ),
     *                 example={"0":{
    "step":1,
    "name":"",
    "email":"",
    "user_group_id":"",
    "manager_email":"optional",
    "unique_id":"optional",
    },"1":{
    "step":2,
    "userId":12,
    "trainingCourseIds":{
    "0":1,
    "1":3
    }
    },"2":{
    "step":3,
    "userId":12,
    "contact_address":"",
    "contact_no":"",
    "contact_email":"",
    "contact_lat":"",
    "contact_long":"",
    "contact_website":"",
    }}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="User Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function store(FrontUserRequest $request)
    {
        try {
            $moduleData = $request;
            $result = $this->user_repository->create($moduleData);
            if (isset($result['response_type']) && $result['response_type'] == "Error") {
                return response()->json(setErrorResponse($result['message']))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else {
                $retunData = array('id' => $result->id);
                $user=User::where('id',$result->id)->get()->toArray();
                app(GetStreamService::class)->GenerateGetStreamToken($user[0]);
                return response()->json(setResponse($retunData, ['message' => __('operator.FrontUser.add')]))->setStatusCode(Response::HTTP_OK);
            }

        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/users/{id}",
     *     tags={"Operator - Users"},
     *     summary="Show User Details",
     *     description="Show User Details",
     *     operationId="show",
     *     @OA\Parameter(
     *         description="Id of User to fetch",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function show($id)
    {
        try {

            $dataId = [];
            $masterUserId = auth()->guard('operator')->id();
            $masterUserDet = MasterUser::find($masterUserId);
            if ($masterUserDet->parent_id != null) {
                $parent = $masterUserDet->parent_id;
            } else {
                $parent = $masterUserId;
            }
            $teamMembers = MasterUser::where('parent_id', $parent)->pluck('id')->toArray();
            array_push($teamMembers, $parent);
            if (in_array($masterUserId, $teamMembers)) {
                $allUsers = UserRelation::where('master_user_id', $parent)->pluck('user_id')->toArray();
                // dd($allUsers);
                if (in_array($id, $allUsers)) {
                    $userDetail = $this->model::with(['user_relation' => function ($q) use ($masterUserId) {
                        $q->where('master_user_id', $masterUserId)->first();
                    }])->whereId($id)->first();
                    if (!is_null($userDetail)) {

                        $userAssignCourse = UserAssignTrainingCourses::where('user_id', $userDetail->id)->whereMasterUserId(getOperatorId())->get();

                        if (!$userAssignCourse->isEmpty()) {
                            foreach ($userAssignCourse as $key => $course) {
                                $dataId[] = $course->training_course_id;
                            }
                            $userDetail->assign_course = $dataId;
                        }

                        if (!empty($userDetail->userOperator) && $userDetail->userOperator->user_group_id != '') {
                            $assignCourse = AssignTrainingCourses::where('user_group_id', $userDetail->userOperator->user_group_id)->whereMasterUserId($masterUserId)->get();

                            if (!$assignCourse->isEmpty()) {
                                foreach ($assignCourse as $key => $course) {
                                    $dataId[] = $course->training_course_id;
                                }

                                if (!is_null($userDetail->assign_course)) {
                                    $userDetail->assign_course = array_unique(array_merge($userDetail->assign_course, $dataId));
                                } else {
                                    $userDetail->assign_course = $dataId;
                                }
                            }
                        }

                        $userDetail = new ShowUserResource($userDetail);
                    }
                    $message = !empty($userDetail) ? __('operator.FrontUser.found') : __('operator.FrontUser.notFound');
                    return response()->json(setResponse($userDetail, ['message' => $message]))->setStatusCode(Response::HTTP_OK);

                } else {
                    return response()->json(setErrorResponse(__('You cannot access this record')))->setStatusCode(Response::HTTP_NOT_FOUND);

                }
            }

        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * @OA\Put(
     *     path="/operator/users/{id}",
     *     tags={"Operator - Users"},
     *     summary="Update User details",
     *     description="Update User details",
     *     operationId="update",
     *     @OA\Parameter(
     *         description="Id of User to update",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="User Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={"0":{
     *                  "step":1,
    "name":"",
    "email":"",
    "password": "",
    "user_group_id":"",
    "manager_email":"",
    "unique_id":"",
    },"1":{
    "step":2,
    "trainingCourseIds":{
    "0":1,
    "1":3
    }
    },"2":{
    "step":3,
    "contact_address":"",
    "contact_no":"",
    "contact_email":"",
    "contact_lat":"",
    "contact_long":"",
    "contact_website":"",
    }}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="User Updated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function update(FrontUserUpdateRequest $request, $id)
    {
        try {
            $response = $this->user_repository->update($request, $id);
            if (isset($response['response_type']) && $response['response_type'] == "Error") {
                return response()->json(setErrorResponse($response['message']))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else {
                $data['id'] = $id;
                return response()->json(setResponse($data, ['message' => __('operator.FrontUser.update')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/users/getAllUserGroup",
     *     tags={"Operator - Users"},
     *     summary="Get all User Groups",
     *     description="Get all User Groups",
     *     operationId="getAllUserGroup",
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Get user groups successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getAllUserGroup(Request $request)
    {
        try {
            $masterUserId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $allGroups = UserGroup::whereParentId($masterUserId)->select('id', 'name', 'status')->get();
            $message = !empty($allGroups) ? __('User group detail found.') : __('User group not found.');
            return response()->json(setResponse($allGroups, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/users/getUserGroupDetailById/{id}",
     *     tags={"Operator - Users"},
     *     summary="Get User Group details",
     *     description="Get User Group details",
     *     operationId="getUserGroupDetailById",
     *     @OA\Parameter(
     *         description="Id of role to fetch",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getUserGroupDetailById($id)
    {
        try {
            $groupDetail = UserGroup::find($id, ['id', 'manager_email', 'unique_id']);
            $message = !empty($groupDetail) ? __('operator.FrontUser.groupFound') : __('operator.FrontUser.dataNotFound');
            return response()->json(setResponse($groupDetail, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/users/getUserManagerDetailByField",
     *     tags={"Operator - Users"},
     *     summary="Get Operator Manager details",
     *     description="Get Operator Manager details",
     *     operationId="getUserManagerDetailByField",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="User Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
    enum={"Active", "Inactive"}
     *                 ),
     *                 example={
    "search_type":"email/unique_id",
    "search_key":"email or uid"
    }
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="User Updated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getUserManagerDetailByField(UserManageDetailsRequest $request)
    {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $operatorUniqueIdName = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->unique_id_name;
            $operatorData = MasterUser::leftJoin('groups', 'groups.email', '=', 'master_users.email')->select('master_users.id', 'master_users.email', 'master_users.unique_id', 'groups.id as user_group_id');
            $term = $request->search_key;

            if ($request->search_type == 'email') {
                $operatorData->where('master_users.email', '=', $term);
            } else {
                $operatorData->where('master_users.unique_id', '=', $term);
            }
            $operatorData = $operatorData->Where(function ($query) use ($operatorId) {
                $query->where('master_users.parent_id', $operatorId)
                    ->orWhere('master_users.id', '=', $operatorId);
            })->get()->toArray();
            if (empty($operatorData)) {
                $message = $request->search_type == 'email' ? __('operator.FrontUser.managerEmail') : ($operatorUniqueIdName ? $operatorUniqueIdName . ' not found.' : 'Unique Id not found.');
            } else {
                $message = __('operator.FrontUser.managerFound');
            }
            return response()->json(setResponse($operatorData, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/users/delete",
     *     tags={"Operator - Users"},
     *     summary="Delete  User",
     *     description="Delete  User",
     *     operationId="destroy",
    @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="id",
     *                     description="id",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={ "ids":{
    "0":"1",
    "1":"2"
    }
    }
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="User Deleted successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function destroy(FrontUserDeleteRequest $request)
    {
        try {
            $this->user_repository->delete($request);
            return response()->json(setResponse([], ['message' => __('operator.FrontUser.delete')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/users/changeStatus",
     *     tags={"Operator - Users"},
     *     summary="User Change Status",
     *     description="User Change Status",
     *     operationId="changeStatus",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="id",
     *                     description="id",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={"status":"Inactive",
    "ids":{
    "0":"1",
    "1":"2"
    }
    }
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Status changed successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */

    public function changeStatus(FrontUserStatusRequest $request)
    {
        try {
            $this->user_repository->change_status($request);
            return response()->json(setResponse([], ['message' => __('operator.FrontUser.status')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/users/getFrontUsersList",
     *     tags={"Operator - Users"},
     *     summary="List All Users",
     *     description="List All Users",
     *     operationId="getFrontUsersList",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                  @OA\Property(
     *                     property="group_id",
     *                     description="Group Id",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="isExport",
     *                     description="for export csv",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="exportFields",
     *                     description="for export csv fields",
     *                     type="string"
     *                 ),
     *                 example={"per_page": 10, "page": 1, "search_key": "", "sort_by": "name", "order_by": "asc", "filters": {"name": "", "email": "", "created_at": "", "status": "", "manager_email": "", "unique_id": ""}, "isExport": 0, "exportFields": {"id", "name","email","group_name","manager_email","unique_id","assigned_course","last_logged_in_at", "created_at", "status"}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getFrontUsersList(CommonListingRequest $request)
    {
        try {
            $users = $this->user_repository->getUsersListing($request->all());
            if ($request->isExport) {
                return $this->user_repository->exportCsv($users->get(), $request->exportFields);
            }
            return FrontUserResource::collection($users->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/users/importFrontUsers",
     *     tags={"Operator - Users"},
     *     summary="Import Front Users",
     *     description="Import Front Users",
     *     operationId="importFrontUsers",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="file",
     *                     description="CSV file of Users",
     *                     type="file"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function importFrontUsers(ImportFrontUsersRequest $request)
    {
        try {
            $isImported = $this->user_repository->importFrontUsers($request);
            if (isset($isImported) && $isImported[0]) {
                return response()->json(setResponse($isImported[1], ['message' => __('operator.FrontUser.import-success')]))->setStatusCode(Response::HTTP_OK);
            } else {
                return response()->json(setErrorResponse($isImported[1], ['message' => __('operator.FrontUser.import-error')]))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/users/getTrainingCourseList",
     *     tags={"Operator - Users"},
     *     summary="Get Courses list",
     *     description="Get Courses list",
     *     operationId="getTrainingCourseList",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="User Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="User Status",
     *                     type="string",
    enum={"Active", "Inactive"}
     *                 ),
     *                 example={
    "per_page":10,
    "sort_by":"createdBy/courseName",
    "order_by":"asc",
    "user_group_id":null,
    "search_key":"my course",
    "filters": {
    "courseName": "",
    "createdBy": "",
    "updated_at": "",
    "no_of_assignees": ""
    }
    }
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Courses list genetated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */

    public function getTrainingCourseList(CommonListingRequest $request)
    {
        try {
            $moduleData = $request;
            $courseTemp = $this->user_repository->trainingCourseList($moduleData);
            return UserCourseListResource::collection($courseTemp->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function getGroupTrainingCourseList(CommonListingRequest $request)
    {
        try {
            $moduleData = $request;
            if($moduleData['user_group_id']){
            $courseTemp = $this->user_repository->GrouptrainingCourseList($moduleData);
            if($courseTemp){
            return UserCourseListResource::collection($courseTemp->paginate($request->per_page));
            }else{
                $courseTemp =[];
                return UserCourseListResource::collection($courseTemp);
            }
            }else{
            $courseTemp =[];
            return UserCourseListResource::collection($courseTemp);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    public function getOnlyTrainingCourseList(CommonListingRequest $request)
    {
        try {
            $moduleData = $request;
            $userId = $moduleData->user_id;
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $userDetail = $this->model::with(['user_relation' => function ($q) use ($operatorId,$userId) {
                $q->where('master_user_id', $operatorId)->first();
            }])->whereId($userId)->first();
            if(!empty($userDetail)){
                $courseTemp = $this->user_repository->OnlytrainingCourseList($moduleData,$operatorId,$userDetail);
                return UserCourseListResource::collection($courseTemp->paginate($request->per_page));
            }else{
                return response()->json(setResponse([], ['message' => 'User not found']))->setStatusCode(Response::HTTP_OK);
            }

        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/users/getProfile/{id}",
     *     tags={"Operator - Users"},
     *     summary="Get User Profile Details",
     *     description="Get User Profile Details",
     *     operationId="getProfile",
     *     @OA\Parameter(
     *         description="Id of User to fetch",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getProfile($id)
    {
        try {
            $masterUserId = auth()->guard('operator')->id();
            $masterUserDet = MasterUser::find($masterUserId);
            if ($masterUserDet->parent_id != null) {
                $parent = $masterUserDet->parent_id;
            } else {
                $parent = $masterUserId;
            }
            $teamMembers = MasterUser::where('parent_id', $parent)->pluck('id')->toArray();
            array_push($teamMembers, $parent);
            if (in_array($masterUserId, $teamMembers)) {
                $allUsers = UserRelation::where('master_user_id', $parent)->pluck('user_id')->toArray();
                if (in_array($id, $allUsers)) {
                    $userDetail = $this->model::with(['user_relation' => function ($q) use ($masterUserId) {
                        $q->where('master_user_id', $masterUserId)->first();
                    }])->whereId($id)->first();
                    $dataViewPermissions=Roles::where('id',$userDetail->role_id)->value('permission');
                    $userData = [];
                    if ($userDetail) {
                        $userData['id'] = $userDetail->id;
                        $userData['email'] = $userDetail->email;
                        $userData['name'] = $userDetail->name;
                        $userData['manager_email'] = (!empty($userDetail->userOperator) ? $userDetail->userOperator->manager_email : '');
                        $userData['unique_id'] = (!empty($userDetail->userOperator) ? $userDetail->userOperator->unique_id : '');
                        $userData['status'] = $userDetail->status;
                        $userData['group_name'] = null;
                        $userData['photo'] = $userDetail->photo_url;
                        $userData['total_assigned_course'] = 0;
                        $userData['assessor_id'] = $userDetail->assessor_id;
                        $userData['assessor_role'] = $userDetail->assessor_role;
                        $userData['get_stream_user_id'] = $userDetail->get_stream_user_id;
                        $userData['get_stream_token'] = $userDetail->get_stream_token;
                        $userData['data_view_permissions'] = (int)$dataViewPermissions;
                        $userData['qr_code'] = env('CDN_URL') . 'users/qrcode/' . $userDetail->id . '/' . $userDetail->qr_code;

                        $trainingCourseCount = 0;
                        $groupTrainingCourseIds = [];
                        $manualTrainingCourseIds = [];
                        if (!is_null($userDetail->userOperator->user_group_id)) {
                            $groupTrainingCourseIds = $userDetail->userOperator->group->assignCourses()->pluck('training_course_id')->toArray();
                        }
                        $manualTrainingCourseIds = $userDetail->userAssignedCourseOperator()->pluck('training_course_id')->toArray();
                        $trainingCourseCount = count(array_unique(array_merge($groupTrainingCourseIds, $manualTrainingCourseIds)));

                        $userData['total_assigned_course'] = $trainingCourseCount;

                        if (!empty($userDetail->userOperator) && $userDetail->userOperator->user_group_id != '') {
                            $userData['group_name'] = $userDetail->userOperator->group->name;
                        }
                    }

                    $message = !empty($userDetail) ? __('operator.FrontUser.found') : __('operator.FrontUser.notFound');
                    return response()->json(setResponse($userData, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
                } else {
                    return response()->json(setErrorResponse(__('You cannot access this record')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/users/assignedCourses",
     *     tags={"Operator - User"},
     *     summary="Get Assigned user courses",
     *     description="Get Assigned user courses",
     *     operationId="index",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="user_id",
     *                     description="User Id",
     *                     type="integer"
     *                 ),
     *                 example={"user_id": 1}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Role Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */

    public function assignedCourses(AssignedCourseDetailsRequest $request)
    {
        try {
            $userId = $request->user_id;
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;

            // Get Assigned Courses
            $trainingCourseIds = [];
            $groupTrainingCourseIds = [];
            $manualTrainingCourseIds = [];
            $user = User::find($userId);

            if(!is_null($user->userOperator->user_group_id) && $user->userOperator->group && $user->userOperator->group->assignCourses()){
                $groupTrainingCourseIds = $user->userOperator->group->assignCourses()->pluck('training_course_id')->toArray();
            }
            $manualTrainingCourseIds = UserAssignTrainingCourses::whereMasterUserId($operatorId)
                ->whereUserId($userId)->pluck('training_course_id')->toArray();
            $trainingCourseIds = array_unique(array_merge($groupTrainingCourseIds, $manualTrainingCourseIds));

            $trainingCourseList = [];
            $count = 0;
            if (count($trainingCourseIds) > 0) {

                $courseList = TrainingCourse::whereIn('id', $trainingCourseIds)->get();

                foreach ($courseList as $key => $course) {

                    $progressExist = TrainingCourseProgress::whereTrainingCourseId($course->id)
                        ->whereMasterUserId($operatorId)
                        ->whereUserId($userId)
                        ->first();
                    if ($progressExist && $progressExist->course) {
                        $trainingCourseList[$count]['course_id'] = $progressExist->course->id;
                        $trainingCourseList[$count]['course_name'] = $progressExist->course->title;
                        $trainingCourseList[$count]['image'] = $progressExist->course->primary_image_url;
                        $trainingCourseList[$count]['progress'] = $progressExist->course_progress;
                        if($progressExist->course_progress>=100)
                        {
                            $certificate=UserCourseCertificate::where('training_course_id',$progressExist->course->id)->where('user_id',$userId)->first();
                            if(!empty($certificate)){
                                $certificate_pdf=env('CDN_URL') .'user_course_certificate/'.$userId.'/'.$progressExist->course->id.'/'.$certificate->certificate_name;
                            }else{
                                $certificate_pdf=null;
                            }
                        }else{
                            $certificate_pdf=null;
                        }
                        $trainingCourseList[$count]['certificate_url'] = $certificate_pdf;

                        // Get Modules
                        $moduleList = TrainingCourseModuleProgress::whereTrainingCourseId($course->id)
                            ->whereMasterUserId($operatorId)
                            ->whereUserId($userId)
                            ->get();
                        if ($moduleList) {
                            $trainingCourseList[$count]['total_modules'] = count($moduleList);
                            $totalCompletedModuleCount = 0;
                            foreach ($moduleList as $k => $module) {
                                if ($module->module_progress >= 100) {
                                    $totalCompletedModuleCount++;
                                }
                                $trainingCourseList[$count]['modules'][$k]['module_id'] = $module->modules->id;
                                $trainingCourseList[$count]['modules'][$k]['module_name'] = $module->modules->name;
                                $trainingCourseList[$count]['modules'][$k]['progress'] = $module->module_progress;
                                $trainingCourseList[$count]['modules'][$k]['unlock'] = $this->getUserModuleLock($course->id, $module->modules->id, $userId, $module->modules);
                                $trainingCourseList[$count]['modules'][$k]['open'] = ($module->date_time != $module->created_at) ? 1 : 0;
                                $trainingCourseList[$count]['modules'][$k]['completed'] = ($module->module_progress == 100) ? 1 : 0;

                                // checking for assessment portal feature setting
                                $checkAssessment = checkAssessment();
                                // Get Sub Modules
                                $submoduleListNew = TrainingCourseSubmoduleProgress::query();
                                                    $submoduleListNew->whereTrainingCourseId($course->id);
                                                    $submoduleListNew->whereMasterUserId($operatorId);
                                                    $submoduleListNew->whereUserId($userId);
                                                    $submoduleListNew->whereModuleId($module->module_id);
                                                    if($checkAssessment == 1){
                                                        $submoduleListNew->where('submodule_type_id','!=',16);
                                                    }
                                $submoduleList =    $submoduleListNew->get();
                                if ($submoduleList) {
                                    $trainingCourseList[$count]['modules'][$k]['total_submodules'] = count($submoduleList);
                                    $trainingCourseList[$count]['modules'][$k]['pdf_links'] = [];
                                    $totalCompletedSubModuleCount = 0;
                                    foreach ($submoduleList as $kk => $submodule) {
                                        if ($submodule->submodule_progress >= 100) {
                                            $totalCompletedSubModuleCount++;
                                        }

                                        if ($submodule->submodule_type_id == 12) {
                                            $quiz = TrainingCourseSubModuleQuizResults::where([
                                                'training_course_id' => $submodule->training_course_id,
                                                'module_id' => $submodule->module_id,
                                                'submodule_id' => $submodule->submodule_id,
                                                'user_id' => $userId,
                                            ])->orderBy('id', 'DESC')->get();
                                            $result = TrainingCourseSubModuleQuizPdfResultResource::collection($quiz);
                                        }
                                        if ($submodule->submodule_type_id == 7) {
                                            $quiz2 = TrainingCourseSubModuleFeedbackPDFResults::where([
                                                'training_course_id' => $submodule->training_course_id,
                                                'module_id' => $submodule->module_id,
                                                'submodule_id' => $submodule->submodule_id,
                                                'user_id' => $userId,
                                            ])->orderBy('id', 'DESC')->get();
                                            $result2 = TrainingCourseSubModuleMiniQuizPdfResultResource::collection($quiz2);

                                        }

                                        /** Max attempts check start */
                                        $quiz = TrainingCourseSubModuleQuiz::where(['submodule_id' => $submodule->submodule_id])->first();
                                        $maxAttemptsQuiz = false;
                                        $lastattempt = TrainingCourseSubModuleQuizResults::where(['submodule_id' => $submodule->submodule_id, 'user_id' => $userId])->orderBy('id', 'DESC')->first();
                                        if (!empty($lastattempt)) {
                                            if ($lastattempt->is_pass != 1) {
                                                $countResult = TrainingCourseSubModuleQuizResults::where(['submodule_id' => $submodule->submodule_id, 'user_id' => $userId])->count();
                                                $maxAttempts = $quiz->max_attempts_before_fail;
                                                if ($maxAttempts == 0) {
                                                    $maxAttemptsQuiz = false;
                                                } else if ($countResult < $maxAttempts) {
                                                    $maxAttemptsQuiz = false;
                                                } else {
                                                    $maxAttemptsQuiz = true;
                                                }
                                            }
                                        } else {
                                            $maxAttemptsQuiz = false;
                                        }
                                        /** Max attempts check end */
                                        if($submodule->subModule){
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['submodule_id'] = $submodule->subModule->id;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['submodule_type_id'] = $submodule->subModule->submodule_type_id;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['submodule_name'] = $submodule->subModule->submodule_name;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['remove_progress_calculation'] = $submodule->subModule->remove_progress_calculation;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['progress'] = $submodule->submodule_progress;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['unlock'] = $this->getUserSubmoduleLock($course->id, $submodule->subModule->id, $userId, $submodule->subModule);
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['open'] = ($submodule->date_time != $submodule->created_at) ? 1 : 0;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['completed'] = ($submodule->submodule_progress == 100) ? 1 : 0;
                                        }
                                        if ($submodule->submodule_type_id == 12) {
                                            if (count($result->pluck('pdf_url')) > 0) {
                                                $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]
                                                ['pdf_links'] = $result->pluck('pdf_url');
                                                $trainingCourseList[$count]['modules'][$k]['pdf_links'] = array_merge($trainingCourseList[$count]['modules'][$k]['pdf_links'], $result->pluck('pdf_url')->toArray());
                                            }
                                            /** is_maximum_attempts true/false on the basis of max attempts set on quiz submodule */
                                            $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['is_maximum_attempts'] = $maxAttemptsQuiz;
                                        }

                                        if ($submodule->submodule_type_id == 7) {
                                            if (count($result2->pluck('pdf_url')) > 0) {
                                                $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]
                                                ['pdf_links'] = $result2->pluck('pdf_url');
                                                $trainingCourseList[$count]['modules'][$k]['pdf_links'] = array_merge($trainingCourseList[$count]['modules'][$k]['pdf_links'], $result2->pluck('pdf_url')->toArray());
                                            }
                                        }
                                    }
                                    $trainingCourseList[$count]['modules'][$k]['total_completed_submodules'] = $totalCompletedSubModuleCount;
                                } else {
                                    $trainingCourseList[$count]['modules'][$k]['total_submodules'] = 0;
                                    $trainingCourseList[$count]['modules'][$k]['total_completed_submodules'] = 0;
                                    $trainingCourseList[$count]['modules'][$k]['submodules'] = [];
                                }
                            }
                            $trainingCourseList[$count]['total_completed_modules'] = $totalCompletedModuleCount;
                        } else {
                            $trainingCourseList[$count]['total_modules'] = 0;
                            $trainingCourseList[$count]['total_completed_modules'] = 0;
                            $trainingCourseList[$count]['modules'] = [];
                        }
                    } else {

                        $trainingCourseList[$count]['course_id'] = $course->id;
                        $trainingCourseList[$count]['course_name'] = $course->title;
                        $trainingCourseList[$count]['image'] = $course->primary_image_url;
                        $trainingCourseList[$count]['progress'] = 0;
                        $trainingCourseList[$count]['certificate_url'] = null;
                        if ($course->modules) {
                            $trainingCourseList[$count]['total_modules'] = count($course->modules);
                            $trainingCourseList[$count]['total_completed_modules'] = 0;
                            foreach ($course->modules as $k => $module) {
                                $trainingCourseList[$count]['modules'][$k]['module_id'] = $module->id;
                                $trainingCourseList[$count]['modules'][$k]['module_name'] = $module->name;
                                $trainingCourseList[$count]['modules'][$k]['progress'] = 0;
                                $trainingCourseList[$count]['modules'][$k]['unlock'] = 0;
                                $trainingCourseList[$count]['modules'][$k]['open'] = 0;
                                $trainingCourseList[$count]['modules'][$k]['completed'] = 0;

                                // Get Sub Modules
                                if ($module->subModules) {
                                    $trainingCourseList[$count]['modules'][$k]['total_submodules'] = count($module->subModules);
                                    $trainingCourseList[$count]['modules'][$k]['total_completed_submodules'] = 0;
                                    foreach ($module->subModules as $kk => $submodule) {
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['submodule_id'] = $submodule->id;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['submodule_type_id'] = $submodule->submodule_type_id;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['submodule_name'] = $submodule->submodule_name;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['remove_progress_calculation'] = $submodule->remove_progress_calculation;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['progress'] = 0;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['unlock'] = 0;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['open'] = 0;
                                        $trainingCourseList[$count]['modules'][$k]['submodules'][$kk]['completed'] = 0;
                                    }
                                } else {
                                    $trainingCourseList[$count]['modules'][$k]['total_submodules'] = 0;
                                    $trainingCourseList[$count]['modules'][$k]['submodules'] = [];
                                    $trainingCourseList[$count]['modules'][$k]['total_completed_submodules'] = 0;
                                }
                            }
                        } else {
                            $trainingCourseList[$count]['modules'][$k]['total_submodules'] = 0;
                            $trainingCourseList[$count]['modules'][$k]['submodules'] = [];
                            $trainingCourseList[$count]['modules'][$k]['total_completed_submodules'] = 0;
                        }
                    }
                    $count++;
                }

                $trainingCourseList = count($trainingCourseList) > 0 ? array_values($trainingCourseList) : $trainingCourseList;
                $message = (count($trainingCourseList) > 0) ? __('operator.FrontUser.assignedCourseFound') : __('operator.FrontUser.assignedCourseNotFound');
                return response()->json(setResponse($trainingCourseList, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
            } else {
                return response()->json(setResponse([], ['message' => __('operator.FrontUser.assignedCourseNotFound')]))->setStatusCode(Response::HTTP_OK);
                // return response()->json(setErrorResponse(__('operator.FrontUser.assignedCourseNotFound')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    public function overRideCourseProgress(OverRideCourseProgressRequest $request)
    {
        try {
            $masterId=getOperatorId();
            $isExist = TrainingCourseProgress::whereUserId($request['user_id'])->whereTrainingCourseId($request['training_course_id'])->whereMasterUserId($masterId)->first();
            if($isExist){
                $isExist->update(['course_progress'=>100,'is_result_override'=>'OverRide','complete_date'=>date('Y-m-d H:i:s')]);
                $this->overRideModuleProgress($request['training_course_id'],$isExist->id,$masterId,$request['user_id'],100);
            }else{
                $this->addDefaultProgress($request['training_course_id'],$request['user_id'],$masterId,100);
            }
            return response()->json(setResponse([], ['message' => __('operator.FrontUser.override_progress')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    public function addDefaultProgress($trainingCourseId,$userId,$masterUserId,$progress){
        $courseEntry = new TrainingCourseProgress;
        $courseEntry->training_course_id = $trainingCourseId;
        $courseEntry->master_user_id = $masterUserId;
        $courseEntry->user_id = $userId;
        $courseEntry->course_progress = $progress;
        $courseEntry->date_time = date('Y-m-d H:i:s');
        $courseEntry->is_result_override = 'OverRide';
        $courseEntry->complete_date = date('Y-m-d H:i:s');
        $courseEntry->is_new = 0;
        $courseEntry->save();
        if($courseEntry){
            $this->overRideModuleProgress($trainingCourseId,$courseEntry->id,$masterUserId,$userId,100);
        }
    }
    public function overRideModuleProgress($trainingCourseId,$courseProgressId, $operatorId,$userId,$progress){
        $moduleList=TrainingCourseModules::select('id')->where('training_course_id',$trainingCourseId)->get('id')->toArray();
            foreach($moduleList as $module_value){
            $moduleExist=TrainingCourseModuleProgress::where('training_course_id',$trainingCourseId)->where('master_user_id',$operatorId)->where('module_id',$module_value['id'])->where('user_id',$userId)->first();
            if(empty($moduleExist)){
            $courseModuleEntry = new TrainingCourseModuleProgress;
            $courseModuleEntry->training_course_id = $trainingCourseId;
            $courseModuleEntry->course_progress_id = $courseProgressId;
            $courseModuleEntry->master_user_id = $operatorId;
            $courseModuleEntry->module_id = $module_value['id'];
            $courseModuleEntry->user_id = $userId;
            $courseModuleEntry->module_progress = $progress;
            $courseModuleEntry->is_result_override = 'OverRide';
            $courseModuleEntry->date_time = date('Y-m-d H:i:s');
            $courseModuleEntry->is_new = 0;
            $courseModuleEntry->save();
            $this->overRideSubmoduleProgress($trainingCourseId, $operatorId,$module_value['id'],$userId,$progress,$courseModuleEntry->id);
            }
            else{
                $moduleExist->update(['module_progress'=>100,'is_result_override'=>'OverRide']);
                $this->overRideSubmoduleProgress($trainingCourseId, $operatorId,$module_value['id'],$userId,$progress,$moduleExist->id);
            }

        }        
    }
    public function overRideSubmoduleProgress($training_course_id,$operatorId, $moduleId,$userId,$progress,$courseModuleProgressid){
        $subModuleList=TrainingCourseSubmoduleDetails::select('id','submodule_type_id')->where('module_id',$moduleId)->get()->toArray();
        foreach($subModuleList as $subModule_value){
            $submoduleExist=TrainingCourseSubmoduleProgress::where('training_course_id',$training_course_id)->where('master_user_id',$operatorId)->where('module_id',$moduleId)->where('submodule_id',$subModule_value['id'])->where('user_id',$userId)->first();
            if(empty($submoduleExist)){
                $subModuleDetail=TrainingCourseSubmoduleDetails::select('time_spent','touch_count','total_subdata','status','submodule_type_id')->where('id',$subModule_value['id'])->first()->toArray();
                $courseSubModuleEntry = new TrainingCourseSubmoduleProgress;
                $courseSubModuleEntry->training_course_id = $training_course_id;
                $courseSubModuleEntry->module_progress_id = $courseModuleProgressid;
                $courseSubModuleEntry->master_user_id = $operatorId;
                $courseSubModuleEntry->module_id = $moduleId;
                $courseSubModuleEntry->submodule_id = $subModule_value['id'];
                $courseSubModuleEntry->submodule_type_id = $subModuleDetail['submodule_type_id'];
                $courseSubModuleEntry->user_id = $userId;
                $courseSubModuleEntry->submodule_progress = $progress;
                $courseSubModuleEntry->time_spent = 0;
                $courseSubModuleEntry->total_spent = $subModuleDetail['time_spent'];
                $courseSubModuleEntry->touch_count = 0;
                $courseSubModuleEntry->total_touch_count = $subModuleDetail['touch_count'];
                $courseSubModuleEntry->total_child = $subModuleDetail['total_subdata'];
                $courseSubModuleEntry->visited_child = 0;
                $courseSubModuleEntry->date_time = date('Y-m-d H:i:s');
                $courseSubModuleEntry->is_new = 0;
                $courseSubModuleEntry->status = $subModuleDetail['status'];
                $courseSubModuleEntry->is_result_override = 'OverRide';
                $courseSubModuleEntry->save();
            }else{
                $submoduleExist->update(['submodule_progress'=>100,'is_result_override'=>'OverRide']);
            }

            /** Quiz N/A entries and PDF generate */
            if($subModule_value['submodule_type_id']==12){
                $indivudualResult = TrainingCourseSubModuleQuizResults::where('training_course_id',$training_course_id)
                                                    ->where('module_id',$moduleId)
                                                    ->where('submodule_id',$subModule_value['id'])
                                                    ->where('master_user_id',$operatorId)
                                                    ->where('user_id',$userId)
                                                    ->first();
                if(empty($indivudualResult)){
                    // Prepare the data for insertion
                    $newResultData = [
                        'training_course_id' => $training_course_id,
                        'module_id' => $moduleId,
                        'submodule_id' => $subModule_value['id'],
                        'master_user_id' => $operatorId,
                        'user_id' => $userId,
                        'is_pass' => 1,
                        'total_questions' => 0,
                        'correct_answers' => 0,
                        'passed_by_operator' => 1,
                        'duration' => 0,
                        'attempts' => 1,
                        'time_spent' => 0,
                        'is_result_override' => 1,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                    ];
                    // Insert the new Quiz result into the database
                    TrainingCourseSubModuleQuizResults::insert($newResultData);
                }
                
                $results = TrainingCourseSubModuleQuizResults::where(['master_user_id' => $operatorId])
                        ->where('user_id',$userId)
                        ->where(function($query) {
                            $query->where('pdf','=','')->orWhereNull('pdf');
                        })
                        ->get();
                if(!empty($results)){
                    foreach ($results as $key => $result) {
                        dispatch(new QuizResultPdfSendMailJob($result->id, $userId, $result->correct_answers, $result->user->user_relation,'OPERATOR',$result->late_submission));
                    }
                }
            }

            /** PA N/A entries */
            if($subModule_value['submodule_type_id']==16){
                $defaultAssessor = User::where('email','<EMAIL>')
                                    ->whereNull('deleted_at')
                                    ->first();
                if(!empty($defaultAssessor)){
                    $indivudualPAResult = TrainingCourseSubModulePracticalAssessmentResults::where('training_course_id',$training_course_id)
                                        ->where('module_id',$moduleId)
                                        ->where('submodule_id',$subModule_value['id'])
                                        ->where('master_user_id',$operatorId)
                                        ->where('user_id',$userId)
                                        ->where('assessor_id',$defaultAssessor->id)
                                        ->first();
                    if(empty($indivudualPAResult)){
                        // Prepare the data for insertion
                        $newPAResultData = [
                            'training_course_id' => $training_course_id,
                            'module_id' => $moduleId,
                            'submodule_id' => $subModule_value['id'],
                            'master_user_id' => $operatorId,
                            'assessor_id' => $defaultAssessor->id,
                            'user_id' => $userId,
                            'is_pass' => 1,
                            'time_spent' => '00:00',
                            'status' => 'Approved',
                            'is_mail_sent' => 0,
                            'is_result_override' => 1,
                            'adjust_result' => 'manual',
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s'),
                        ];
                        // Insert the new PA result into the database
                        TrainingCourseSubModulePracticalAssessmentResults::insert($newPAResultData);
                    }
                }
            }
        }        
    }
    public function getUserModuleLock($trainingCourseId, $moduleId, $userId, $currentModule)
    {
        try {
            $moduleLock = 0;
            // Check Prerequisite Modules progress
            $prerequisiteModules = PrerequisiteModules::where(['training_course_id' => $trainingCourseId, 'module_id' => $moduleId])->get();

            if (count($prerequisiteModules)) {
                $moduleProgress = [];
                foreach ($prerequisiteModules as $modules) {
                    $moduleProgress[] = TrainingCourseModuleProgress::where(['training_course_id' => $modules->training_course_id, 'module_id' => $modules->prerequisite_module_id, 'user_id' => $userId])->where('module_progress', '>=', $modules->percentage)->count();
                }
                $moduleLock = in_array(0, $moduleProgress) ? 1 : 0;
            }

            if ($moduleLock == 0) {
                if ($currentModule->module_lock == 1) {
                    if ($currentModule->module_complete == 1) {
                        $data = ['duration' => $currentModule->duration, 'type' => $currentModule->duration_type];
                        $dateTime = TrainingCourseModuleProgress::where(['training_course_id' => $currentModule->training_course_id])->pluck('date_time')->first();

                        $data['date_time'] = (!is_null($dateTime) ? $dateTime : $currentModule->created_at);
                        $durationDate = addLockDurationToDate($data);
                        $moduleLock = ($durationDate <= Carbon::now() ? 0 : 1);
                    } else {
                        $moduleLock = ($currentModule->unlock_datetime <= Carbon::now() ? 0 : 1);
                    }
                } else {
                    $moduleLock = $currentModule->module_lock;
                }
            }
            return $moduleLock;
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function getUserSubmoduleLock($trainingCourseId, $subModuleId, $userId, $currentSubModule)
    {
        try {
            $subModuleLock = 0;
            // Check Prerequisite Submodules progress
            $prerequisiteSubModules = PrerequisiteSubModules::where(['training_course_id' => $trainingCourseId, 'submodule_id' => $subModuleId])->get();
            if (count($prerequisiteSubModules)) {
                $subModuleProgress = [];
                foreach ($prerequisiteSubModules as $subModules) {
                    $subModuleProgress[] = TrainingCourseSubmoduleProgress::where(['training_course_id' => $subModules->training_course_id, 'submodule_id' => $subModules->prerequisite_submodule_id, 'user_id' => $userId])->where('submodule_progress', '>=', $subModules->percentage)->count();
                }
                $subModuleLock = in_array(0, $subModuleProgress) ? 1 : 0;

            }
            if ($subModuleLock == 0) {
                if ($currentSubModule->submodule_lock == 1) {
                    if ($currentSubModule->submodule_complete == 1) {
                        $data = ['duration' => $currentSubModule->duration, 'type' => $currentSubModule->duration_type];
                        $dateTime = TrainingCourseProgress::where(['training_course_id' => $currentSubModule->training_course_id, 'user_id' => $userId])->pluck('date_time')->first();
                        $data['date_time'] = (!is_null($dateTime) ? $dateTime : $currentSubModule->created_at);
                        $durationDate = addLockDurationToDate($data);
                        $subModuleLock = ($durationDate <= Carbon::now() ? 0 : 1);
                    } else {
                        $subModuleLock = ($currentSubModule->unlock_datetime <= Carbon::now() ? 0 : 1);
                    }
                } else {
                    $subModuleLock = $currentSubModule->submodule_lock;
                }
            }
            return $subModuleLock;
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/users/verifyRegisteredUser",
     *     tags={"Operator - User"},
     *     summary="verify Registered User By Operator Panel",
     *     description="verify Registered User By Operator Panel",
     *     operationId="index",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="user_id",
     *                     description="User Id",
     *                     type="integer"
     *                 ),
     *                 example={"user_id": 1}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Role Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */

    public function verifyRegisteredUser(VerifyRegisteredUser $request)
    {
        try {
            $result = $this->model::where('id', '=', $request->user_id)->update(['status' => 'Active', 'email_verified_at' => date('Y-m-d h:i:s')]);
            if ($result) {
                $user = $this->model::find($request->user_id);
                //Checking for email address if smartawards contains only digits before @
                $smartAwardsMailSendFlag = smartAwardsEmailSendResctrict($user->email);
                if($smartAwardsMailSendFlag == 1){
                    Notification::send($user, new VerifyRegisteredUserNotification($user, 'Operator'));
                }
            }
            return response()->json(setResponse([], ['message' => __('operator.FrontUser.verified')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    /**
     * @OA\Post(
     *     path="/operatorSmartAward/user",
     *     tags={"Operator - User Management for Smart Awards"},
     *     summary="Add user by Smart Awards",
     *     description="Add user by Smart Awards",
     *     operationId="store",
     *     @OA\Parameter(
     *         name="api-key",
     *         in="header",
     *         description="Operator API Key",
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="User Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="email",
     *                     description="User Email",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="User Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={"name":"lita","email":"<EMAIL>","status":"Active"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="User Account created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function storeSmartAwardUser(FrontUserSmartAwardRequest $request)
    {
        try {
            $executionTime = microtime(true) - LUMEN_START;
            $moduleData = $request;
            $result = $this->user_repository->createSmartAwardUser($moduleData);
            if (isset($result['response_type']) && $result['response_type'] == "Error") {
                JsonRequestSubmit('64',$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$result,$result['message'],$executionTime);
                return response()->json(setErrorResponse($result['message']))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else {
                $retunData = array('id' => $result->id);
                JsonRequestSubmit('64',$request->all(),$status=200,$retunData,$error=null,$executionTime);
                return response()->json(setResponse($retunData, ['message' => __('operator.FrontUser.addUser')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            $executionTime = microtime(true) - LUMEN_START;
            JsonRequestSubmit('64',$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$response=null,$e->getMessage(),$executionTime);
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Put(
     *     path="/operatorSmartAward/changeUserEmail",
     *     tags={"Operator - User Management for Smart Awards"},
     *     summary="Change email address of smart award users",
     *     description="Change email address of smart award users",
     *     operationId="changeUserEmail",
     *     @OA\Parameter(
     *         name="api-key",
     *         in="header",
     *         description="Operator API Key",
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="current_email_address",
     *                     description="User current email address",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="new_email_address",
     *                     description="User new email address",
     *                     type="string"
     *                 ),
     *                 example={"current_email_address":"<EMAIL>","new_email_address":"<EMAIL>"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="User Account email changed successfully."),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function changeSmartAwardUserEmail(FrontUserUpdateSmartAwardRequest $request)
    {
        try {
            $executionTime = microtime(true) - LUMEN_START;
            $response = $this->user_repository->changeSmartAwardUserEmail($request);
            if (isset($response['message']) && $response['message'] != "") {
                JsonRequestSubmit('64',$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$response,$response['message'],$executionTime);
                return response()->json(setErrorResponse($response['message']))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else {
                $data['email'] = $request->new_email_address;
                JsonRequestSubmit('64',$request->all(),Response::HTTP_OK,$response,$error=null,$executionTime);
                return response()->json(setResponse($data, ['message' => __('operator.FrontUser.updateEmail')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            $executionTime = microtime(true) - LUMEN_START;
            JsonRequestSubmit('64',$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$response=null,$e->getMessage(),$executionTime);
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Put(
     *     path="/operatorSmartAward/setUserPassword",
     *     tags={"Operator - User Management for Smart Awards"},
     *     summary="Change password of smart award users",
     *     description="Change password of smart award users",
     *     operationId="setUserPassword",
     *     @OA\Parameter(
     *         name="api-key",
     *         in="header",
     *         description="Operator API Key",
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="email",
     *                     description="User email address",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="password",
     *                     description="User password",
     *                     type="string"
     *                 ),
     *                 example={"email":"<EMAIL>","password":"Test@12345"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="User Account password changed successfully."),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function setSmartAwardUserPassword(ChangePasswordSmartAwardRequest $request)
    {
        try {
            $executionTime = microtime(true) - LUMEN_START;
            $response = $this->user_repository->setSmartAwardUserPassword($request);
            if (isset($response['message']) && $response['message'] != "") {
                JsonRequestSubmit('64',$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$response,$result['message'],$executionTime);
                return response()->json(setErrorResponse($response['message']))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else {
                $data['email'] = $request->email;
                JsonRequestSubmit('64',$request->all(),Response::HTTP_OK,$response,$error=null,$executionTime);
                return response()->json(setResponse($data, ['message' => __('operator.FrontUser.updatePassword')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            $executionTime = microtime(true) - LUMEN_START;
            JsonRequestSubmit('64',$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$error=null,$e->getMessage(),$executionTime);
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operatorSmartAward/users/assignedCourses",
     *     tags={"Operator - User Management for Smart Awards"},
     *     summary="Register User for Training Course",
     *     description="Register User for Training Course",
     *     operationId="assignedCourses",
     *     @OA\Parameter(
     *         name="api-key",
     *         in="header",
     *         description="Operator API Key",
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="user_email",
     *                     description="User Email",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="training_course_ids",
     *                     description="Training course ids",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 example={"user_email" : "<EMAIL>","training_course_ids" : {158,160,161}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="User Account assigned to Training course IDs successfully"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Training Course ID not found"),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function smartAwardsAssignedCourses(AssignedCourseDetailsSmartAwardRequest $request)
    {
        try {
            $executionTime = microtime(true) - LUMEN_START;
            $trainingCourseIds = $request->training_course_ids;
            $master_user_id = config('constants.smart_award_operator_id');
            $existUser = UserRelation::whereMasterUserId($master_user_id)->get()->pluck('user_id')->toArray();

            $userData = User::whereEmail($request->user_email)->first();
            if (!empty($userData)) {
                if(in_array($userData->id,$existUser)){
                    $user_id = $userData->id;
                    if (count($trainingCourseIds) > 0) {
                        /** Checking for the trainig courses not exists in table or belongs to another operator **/
                        $training_course_ids = TrainingCourse::whereIn('id', $trainingCourseIds)
                            ->where('master_user_id', $master_user_id)
                            ->where('status', 'Active')
                            ->whereNull('deleted_at')
                            ->pluck('id')->toArray();
                        $not_exist_temp_ids = [];
                        foreach ($trainingCourseIds as $key => $value) {
                            if (!in_array($value, $training_course_ids)) {
                                $not_exist_temp_ids[$key] = $value;
                            }
                        }

                        if (!empty($not_exist_temp_ids)) {
                            $not_exist_temp_ids = implode(',', $not_exist_temp_ids);
                            $errormessage = "Training Course ID " . $not_exist_temp_ids . " not found";
                            JsonRequestSubmit($master_user_id,$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$response=null,$errormessage,$executionTime);
                            return response()->json(setErrorResponse($errormessage))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                        }
                        /** Checking for the trainig courses not exists in table or belongs to another operator **/

                        /** For default training course ids assigned to user **/
                        $default_training_course_ids = TrainingCourse::whereIn('id', $trainingCourseIds)
                            ->where('master_user_id', $master_user_id)
                            ->where('status', 'Active')
                            ->where('is_default', 1)
                            ->whereNull('deleted_at')
                            ->pluck('id')->toArray();
                        $default_temp_ids = [];
                        foreach ($default_training_course_ids as $key => $default_ids) {
                            if (in_array($default_ids, $trainingCourseIds)) {
                                $default_temp_ids[$key] = $default_ids;
                            }
                        }

                        if (!empty($default_temp_ids)) {
                            $def_ids = implode(',', $default_temp_ids);
                            $message = "User Account already assigned to the default Training course ID " . $def_ids;
                            JsonRequestSubmit($master_user_id,$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$response=null,$message,$executionTime);
                            return response()->json(setErrorResponse($message))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                        }
                        /** For default training course ids assigned to user **/

                        /** Training course assignment process to user **/
                        $training_course_ids = TrainingCourse::whereIn('id', $trainingCourseIds)
                            ->where('master_user_id', $master_user_id)
                            ->where('status', 'Active')
                            ->where('is_default', 0)
                            ->whereNull('deleted_at')
                            ->pluck('id')->toArray();

                        //delete existing assigned course
                        $oldCourse = UserAssignTrainingCourses::where(['user_id' => $user_id, 'master_user_id' => $master_user_id])->get();
                        foreach ($training_course_ids as $key => $courseId) {
                            $assignmentDetails = UserAssignTrainingCourses::where(['user_id' => $user_id, 'master_user_id' => $master_user_id])->where('training_course_id',$courseId)->where('is_default_course', '!=', '1')->first();
                            if(empty($assignmentDetails)){
                                $newData                       = [];
                                $newData['master_user_id']     = $master_user_id;
                                $newData['user_id']            = $user_id;
                                $newData['training_course_id'] = $courseId;
                                $newData['is_manual_course']   = 1;
                                if (!is_null($user_id)) {
                                    $courseExist = UserAssignTrainingCourses::where(['user_id' => $user_id, 'master_user_id' => $master_user_id, 'training_course_id' => $courseId])->first();
                                    if (empty($courseExist)) {
                                        UserAssignTrainingCourses::create($newData);
                                    }
                                }
                            }
                        }
                        /** Training course assignment process to user **/

                        $userDetails = User::find($user_id);
                        // Send Push notifications

                        if (!$oldCourse->isEmpty()) {
                            $oldIds = $oldCourse->pluck('training_course_id')->toArray();
                            $newIds = $training_course_ids;
                            $notificationListIds = array_diff($newIds, array_values($oldIds));
                            if (count($notificationListIds) > 0) {
                                dispatch((new \App\Jobs\CourseNotificationJob($userDetails, $master_user_id, array_values($notificationListIds))));
                            }
                        } else {

                            $defaultCourse = UserAssignTrainingCourses::where(['user_id' => $user_id, 'master_user_id' => $master_user_id])->where('is_default_course', '=', '1')->get();

                            if (!$defaultCourse->isEmpty()) {
                                $arrayDiff = array_diff($training_course_ids, array_values($defaultCourse->pluck('training_course_id')->toArray()));
                                if (!empty($arrayDiff)) {
                                    dispatch((new \App\Jobs\CourseNotificationJob($userDetails, $master_user_id, array_values($arrayDiff))));
                                }
                            } else {
                                dispatch((new \App\Jobs\CourseNotificationJob($userDetails, $master_user_id, $training_course_ids)));
                            }
                        }
                    } else {
                        UserAssignTrainingCourses::where(['user_id' => $user_id, 'master_user_id' => $master_user_id]) /*->where('is_default_course', '!=', '1')*/->delete();
                    }

                    $tIds = implode(',', $training_course_ids);
                    $data['user_email'] = $request->user_email;
                    $message = "User Account assigned to Training course ID " . $tIds . " successfully.";
                    JsonRequestSubmit($master_user_id,$request->all(),Response::HTTP_OK,$data,$message,$executionTime);
                    return response()->json(setResponse($data, ['message' => $message]))->setStatusCode(Response::HTTP_OK);

                }else{
                    JsonRequestSubmit($master_user_id,$request->all(),Response::HTTP_OK,$data,$message,$executionTime);
                    return response()->json(setErrorResponse(__('operator.FrontUser.notFound')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }


            } else {
                return response()->json(setErrorResponse(__('operator.FrontUser.notFound')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $e) {
            $executionTime = microtime(true) - LUMEN_START;
            $master_user_id = config('constants.smart_award_operator_id');
            JsonRequestSubmit($master_user_id,$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$data=null,$e->getMessage(),$executionTime);
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Store Training Course Submodule Quiz Results
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function smartAwardsStoreQuizResult(StoreQuizResultSmartAwardRequest $request)
    {
        try {
            $executionTime = microtime(true) - LUMEN_START;
            $data = $request->all();
            $master_user_id = config('constants.smart_award_operator_id');
            $user = User::where('email', $data['email_address'])->where('added_user_id', $master_user_id)->first();
            if ($user) {
                if ($user->user_relation) {
                    if ($user->user_relation->is_disabled) {
                        JsonRequestSubmit($master_user_id,$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$data=null,$message='Account is Disable',$executionTime);
                        return response()->json(setErrorResponse(__('operator.FrontUser.accountdisabled')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                    } elseif ($user->status == 'Active') {

                        $training_course = TrainingCourse::where(['id' => $request->training_course_id, 'master_user_id' => $master_user_id, 'status' => 'Active'])
                            ->whereNull('deleted_at')
                            ->first();

                        // Check training course exists
                        if ($training_course) {

                            $submoduleDetails = TrainingCourseSubmoduleDetails::where('training_course_id', $training_course->id)
                                ->where('submodule_type_id', 12)
                                ->where('status', 'Active')
                                ->first();

                            // check submodule exists
                            if ($submoduleDetails) {

                                $userAssignTrainingCourse = UserAssignTrainingCourses::where('user_id', $user->id)->whereMasterUserId($master_user_id)->where('training_course_id', $training_course->id)->first();

                                //check training course assined to user
                                if ($userAssignTrainingCourse) {
                                    $quizResult = TrainingCourseSubModuleQuizResults::where(['submodule_id' => $submoduleDetails->id, 'user_id' => $user->id])->orderBy('attempts', 'DESC')->first();
                                    if (!is_null($quizResult) && $quizResult->passed_by_operator == 1) {
                                        JsonRequestSubmit($master_user_id,$request->all(),Response::HTTP_OK,$quizResult,('user.TrainingCourseSubmodule.quiz-result.passed-operator'),$executionTime);
                                        return response()->json(setResponse([], ['status' => 1, 'message' => __('user.TrainingCourseSubmodule.quiz-result.passed-operator')]))->setStatusCode(Response::HTTP_OK);
                                    } else {
                                        $quiz = TrainingCourseSubModuleQuiz::where(['submodule_id' => $submoduleDetails->id])->where('created_by', $master_user_id)->first();

                                        // Check quiz exists
                                        if ($quiz) {
                                            $totalQuestions = $quiz->categories->count();

                                            if ($request->total_questions != $totalQuestions) {
                                                $message = "Total questions is incorrect. There is " . $totalQuestions . " questions in this quiz";
                                                JsonRequestSubmit($master_user_id,$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$totalQuestions,$message,$executionTime);
                                                return response()->json(setErrorResponse(__($message)))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                                            } else {
                                                $res['submodule_id'] = $submoduleDetails->id;
                                                $res['is_pass'] = $data['pass'];
                                                $res['correct_answers'] = $data['result'];
                                                $res['total_questions'] = $data['total_questions'];
                                                $res['training_course_id'] = $submoduleDetails->training_course_id;
                                                $res['module_id'] = $submoduleDetails->module_id;
                                                $res['user_id'] = $user->id;
                                                $res['master_user_id'] = $master_user_id;
                                                $res['time_spent'] = 0;

                                                $passingCriteria = $quiz->passing_criteria;

                                                $quizPassCount = (int) round(($totalQuestions * $passingCriteria) / 100);
                                                $totalCorrectAnswers = $res['correct_answers'];

                                                $previousAttempt = TrainingCourseSubModuleQuizResults::where(['training_course_id' => $submoduleDetails->training_course_id, 'submodule_id' => $submoduleDetails->id, 'user_id' => $user->id])->select('attempts', 'is_pass')->orderBy('attempts', 'desc')->first();
                                                $res['attempts'] = is_null($previousAttempt) ? 1 : $previousAttempt->attempts + 1;

                                                if (!empty($previousAttempt) && $previousAttempt->is_pass == 1) {
                                                    JsonRequestSubmit($master_user_id,$request->all(),Response::HTTP_OK,$previousAttempt,$message="You have passed this quiz.",$executionTime);
                                                    return response()->json(setResponse([], ["message" => "You have passed this quiz."]))->setStatusCode(Response::HTTP_OK);
                                                } else {
                                                    $result = TrainingCourseSubModuleQuizResults::create($res);

                                                    // Progress Calculation
                                                    if ($submoduleDetails->enable_time_spend == 1) {

                                                        // Check submodule progress
                                                        $isExist = TrainingCourseSubmoduleProgress::status()->where(['user_id' => $user->id, 'training_course_id' => $data['training_course_id'], 'module_id' => $res['module_id'], 'submodule_id' => $res['submodule_id'], 'submodule_type_id' => 12])->first();
                                                        $res['time_spent'] = (!is_null($isExist) ? ($isExist->time_spent + $res['time_spent']) : $res['time_spent']);

                                                        $res['completion_percentage'] = ($result->is_pass == 1 ? $submoduleDetails->completion_percentage : 0);

                                                        // Quiz Progress
                                                        if ($submoduleDetails->condition == 'and') {
                                                            if ($res['time_spent'] >= $submoduleDetails->time_spent && $data['completion_percentage'] == $submoduleDetails->completion_percentage) {
                                                                $progress = 100;
                                                            } elseif ($data['completion_percentage'] == 100 && $data['time_spent'] >= $submoduleDetails->time_spent) {
                                                                $progress = 100;
                                                            } else {
                                                                $timeSpent = (int) ((100 * $res['time_spent']) / $submoduleDetails->time_spent);
                                                                $progress = (int) (($timeSpent + $res['completion_percentage']) / 2);
                                                            }
                                                        } else {
                                                            $timeSpent = (int) ((100 * $res['time_spent']) / $submoduleDetails->time_spent);
                                                            $progress = max($res['completion_percentage'], $timeSpent);
                                                        }
                                                        $submoduleProgress = $progress;
                                                    } else {
                                                        $submoduleProgress = ($totalCorrectAnswers >= $quizPassCount ? 100 : (int) (($totalCorrectAnswers / $totalQuestions) * 100));
                                                    }

                                                    // Submodule Update progress
                                                    $progressData = ['user_id' => $user->id, 'training_course_id' => $res['training_course_id'], 'module_id' => $res['module_id'], 'submodule_id' => $res['submodule_id'], 'submodule_type_id' => 12, 'time_spent' => $res['time_spent'], 'submodule_progress' => 10];

                                                    (new TrainingCourseSubmoduleProgress)->updateSubmoduleData($progressData);

                                                    // Module progress
                                                    (new TrainingCourseModuleProgress)->calculateModuleProgress($res);

                                                    // Training course progress
                                                    (new TrainingCourseProgress)->calculateTrainingCourseProgress($res);

                                                    // Update New flag
                                                    (new TrainingCourseModuleProgress)->updateIsNew($progressData);
                                                    (new TrainingCourseProgress)->updateIsNew($progressData);
                                                    JsonRequestSubmit($master_user_id,$request->all(),Response::HTTP_OK,$response=null,$message="Quiz submitted successfully.",$executionTime);
                                                    return response()->json(setResponse([], ["message" => "Quiz submitted successfully."]))->setStatusCode(Response::HTTP_OK);
                                                }
                                            }

                                        } else {
                                            JsonRequestSubmit($master_user_id,$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$response=null,('operator.quiz.notFound'),$executionTime);
                                            return response()->json(setErrorResponse(__('operator.quiz.notFound')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                                        }
                                    }
                                } else {
                                    JsonRequestSubmit($master_user_id,$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$response=null,('operator.TrainingCourse.notAssignCourse'),$executionTime);
                                    return response()->json(setErrorResponse(__('operator.TrainingCourse.notAssignCourse')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                                }
                            } else {
                                JsonRequestSubmit($master_user_id,$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$response=null,('operator.TrainingCourse.moduleDosentExist'),$executionTime);
                                return response()->json(setErrorResponse(__('operator.TrainingCourse.moduleDosentExist')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                            }

                        } else {
                            JsonRequestSubmit($master_user_id,$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$response=null,('operator.TrainingCourse.dosentExist'),$executionTime);
                            return response()->json(setErrorResponse(__('operator.TrainingCourse.dosentExist')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                        }
                    } elseif ($user->status == 'Pending') {
                        JsonRequestSubmit($master_user_id,$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$response=null,('operator.FrontUser.accountPending'),$executionTime);
                        return response()->json(setErrorResponse(__('operator.FrontUser.accountPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                    } elseif (is_null($user->email_verified_at)) {
                        JsonRequestSubmit($master_user_id,$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$response=null,('operator.FrontUser.verificationPending'),$executionTime);
                        return response()->json(setErrorResponse(__('operator.FrontUser.verificationPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                    } elseif ($user->status == 'Inactive') {
                        JsonRequestSubmit($master_user_id,$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$response=null,('operator.FrontUser.accountInactive'),$executionTime);
                        return response()->json(setErrorResponse(__('operator.FrontUser.accountInactive')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                    } elseif ($user->status == 'Rejected') {
                        JsonRequestSubmit($master_user_id,$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$response=null,('operator.FrontUser.accountRejected'),$executionTime);
                        return response()->json(setErrorResponse(__('operator.FrontUser.accountRejected')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                    }
                } else {
                    JsonRequestSubmit($master_user_id,$request->all(),Response::HTTP_NOT_FOUND,$response=null,('operator.FrontUser.notFound'),$executionTime);
                    return response()->json(setErrorResponse(__('operator.FrontUser.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
            } else {
                JsonRequestSubmit($master_user_id,$request->all(),Response::HTTP_NOT_FOUND,$response=null,('operator.FrontUser.notFound'),$executionTime);
                return response()->json(setErrorResponse(__('operator.FrontUser.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            $executionTime = microtime(true) - LUMEN_START;
            $operatorId = config('constants.smart_award_operator_id');
            JsonRequestSubmit($operatorId,$request->all(),Response::HTTP_UNPROCESSABLE_ENTITY,$response=null,$e->getMessage(),$executionTime);
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }

    }
    /**
     * Get Assessor User List
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getAssessorUsersList(CommonListingRequest $request)
    {
        try {
            $users = $this->user_repository->getAssessorUsersList($request->all());
            if ($request->isExport) {
                return $this->user_repository->AssessorExportCsv($users->get(), $request->exportFields);
            }
            return FrontUserResource::collection($users->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Assessor User List
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function AssignAssessor(AssignAssessorRequest $request)
    {
        try {
            $this->user_repository->AssignAssessor($request->all());
            return response()->json(setResponse($request->all(), ['message' => 'Assessor ID Assign Successfully']))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Assessor User List
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getAssessorAssignFrontUsersList(AssessorAssignFrontUsersRequest $request)
    {
        try {
            $users = $this->user_repository->getAssessorAssignFrontUsersList($request->all());
            return AssessorAssignFrontUserResource::collection($users->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
