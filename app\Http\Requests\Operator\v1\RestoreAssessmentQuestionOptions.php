<?php

namespace App\Http\Requests\Operator\v1;

use App\Http\Requests\CustomFormRequest;
use DBTableNames;


class RestoreAssessmentQuestionOptions extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array
    {

        return [
            'question_id' => 'required|int|exists:'.\DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_QUESTIONS.',id,deleted_at,NULL|check_assessment_question_type',
        ];
    }
    
    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'question_id.required' => 'The question ID field is required.',
            'question_id.int' => 'The question ID must be an integer.',
            'question_id.exists' => 'The selected question ID is invalid or has been deleted.',
            'check_assessment_question_type' => "Assessment question type is not Calculated Single"
        ];
    }
}
