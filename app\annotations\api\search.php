<?php
/**
     * @OA\Post(
     *     path="/api/search",
     *     tags={"Mobile - Search"},
     *     summary="Search among Products, News, Resources and Training Course",
     *     description="Search among Products, News, Resources and Training Course",
     *     operationId="index",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="search",
     *                     description="Search string",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filter_by",
     *                     description="Filter By",
     *                     type="array",
     *                     @OA\Items(),
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */