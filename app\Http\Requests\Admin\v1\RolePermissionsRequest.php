<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;

class RolePermissionsRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {
        return [
            "role_id" => 'required',
            "module_name" => 'required',
        ];
    }

    public function messages() : array
    {
        return [
            'role_id.required' => __('The role id is required.'),
            'module_name.required' => __('The module name is required.'),
        ];
    }
}
