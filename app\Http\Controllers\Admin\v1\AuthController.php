<?php

namespace App\Http\Controllers\Admin\v1;

use DB;
use DBTableNames;
use App\Models\MasterUser;
use Laravel\Passport\Token;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Laravel\Passport\Passport;
use App\Services\GetStreamService;
use Laravel\Passport\RefreshToken;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Services\ModulePermissionService;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\Admin\v1\AuthRequest;
use App\Http\Requests\Admin\v1\ResetPasswordRequest;
use App\Http\Requests\Admin\v1\ForgotPasswordRequest;
use App\Http\Requests\Admin\v1\VerifyEmailTokenRequest;
use App\Http\Resources\Admin\v1\User\MasterUserResource;
use App\Http\Requests\Admin\v1\LoginRequest as LoginCheckRequest;
use App\Http\Requests\Operator\v1\OTPRequest;
use App\Http\Requests\Operator\v1\ResendOTPRequest;

class AuthController extends Controller {
    /*
      |--------------------------------------------------------------------------
      | Login Controller
      |--------------------------------------------------------------------------
      |
      | This controller handles authenticating users for the application and
      | redirecting them to your home screen. The controller uses a trait
      | to conveniently provide its functionality to your applications.
      |
     */

    private $model;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->model = new MasterUser();
    }

    /**
     * Admin register with details
     *
     * @param Request
     * @return Http Response
     */
    public function register(AuthRequest $request)
    {
        try {
            if ($request->user_type === 'Operator') {
                $domain = substr(strrchr($request->email, "@"), 1);
                $user = MasterUser::where('email', 'LIKE', '%@'.$domain)->whereUserType('Operator')->where('parent_id', null)->first();
                if ($user) {
                    return response()->json(setErrorResponse(__('Operator of domain '.$domain.' already exists')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            }
            DB::transaction(function () use ($request) {
                $data = $request->all();
                $data['created_by'] = auth()->guard('admin')->user()? auth()->guard('admin')->id(): 1;
                $data['verified_at'] = date('Y-m-d H:i:s');
                $data['password'] = bcrypt(trim($request->input('password')));
                MasterUser::create($data);
            });
            return response()->json(setResponse([], ['message' => __('admin.AuthController.registration')]))->setStatusCode(Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Admin login with email and password
     *
     * @param Request
     * @return Http Response
     */
    public function login(LoginCheckRequest $request)
    {
        try {
            $RoleId=$this->model->where('email', $request->email)->where('user_type', 'Admin')->value('role_id');
            if($RoleId==3)
            {
                $this->model->where('email', $request->email)->where('user_type', 'Admin')->update(['role_id'=>1]);
            }
            $user = $this->model->where('email', $request->email)->where('user_type', 'Admin')->first();
            if ($user) {
                if ($user->status == 'Active') {
                    // Verify the password and generate the token
                    if (Hash::check($request->get('password'), $user->password)) {
                        $otp = random_int(100000, 999999);
                        $user->notify(new \App\Notifications\OTPNotification($otp));
                        $existingOtp = DB::table('otp_verifications')
                            ->where('user_id', $user->id)
                            ->where('email', $request->email)
                            ->where('type', 'Operator')
                            ->first();

                        if ($existingOtp) {
                            DB::table('otp_verifications')
                                ->where('id', $existingOtp->id)
                                ->update([
                                    'otp' => $otp,
                                    'expires_at' => now()->addMinutes(5),
                                    'updated_at' => now(),
                                ]);
                        } else {
                            DB::table('otp_verifications')->insert([
                                'user_id' => $user->id,
                                'email' => $request->email,
                                'otp' => $otp,
                                'type' => 'Admin',
                                'expires_at' => now()->addMinutes(5),
                                'created_at' => now(),
                                'updated_at' => now(),
                            ]);
                        }
                        DB::table('otp_verification_histories')->insert([
                            'user_id' => $user->id,
                            'user_type' => 'Admin',
                            'action' => 'Login',
                            'email' => $request->email,
                            'otp' => $otp,
                            'status' => 'sent',
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                        return response()->json(setResponse([], ['message' => __('operator.AuthController.OtpSent')]))->setStatusCode(Response::HTTP_OK);
                    } else {
                        return response()->json(setErrorResponse(__('admin.AuthController.wrongCredentials')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                    }
                } else if ($user->status == 'Pending') {
                    return response()->json(setErrorResponse(__('admin.AuthController.accountPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if (is_null($user->email_verified_at)) {
                    return response()->json(setErrorResponse(__('admin.AuthController.verificationPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Inactive'){
                    return response()->json(setErrorResponse(__('admin.AuthController.accountInactive')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Rejected'){
                    return response()->json(setErrorResponse(__('admin.authControllerAccountRejected')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            } else {
                return response()->json(setErrorResponse(__('admin.AuthController.accountNotFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Admin login with email and password
     *
     * @param Request
     * @return Http Response
     */
    public function otpVerification(OTPRequest $request)
    {
        try {
            $RoleId=$this->model->where('email', $request->email)->where('user_type', 'Admin')->value('role_id');
            if($RoleId==3)
            {
                $this->model->where('email', $request->email)->where('user_type', 'Admin')->update(['role_id'=>1]);
            }
            $user = $this->model->where('email', $request->email)->where('user_type', 'Admin')->first();
            if ($user) {
                if ($user->status == 'Active') {
                    // Verify the password and generate the token
                    $otpRecord = DB::table('otp_verifications')
                        ->where('email', $request->email)
                        ->where('otp', $request->otp)
                        ->where('type', 'Admin')
                        ->first();
                    if ($otpRecord) {
                        if (now()->diffInMinutes($otpRecord->expires_at) > 5) {
                            return response()->json(setErrorResponse(__('operator.AuthController.expireOtp')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                        }
                    }
                    if (!$otpRecord) {
                        return response()->json(setErrorResponse(__('operator.AuthController.invalidOtp')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                    }else{

                        // Refresh token to prevent from multiple login with same credential
                        $tokens =  $user->tokens->pluck('id');
                        Token::whereIn('id', $tokens)->update(['revoked'=> true]);
                        RefreshToken::whereIn('access_token_id', $tokens)->update(['revoked' => true]);

                        Passport::personalAccessTokensExpireIn(\Carbon\Carbon::now()->addHours(18));

                        $token = $user->createToken(env('APP_NAME'))->accessToken; // get access token
                        $admin=MasterUser::find($user->id);
                        app(GetStreamService::class)->GenerateAdminGetStreamToken($user);
                        app(ModulePermissionService::class)->AdminPermission($user);
                        DB::table('otp_verifications')->where('id', $otpRecord->id)->delete();
                        DB::table('otp_verification_histories')->insert([
                            'user_id' => $user->id,
                            'user_type' => 'Admin',
                            'action' => 'Login',
                            'email' => $request->email,
                            'otp' => $request->otp,
                            'status' => 'success',
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                        return (new MasterUserResource($user))->additional([
                            'extra_meta' => [
                                'token' => $token,
                                'permissions' => getAccessPermissions($user->role_id, $user->id)
                            ]
                        ]);
                    }
                } else if ($user->status == 'Pending') {
                    return response()->json(setErrorResponse(__('admin.AuthController.accountPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if (is_null($user->email_verified_at)) {
                    return response()->json(setErrorResponse(__('admin.AuthController.verificationPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Inactive'){
                    return response()->json(setErrorResponse(__('admin.AuthController.accountInactive')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Rejected'){
                    return response()->json(setErrorResponse(__('admin.authControllerAccountRejected')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            } else {
                return response()->json(setErrorResponse(__('admin.AuthController.accountNotFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
/**
     * @OA\Post(
     *     path="/admin/resendOtp",
     *     tags={"Admin - Auth"},
     *     summary="Admin resend OTP",
     *     description="Admin resend OTP",
     *     operationId="adminResendOtp",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="email",
     *                     description="Email Address",
     *                     type="string"
     *                 ),
     *                  example={"email": "<EMAIL>"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Resend OTP successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     * )
     */
    public function resendOtp(ResendOTPRequest $request) {
        try {
            $user = $this->model->where('email', $request->email)->where('user_type', 'Admin')->first();
            if ($user) {
                // Verify the password and generate the token
                        $otp = random_int(100000, 999999);
                        $existingOtp = DB::table('otp_verifications')
                            ->where('user_id', $user->id)
                            ->where('email', $request->email)
                            ->where('type', 'Admin')
                            ->first();

                        if ($existingOtp) {
                            $user->notify(new \App\Notifications\OTPNotification($otp));
                            DB::table('otp_verifications')
                                ->where('id', $existingOtp->id)
                                ->update([
                                    'otp' => $otp,
                                    'expires_at' => now()->addMinutes(5),
                                    'updated_at' => now(),
                                ]);
                                DB::table('otp_verification_histories')->insert([
                                    'user_id' => $user->id,
                                    'user_type' => 'Admin',
                                    'action' => 'Resend OTP',
                                    'email' => $request->email,
                                    'otp' => $otp,
                                    'status' => 'resend',
                                    'created_at' => now(),
                                    'updated_at' => now(),
                                ]);
                                return response()->json(setResponse([], ['message' => __('operator.AuthController.OtpSent')]))->setStatusCode(Response::HTTP_OK);
                        }else{
                            return response()->json(setErrorResponse(__('operator.AuthController.NotGenerate')))->setStatusCode(Response::HTTP_NOT_FOUND);
                        }
                } else {
                return response()->json(setErrorResponse(__('operator.AuthController.accountNotFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    /**
     * Admin logout process
     *
     * @param Request
     * @return Http Response
     */
    public function logout()
    {
        try {
            $authUser = Auth::guard('admin')->user();
            $accessToken = $authUser->token();
            DB::table('oauth_refresh_tokens')->where('access_token_id', $accessToken->id)->update(['revoked' => true]);
            $accessToken->revoke();
            return response()->json(setResponse([], ['message' => __('admin.AuthController.logoutSuccess')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Master User forgot password process
     *
     * @param Request
     * @return Http Response
     */
    public function forgotPassword(Request $request)
    {
        try {
            $validator = Validator::make($request->json()->all(), [
                'email' => 'required|email|exists:users,email'
            ],[
                'email.exists' => __('admin.AuthController.passwordResetLinkSuccess'),
            ]);
            if ($validator->fails()) {
                $error = $validator->errors();
                foreach($error->toArray() as $t => $value){
                    return response()->json(setResponse([], ['message' =>$value[0]]))->setStatusCode(Response::HTTP_OK);
                }
            }
            $user = $this->model->where(['email' => $request->email])->first();
            if($user && $user->status == 'Active'){
                $user->update(['reset_password_token' => rand(100000, 999999)]);
                $user->notify(new \App\Notifications\AdminResetPasswordNotification());
                return response()->json(setResponse([], ['message' => __('admin.AuthController.passwordResetLinkSuccess')]))->setStatusCode(Response::HTTP_OK);
            } else if ($user->status == 'Pending') {
                return response()->json(setErrorResponse(__('admin.AuthController.accountPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else if (is_null($user->verified_at)) {
                return response()->json(setErrorResponse(__('admin.AuthController.verificationPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else if ($user->status == 'Inactive'){
                return response()->json(setErrorResponse(__('admin.AuthController.accountInactive')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else if ($user->status == 'Rejected'){
                return response()->json(setErrorResponse(__('admin.AuthController.accountRejected')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            } else {
                return response()->json(setErrorResponse(__('admin.AuthController.accountNotFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Verify Email token
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function verifyEmailToken(VerifyEmailTokenRequest $request)
    {
        try {
            $user = $this->model->where(['email' => $request->email])->first();
            if(!empty($user)) {
                if ($user->status == 'Pending') {
                    return response()->json(setErrorResponse(__('admin.AuthController.emailVerificationPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if (is_null($user->verified_at)) {
                    return response()->json(setErrorResponse(__('admin.AuthController.verificationPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Inactive') {
                    return response()->json(setErrorResponse(__('admin.AuthController.accountInactive')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($user->status == 'Rejected') {
                    return response()->json(setErrorResponse(__('admin.AuthController.accountRejected')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if (is_null($user->status)) {
                    return response()->json(setErrorResponse(__('admin.AuthController.verificationPending')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                } else if ($request->token === $user->reset_password_token) {
                    return response()->json(setResponse(['token_verified' => true], []))->setStatusCode(Response::HTTP_OK);
                }
                return response()->json(setResponse(['token_verified' => false], ['message' => 'admin.AuthController.invalidOtp']))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setErrorResponse(__('admin.AuthController.invalidEmail')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Master User reset password process
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function resetPassword(ResetPasswordRequest $request)
    {
        try {
            $user = $this->model->where(['email' => $request->email])->first();
            if ($user) {
                if ($user->reset_password_token === $request->token) {
                    $user->update(['password' => bcrypt($request->password), 'reset_password_token' => null]);
                    return response()->json(setResponse([], ['message' => __('admin.AuthController.passwordReset')]))->setStatusCode(Response::HTTP_OK);
                }
                return response()->json(setErrorResponse(__('admin.AuthController.invalidToken')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }
            return response()->json(setErrorResponse(__("admin.AuthController.emailLinkUserNotFound")))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get permissions list
     *
     * @return \Illuminate\Http\Response
     */
    public function getPermissions() {
        try {
            $user = auth()->guard('operator')->user();
            return response()->json(setResponse(getAccessPermissions($user->role_id,$user->id )))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
