<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;

class FeatureSettingsRequest extends CustomFormRequest
{
   /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array
    {
        return [
            // 'id' => 'required|numeric|exists:'.\DBTableNames::FEATURE_SETTINGS.',id',
            'is_feature_on' => 'required|in:0,1',
            'feature_id' => 'required|int|exists:'.\DBTableNames::FEATURES.',id',
            'master_user_id' => 'required|int|exists:'.\DBTableNames::MASTER_USERS.',id',
        ];
    }
    
    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            // 'id.required' => __('Id is required.'),
            // 'id.numeric' => __('Id is invalid.'),
            'status.required' => __('Status is required.'),
            'feature_id.required' =>__('Feature id is required.'),
            'feature_id.numeric' => __('Feature id is invalid.'),
            'master_user_id.required' =>__('Operator id is required.'),
            'master_user_id.numeric' =>__('Operator id is invalid.'),
        ];
    }
    
}
