<?php
/**
     * @OA\Post(
     *     path="/api/verifyQRCode",
     *     tags={"Mobile - Auth"},
     *     summary="Verify QR code and Login in WebApp",
     *     description="Verify QR code and Login in WebApp",
     *     operationId="verifyQRCode",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="qrcode",
     *                     description="qrcode",
     *                     type="string"
     *                 ),
     *                 example={"qrcode": "jgisfdfgisdfbdfnisdfhbidsfndisufd=="}
     *              )
     *          )
     *      ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     * )
     */