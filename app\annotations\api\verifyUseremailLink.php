<?php
/**
     * @OA\Post(
     *     path="/api/verifyUseremailLink",
     *     tags={"Mobile - Auth"},
     *     summary="Verify User Email",
     *     description="Verify User Email",
     *     operationId="verifyUseremailLink",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="token",
     *                     description="token",
     *                     type="string"
     *                 ),
     *                 example={"token": "jgisfdfgisdfbdfnisdfhbidsfndisufd=="}
     *              )
     *          )
     *      ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     * )
     */