<?php

namespace App\Http\Requests\Operator\v1;

use App\Http\Requests\CustomFormRequest;

class TrainingCourseSubmoduleQuizQuestionsRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array
    {
        return [
            'category_id' => 'required|numeric|exists:'.\DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_CATEGORIES.',id',
            'questions' => "required|array|min:1",
            'questions.*.question' => "required",
            'questions.*.question_image' => "nullable",
            'questions.*.answer_image' => "required",
            'questions.*.answer_explanation' => "required",
            'questions.*.options' => "required|array|min:2|max:4",
            'questions.*.options.*.answer' => "required",
            'questions.*.options.*.is_correct_answer' => "required|in:0,1"
        ];
    }
    
    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'category_id.required' => __('Category Id is required.'),
            'category_id.numeric' => __('Category Id is invalid.'),
            'category_id.exists' => __('Category not found.'),
            'questions.required' => __('Questions are required.'),
            'questions.array' => __('Questions must be an array.'),
            'questions.min' => __('Category must have atleast 1 question'),
            'questions.*.question.required' => __('Question is required'),
            'questions.*.answer_image.required' => __('Answer image is required'),
            'questions.*.answer_explanation.required' => __('Answer explanation iis required.'),
            'questions.*.options.required' => __('Options are required.'),
            'questions.*.options.array' => __('Options must be an array.'),
            'questions.*.options.min' => __('Question must have atleast 2 options.'),
            'questions.*.options.max' => __('Question may not have more than 4 options.'),
            'questions.*.options.*.answer.required' => __('Answer is required'),
            'questions.*.options.*.is_correct_answer.required' => __('Correct answer field is required.'),
            'questions.*.options.*.is_correct_answer.in' => __('Invalid correct answer')
        ];
    }
    
}
