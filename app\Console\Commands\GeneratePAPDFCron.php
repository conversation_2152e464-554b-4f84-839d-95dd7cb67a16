<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
// use Barryvdh\DomPDF\Facade as PDF;
use Barryvdh\DomPDF\Facade\Pdf as PDF;
use App\Models\TrainingCourseSubModulePracticalAssessmentResults;
use App\Http\Resources\Operator\v1\PracticalAssessmentPDFExportResource;
use Illuminate\Support\Facades\Notification;
use App\Notifications\PAResultPDFMailSendUserNotification;
use App\Notifications\PAResultPDFMailSendManagerNotification;
use App\Models\TrainingCourseSubmoduleDetails;
use Illuminate\Support\Facades\Config;
use App\Models\whiteLabelSetting;


class GeneratePAPDFCron extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'aws:generate-pa-pdf-files';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command is for generate PDF files of each practical assessment given by user';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $data = TrainingCourseSubModulePracticalAssessmentResults::
                            whereNull('pdf')
                            ->orderBy('id','DESC')
                            ->take(10)->get();
        if($data->count() > 0){
            $practicalAssessmentResource =  PracticalAssessmentPDFExportResource::collection($data)->toArray($data);  
            
            // Quiz PDF Generation
            $quizFolder = base_path('storage/practical_assessment');
            if (!is_dir($quizFolder)) {
                mkdir($quizFolder);
            }
            if(count($practicalAssessmentResource) > 0){
                foreach ($practicalAssessmentResource as $key => $value) {
                    $result = 'failed';
                    if($value['result'] == 1){
                        $result = 'passed';
                    }
                    $userNm = str_replace(' ', '_', $value['user_name']);
                    $userName = str_replace('/', '_', $userNm);
                    $submod = str_replace(' ', '_', $value['submodule_name']); 
                    $submoduleName = str_replace('/', '_', $submod);
                    $pdfName = $submoduleName.'_'.$result.'_'.$userName.'_'.date('Y-m-d').'_'.time().'.pdf';
                    $pdfPath = $quizFolder . '/' . $pdfName;
                    $geoLocation = \App\Models\PracticalAssessmentGeoLocation::where('submodule_id', $value['submodule_id'])->where('assessor_id', $value['assessor_user_id'])->where('user_id', $value['user_id'])->first();
                    $value['geoLocation'] = $geoLocation;
                    $pdfSettings = whiteLabelSetting::where('operator_id', $value['manager_id'])->where('is_pdf_settings_on',1)->first();
                    $pdfLogos=[];
                    if($pdfSettings){
                        $pdfLogos = getWhitelabelPDFLogoPath($pdfSettings);
                    }
                    if($value['manager_id'] == config('constants.dexgreen_operator_id')){
                        $pdf = PDF::loadView('PA_pdf.dexgreen.practical_assessment_pdf', ['pData' => $value])
                                    ->setPaper('a4', 'portrait')->setWarnings(false)
                                    ->setOptions(['isRemoteEnabled' => true, 'isHtml5ParserEnabled' => true, 'isPhpEnabled' => true])
                                    ->save($pdfPath);
                    }else{
                        if($value['is_result_override'] == 1){
                            $pdf = PDF::loadView('PA_pdf.practical_assessment_override_pdf', ['pData' => $value,'settings'=>$pdfSettings,'pdfLogo'=>$pdfLogos])
                            ->setPaper('a4', 'portrait')->setWarnings(false)
                            ->setOptions(['isRemoteEnabled' => true, 'isHtml5ParserEnabled' => true, 'isPhpEnabled' => true])
                            ->save($pdfPath);
                        }else{
                            $pdf = PDF::loadView('PA_pdf.practical_assessment_pdf', ['pData' => $value,'settings'=>$pdfSettings,'pdfLogo'=>$pdfLogos])
                                    ->setPaper('a4', 'portrait')->setWarnings(false)
                                    ->setOptions(['isRemoteEnabled' => true, 'isHtml5ParserEnabled' => true, 'isPhpEnabled' => true])
                                    ->save($pdfPath);
                        }
                    }
                    
                    \Storage::disk('s3')->put(getTrainingCourseSubmodulePAPath($pdfName,'pathOnly'), file_get_contents($pdfPath));
                    unlink($pdfPath);
                    TrainingCourseSubModulePracticalAssessmentResults::where('id',$value['id'])->update(['pdf'=>$pdfName]);
                }
            }
        }
        // PDF result send on user/manager email address
        $createdPDFResultsData = TrainingCourseSubModulePracticalAssessmentResults::whereNotNull('pdf')
                                ->where('is_mail_sent',0)
                                ->orderBy('id','DESC')
                                ->take(10)->get();
        if($createdPDFResultsData->count() > 0){ 
            foreach ($createdPDFResultsData as $key => $result) {
                $allowEmailSend=TrainingCourseSubmoduleDetails::where('id',$result['submodule_id'])->first();
                $user = $result->user;
                $managerId = $result->user->user_relation->manager_id ?? $result->user->user_relation->master_user_id;
		        $manager = \App\Models\MasterUser::where('id',$managerId)->first();
                if(!empty($allowEmailSend)){
                    TrainingCourseSubModulePracticalAssessmentResults::where('id',$result->id)->update(['is_mail_sent'=>1]);
                    if($allowEmailSend->allow_email_PA_pdf == 1 && $result->is_mail_sent == 0){
                        if($user){
                            $smartAwardsMailSendFlag = smartAwardsEmailSendResctrict($user->email);
                            if($smartAwardsMailSendFlag == 1){
                                $headerData = [
                                    'guard' => 'operator',
                                    'user_id' => $result->master_user_id,
                                    'extra' => '',
                                ];
                                OverrideMailConfigFunction($headerData);
                                Notification::send($user, new PAResultPDFMailSendUserNotification($result));
                            }
                        }
                    }
                    if($allowEmailSend->allow_email_PA_pdf_manager == 1  && $result->is_mail_sent == 0){
                        if($manager){
                            $smartAwardsMailSendFlag = smartAwardsEmailSendResctrict($user->email);
                            if($smartAwardsMailSendFlag == 1){
                                $headerData = [
                                    'guard' => 'operator',
                                    'user_id' => $result->master_user_id,
                                    'extra' => '',
                                ];
                                OverrideMailConfigFunction($headerData);
                                Notification::send($manager, new PAResultPDFMailSendManagerNotification($result));
                            }
                        }
                    } 
                }
            }
        }
    }
}
