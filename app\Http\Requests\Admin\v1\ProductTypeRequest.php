<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;
use App\Models\ProductType;
class ProductTypeRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array
    {
        \Validator::extend('name_exists',
            function($attribute, $value, $parameters)
            {
                $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
                if(isset($parameters[0]) && !is_null($parameters[0])){
                    $existNews = ProductType::where('name',$value)->whereMasterUserId($operatorId)->where('id','!=', $parameters[0])->first();
                }else{
                    $existNews = ProductType::where('name',$value)->whereMasterUserId($operatorId)->first();
                }
                return $existNews ? false : true;
            }
        );

        return [
            'name' => 'required|max:255|name_exists:'.$this->id,
            'status' => 'required|in:Active,Inactive',
	    'image' => "required",
        ];
    }
    
    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'name.required' => __('Product type is required.'),
            'name.name_exists' => __('Product type already exists.'),
            'name.max' => __('Product type may not be greater than 255 characters.'),
            'status.required' => __('Status is required.'),
            'status.in' => __('Invalid status.'),
        ];
    }
    
}
