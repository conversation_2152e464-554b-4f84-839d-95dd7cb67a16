<?php

namespace App\Http\Controllers\Admin\v1;

use App\Models\Roles;
use App\Models\MasterUser;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Services\GetStreamService;
use App\Notifications\TeamMemberNotification;
use App\Http\Requests\Admin\v1\TeamMemberRequest;
use App\Repositories\Admin\v1\MasterUserRepository;
use App\Http\Requests\Admin\v1\TeamMemberStatusRequest;
use App\Http\Requests\Admin\v1\TeamMemberListingRequest;
use App\Http\Resources\Admin\v1\TeamMemberDetailResource;
use App\Http\Resources\Admin\v1\TeamMemberListingResource;
use App\Notifications\TeamMemberResendPasswordNotification;

class TeamMembersController extends Controller
{
    protected $model;
    protected $repository;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->model = new MasterUser();
        $this->repository = new MasterUserRepository($this->model);
    }

    /**
     * Get Admin Roles List
     *
     * @return \Illuminate\Http\Response
     */
    public function getRoles() {
        try {
            $roles = Roles::admin()->select('name', 'id')->get();
            return response()->json(setResponse($roles))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * List team members
     *
     * @return \Illuminate\Http\Response
     */
    public function getListing(TeamMemberListingRequest $request) {
        try {
            $teamMembers = $this->repository->getTeamMembersListing($request->all());
            if ($request->isExport) {
                return $this->repository->exportCsv($teamMembers->get());
            }
            return TeamMemberListingResource::collection($teamMembers->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Store team member
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(TeamMemberRequest $request) {
        try {
            $data = $request->all();
            $password = getRandomPassword();
            $operator = auth()->guard('admin')->user();
            $data['user_type'] = $operator->user_type;
            $data['unique_id'] = 0;
            $data['created_by'] = $data['parent_id'] = $operator->id;
            $data['verified_at'] = date('Y-m-d H:i:s');
            $data['password'] = bcrypt($password);
            $user = MasterUser::create($data);
            app(GetStreamService::class)->GenerateAdminGetStreamToken($user);
            $user->notify(new TeamMemberNotification($password));
            return response()->json(setResponse([], ['message' => __('admin.TeamMember.add')]))->setStatusCode(Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get team member detail
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id) {
        try {
            $teamMember = $this->model->find($id);
            return ($teamMember) ?
                    (new TeamMemberDetailResource($teamMember)) :
                    response()->json(setErrorResponse(__('admin.TeamMember.NotFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Update team member
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(TeamMemberRequest $request, $id) {
        try {
            $teamMember = $this->model->find($id);
            if ($teamMember) {
                $data = $request->all();
                if (empty($request->image)) {
                    \Storage::disk('s3')->delete(getUsersPath(), $teamMember->image);
                    $data['image'] = null;
                }
                $data['modified_by'] = auth()->guard('admin')->user()->id;
                $teamMember->update($data);
                return response()->json(setResponse([], ['message' => __('admin.TeamMember.update')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setErrorResponse(__('admin.TeamMember.NotFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Delete team member
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id) {
        try {
            $teamMember = $this->model->find($id);
            if ($teamMember) {
                $teamMember->delete();
                return response()->json(setResponse([], ['message' => __('admin.TeamMember.delete')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setErrorResponse(__('admin.TeamMember.NotFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Change team member(s) status
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function changeStatus(TeamMemberStatusRequest $request) {
        try {
            $this->model->whereIn('id', $request->ids)->update(['modified_by' => auth()->guard('admin')->id(), 'status' => $request->status]);
            return response()->json(setResponse([], ['message' => __('admin.TeamMember.status')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Resend Password to team member
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function resendPassword($id) {
        try {
            $teamMember = $this->model->find($id);
            if ($teamMember) {
                $password = getRandomPassword();
                $teamMember->update(['password' => bcrypt($password)]);
                $teamMember->notify(new TeamMemberResendPasswordNotification($password, 'admin'));
                return response()->json(setResponse([], ['message' => __('admin.TeamMember.resendPassword')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setErrorResponse(__('admin.TeamMember.NotFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
