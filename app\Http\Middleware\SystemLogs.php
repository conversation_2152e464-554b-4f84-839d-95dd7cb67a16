<?php

namespace App\Http\Middleware;

use Closure;
use App\Models\SystemLog;
use App\Models\User;
use App\Models\MasterUser;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

class SystemLogs
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next, $guard = '')
    {

        $route = $request->route();
        $action = $route[1];
        $info = $action['uses'] ?? null;
        $explodeUrl=explode('@',$info);
        $endfunction=end($explodeUrl);

        $subModuleId=null;
        if(isset($request['submodule_id']) && $endfunction !='globalTracking'){
            $subModuleId=$request['submodule_id'];
        }

        $response = $next($request);
        $guard = $request->segment(2);
        if($guard!='operatorSmartAward' || $guard!='analytics'){
            $guardUser = auth()->guard($guard)->user();
            switch ($guard) {
                case 'api':
                    $is_app_operator_admin = 1;
                    $authUser = $guardUser ?? User::where('email',$request->email)->first();
                    break;
                case 'operator':
                    $is_app_operator_admin = 2;
                    $authUser = $guardUser ?? Masteruser::where('email',$request->email)->first();
                    break;
                case 'admin':
                    $is_app_operator_admin = 3;
                    $authUser = $guardUser ?? Masteruser::where('email',$request->email)->first();
                    break;
            }

            if ($authUser) {
                $user_email = $authUser->email;
                $user_id = $authUser->parent_id ?? $authUser->id;

                $systemSectionSegment = $request->segment(3);
                $systemSection = ($this->capitalizeAndSpace($systemSectionSegment)) ? ucwords($this->capitalizeAndSpace($systemSectionSegment)) : $this->getSystemSection($request);

                $storeData = [
                    'user_id' => $user_id ?? null,  // User, Operator OR Admin Id Done
                    'email' => $user_email ?? null, // User, Operator OR Admin Email Addess Done
                    'action' => $this->getAction($request),         // Create, Update, Delete
                    'action_name' =>$this->getActionName($request,$guard),  // Created a User, Deleted a PA Result
                    'url' => str_replace(url('/v1/'), '', $request->fullUrl()) ?? null, // Done
                    'is_app_operator_admin' => $is_app_operator_admin ?? null,  //1 = Mobile App , 2 = Operator panel and 3 = Admin panel Done
                    'system_section' => $systemSection, // Training Course or Practical Assessment, News, User Management
                    'item_id' => $subModuleId,        // Something to relate back to this individual PA
                ];
                SystemLog::create($storeData);
            }
        }
        return $response;
    }

    public function GetAction($request){
        $route = $request->route();
        $action = $route[1];
        $info = $action['uses'] ?? null;
        $explodeUrl=explode('@',$info);
        $endfunction=end($explodeUrl);
        switch($endfunction)
        {
            case 'store':
                $value='Create';
                break;
            case 'index':
                $value='Listing';
                break;
            case 'update':
                $value='Update';
                break;
            case 'destroy':
                $value='Delete';
                break;
            case 'show':
                $value='Show Detail';
                break;
            default:
                $value=$endfunction;

        }
        return $value;
    }

    public function getActionName($request,$guard){
        $Action=$this->getAction($request);
        $routeName = $request->route();
        if(isset($routeName[1]['as'])){
            $url=explode('.',$routeName[1]['as']);
            if(isset($url[1]) && !empty($url[1])){
                $changedValue = str_replace('_', ' ', $url[1]);
                switch($Action)
                    {
                        case 'Create':
                            $value='Create a '.ucwords($changedValue);
                            break;
                        case 'Listing':
                            $value='Listing a '.ucwords($changedValue);
                            break;
                        case 'Update':
                            $value='Update a '.ucwords($changedValue);
                            break;
                        case 'Delete':
                            $value='Delete a '.ucwords($changedValue);
                            break;
                        case 'Show Detail':
                            $value='Show Detail a '.ucwords($changedValue);
                            break;
                        default:
                            $value=ucwords($changedValue);

                }
                return $value;
            }
        }else{
            switch($guard)
            {
                case 'api':
                    $Action='User '.$Action;
                    break;
                case 'operator':
                    $Action='operator '.$Action;
                    break;
                case 'admin':
                    $Action='admin '.$Action;
                    break;
                default:
                    $Action=$Action;

            }
            return ucwords($Action);
        }
    }

    //System Section
    function capitalizeAndSpace($string) {
        // Replace hyphens with spaces
        $withSpaces = str_replace('-', ' ', $string);
        
        // Separate words based on transition from lowercase to uppercase
        $separated = preg_replace('/(?<=[a-z])(?=[A-Z])/', ' ', $withSpaces);
        
        // Capitalize each word that starts with a capital letter
        $capitalized = preg_replace_callback('/\b[A-Z][a-z]*\b/', function($matches) {
            return ucfirst(strtolower($matches[0]));
        }, $separated);
        
        return $capitalized;
    }

    public function getSystemSection($request){
        $Action=$this->getAction($request);
        $routeName = $request->route();
        if(isset($routeName[1]['as'])){
            $url=explode('.',$routeName[1]['as']);
            if(isset($url[1]) && !empty($url[1])){
            $changedValue = str_replace('_', ' ', $url[1]);
                return ucwords($changedValue);
            }
        }
        else{
            return ucwords($Action);
        }
    }
}
