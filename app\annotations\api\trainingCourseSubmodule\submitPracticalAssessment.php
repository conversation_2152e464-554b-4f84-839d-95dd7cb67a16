<?php
/**
     * @OA\Post(
     *     path="/api/trainingCourseSubmodule/submitPracticalAssessment/",
     *     tags={"Mobile - Training Course Submodule"},
     *     summary="Submit Practical Assessment",
     *     description="Submit Practical Assessment",
     *     operationId="submitPracticalAssessment",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="module_id",
     *                     description="Module Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="submodule_id",
     *                     description="Submodule Id",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="result",
     *                     description="Result",
     *                     type="array",
     *                     @OA\Items(
     *                     type="object",
     *                          @OA\Property(
     *                              property="question_id",
     *                              description="Question Id",
     *                              type="string"
     *                          ),
     *                     ),
     *                     @OA\Items(
     *                     type="object",
     *                          @OA\Property(
     *                              property="media",
     *                              description="Image/Video file - Not supported here",
     *                              type="string"
     *                          ),
     *                          @OA\Property(
     *                              property="media_type",
     *                              description="Media Type : image/video",
     *                              type="string"
     *                          ),
     *                           @OA\Property(
     *                              property="media_content",
     *                              description="Media Content : Main/Extra",
     *                              type="string"
     *                          )
     *                      )
     *                 ),
     *                 @OA\Property(
     *                     property="time_spent",
     *                     description="Time Spent",
     *                     type="string"
     *                 ), 
     *                 
     *             )
     *         )
     *     ),
     *     security={
     *         {"User": {}}
     *     },
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */