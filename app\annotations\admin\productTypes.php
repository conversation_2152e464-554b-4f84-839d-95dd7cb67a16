<?php
/**
     * @OA\Post(
     *     path="/admin/productTypes",
     *     tags={"Admin - Product Types"},
     *     summary="Add Product Types ",
     *     description="Add Product Types ",
     *     operationId="store",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={"name": "Discrete Connectors", "status": "Active"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */ 

/**
     * @OA\Get(
     *     path="/admin/productTypes/{id}",
     *     tags={"Admin - Product Types"},
     *     summary="Get Product Type Details",
     *     description="Get Product Type Details",
     *     operationId="show",
     *     @OA\Parameter(
     *         description="ID of Product type",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Fetched successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */

/**
     * @OA\Put(
     *     path="/admin/productTypes/{id}",
     *     tags={"Admin - Product Types"},
     *     summary="Update Product Types Details",
     *     description="Update Product Types Details",
     *     operationId="update",
     *     @OA\Parameter(
     *         description="ID of Product type to Update",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="integer"
     *         )
     *     ),
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={"name": "Discrete Connectors", "status": "Active"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Updated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */ 

/**
     * @OA\Delete(
     *     path="/admin/productTypes/{id}",
     *     tags={"Admin - Product Types"},
     *     summary="Delete Product Types",
     *     description="Delete Product Types",
     *     operationId="delete",
     *     @OA\Parameter(
     *         description="ID of Product type to Delete",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Deleted successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */