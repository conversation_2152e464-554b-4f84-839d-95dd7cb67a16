<?php

namespace App\Http\Controllers\Api\V1;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\CmsPages;
use Illuminate\Http\Response;
use App\Http\Resources\V1\CmsPages\CmsPagesResource;

class CmsPagesController extends Controller
{
    
    /**
     * Display a listing of the resources.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $slug)
    {
        try {            
            if(isset($slug)){
                $cmsPage = CmsPages::where(['slug' => $slug])->first();
                if ($cmsPage) {
                    return response()->json(setResponse(new CmsPagesResource($cmsPage), ['message' => __('Record found successfully.')]))->setStatusCode(Response::HTTP_OK);
                    
                }else{
                    return response()->json(setErrorResponse(__('Record not found')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            }else{
                return response()->json(setErrorResponse(__('user.CmsPages.notFound')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
