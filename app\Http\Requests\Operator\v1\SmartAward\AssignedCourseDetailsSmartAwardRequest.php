<?php

namespace App\Http\Requests\Operator\v1\SmartAward;

use App\Http\Requests\CustomFormRequest;

class AssignedCourseDetailsSmartAwardRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'user_email' => 'required',
            'training_course_ids' => 'required|array'
        ];
    }
    
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'user_email.required' => __("User email is required."),
            'training_course_ids.required' => __("Training course id(s) required."),
            'training_course_ids.array' => __("Training course id(s) must be in array."),
        ];
    }

}
