<?php
/**
     * @OA\Post(
     *     path="/api/oauth/web/login",
     *     tags={"Web - Auth"},
     *     summary="Web user login",
     *     description="Web user login with email and password and device info",
     *     operationId="webLogin",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="email",
     *                     description="Email Address",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="password",
     *                     description="Password",
     *                     type="string"
     *                 ),
     *                  @OA\Property(
     *                     property="device_type",
     *                     description="device_type Iphone,Android",
     *                     type="string"
     *                 ),
     *                  @OA\Property(
     *                     property="device_token",
     *                     description="device_token",
     *                     type="string"
     *                 ),
     *                  example={"email": "<EMAIL>", "password": "Indianic@123", "device_type": "Android", "device_token": "pfe9dgXujGVqDVypYOZb2b6TMrGWBP"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     * )
     */ 