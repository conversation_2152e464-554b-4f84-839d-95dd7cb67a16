<?php
/**
     * @OA\Post(
     *     path="/api/users/submitHelp",
     *     tags={"Mobile - User"},
     *     summary="Submit Help Form",
     *     description="Submit Help Form",
     *     operationId="submitHelp",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="phone",
     *                     description="phone",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="message",
     *                     description="message",
     *                     type="string"
     *                 )
     *              )
     *          )
     *      ),
     *     security={
     *         {"User": {}}
     *     },
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     * )
     */