<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;

class UserGroupStatusRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {
        return [
            'ids' => 'required|array', 
            'ids.*' => 'required|int',
            'status' => 'required|in:Active,Inactive',
        ];
    }

    public function messages() : array
    {
        return [
            'status.required' => __('Status is required.'),
            'ids.required' => __('Group id is required.'),
            'ids.*' => __('Group id must be integer.'),
        ];
    }
}
