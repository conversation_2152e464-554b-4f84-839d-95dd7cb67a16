<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Barryvdh\DomPDF\Facade\Pdf as PDF;
use Illuminate\Support\Facades\Config;
use App\Models\TrainingCourseProgress;
use App\Models\UserCourseCertificate;
use App\Models\Certificate;
use App\Models\UserRelation;
use Illuminate\Support\Facades\Notification;
use App\Notifications\SendUserCourseCertificateNotification;
use App\Jobs\SendUserCourseCertificateNotificationJob;
use App\Jobs\SendUserCourseCertificateNotificationToManagerJob;
use App\Jobs\SendUserCourseCertificateNotificationToOtherJob;

class GenerateUserCourseCertificateCron extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate-user-course-certificate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'In this command, PDF files are generated and QR codes are generated for each user whose course progress has reached 100%';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
	parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
	$data = TrainingCourseProgress::select('training_course_progress.*')->join('certificates', function ($join) {
			    $join->on('certificates.master_user_id', '=', 'training_course_progress.master_user_id')
			    ->on('certificates.training_course_id', '=', 'training_course_progress.training_course_id');
			})
			->whereIn('training_course_progress.id', function ($query) {
			    $query->selectRaw('MAX(id)')
			    ->from('training_course_progress')
			    ->where('is_certificate_generated', 0)
			    ->groupBy(['training_course_id', 'master_user_id', 'user_id']);
			})
			->where('course_progress', '>=', 100)
			->orderBy('training_course_progress.id', 'DESC')->take(10)->get();
	$folder = base_path('storage/user_course_certificate');
	if (!is_dir($folder)) {
	    mkdir($folder);
	}
	foreach ($data as $course) {
	    $certSettings = Certificate::whereMasterUserId($course->master_user_id)->whereTrainingCourseId($course->training_course_id)->first();
	    if ($certSettings) {
		$certificateName = 'certificate_' . time() . '.pdf';
		$certificate = UserCourseCertificate::updateOrCreate(
				[
				    'training_course_id' => $course->training_course_id,
				    'master_user_id' => $course->master_user_id,
				    'user_id' => $course->user_id,
				],
				[
				    'training_course_id' => $course->training_course_id,
				    'master_user_id' => $course->master_user_id,
				    'user_id' => $course->user_id,
				    'certificate_no' => $this->generateUniqueNumber(),
				    'certificate_name' => $certificateName,
				    'issue_date' => date('Y-m-d'), //
				    'expiry_date' => ($certSettings->cert_never_expires == 'false' && $certSettings->show_expiry_date == 'true' && $certSettings->expiry_date != null ? date('Y-m-d', strtotime(date("Y-m-d") . ' + ' . $certSettings->expiry_date . ' years')) : null),
				]
		);
		/* generate deeplink and QR code to validate certificate */
		$generatedCode = $this->QRCodeGenerate($certificate);
		$certificate->certificate_qr = $generatedCode['qrCode'];
		$certificate->certificate_qr_dynamic_url = $generatedCode['dynamicUrl'];

		/* generating deeplink to fetch details */
		/*
		  $actualURL = url(route('api.certificate.show', ['id' => encrypt($certificate['certificate_no'])]));
		  //remove below line it for testing purpose
		  $dynamicUrl = generateFirebaseDeepLink($actualURL, $certificate['master_user_id']);
		  $certificate->fetch_dynamic_url = $dynamicUrl;
		 */
		$certificate->save();
		/* General pdf file for certificate */
		$pdfPath = $folder . '/' . $certificateName;
		if ($certificate) {
		    $displayData = [
		    'certificate_no' => $certificate->certificate_no,
		    'certificate_qr_url' => $certificate->certificate_qr_url,
		    'user_name' => $course->user->name,
		    'user_email' => $course->user->email,
		    'course_name' => $course->course->title,
		    'show_issue_date' => $certSettings->show_issue_date,
		    'show_expiry_date' => $certSettings->show_expiry_date,
		    'cert_never_expires' => $certSettings->cert_never_expires,
		    'issue_date' => date('d M Y', strtotime($certificate->issue_date)),
		    'expiry_date' => ($certSettings->cert_never_expires == 'false' && $certSettings->show_expiry_date == 'true' && $certificate->expiry_date != null ? date('d M Y', strtotime($certificate->expiry_date)) : '---'),
		    'logo_image' => ($certSettings->logo_image_url != null ? $certSettings->logo_image_url : null),
		    'certification_name' => ($certSettings->certification_name != null ? $certSettings->certification_name : 'CERTIFICATE'),
		    'master_user_id' => $certificate->master_user_id,
		    'user_id' => $course->user_id,
		    'fetch_dynamic_url' => env('WEB_BASE_URL') . '/certValidator/' . encrypt($certificate['certificate_no']),
		    'certificate_footer' => $certSettings->certificate_footer,
                    'certificate_pdf_url' => getUserCourseCertificatePath($certificateName, $course->user_id, $course->training_course_id)
		];
		    $certTitle = str_replace(['{{course_name}}', '{{email}}', '{{name}}', '{{issue_date}}', '{{expiry_date}}'], [$displayData['course_name'], $displayData['user_email'], $displayData['user_name'], $displayData['issue_date'], $displayData['expiry_date']], $certSettings->certificate_title);
		    $certBody = str_replace(['{{course_name}}', '{{email}}', '{{name}}', '{{issue_date}}', '{{expiry_date}}'], [$displayData['course_name'], $displayData['user_email'], $displayData['user_name'], $displayData['issue_date'], $displayData['expiry_date']], $certSettings->certificate_body);
		    $certFooter = str_replace(['{{course_name}}', '{{email}}', '{{name}}', '{{issue_date}}', '{{expiry_date}}'], [$displayData['course_name'], $displayData['user_email'], $displayData['user_name'], $displayData['issue_date'], $displayData['expiry_date']], $certSettings->certificate_footer);
		    $displayData['cert_title'] = json_decode($certTitle);
		    $displayData['cert_body'] = json_decode($certBody);
		    $displayData['cert_footer'] = $certFooter;

		    if ($certSettings->landscape_portrait == 'landscape') {
			$displayData['background_image'] = ($certSettings->custom_background == 'true' && $certSettings->primary_background_image_url != null ? $certSettings->primary_background_image_url : null);

			$pdf = PDF::loadView('user_certificate.landscape', ['data' => $displayData])
				->setPaper('a4', 'landscape')->setWarnings(false)
				->setOptions(['isRemoteEnabled' => true, 'isHtml5ParserEnabled' => true, 'isPhpEnabled' => true])
				->save($pdfPath);
			\Storage::disk('s3')->put(getUserCourseCertificatePath($certificateName, $course->user_id, $course->training_course_id, 'pathOnly'), file_get_contents($pdfPath));
		    } else {
			$displayData['background_image'] = ($certSettings->custom_background == 'true' && $certSettings->primary_background_image_url != null ? $certSettings->primary_background_image_url : null);
			$pdf = PDF::loadView('user_certificate.portrait', ['data' => $displayData])
				->setPaper('a4', 'portrait')->setWarnings(false)
				->setOptions(['isRemoteEnabled' => true, 'isHtml5ParserEnabled' => true, 'isPhpEnabled' => true])
				->save($pdfPath);
			\Storage::disk('s3')->put(getUserCourseCertificatePath($certificateName, $course->user_id, $course->training_course_id, 'pathOnly'), file_get_contents($pdfPath));
		    }
		    unlink($pdfPath);
		    /* Send notification to user and manage and other email that specified in course certificate settings */
		    /* When the job runs, they will see the following data in the job header */
		    $headerData = [
			'guard' => 'operator',
			'user_id' => $course->master_user_id ?? null,
			'extra' => '',
		    ];
		    app('queue')->createPayloadUsing(function () use ($headerData) {
			return ['headerData' => $headerData];
		    });
		    /* Remove comments for notification after testing on dev server */
		    dispatch(new SendUserCourseCertificateNotificationJob($displayData, $certSettings));
                    $managerData=UserRelation::whereUserId($displayData['user_id'])->whereMasterUserId($displayData['master_user_id'])->first();
                    $displayData['manager_id'] = $managerData->manager_id??($managerData->master_user_id??null);
		    dispatch(new SendUserCourseCertificateNotificationToManagerJob($displayData, $certSettings));
		    dispatch(new SendUserCourseCertificateNotificationToOtherJob($displayData, $certSettings));
                    
		    /* Update records that generated certificate */
		    TrainingCourseProgress::whereMasterUserId($course->master_user_id)->whereTrainingCourseId($course->training_course_id)->whereUserId($course->user_id)->update(['is_certificate_generated' => 1]);
		    $course->save();
		}
	    }
	}
    }

    function generateUniqueNumber() {
	$part1 = date('Y');//str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
	$part2 = substr(microtime(), 4, 5).substr(uniqid(mt_rand(), true), 0, 4);
	return "{$part1}/{$part2}";
    }

    public function QRCodeGenerate($data) {
	$actualURL = env('WEB_BASE_URL') . '/certValidator/' . c_encrypt($data['certificate_no']) . '?cert=1';
//	$actualURL = str_replace("#", "{{hash}}", $actualURL);
//	$operatorId = $data['master_user_id'];
//	$dynamicUrl = generateFirebaseDeepLink($actualURL, $operatorId);
	$dynamicUrl = $actualURL;
	$qrCode = generateQRCode($dynamicUrl);
	$file = storage_path('/qrcodes/') . $qrCode;
	\Storage::disk('s3')->put(getUserCourseCertificatePath($qrCode, $data->user_id, $data->training_course_id, 'pathOnly'), file_get_contents($file));
        unlink($file);
	return ['qrCode' => $qrCode, 'dynamicUrl' => $dynamicUrl];
    }

}
