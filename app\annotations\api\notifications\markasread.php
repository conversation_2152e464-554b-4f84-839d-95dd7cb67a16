<?php
/**
     * @OA\post(
     *     path="/api/notifications/markasread",
     *     tags={"Mobile - Notifications"},
     *     summary="Mark as Read Notifications",
     *     description="Mark as Read Notifications",
     *     operationId="markasread",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="notification_id",
     *                     description="Notification Id",
     *                     type="integer"
     *                 ),
     *                 example={"notification_id": 1}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */