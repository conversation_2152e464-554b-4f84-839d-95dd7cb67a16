<?php
/**
     * @OA\Post(
     *     path="/admin/emailTemplates",
     *     tags={"Admin - Email Templates"},
     *     summary="Add Email Templates ",
     *     description="Add Email Templates ",
     *     operationId="store",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="template_title",
     *                     description="Title",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="email_subject",
     *                     description="Email subject",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="email_content",
     *                     description="Email Content",
     *                     type="string"
     *                 ),
     *                 example={"template_title": "Reset Password","email_subject": "Reset Password", "email_content":"This is demo email template"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */ 

/**
     * @OA\Get(
     *     path="/admin/emailTemplates/{id}",
     *     tags={"Admin - Email Templates"},
     *     summary="Get  Email Templates Details",
     *     description="Get  Email Templates Details",
     *     operationId="show",
     *     @OA\Parameter(
     *         description="id",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */

/**
     * @OA\Put(
     *     path="/admin/emailTemplates/{id}",
     *     tags={"Admin - Email Templates"},
     *     summary="Update Email Templates Details",
     *     description="Update  Email Templates Details",
     *     operationId="update",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="template_title",
     *                     description="Title",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="email_subject",
     *                     description="Email subject",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="email_content",
     *                     description="Email Content",
     *                     type="string"
     *                 ),
     *                 example={"template_title": "Reset Password","email_subject": "Reset Password", "email_content":"This is demo email template"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */ 

/**
     * @OA\Delete(
     *     path="/admin/emailTemplates/{id}",
     *     tags={"Admin - Email Templates"},
     *     summary="Delete  Email Templates",
     *     description="Delete  Email Templates",
     *     operationId="delete",
     *     @OA\Parameter(
     *         description="id",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */