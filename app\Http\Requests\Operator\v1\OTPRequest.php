<?php

namespace App\Http\Requests\Operator\v1;
use DBTableNames;
use App\Http\Requests\CustomFormRequest;

class OTPRequest extends CustomFormRequest {

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'email' => 'required|email',
            'otp' => 'required'
        ];
    }
    
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'email.required' => 'Please enter email.',
            'email.email' => 'Email must be a valid email address (E.g.: <EMAIL>).',
            'otp.required' => 'Please enter otp.'
        ];
    }

}
