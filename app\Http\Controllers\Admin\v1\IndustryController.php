<?php

namespace App\Http\Controllers\Admin\v1;

use Illuminate\Http\Response;
use App\Models\Industry;
use App\Http\Requests\Admin\v1\IndustryRequest;
use App\Http\Requests\Admin\v1\ImportCSVFileRequest;
use App\Http\Requests\Admin\v1\CommonListingRequest;
use App\Http\Requests\Admin\v1\CommonBulkStatusChangeRequest;
use App\Http\Resources\Admin\v1\IndustryResource;
use App\Repositories\Admin\v1\IndustryRepository;

class IndustryController extends Controller
{
    private $model;
    private $repository;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->model = new Industry();
        $this->repository = new IndustryRepository($this->model);
    }

    /**
     * Get Industries List
     *
     * @return \Illuminate\Http\Response
     */
    public function getListing(CommonListingRequest $request) {
        try {
            $industries = $this->repository->getListing($request->all());
            if ($request->isExport) {
                return $this->repository->exportCsv($industries->get());
            }
            return IndustryResource::collection($industries->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Add Industries
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(IndustryRequest $request) {
        try {
            $this->model->create($request->all());
            return response()->json(setResponse([], ['message' => __('admin.industry.created')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Industry Details
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id) {
        try {
            $industry = $this->model->find($id);
            return ($industry) ?
                    (new IndustryResource($industry)) :
                    response()->json(setResponse([], ['message' => __('admin.industry.not-found')]))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Update Industries Details
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(IndustryRequest $request, $id) {
        try {
            $industry = $this->model->find($id);
            if ($industry) {
                $industry->update(['name' => $request->name, 'status' => $request->status]);
                return response()->json(setResponse([], ['message' => __('admin.industry.updated')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setResponse([], ['message' => __('admin.industry.not-found')]))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Delete Industries
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id) {
        try {
            $industry = $this->model->find($id);
            if ($industry) {
                $industry->delete();
                return response()->json(setResponse([], ['message' => __('admin.industry.deleted')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setResponse([], ['message' => __('admin.industry.not-found')]))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * Change Industries Status
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function changeStatus(CommonBulkStatusChangeRequest $request) {
        try {
            $this->model->whereIn('id', $request->ids)->update(['status' => $request->status]);
            return response()->json(setResponse([], ['message' => __('admin.industry.status-updated')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * Import Industries
     *
     * @return \Illuminate\Http\Response
     */
    public function importIndustries(ImportCSVFileRequest $request) {
        try {
            $isImported = $this->repository->importIndustries($request);
            if($isImported) {
                return response()->json(setResponse([], ['message' => __('admin.industry.import-success')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setErrorResponse(__('admin.industry.import-error')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
}
