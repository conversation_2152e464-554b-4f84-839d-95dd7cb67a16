<?php

namespace App\Http\Controllers\Operator\v1;

use Illuminate\Http\Request;
use App\Models\WebNotifications;
use Illuminate\Http\Response;
use App\Http\Resources\Operator\v1\WebNotificationResource;

class WebNotificationController extends Controller
{

    /**
     * @OA\Get(
     *     path="/operator/webNotifications/previousWebNotifications",
     *     tags={"Operator - Web Notification"},
     *     summary="Get Previous Notification List",
     *     description="Get Previous Notification List",
     *     operationId="index",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="Previous Page Limit",
     *                     description="previous_page_limit / Optional",
     *                     type="integer"
     *                 ),
     *                 example={"previous_page_limit": "10"}
     *              )
     *          )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function previousWebNotifications(Request $request) {
        try {
            if(isset($request->previous_page_limit)){
                if(is_null(auth()->guard('operator')->user()->parent_id)){
                    $oldNotificationList = WebNotifications::whereMasterUserId(auth()->guard('operator')->user()->id)->whereDate('created_at','<', date('Y-m-d'))->orderBy('created_at', 'DESC');
                }else{
                    $oldNotificationList = WebNotifications::whereManagerId(auth()->guard('operator')->user()->id)->whereDate('created_at','<', date('Y-m-d'))->orderBy('created_at', 'DESC');
                }
                return WebNotificationResource::collection($oldNotificationList->paginate($request->previous_page_limit));
            }else{
                if(is_null(auth()->guard('operator')->user()->parent_id)){
                    $oldNotificationList = WebNotifications::whereMasterUserId(auth()->guard('operator')->user()->id)->whereDate('created_at','<', date('Y-m-d'))->orderBy('created_at', 'DESC')->get();
                }else{
                    $oldNotificationList = WebNotifications::whereManagerId(auth()->guard('operator')->user()->id)->whereDate('created_at','<', date('Y-m-d'))->orderBy('created_at', 'DESC')->get();
                }
                return response()->json(setResponse(WebNotificationResource::collection($oldNotificationList), ['message' => __('operator.NotificationController.notificationList')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }    
    }

    /**
     * @OA\Get(
     *     path="/operator/webNotifications/todayWebNotifications",
     *     tags={"Operator - Web Notification"},
     *     summary="Get Today Notification List",
     *     description="Get Today Notification List",
     *     operationId="index",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="Today Page Limit",
     *                     description="today_page_limit  / Optional",
     *                     type="integer"
     *                 ),
     *                 example={"today_page_limit": "5"}
     *              )
     *          )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function todayWebNotifications(Request $request) {
        try {
            if(isset($request->today_page_limit)){
                if(is_null(auth()->guard('operator')->user()->parent_id)){
                    $newNotificationList = WebNotifications::whereMasterUserId(auth()->guard('operator')->user()->id)->where('created_at','LIKE', date('Y-m-d')."%")->orderBy('created_at', 'DESC');
                }else{
                    $newNotificationList = WebNotifications::whereManagerId(auth()->guard('operator')->user()->id)->where('created_at','LIKE', date('Y-m-d')."%")->orderBy('created_at', 'DESC');
                }
                return WebNotificationResource::collection($newNotificationList->paginate($request->today_page_limit));
            }else{
                if(is_null(auth()->guard('operator')->user()->parent_id)){
                    $newNotificationList = WebNotifications::whereMasterUserId(auth()->guard('operator')->user()->id)->where('created_at','LIKE', date('Y-m-d')."%")->orderBy('created_at', 'DESC')->get();
                }else{
                    $newNotificationList = WebNotifications::whereManagerId(auth()->guard('operator')->user()->id)->where('created_at','LIKE', date('Y-m-d')."%")->orderBy('created_at', 'DESC')->get();
                }
                return response()->json(setResponse(WebNotificationResource::collection($newNotificationList), ['message' => __('operator.NotificationController.notificationList')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }    
    }

     /**
     * @OA\Get(
     *     path="/operator/webNotifications/notificationCount",
     *     tags={"Operator - Web Notification"},
     *     summary="Get Unread Notification count",
     *     description="Get Unread Notification count",
     *     operationId="index",
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function notificationCount(Request $request) {
        try {
            if(is_null(auth()->guard('operator')->user()->parent_id)){
                $newNotificationCount = WebNotifications::whereMasterUserId(auth()->guard('operator')->user()->id)->where('is_read', 1)->get()->count();
            }else{
                $newNotificationCount = WebNotifications::whereManagerId(auth()->guard('operator')->user()->id)->where('is_read', 1)->get()->count();
            }
            $count['count'] = $newNotificationCount;
            return response()->json(setResponse($newNotificationCount, ['message' => __('operator.NotificationController.notificationList')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\post(
     *     path="/operator/webNotifications/markasread",
     *     tags={"Operator - Web Notification"},
     *     summary="Mark as Read Notifications",
     *     description="Mark as Read Notifications",
     *     operationId="markasread",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="id",
     *                     description="Notification Id / optional",
     *                     type="string"
     *                 ),
     *                 example={"id": "1"}
     *              )
     *          )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function markAsRead(Request $request) {
        try {
            if(isset($request->id)){
                if(is_null(auth()->guard('operator')->user()->parent_id)){
                    WebNotifications::whereId($request->id)->whereMasterUserId(auth()->guard('operator')->user()->id)->update(['is_read' => 0]);
                }else{
                    WebNotifications::whereId($request->id)->whereManagerId(auth()->guard('operator')->user()->id)->update(['is_read' => 0]);
                }
            }else{
                if(is_null(auth()->guard('operator')->user()->parent_id)){
                    WebNotifications::whereMasterUserId(auth()->guard('operator')->user()->id)->update(['is_read' => 0]);
                }else{
                    WebNotifications::whereManagerId(auth()->guard('operator')->user()->id)->update(['is_read' => 0]);
                }
            }
            return response()->json(setResponse([], ['message' => __('operator.NotificationController.markAsRead')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }    
    }

     /**
     * @OA\Delete(
     *     path="/operator/webNotifications/delete",
     *     tags={"Operator - Web Notification"},
     *     summary="Delete Notifications",
     *     description="Delete Notifications",
     *     operationId="delete",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="ids",
     *                     description="Notification Ids",
     *                     type="string"
     *                 ),
     *                 example={"ids": "[9,10]"}
     *              )
     *          )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function deleteNotifications(Request $request) {
        try {
            if(isset($request->ids)){
                WebNotifications::destroy($request->ids);
                return response()->json(setResponse([], ['message' => __('operator.NotificationController.destroyed')]))->setStatusCode(Response::HTTP_OK);
            }else{
                return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }    
    }
}
