<?php

namespace App\Console\Commands;

use Exception;
use DBTableNames;
use App\Models\TrainingCourse;
use Illuminate\Console\Command;
use App\Models\whiteLabelSetting;
use Illuminate\Support\Facades\DB;
use App\Models\TrainingCourseSubmoduleDetails;
use App\Models\TrainingCourseSubModuleHappyUnhappy;
use App\Services\DeeplinkService;

class GenerateWhiteLabelDeeplink extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create:whitelabel-deeplink';

    private DeeplinkService $deeplinkService;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate/Create White Label deep link for existing tables in the system';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(DeeplinkService $deeplinkService)
    {
        parent::__construct();
        $this->deeplinkService = $deeplinkService;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $tables = [
            DBTableNames::TRAINING_COURSE_SUBMODULE_HAPPY_UNHAPPY,
            DBTableNames::TRAINING_COURSE_SUBMODULE_VIDEO_GUIDE,
            DBTableNames::TRAINING_COURSE_SUBMODULE_IMAGE_GALLERY,
            DBTableNames::TRAINING_COURSE_SUBMODULE_PRODUCT_LIST,
            DBTableNames::TRAINING_COURSE_SUBMODULE_DETAILS,
            DBTableNames::TRAINING_COURSE,
            DBTableNames::RESOURCES,
            DBTableNames::PRODUCTS
        ];

        try {
            $Operators=whiteLabelSetting::where('is_white_label_feature_on',1)
                                            ->where('is_deeplink_generate',0)
                                            ->whereNotNull('domain_uri_prefix')
                                            ->whereNotNull('ios_app_store_id')
                                            ->whereNotNull('ios_package_name')
                                            ->whereNotNull('android_package_name')
                                            ->pluck('operator_id');
            $share_url=array();
            foreach ($Operators as $operatorId) {
                $packageName=whiteLabelSetting::where('operator_id',$operatorId)
                                            ->value('ios_package_name');
                foreach ($tables as $key=> $table) {
                    $res=\DB::table($table)->get();
                    $cnt = $res->count();
                    if($table=='training_course_submodule_happy_unhappy'){
                        for ($i = 0; $i < $cnt; $i++) {
                            $submodule_id = isset($res[$i]->submodule_id) && $res[$i]->submodule_id != null ? $res[$i]->submodule_id : null;
                            $trainingCourseId = isset($res[$i]->training_course_id) && $res[$i]->training_course_id != null ? $res[$i]->training_course_id : null;
                            $operatorId = ($operatorId);
                            if(!DB::table('training_course_submodule_happy_unhappy_whitelabel_deeplink')->where('operator_id',$operatorId)
                                ->where('training_course_id',$res[$i]->id)->where('submodule_id',$submodule_id)->exists()){
                                // $url1 = generateFirebaseDeepLink(url(route('trainingCourseSubmodule.happyUnhappyList', ['id' => $submodule_id]).'?appId='.$packageName),$operatorId) . '?type=happyUnhappyList&id=' . $submodule_id;
                                $targetUrl = url(route('trainingCourseSubmodule.happyUnhappyList', ['id' => $submodule_id]).'?appId='.$packageName) . '?type=happyUnhappyList&id=' . $submodule_id;
                                $deeplinkUrl =$this->deeplinkService->generateDeeplink([
                                    'target_url' => $targetUrl,
                                    'operator_id' => $operatorId,
                                    'type' => 'training_course',
                                    'entity_id' => $trainingCourseId,
                                    'entity_type' => 'TrainingCourse'
                                ]);
                                $url1 = $deeplinkUrl;
                                $share_url[$i]['operator_id'] = $operatorId;
                                $share_url[$i]['training_course_id'] = $res[$i]->id;
                                $share_url[$i]['submodule_id'] = $submodule_id;
                                $share_url[$i]['created_at'] = date('Y-m-d H:i:s');
                                $share_url[$i]['updated_at'] = date('Y-m-d H:i:s');
                                $share_url[$i]['share_url'] = $url1;
                            }
                        }
                        if(isset($share_url)){
                        \DB::table('training_course_submodule_happy_unhappy_whitelabel_deeplink')->insert($share_url);
                        unset($share_url);
                        }
                    }
                    if($table=='training_course_submodule_video_guide'){
                        for ($i = 0; $i < $cnt; $i++) {
                            $submodule_id = isset($res[$i]->submodule_id) && $res[$i]->submodule_id != null ? $res[$i]->submodule_id : null;
                            $trainingCourseId = isset($res[$i]->training_course_id) && $res[$i]->training_course_id != null ? $res[$i]->training_course_id : null;
                            $operatorId = ($operatorId);
                            if(!DB::table('training_course_submodule_video_guide_whitelabel_deeplink')->where('operator_id',$operatorId)
                                ->where('training_course_id',$res[$i]->id)->where('submodule_id',$submodule_id)->exists()){
                                // $url2 = generateFirebaseDeepLink(url(route('trainingCourseSubmodule.videoGuideList', ['id' => $submodule_id]).'?appId='.$packageName),$operatorId) . '?type=videoGuideList&id=' . $submodule_id;
                                $targetUrl = url(route('trainingCourseSubmodule.videoGuideList', ['id' => $submodule_id]).'?appId='.$packageName). '?type=videoGuideList&id=' . $submodule_id;
                                $deeplinkUrl =$this->deeplinkService->generateDeeplink([
                                    'target_url' => $targetUrl,
                                    'operator_id' => $operatorId,
                                    'type' => 'training_course',
                                    'entity_id' => $trainingCourseId,
                                    'entity_type' => 'TrainingCourse'
                                ]);
                                $url2 = $deeplinkUrl;
                                $share_url[$i]['operator_id'] = $operatorId;
                                $share_url[$i]['training_course_id'] = $res[$i]->id;
                                $share_url[$i]['submodule_id'] = $submodule_id;
                                $share_url[$i]['created_at'] = date('Y-m-d H:i:s');
                                $share_url[$i]['updated_at'] = date('Y-m-d H:i:s');
                                $share_url[$i]['share_url'] = $url2;
                            }
                        }
                        if(isset($share_url)){
                        \DB::table('training_course_submodule_video_guide_whitelabel_deeplink')->insert($share_url);
                        unset($share_url);
                        }
                    }
                    if($table=='training_course_submodule_image_gallery'){
                        for ($i = 0; $i < $cnt; $i++) {
                            $submodule_id = isset($res[$i]->submodule_id) && $res[$i]->submodule_id != null ? $res[$i]->submodule_id : null;
                            $trainingCourseId = isset($res[$i]->training_course_id) && $res[$i]->training_course_id != null ? $res[$i]->training_course_id : null;
                            $operatorId = ($operatorId);
                            if(!DB::table('training_course_submodule_image_gallery_whitelabel_deeplink')->where('operator_id',$operatorId)
                                ->where('training_course_id',$res[$i]->id)->where('submodule_id',$submodule_id)->exists()){
                                // $url3 = generateFirebaseDeepLink(url(route('trainingCourseSubmodule.imageGalleryList', ['id' => $submodule_id]).'?appId='.$packageName),$operatorId) . '?type=imageGalleryList&id=' . $submodule_id;
                                $targetUrl = url(route('trainingCourseSubmodule.imageGalleryList', ['id' => $submodule_id]).'?appId='.$packageName) . '?type=imageGalleryList&id=' . $submodule_id;
                                $deeplinkUrl =$this->deeplinkService->generateDeeplink([
                                    'target_url' => $targetUrl,
                                    'operator_id' => $operatorId,
                                    'type' => 'training_course',
                                    'entity_id' => $trainingCourseId,
                                    'entity_type' => 'TrainingCourse'
                                ]);
                                $url3 = $deeplinkUrl;
                                $share_url[$i]['operator_id'] = $operatorId;
                                $share_url[$i]['training_course_id'] = $res[$i]->id;
                                $share_url[$i]['submodule_id'] = $submodule_id;
                                $share_url[$i]['created_at'] = date('Y-m-d H:i:s');
                                $share_url[$i]['updated_at'] = date('Y-m-d H:i:s');
                                $share_url[$i]['share_url'] = $url3;
                            }
                        }
                        if(isset($share_url)){
                        \DB::table('training_course_submodule_image_gallery_whitelabel_deeplink')->insert($share_url);
                        unset($share_url);
                        }
                    }
                    if($table=='training_course_submodule_product_list'){
                        for ($i = 0; $i < $cnt; $i++) {
                            $submodule_id = isset($res[$i]->submodule_id) && $res[$i]->submodule_id != null ? $res[$i]->submodule_id : null;
                            $trainingCourseId = isset($res[$i]->training_course_id) && $res[$i]->training_course_id != null ? $res[$i]->training_course_id : null;
                            $operatorId = ($operatorId);
                            if(!DB::table('training_course_submodule_product_list_whitelabel_deeplink')->where('operator_id',$operatorId)
                                ->where('training_course_id',$res[$i]->id)->where('submodule_id',$submodule_id)->exists()){
                                // $url4 = generateFirebaseDeepLink(url(route('trainingCourseSubmodule.productList', ['id' => $submodule_id]).'?appId='.$packageName),$operatorId) . '?type=productList&id=' . $submodule_id;
                                $targetUrl = url(route('trainingCourseSubmodule.productList', ['id' => $submodule_id]).'?appId='.$packageName) . '?type=productList&id=' . $submodule_id;
                                $deeplinkUrl =$this->deeplinkService->generateDeeplink([
                                    'target_url' => $targetUrl,
                                    'operator_id' => $operatorId,
                                    'type' => 'training_course',
                                    'entity_id' => $trainingCourseId,
                                    'entity_type' => 'TrainingCourse'
                                ]);
                                $url4 = $deeplinkUrl;
                                $share_url[$i]['operator_id'] = $operatorId;
                                $share_url[$i]['training_course_id'] = $res[$i]->id;
                                $share_url[$i]['submodule_id'] = $submodule_id;
                                $share_url[$i]['created_at'] = date('Y-m-d H:i:s');
                                $share_url[$i]['updated_at'] = date('Y-m-d H:i:s');
                                $share_url[$i]['share_url'] = $url4;
                            }
                        }
                        if(isset($share_url)){
                        \DB::table('training_course_submodule_product_list_whitelabel_deeplink')->insert($share_url);
                        unset($share_url);
                        }
                    }
                    if($table=='training_course_submodule_details'){
                        for ($i = 0; $i < $cnt; $i++) {
                            $submodule_id = isset($res[$i]->submodule_id) && $res[$i]->submodule_id != null ? $res[$i]->submodule_id : null;
                            $trainingCourseId = isset($res[$i]->training_course_id) && $res[$i]->training_course_id != null ? $res[$i]->training_course_id : null;
                            $operatorId = ($operatorId);
                            if(!DB::table('training_course_submodule_details_whitelabel_deeplink')->where('operator_id',$operatorId)
                                ->where('training_course_id',$res[$i]->training_course_id)->where('module_id',$res[$i]->module_id)->exists()){
                                // $url5 = generateFirebaseDeepLink(url(route('trainingCourseSubmodule.getUploadVideo', ['id' => $submodule_id]).'?appId='.$packageName),$operatorId) . '?type=getUploadVideo&id=' . $submodule_id;
                                $targetUrl = url(route('trainingCourseSubmodule.getUploadVideo', ['id' => $submodule_id]).'?appId='.$packageName). '?type=getUploadVideo&id=' . $submodule_id;
                                $deeplinkUrl =$this->deeplinkService->generateDeeplink([
                                    'target_url' => $targetUrl,
                                    'operator_id' => $operatorId,
                                    'type' => 'training_course',
                                    'entity_id' => $trainingCourseId,
                                    'entity_type' => 'TrainingCourse'
                                ]);
                                $url5 = $deeplinkUrl;
                                $share_url[$i]['operator_id'] = $operatorId;
                                $share_url[$i]['training_course_submodule_details_id'] = $res[$i]->id;
                                $share_url[$i]['training_course_id'] = $res[$i]->training_course_id;
                                $share_url[$i]['module_id'] = $res[$i]->module_id;
                                $share_url[$i]['created_at'] = date('Y-m-d H:i:s');
                                $share_url[$i]['updated_at'] = date('Y-m-d H:i:s');
                                $share_url[$i]['share_url'] = $url5;
                            }
                        }
                        if(isset($share_url)){
                        \DB::table('training_course_submodule_details_whitelabel_deeplink')->insert($share_url);
                        unset($share_url);
                        }
                    }
                    if($table=='training_course'){
                        for ($i = 0; $i < $cnt; $i++) {
                            $operatorId = ($operatorId);
                            if(!DB::table('training_course_whitelabel_deeplink')->where('operator_id',$operatorId)
                                ->where('training_course_id',$res[$i]->id)->exists()){
                                // $url6 = generateFirebaseDeepLink(url(route('trainingCourse.show', ['id' => $res[$i]->id]).'?appId='.$packageName),$operatorId) . '?type=trainingCourse&id=' . $res[$i]->id;
                                $targetUrl = url(route('trainingCourse.show', ['id' => $res[$i]->id]).'?appId='.$packageName) . '?type=trainingCourse&id=' . $res[$i]->id;
                                $deeplinkUrl =$this->deeplinkService->generateDeeplink([
                                    'target_url' => $targetUrl,
                                    'operator_id' => $operatorId,
                                    'type' => 'training_course',
                                    'entity_id' => $res[$i]->id,
                                    'entity_type' => 'TrainingCourse'
                                ]);
                                $url6 = $deeplinkUrl;
                                $share_url[$i]['operator_id'] = $operatorId;
                                $share_url[$i]['training_course_id'] = $res[$i]->id;
                                $share_url[$i]['created_at'] = date('Y-m-d H:i:s');
                                $share_url[$i]['updated_at'] = date('Y-m-d H:i:s');
                                $share_url[$i]['share_url'] = $url6;
                            }
                        }
                        if(isset($share_url)){
                        \DB::table('training_course_whitelabel_deeplink')->insert($share_url);
                        unset($share_url);
                        }
                    }
                    if($table=='resources'){
                        for ($i = 0; $i < $cnt; $i++) {
                            $operatorId = ($operatorId);
                            if(!DB::table('resources_whitelabel_deeplink')->where('operator_id',$operatorId)
                                ->where('resource_id',$res[$i]->id)->exists()){
                                // $url7 = generateFirebaseDeepLink(url(route('resources.show', ['id' => $res[$i]->id]).'?appId='.$packageName),$operatorId) . '?type=resources&id=' . $res[$i]->id;
                                $targetUrl = url(route('resources.show', ['id' => $res[$i]->id]).'?appId='.$packageName) . '?type=resources&id=' . $res[$i]->id;
                                $deeplinkUrl =$this->deeplinkService->generateDeeplink([
                                    'target_url' => $targetUrl,
                                    'operator_id' => $operatorId,
                                    'type' => 'resources',
                                    'entity_id' => $res[$i]->id,
                                    'entity_type' => 'Resource'
                                ]);
                                $url7 = $deeplinkUrl;
                                $share_url[$i]['operator_id'] = $operatorId;
                                $share_url[$i]['resource_id'] = $res[$i]->id;
                                $share_url[$i]['created_at'] = date('Y-m-d H:i:s');
                                $share_url[$i]['updated_at'] = date('Y-m-d H:i:s');
                                $share_url[$i]['share_url'] = $url7;
                            }
                        }
                        if(isset($share_url)){
                        \DB::table('resources_whitelabel_deeplink')->insert($share_url);
                        unset($share_url);
                        }
                    }
                    if($table=='products'){
                        for ($i = 0; $i < $cnt; $i++) {
                            $operatorId = ($operatorId);
                            if(!DB::table('products_whitelabel_deeplink')->where('operator_id',$operatorId)
                                ->where('product_id',$res[$i]->id)->exists()){
                                // $url8 = generateFirebaseDeepLink(url(route('products.show', ['id' => $res[$i]->id]).'?appId='.$packageName),$operatorId) . '?type=products&id=' . $res[$i]->id;
                                $targetUrl = url(route('products.show', ['id' => $res[$i]->id]).'?appId='.$packageName) . '?type=products&id=' . $res[$i]->id;
                                $deeplinkUrl =$this->deeplinkService->generateDeeplink([
                                    'target_url' => $targetUrl,
                                    'operator_id' => $operatorId,
                                    'type' => 'products',
                                    'entity_id' => $res[$i]->id,
                                    'entity_type' => 'Product'
                                ]);
                                $url8 = $deeplinkUrl;
                                $share_url[$i]['operator_id'] = $operatorId;
                                $share_url[$i]['product_id'] = $res[$i]->id;
                                $share_url[$i]['created_at'] = date('Y-m-d H:i:s');
                                $share_url[$i]['updated_at'] = date('Y-m-d H:i:s');
                                $share_url[$i]['share_url'] = $url8;
                            }
                        }
                        if(isset($share_url)){
                        \DB::table('products_whitelabel_deeplink')->insert($share_url);
                        unset($share_url);
                        }
                    }
                    DB::table('white_label_settings')
                            ->where('operator_id', $operatorId)
                            ->update(['is_deeplink_generate' => 1]);
                }
            }
        } catch (Exception $e) {
            $this->info($e->getMessage());
        }
    }
}
