# Location Matrix API Documentation

## Overview

The Location Matrix feature allows users to select locations from a large dataset using hierarchical filtering and search functionality. This document outlines all API endpoints and payloads for frontend and mobile teams.

## Table of Contents

1. [Execution Flow](#execution-flow)
2. [CSV Upload API](#csv-upload-api)
3. [Mobile API Endpoints](#mobile-api-endpoints)
4. [Data Structures](#data-structures)
5. [<PERSON>rro<PERSON> Handling](#error-handling)
6. [Implementation Examples](#implementation-examples)

---

## Execution Flow

The Location Matrix feature follows a specific execution flow that integrates with the existing submodule creation process:

### Frontend Execution Flow

1. **Create Training Course**
   - Standard training course creation

2. **Upload CSV File** (During Submodule Creation)
   - Upload CSV file using `POST /operator/uploadQuestionMedia`
   - Include `question_type: "locationMatrix"` and `id: {training_course_id}`
   - Receive file path and validation results
   - CSV is validated but not processed yet

3. **Create Submodule with Questions**
   - Submit submodule creation with question list
   - Include `file_name` and `file_path` from step 2 in question data
   - CSV data is automatically processed when question is created

4. **View/Update Submodule**
   - Use standard submodule detail and update APIs
   - CSV data is available for mobile consumption

### Backend Processing Flow

1. **CSV Upload** (`uploadQuestionMedia`)
   - Validate CSV structure and headers
   - Check for required columns and data integrity
   - Store file in S3 and return validation results

2. **Question Creation** (`TrainingCourseSubModulePracticalAssessmentQuestion::createData`)
   - Process uploaded CSV file
   - Parse and store data in `location_matrix_data` table
   - Create hierarchical filter structure

3. **Mobile API Access**
   - Provide filtered data through API endpoints

---

## CSV Upload API

### Upload Location Matrix CSV

**Endpoint:** `POST /operator/uploadQuestionMedia`

**Description:** Upload CSV file for location matrix question type using existing upload infrastructure. This is done during submodule creation, before questions are created.

**Headers:**
```
Content-Type: multipart/form-data
Authorization: Bearer {operator_token}
```

**Request Payload:**
```javascript
{
  "type": "submodules",                    // Required: string
  "media_type": "document",                // Required: string
  "file": File,                           // Required: CSV file
  "question_type": "locationMatrix",         // Required: string
  "id": 344                               // Required: integer (training course ID)
}
```

**CSV File Requirements:**
- Required headers: `Region`, `Territory`, `Exchange`
- Optional headers: `Index`, `Additional Data` (stored as additional_data)
- Exchange must not be blank or duplicate
- File format: CSV with comma separation
- Maximum file size: Follow default upload limits

---

## Mobile API Endpoints

### Unified Location Matrix API

**Endpoint:** `GET /api/trainingCourseSubmodule/locationMatrix/{questionId}`

**Description:** Single, flexible API endpoint that supports multiple result types with filtering and search capabilities.

**Headers:**
```
Authorization: Bearer {user_token}
```

**Query Parameters:**
```
resultType: region|territory|exchange (default: region)
region: Filter by region
territory: Filter by territory
search: Free text search across all fields
page: Page number (default: 1)
pageSize: Items per page (default: 20, max: 100)
distinct: Return unique values only (default: true)
```

**Result Types:**
- `region`: Returns unique regions
- `territory`: Returns unique territories for a given region
- `exchange`: Returns unique exchanges for a given region and territory

**Response:**
```javascript
{
  "resultType": "region",
  "filters": {
    "region": "Africa",
    "territory": "Egypt",
    "search": "Cairo"
  },
  "pagination": {
    "page": 1,
    "pageSize": 20,
    "distinct": true
  },
  "data": [
    "Africa",
    "Asia",
    "Europe",
    "North America",
    "Oceania",
    "South America"
  ]
}
```

**Implementation Note:** The `data` array contains the result data based on the `resultType` parameter.

---

## Data Structures

### Location Matrix Data

**Description:** This data structure represents the parsed and stored data in the `location_matrix_data` table.

**Data Structure:**
```javascript
{
  "id": 1,
  "question_id": 123,
  "region": "Africa",
  "territory": "Egypt",
  "exchange": "Cairo",
  "additional_data": {
    "Index": "1",
    "Additional Data": "Additional data"
  }
}
```

---

## Error Handling

### Invalid Result Type Error

**Description:** This error occurs when the `resultType` query parameter is not one of the supported values.

**Response:**
```javascript
{
  "success": false, 
  "message": "Invalid resultType. Must be: region, territory, or exchange"
}
```

---

## Implementation Examples

### Frontend (Operator Panel) JavaScript

```javascript
// CSV Upload Functionality
async function uploadLocationMatrixCsv() {
    const formData = new FormData();
    formData.append('file', document.getElementById('csvFile').files[0]);
    formData.append('question_type', 'locationMatrix');
    formData.append('id', 123); // Training course ID

    try {
        const response = await fetch('/operator/uploadQuestionMedia', {
            method: 'POST',
            body: formData,
            headers: {
                'Authorization': `Bearer ${operatorToken}`
            }
        });

        const result = await response.json();

        if (result.success) {
            console.log('CSV uploaded successfully:', result.data.location_matrix_validation);
            // Show success message with validation info
            showUploadSuccess(result.data.location_matrix_validation);

            // Return file info for use in question creation
            return {
                file_name: result.data.file,
                file_path: result.data.url
            };
        } else {
            console.error('Upload failed:', result.message);
            showUploadError(result.message);
            return null;
        }
    } catch (error) {
        console.error('Upload error:', error);
        showUploadError('Upload failed. Please try again.');
        return null;
    }
}
```

---

For any questions or issues with the API implementation, please contact the development team.
