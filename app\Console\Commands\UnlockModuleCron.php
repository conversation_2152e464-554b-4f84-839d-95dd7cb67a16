<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\TrainingCourse;
use App\Models\TrainingCourseModules;
use Carbon\Carbon;


class UnlockModuleCron extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'unlock:module';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command is for send notification to user on unlock module';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $trainingCourseList = TrainingCourse::where('publish_now', 1)->whereStatus('Active')->get();
        if($trainingCourseList->count() > 0){
            foreach($trainingCourseList as $courseList){
                $module = TrainingCourseModules::where('training_course_id',$courseList->id)
                                               ->where('module_lock',1)
                                               ->where('status',1)
                                               ->where('notify_users',1)
                                                ->get();
                if($module->count() > 0){
                    foreach ($module as $key => $mod) {   
                        /*When the job runs, they will see the following data in the job header*/
                        $headerData = [
                            'guard' => 'operator',
                            'user_id' => $mod->course->master_user_id??null,
                            'extra' => '',
                        ];
                        app('queue')->createPayloadUsing(function () use ($headerData) {
                            return ['headerData' => $headerData];
                        });
                    
                        $moduleNotificationJob = (new \App\Jobs\ModuleUnlockUserNotificationJob($mod, $courseList));
                        dispatch($moduleNotificationJob);
                    }
                }
            }
        }
    }
}
