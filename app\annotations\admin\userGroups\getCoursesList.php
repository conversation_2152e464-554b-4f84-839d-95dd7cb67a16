<?php
/**
     * @OA\Post(
     *     path="/admin/userGroups/getCoursesList",
     *     tags={"Admin - User Groups"},
     *     summary="Get Courses list",
     *     description="Get Courses list",
     *     operationId="getCoursesList",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="User Group Name",
     *                     type="string"
     *                 ),
    *                 @OA\Property(
     *                     property="status",
     *                     description="User Group Status",
     *                     type="string",
                           enum={"Active", "Inactive"}
     *                 ),
     *                 example={
                        "per_page":10,
                        "sort_by":"createdBy/CourseName",
                        "order_by":"asc",
                        "search_key":"my course"
                        }
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Courses list genetated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */ 