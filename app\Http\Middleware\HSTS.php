<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class HSTS
{
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        if (method_exists($response, 'header')) {
            //max-age=<expire-time> is in seconds , 31536000 = approx 1 year
            $response->header('Strict-Transport-Security', 'max-age=31536000; includeSubdomains; preload');
        }else{
            if($response instanceof BinaryFileResponse){
                $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubdomains; preload');
            }
        }
        return $response;
    }
}