<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCableMatrixSelectionToPracticalAssessmentAnswers extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('training_course_submodule_practical_assessment_answers', function (Blueprint $table) {
            $table->json('cable_matrix_selection')->nullable()->after('question_options')
                  ->comment('Store cable matrix selection: {diameter, type, description, additional_data}');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('training_course_submodule_practical_assessment_answers', function (Blueprint $table) {
            $table->dropColumn('cable_matrix_selection');
        });
    }
}
