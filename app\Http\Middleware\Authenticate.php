<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\URL;
use Illuminate\Contracts\Auth\Factory as Auth;

class Authenticate {

    /**
     * The authentication guard factory instance.
     *
     * @var \Illuminate\Contracts\Auth\Factory
     */
    protected $auth;

    /**
     * Create a new middleware instance.
     *
     * @param  \Illuminate\Contracts\Auth\Factory  $auth
     * @return void
     */
    public function __construct(Auth $auth) {
	$this->auth = $auth;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $guard
     * @return mixed
     */
    public function handle($request, Closure $next, $guard = null) {
	$guard = empty($guard) ? $this->auth->getDefaultDriver() : $guard;
	if ($this->auth->guard($guard)->guest()) {
	    if ($request->expectsJson()) {
		return response()->json(setErrorResponse(__('Unauthorized')))
				->setStatusCode(Response::HTTP_UNAUTHORIZED);
	    }
	    $browser = getUserBrowser();
	    if (!empty($browser)) {
		if ($request->query('cert') == 1) {
		    return $next($request);
		}
		if ($request->query('appId') == 'com.dexgreen.app') {
		    header("Location: https://dexgreen.com/");
		    exit;
		}
        else if ($request->query('appId') == 'com.AFL.app') {
		    header("Location: https://www.aflglobal.com/");
		    exit;
		} else {
		    header("Location: https://skillsbase.io/");
		    exit;
		}
	    } else {
		return response()->json(setErrorResponse(__('You have been Signed Out')))
				->setStatusCode(Response::HTTP_UNAUTHORIZED);
	    }
	}
	return $next($request);
    }

}
