<?php

namespace App\Http\Resources\V1;

use App\Models\TrainingCourseSubmoduleDetails;
use Illuminate\Http\Resources\Json\JsonResource;

class AttemptResource extends JsonResource
{

    public function toArray($request)
    {
        $submodule = TrainingCourseSubmoduleDetails::find($this->submodule_id)->submodule_name;
        return [
            "submodule_name" => $submodule,
            "attempt" => $this->attempt,
            "attempt_date" => $this->attempt_date,
        ];
    
    }
}
