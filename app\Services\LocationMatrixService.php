<?php

namespace App\Services;

use App\Models\LocationMatrixData;
use App\Models\TrainingCourseSubModulePracticalAssessmentQuestion;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class LocationMatrixService
{
    /**
     * Process uploaded CSV file and store location matrix data
     */
    public function processCsvFile($questionId, $filePath)
    {
        try {
            DB::beginTransaction();

            $question = TrainingCourseSubModulePracticalAssessmentQuestion::findOrFail($questionId);
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;

            // Clear existing data for this question
            LocationMatrixData::where('question_id', $questionId)
                ->where('master_user_id', $operatorId)
                ->delete();

            // Read and parse CSV
            $csvData = $this->parseCsvFromS3($filePath);

            // Validate CSV structure
            $validation = $this->validateCsvStructure($csvData);
            if (!$validation['valid']) {
                throw new \Exception('Invalid CSV structure: ' . implode(', ', $validation['errors']));
            }

            // Process and store data
            $result = $this->storeCsvData($csvData, $questionId, $operatorId);

            DB::commit();

            return [
                'success' => true,
                'data' => $result
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Location Matrix CSV Processing Error: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Process CSV from file path (for question creation)
     */
    public function processCsvFromFilePath($questionId, $filePath, $operatorId)
    {
        try {
            DB::beginTransaction();

            // Clear existing data for this question
            LocationMatrixData::where('question_id', $questionId)
                ->where('master_user_id', $operatorId)
                ->delete();

            // Read and parse CSV
            $csvData = $this->parseCsvFromS3($filePath);

            // Validate CSV structure
            $validation = $this->validateCsvStructure($csvData);
            if (!$validation['valid']) {
                throw new \Exception('Invalid CSV structure: ' . implode(', ', $validation['errors']));
            }

            // Process and store data
            $result = $this->storeCsvData($csvData, $questionId, $operatorId);

            DB::commit();

            Log::info('Location Matrix: Successfully processed CSV for question ' . $questionId . ' - ' . $result['processed_rows'] . ' rows');

            return [
                'success' => true,
                'data' => $result
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Location Matrix CSV Processing Error for question ' . $questionId . ': ' . $e->getMessage());

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Parse CSV file from S3
     */
    private function parseCsvFromS3($filePath)
    {
        $csvContent = Storage::disk('s3')->get($filePath);
        $lines = explode("\n", $csvContent);
        $csvData = [];
        $headers = null;

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            $row = str_getcsv($line);
            
            if ($headers === null) {
                $headers = $row;
            } else {
                if (count($row) === count($headers)) {
                    $csvData[] = array_combine($headers, $row);
                }
            }
        }

        return $csvData;
    }

    /**
     * Validate CSV structure
     */
    private function validateCsvStructure($csvData)
    {
        $requiredHeaders = ['Exchange', 'Territory', 'Region'];
        $errors = [];

        if (empty($csvData)) {
            $errors[] = 'CSV file is empty or invalid';
            return ['valid' => false, 'errors' => $errors];
        }

        $headers = array_keys($csvData[0]);
        $missingHeaders = array_diff($requiredHeaders, $headers);

        if (!empty($missingHeaders)) {
            $errors[] = 'Missing required headers: ' . implode(', ', $missingHeaders);
        }

        // Validate data rows and check for duplicates
        $validRows = 0;
        $exchanges = [];
        $duplicates = [];
        
        foreach ($csvData as $index => $row) {
            $exchange = trim($row['Exchange'] ?? '');
            
            if (!empty($exchange)) {
                if (in_array($exchange, $exchanges)) {
                    $duplicates[] = $exchange;
                } else {
                    $exchanges[] = $exchange;
                    $validRows++;
                }
            }
        }

        if ($validRows === 0) {
            $errors[] = 'No valid data rows found with Exchange';
        }

        if (!empty($duplicates)) {
            $errors[] = 'Duplicate Exchanges found: ' . implode(', ', array_unique($duplicates));
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'total_rows' => count($csvData),
            'valid_rows' => $validRows,
            'duplicates' => $duplicates
        ];
    }

    /**
     * Store CSV data in database
     */
    private function storeCsvData($csvData, $questionId, $operatorId)
    {
        $processedRows = 0;
        $skippedRows = 0;

        foreach ($csvData as $rowIndex => $row) {
            $exchange = trim($row['Exchange'] ?? '');
            
            // Skip rows with empty exchange
            if (empty($exchange)) {
                $skippedRows++;
                continue;
            }

            // Prepare additional data (all columns except the main three)
            $additionalData = [];
            foreach ($row as $key => $value) {
                if (!in_array($key, ['Exchange', 'Territory', 'Region'])) {
                    $additionalData[$key] = $value;
                }
            }

            try {
                $locationData = LocationMatrixData::create([
                    'question_id' => $questionId,
                    'master_user_id' => $operatorId,
                    'region' => trim($row['Region'] ?? ''),
                    'territory' => trim($row['Territory'] ?? ''),
                    'exchange' => $exchange,
                    'additional_data' => $additionalData
                ]);

                $processedRows++;

                if ($processedRows <= 3) {
                    Log::info('Location Matrix: Created record ' . $locationData->id . ' - ' . $exchange);
                }
            } catch (\Exception $e) {
                Log::error('Location Matrix: Failed to create record for row ' . $rowIndex . ': ' . $e->getMessage());
                $skippedRows++;
            }
        }

        return [
            'total_rows' => count($csvData),
            'processed_rows' => $processedRows,
            'skipped_rows' => $skippedRows
        ];
    }

    /**
     * Validate CSV file during upload (without question_id)
     */
    public function validateCsvFile($filePath)
    {
        try {
            // Read and parse CSV
            $csvData = $this->parseCsvFromS3($filePath);

            // Validate CSV structure
            $validation = $this->validateCsvStructure($csvData);

            if ($validation['valid']) {
                return [
                    'success' => true,
                    'data' => [
                        'total_rows' => $validation['total_rows'],
                        'valid_rows' => $validation['valid_rows'],
                        'headers' => array_keys($csvData[0] ?? []),
                        'sample_data' => array_slice($csvData, 0, 3) // First 3 rows as sample
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => implode(', ', $validation['errors'])
                ];
            }

        } catch (\Exception $e) {
            Log::error('Location Matrix CSV Validation Error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to validate CSV file: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get regions for a question
     */
    public function getRegions($questionId, $search = null, $distinct = true)
    {
        $question = TrainingCourseSubModulePracticalAssessmentQuestion::findOrFail($questionId);
        $masterUserId = $question->subModule->trainingCourse->master_user_id;

        $query = LocationMatrixData::byQuestion($questionId)
            ->where('master_user_id', $masterUserId);

        if ($search) {
            $query->where('region', 'like', '%' . $search . '%');
        }

        if ($distinct) {
            $regions = $query->distinct()->pluck('region')->filter()->values();
            return [
                'regions' => $regions,
                'count' => $regions->count()
            ];
        } else {
            $regions = $query->pluck('region')->filter()->values();
            return [
                'regions' => $regions,
                'count' => $regions->count()
            ];
        }
    }

    /**
     * Get territories for a question, optionally filtered by region
     */
    public function getTerritories($questionId, $region = null, $search = null, $distinct = true)
    {
        $question = TrainingCourseSubModulePracticalAssessmentQuestion::findOrFail($questionId);
        $masterUserId = $question->subModule->trainingCourse->master_user_id;

        $query = LocationMatrixData::byQuestion($questionId)
            ->where('master_user_id', $masterUserId);

        if ($region) {
            $query->where('region', $region);
        }

        if ($search) {
            $query->where('territory', 'like', '%' . $search . '%');
        }

        if ($distinct) {
            $territories = $query->distinct()->pluck('territory')->filter()->values();
            return [
                'territories' => $territories,
                'count' => $territories->count(),
                'filters' => ['region' => $region]
            ];
        } else {
            $territories = $query->pluck('territory')->filter()->values();
            return [
                'territories' => $territories,
                'count' => $territories->count(),
                'filters' => ['region' => $region]
            ];
        }
    }

    /**
     * Get exchanges with pagination and filtering
     */
    public function getExchanges($questionId, $region = null, $territory = null, $search = null, $page = 1, $pageSize = 20, $distinct = true)
    {
        $question = TrainingCourseSubModulePracticalAssessmentQuestion::findOrFail($questionId);
        $masterUserId = $question->subModule->trainingCourse->master_user_id;

        $query = LocationMatrixData::byQuestion($questionId)
            ->where('master_user_id', $masterUserId);

        if ($region) {
            $query->where('region', $region);
        }

        if ($territory) {
            $query->where('territory', $territory);
        }

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('exchange', 'like', '%' . $search . '%')
                  ->orWhere('region', 'like', '%' . $search . '%')
                  ->orWhere('territory', 'like', '%' . $search . '%');
            });
        }

        if ($distinct) {
            // For distinct results, we need to handle pagination differently
            $totalQuery = clone $query;
            $total = $totalQuery->distinct()->count('exchange');

            $exchanges = $query->distinct()
                ->select('exchange', 'region', 'territory', 'additional_data')
                ->offset(($page - 1) * $pageSize)
                ->limit($pageSize)
                ->get();
        } else {
            $total = $query->count();
            $exchanges = $query->select('exchange', 'region', 'territory', 'additional_data')
                ->offset(($page - 1) * $pageSize)
                ->limit($pageSize)
                ->get();
        }

        return [
            'exchanges' => $exchanges,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $pageSize,
                'total' => $total,
                'last_page' => ceil($total / $pageSize),
                'from' => (($page - 1) * $pageSize) + 1,
                'to' => min($page * $pageSize, $total)
            ],
            'filters' => [
                'region' => $region,
                'territory' => $territory,
                'search' => $search
            ]
        ];
    }

    /**
     * Get filter options for hierarchical filtering
     */
    public function getFilterOptions($questionId, $region = null, $territory = null)
    {
        $question = TrainingCourseSubModulePracticalAssessmentQuestion::findOrFail($questionId);
        $masterUserId = $question->subModule->trainingCourse->master_user_id;

        $query = LocationMatrixData::byQuestion($questionId)
            ->where('master_user_id', $masterUserId);

        // Get all regions
        $regions = $query->distinct()->pluck('region')->filter()->values();

        $result = [
            'regions' => $regions
        ];

        // If region is specified, get territories for that region
        if ($region) {
            $territories = LocationMatrixData::byQuestion($questionId)
                ->where('master_user_id', $masterUserId)
                ->where('region', $region)
                ->distinct()
                ->pluck('territory')
                ->filter()
                ->values();

            $result['territories'] = $territories;
        }

        return $result;
    }
}
