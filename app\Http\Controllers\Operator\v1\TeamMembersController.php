<?php

namespace App\Http\Controllers\Operator\v1;

use App\Models\Roles;
use App\Models\UserGroup;
use App\Models\MasterUser;
use App\Models\UserRelation;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Services\GetStreamService;
use App\Http\Resources\CustomCollection;
use App\Notifications\TeamMemberNotification;
use App\Http\Requests\Operator\v1\TeamMemberRequest;
use App\Repositories\Operator\v1\MasterUserRepository;
use App\Http\Resources\Operator\v1\MemberDetailResource;
use App\Http\Requests\Operator\v1\TeamMemberStatusRequest;
use App\Http\Requests\Operator\v1\TeamMemberListingRequest;
use App\Http\Resources\Operator\v1\TeamMemberDetailResource;
use App\Http\Resources\Operator\v1\TeamMemberListingResource;
use Illuminate\Support\Facades\Password;

class TeamMembersController extends Controller {

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    protected $model;
    protected $repository;

    public function __construct() {
        $this->model = new MasterUser();
        $this->repository = new MasterUserRepository($this->model);
    }

    /**
     * @OA\Get(
     *     path="/operator/teamMembers/getRoles",
     *     tags={"Operator - Team Members Management"},
     *     summary="Get Operator Roles List",
     *     description="Get Operator Roles List",
     *     operationId="getRoles",
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getRoles() {
        try {
            $roles = Roles::operator()->select('name', 'id')->get();
            return response()->json(setResponse($roles))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/teamMembers/getListing",
     *     tags={"Operator - Team Members Management"},
     *     summary="List team members",
     *     description="List team members",
     *     operationId="getListing",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="isExport",
     *                     description="for export csv",
     *                     type="integer"
     *                 ),
     *                 example={"per_page": 10, "page": 1, "search_key": "", "sort_by": "name", "order_by": "asc", "filters": {"name": "", "email": "", "role": "", "status": ""}, "isExport": 0}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getListing(TeamMemberListingRequest $request) {
        try {
            $teamMembers = $this->repository->getTeamMembersListing($request->all());
            if ($request->isExport) {
                return $this->repository->exportCsv($teamMembers->get());
            }
            return TeamMemberListingResource::collection($teamMembers->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/teamMembers",
     *     tags={"Operator - Team Members Management"},
     *     summary="Store team member",
     *     description="Store team member",
     *     operationId="store",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="email",
     *                     description="Email Address",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="image",
     *                     description="Image",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="role_id",
     *                     description="Role ID",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="unique_id",
     *                     description="Unique ID",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status:- Active|Inactive",
     *                     type="string"
     *                 ),
     *                 example={"name": "Operator Name", "email": "<EMAIL>", "image": "", "role_id": 2, "unique_id": "123456", "status": "Active"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function store(TeamMemberRequest $request) {
        try {
            $data = $request->all();
            if (!empty($data['password'])) {
                $password = trim($data['password']);
            } else {
                $password = getRandomPassword();
            }
            $token = random_int(100000, 999999);
            $operator = auth()->guard('operator')->user();
            $data['user_type'] = $operator->user_type;
            $data['created_by'] = $operator->id;
            $data['parent_id'] = $operator->parent_id ?? $operator->id;
            $data['verified_at'] = date('Y-m-d H:i:s');
            $data['password'] = bcrypt($password);
            $data['reset_password_token'] = $token;
            $user = MasterUser::create($data);
            app(GetStreamService::class)->GenerateGetStreamOperatorToken($user, 2);

            //Checking for email address if smartawards contains only digits before @
            $smartAwardsMailSendFlag = smartAwardsEmailSendResctrict($user->email);
            if ($smartAwardsMailSendFlag == 1) {
                $user->sendCreateTeamMemberPasswordNotification($token, "Create");
            }
            return response()->json(setResponse([], ['message' => __('operator.TeamMember.add')]))->setStatusCode(Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/teamMembers/{id}",
     *     tags={"Operator - Team Members Management"},
     *     summary="Get team member",
     *     description="Get team member",
     *     operationId="show",
     *     @OA\Parameter(
     *         description="id",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function show($id) {
        try {
            $teamMember = $this->model->find($id);
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $teamMemberList = $this->model->where('parent_id', $operatorId)->pluck('id')->toArray();
            if (in_array($id, $teamMemberList)) {
                return ($teamMember) ?
                        (new TeamMemberDetailResource($teamMember)) :
                        response()->json(setErrorResponse(__('operator.TeamMember.NotFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            } else {
                return response()->json(setErrorResponse(__('You cannot access this record')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Put(
     *     path="/operator/teamMembers/{id}",
     *     tags={"Operator - Team Members Management"},
     *     summary="Update team member",
     *     description="Update team member",
     *     operationId="update",
     *     @OA\Parameter(
     *         description="Id of team member to update",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="email",
     *                     description="Email Address",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="image",
     *                     description="Image",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="role_id",
     *                     description="Role ID",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="unique_id",
     *                     description="Unique ID",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status:- Active|Inactive",
     *                     type="string"
     *                 ),
     *                 example={"name": "Operator Name", "email": "<EMAIL>", "image": "", "role_id": 2, "unique_id": "123456", "status": "Active"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function update(TeamMemberRequest $request, $id) {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $teamMember = $this->model->find($id);
            $oldData = $this->model->find($id);
            if ($teamMember) {
                $data = $request->all();
                if (empty($request->image)) {
                    \Storage::disk('s3')->delete(getUsersPath(), $teamMember->image);
                    $data['image'] = null;
                }
                $data['modified_by'] = auth()->guard('operator')->user()->id;
                $teamMember->update($data);
                app(GetStreamService::class)->TeamMemberUpdate($teamMember);
                if (!empty($data['password'])) {
                    $password = $data['password'];
                    $data['password'] = bcrypt($password);
                    $teamMember->update(['password' => $data['password']]);
                }
                // map update data with users
                if ($oldData->email != $request->email) {
                    UserGroup::whereParentId($operatorId)->whereManagerEmail($oldData->email)->update(['manager_email' => $request->email]);
                    UserRelation::whereMasterUserId($operatorId)->whereManagerEmail($oldData->email)->update(['manager_email' => $request->email]);
                }
                if ($oldData->unique_id != $request->unique_id) {
                    UserGroup::whereParentId($operatorId)->whereUniqueId($oldData->unique_id)->update(['unique_id' => $request->unique_id]);
                    UserRelation::whereMasterUserId($operatorId)->whereUniqueId($oldData->unique_id)->update(['unique_id' => $request->unique_id]);
                }
                return response()->json(setResponse([], ['message' => __('operator.TeamMember.update')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setErrorResponse(__('operator.TeamMember.NotFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Delete(
     *     path="/operator/teamMembers/{id}",
     *     tags={"Operator - Team Members Management"},
     *     summary="Delete team member",
     *     description="Delete team member",
     *     operationId="destroy",
     *     @OA\Parameter(
     *         description="id",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function destroy($id) {
        try {
            $teamMember = $this->model->find($id);
            if ($teamMember) {
                $teamMemberUsers = $teamMember->teamMemberUsers;
                if (count($teamMemberUsers) > 0) {
                    foreach ($teamMemberUsers as $users) {
                        $users->update([
                            'manager_email' => $teamMember->mainOperator->email,
                            'manager_id' => $teamMember->parent_id,
                            'unique_id' => $teamMember->mainOperator->unique_id]
                        );
                    }
                }
                $teamMember->delete();
                app(GetStreamService::class)->DeleteTeamMemberGetStream($id);
                return response()->json(setResponse([], ['message' => __('operator.TeamMember.delete')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setErrorResponse(__('operator.TeamMember.NotFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/teamMembers/changeStatus",
     *     tags={"Operator - Team Members Management"},
     *     summary="Change team member(s) status",
     *     description="Change team member(s) status",
     *     operationId="changeStatus",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="ids",
     *                     description="ids",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={"ids": {"3","4"}, "status": "Active"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function changeStatus(TeamMemberStatusRequest $request) {
        try {
            $this->model->whereIn('id', $request->ids)->update(['modified_by' => auth()->guard('operator')->id(), 'status' => $request->status]);
            return response()->json(setResponse([], ['message' => __('operator.TeamMember.status')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/teamMembers/getLoginOperatorDetails",
     *     tags={"Operator - Team Members Management"},
     *     summary="Get team members listing",
     *     description="Get team members listing (Side Bar)",
     *     operationId="getLoginOperatorDetails",
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getLoginOperatorDetails(Request $request) {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $operatorData['id'] = auth()->guard('operator')->id();
            $operatorData['name'] = auth()->guard('operator')->user()->name;
            $operatorData['image'] = auth()->guard('operator')->user()->image_url;
            $operatorData['team_members'] = new CustomCollection(MasterUser::where('parent_id', $operatorId)->whereNotIn('id', [auth()->guard('operator')->user()->id])->orderBy('id', 'DESC')->take(5)->get(), 'App\Http\Resources\Operator\v1\MemberDetailResource');
            return response()->json(setResponse($operatorData, ['message' => __('operator.TeamMember.found')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

}
