<?php
/**
     * @OA\Post(
     *     path="/admin/userGroups",
     *     tags={"Admin - User Groups"},
     *     summary="Add User Group ",
     *     description="Add User Group ",
     *     operationId="store",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="User Group Name",
     *                     type="string"
     *                 ),
    *                 @OA\Property(
     *                     property="status",
     *                     description="User Group Status",
     *                     type="string",
                           enum={"Active", "Inactive"}
     *                 ),
     *                 example={"0":{
                      "step":1,
                      "name":"My Group",
                      "status":"Active",
                      "manager_email":"<EMAIL>",
                      "unique_id":"eXK265",
                    },"1":{
                    "step":2,
                      "groupId":12,
                            "trainingCourseIds":{
                                "0":1,
                                "1":3
                            }
                    },"2":{
                        "step":3,
                        "groupId":12,
                        "address":"76 nyc street CA",
                        "contact_no":"1111111111",
                        "latitude":"22.725524",
                        "longitude":"75.854401",
                        "website":"https://www.abc.com",
                    }}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="User Group Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */ 

/**
     * @OA\Get(
     *     path="/admin/userGroups/{id}",
     *     tags={"Admin - User Groups"},
     *     summary="Show User Group Details",
     *     description="Show User Group Details",
     *     operationId="show",
     *     @OA\Parameter(
     *         description="Id of User group to fetch",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */

/**
     * @OA\Put(
     *     path="/admin/userGroups/{id}",
     *     tags={"Admin - User Groups"},
     *     summary="Update User Group details",
     *     description="Update User Group details",
     *     operationId="update",
     *     @OA\Parameter(
     *         description="Id of User group to update",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
    *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="User Group Name",
     *                     type="string"
     *                 ),
    *                 @OA\Property(
     *                     property="status",
     *                     description="group Status",
     *                     type="string",
                           enum={"Active", "Inactive"}
     *                 ),
     *                 example={"0":{
                      "step":1,
                      "name":"My Group",
                      "status":"Active",
                      "manager_email":"<EMAIL>",
                      "unique_id":"eXK265",
                    },"1":{
                    "step":2,
                            "trainingCourseIds":{
                                "0":1,
                                "1":3
                            }
                    },"2":{
                        "step":3,
                        "address":"76 nyc street CA",
                        "contact_no":"1111111111",
                        "latitude":"22.725524",
                        "longitude":"75.854401",
                        "website":"https://www.abc.com",
                    }}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Role Updated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */ 

/**
     * @OA\Post(
     *     path="/admin/userGroups/delete",
     *     tags={"Admin - User Groups"},
     *     summary="Delete User Groups",
     *     description="Delete User Groups",
     *     operationId="destroy",
           @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="id",
     *                     description="id",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={ "ids":{
                                "0":"1",
                                "1":"2"
                            }
                        }
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="User Group Deleted successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */