<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterShareUrlTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        \DB::table('white_label_settings')
                            ->update(['is_deeplink_generate' => 1]);
        \DB::table('training_course_submodule_happy_unhappy_whitelabel_deeplink')->delete();
        \DB::table('training_course_submodule_video_guide_whitelabel_deeplink')->delete();
        \DB::table('training_course_submodule_image_gallery_whitelabel_deeplink')->delete();
        \DB::table('training_course_submodule_product_list_whitelabel_deeplink')->delete();
        \DB::table('training_course_submodule_details_whitelabel_deeplink')->delete();
        \DB::table('training_course_whitelabel_deeplink')->delete();
        \DB::table('resources_whitelabel_deeplink')->delete();
        \DB::table('products_whitelabel_deeplink')->delete();
        \DB::table('products_whitelabel_deeplink')->delete();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
