<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddNewEnumTypeInTrainingCourseSubmodulePracticalAssessmentQuestionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('training_course_submodule_practical_assessment_questions', function (Blueprint $table) {
            // Doctrine DBAL does not support modifying ENUM columns directly.
            // Use raw SQL to update the ENUM values.
            DB::statement("ALTER TABLE `training_course_submodule_practical_assessment_questions` CHANGE `question_type` `question_type` ENUM('text', 'rating', 'single', 'multiple', 'calculated_single', 'location', 'image', 'video', 'cableMatrix', 'locationSelection', 'document') NOT NULL");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('training_course_submodule_practical_assessment_questions', function (Blueprint $table) {
            $table->enum('question_type', ['text', 'rating', 'single', 'multiple', 'calculated_single', 'location', 'image', 'video', 'cableMatrix', 'locationSelection', 'document'])->change();
        });
    }
}
