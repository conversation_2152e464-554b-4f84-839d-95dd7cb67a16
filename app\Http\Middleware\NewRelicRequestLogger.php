<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Log;

class NewRelicRequestLogger
{
    /**
     * Handle an incoming request and log it to New Relic.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // Get the route and controller/method information
        $route = $request->route();
        $routeInfo = '';
        
        if (isset($route[1]['uses'])) {
            $routeInfo = $route[1]['uses'];
        }
        
        // Get request method and URI
        $method = $request->method();
        $uri = $request->path();
        
        // Get request payload (excluding sensitive data)
        $payload = $this->sanitizePayload($request->all());
        
        // Add custom parameters to New Relic
        if (extension_loaded('newrelic')) {
            // Set transaction name
            \newrelic_name_transaction("$method $uri");
            
            // Add request details as custom parameters
            \newrelic_add_custom_parameter('request.method', $method);
            \newrelic_add_custom_parameter('request.uri', $uri);
            \newrelic_add_custom_parameter('request.route', $routeInfo);
            
            // Add request payload as a custom parameter (as JSON)
            if (!empty($payload)) {
                \newrelic_add_custom_parameter('request.payload', json_encode($payload, JSON_UNESCAPED_UNICODE));
            }
            
            // Add user information if authenticated
            if ($request->user()) {
                \newrelic_add_custom_parameter('user.id', $request->user()->id);
                \newrelic_add_custom_parameter('user.email', $request->user()->email);
            }
        }
        
        // Process the request
        $response = $next($request);
        
        // Log response status
        if (extension_loaded('newrelic')) {
            \newrelic_add_custom_parameter('response.status', $response->getStatusCode());
        }
        
        return $response;
    }
    
    /**
     * Sanitize the payload to remove sensitive information
     *
     * @param array $payload
     * @return array
     */
    protected function sanitizePayload($payload)
    {
        // Create a copy of the payload
        $sanitized = $payload;
        
        // List of sensitive fields to mask
        $sensitiveFields = [
            'password', 'password_confirmation', 'current_password', 
            'token', 'access_token', 'refresh_token', 'api_key', 
            'secret', 'credit_card', 'card_number'
        ];
        
        // Mask sensitive fields
        foreach ($sensitiveFields as $field) {
            if (isset($sanitized[$field])) {
                $sanitized[$field] = '********';
            }
        }
        
        return $sanitized;
    }
}
