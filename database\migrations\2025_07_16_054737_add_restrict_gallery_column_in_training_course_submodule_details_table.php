<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddRestrictGalleryColumnInTrainingCourseSubmoduleDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('training_course_submodule_details', function (Blueprint $table) {
            if (!Schema::hasColumn('training_course_submodule_details', 'restrict_gallery')) {
                $table->boolean('restrict_gallery')->default(0)->after('allow_sa_fail');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('training_course_submodule_details', function (Blueprint $table) {
            if (Schema::hasColumn('training_course_submodule_details', 'restrict_gallery')) {
                $table->dropColumn('restrict_gallery');
            }
        });
    }
}
