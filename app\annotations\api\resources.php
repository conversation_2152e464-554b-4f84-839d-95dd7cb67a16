<?php
/**
     * @OA\Post(
     *     path="/api/resources",
     *     tags={"Mobile - Resources"},
     *     summary="Resources Listing",
     *     description="Resources Listing",
     *     operationId="index",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                  @OA\Property(
     *                     property="product_id",
     *                     description="Product Id",
     *                     type="integer"
     *                 ),
     *                  @OA\Property(
     *                     property="news_id",
     *                     description="News Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="type",
     *                     description="Resource Type",
     *                     type="string",
     *                     enum={"all","pdf","image","video"}
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort By",
     *                     type="string",
     *                     enum={"asc","desc","latest","oldest"}
     *                 ),
     *                 example={"product_id": 0, "news_id": 0, "type": "all", "sort_by": "asc"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */

/**
     * @OA\Get(
     *     path="/api/resources/{id}",
     *     tags={"Mobile - Resources"},
     *     summary="Resource Details",
     *     description="Resource Details",
     *     operationId="show",
     *     @OA\Parameter(
     *         description="Id of resource to fetch",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */
