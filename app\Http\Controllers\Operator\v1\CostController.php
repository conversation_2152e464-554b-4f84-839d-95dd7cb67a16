<?php

namespace App\Http\Controllers\Operator\v1;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\TrainingCourse;
use App\Models\TrainingCourseSubmoduleDetails;
use App\Models\TrainingCourseModules;
use App\Http\Requests\Operator\v1\CostRequest;
use App\Http\Resources\CustomCollection;


class CostController extends Controller
{

    /**
     * @OA\Get(
     *     path="/operator/costs/getUploadmediaSubmodules",
     *     tags={"Operator - Training Course Uploadmedia Submodule"},
     *     summary="Get All Uploadmedia Submodules",
     *     description="Get All Uploadmedia Submodules",
     *     operationId="getTrainingCourseUploadmediaSubmodules",
     *  
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getTrainingCourseUploadmediaSubmodules(){
        try {
            $trainingCourseList = TrainingCourse::with('modules','modules.subModulesType')
                                        ->has('modules')
                                        ->with(['modules' => function($q){
                                            $q->whereHas('subModulesType', function($q){
                                                $q->where('submodule_type_id',13);    
                                            }); 
                                        }])
                                        ->whereHas('modules', function($q){
                                            $q->whereHas('subModulesType', function($q){
                                                $q->where('submodule_type_id',13);    
                                            });
                                        })
                                        ->whereIn('master_user_id', getTeamMembers())
                                        ->get();

            $trainingCourseSubmoduleList = new CustomCollection($trainingCourseList, 'App\Http\Resources\Operator\v1\TrainingCoursesResource');
            $message = (count($trainingCourseList) > 0) ? __('operator.cost.uploadMediaFound') : __('operator.cost.uploadMediaNotFound');
            return response()->json(setResponse($trainingCourseSubmoduleList, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

     /**
     * @OA\Post(
     *     path="/operator/costs/saveCost",
     *     tags={"Save Upload Media Costing"},
     *     summary="Save Upload Media Costing",
     *     description="Save Upload Media Costing",
     *     operationId="saveCost",
     *      @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 @OA\Property(
     *                     property="submodules",
     *                     description="sumodule_id and cost",
     *                     type="array",
     *                     @OA\Items(
     *                     type="object",
     *                          @OA\Property(
     *                              property="submodule_id",
     *                              description="Submodule Id",
     *                              type="integer"
     *                          ),
     *                          @OA\Property(
     *                              property="cost",
     *                              description="Cost",
     *                              type="integer"
     *                          )
     *                      )
     *                 ),
     *              
     *                 example={"submodules":{{"submodule_id":12,"cost":24},{"submodule_id":1165,"cost":555},{"submodule_id":1166,"cost":33},{"submodule_id":1189,"cost":0},{"submodule_id":1190,"cost":0},{"submodule_id":1420,"cost":0},{"submodule_id":1421,"cost":0},{"submodule_id":1245,"cost":0},{"submodule_id":1382,"cost":0},{"submodule_id":1383,"cost":0},{"submodule_id":1427,"cost":0},{"submodule_id":1471,"cost":0},{"submodule_id":1655,"cost":0},{"submodule_id":1554,"cost":0},{"submodule_id":1643,"cost":0},{"submodule_id":1662,"cost":10},{"submodule_id":1656,"cost":0},{"submodule_id":1618,"cost":10},{"submodule_id":1638,"cost":20},{"submodule_id":1639,"cost":50},{"submodule_id":1640,"cost":0},{"submodule_id":1641,"cost":120},{"submodule_id":1654,"cost":100},{"submodule_id":1658,"cost":250},{"submodule_id":1661,"cost":0},{"submodule_id":1685,"cost":10},{"submodule_id":1686,"cost":20},{"submodule_id":1687,"cost":50},{"submodule_id":1688,"cost":0},{"submodule_id":1689,"cost":120},{"submodule_id":1691,"cost":100},{"submodule_id":1692,"cost":0},{"submodule_id":1693,"cost":0},{"submodule_id":1749,"cost":23},{"submodule_id":1813,"cost":23}}}
     *                       
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function saveCost(CostRequest $request){
        try {
            $costSubmodule = $request['submodules'];
            if(count($costSubmodule)>0){
                foreach($costSubmodule as $submodule){
                    $submodule = TrainingCourseSubmoduleDetails::where('id',$submodule['submodule_id'])->update(['cost' => $submodule['cost']]);    
                }
            }
            return response()->json(setResponse([], ['message' => __("operator.cost.costSaveSuccess")]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
