<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;

class TrainingCourseModuleCopyPasteRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array
    {
        return [
            'trainingCourseId' => 'required|int|exists:'.\DBTableNames::TRAINING_COURSE.',id',
            'moduleId' => 'required|int|exists:'.\DBTableNames::TRAINING_COURSE_MODULES.',id'
        ];
    }
    
    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'trainingCourseId.required' => __('Training course id is required.'),
            'moduleId.required' => __('Training course module id is required.'),
            'trainingCourseId.int' => __('training course id must be integer.'),
            'moduleId.int' => __('training course module id must be integer.'),
            'trainingCourseId.exists' => __('Invalid training course id.'),
            'moduleId.exists' => __('Invalid training course module id.'),
        ];
    }
}
