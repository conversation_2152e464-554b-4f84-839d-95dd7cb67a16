<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Http\Response;


class EnsureEmailIsVerified {

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null
     * @return mixed
     */
    public function handle($request, Closure $next) {
        if (!$request->user() || ($request->user() instanceof MustVerifyEmail && !$request->user()->hasVerifiedEmail())) {
            return response()->json(setErrorResponse('Unauthorized, your email address ' . $request->user()->email . ' is not verified.', Response::HTTP_UNAUTHORIZED))
                            ->setStatusCode(Response::HTTP_UNAUTHORIZED);
        }
        return $next($request);
    }

}
