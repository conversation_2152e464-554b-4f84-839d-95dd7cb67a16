<?php

namespace App\Http\Requests\Operator\v1;

use App\Http\Requests\CustomFormRequest;

class TrainingCourseSubmoduleJobProgressUpdateRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {
        return [
            'status' => 'required|in:Approved,Rejected',
            'operator_comments' => 'required_if:status,Rejected',
        ];
    }

    public function messages() : array
    {
        return [
            'status.required' => __('Status is required.'),
            'status.in' => __('Invalid status.'),
            'operator_comments.required_if' => __('Comment is required.'),
        ];
    }
}
