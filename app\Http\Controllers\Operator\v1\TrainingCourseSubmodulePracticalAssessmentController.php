<?php

namespace App\Http\Controllers\Operator\v1;

use App\Models\User;
use App\Models\Roles;
use App\Models\Severity;
use App\Models\AdjustResult;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\PQMSQuestionCategory;
use App\Models\TrainingCourseProgress;
use Barryvdh\DomPDF\Facade\Pdf as PDF;
use App\Models\PracticalAssessmentMedia;
use Illuminate\Database\Eloquent\Builder;
use App\Models\PracticalAssessmentComment;
use App\Http\Requests\Operator\v1\PARequest;
use App\Models\TrainingCourseModuleProgress;
use App\Models\PracticalAssessmentGeoLocation;
use App\Models\TrainingCourseSubmoduleProgress;
use App\Http\Requests\Operator\v1\CommonListingRequest;
use App\Models\TrainingCourseSubModulePracticalAssessmentAnswer;
use App\Models\TrainingCourseSubModulePracticalAssessmentOption;
use App\Models\TrainingCourseSubModulePracticalAssessmentResults;
use App\Http\Resources\Operator\v1\PracticalAssessmentCatResource;
use App\Models\TrainingCourseSubModulePracticalAssessmentQuestion;
use App\Http\Resources\Operator\v1\PracticalAssessmentCommentResource;
use App\Http\Resources\Operator\v1\PracticalAssessmentListingResource;
use App\Http\Resources\Operator\v1\PracticalAssessmentPDFExportResource;
use App\Http\Resources\Operator\v1\PracticalAssessmentSystemLogsResource;
use App\Repositories\Operator\v1\TrainingCourseSubModuleAssessmentRepository;
use App\Http\Requests\Operator\v1\TrainingCourseSubModuleAssessmentListingRequest;
use App\Http\Resources\Operator\v1\TrainingCourseSubModuleAssessmentResponseResource;
use App\Http\Resources\Operator\v1\TrainingCourseSubModulePracticalAssessmentResource;
use App\Http\Resources\Operator\v1\TrainingCourseSubModuleAssessmentExtraResponseResource;
use App\Http\Resources\Operator\v1\TrainingCourseSubModuleSelfAssessmentResource;

class TrainingCourseSubmodulePracticalAssessmentController extends Controller
{
    protected $assessmentModel;
    protected $assessmentkRepository;

    public function __construct() {
        $this->assessmentModel = new TrainingCourseSubModulePracticalAssessmentAnswer();
        $this->assessmentRepository = new TrainingCourseSubModuleAssessmentRepository($this->assessmentModel);
        $this->assessmentResultModel = new TrainingCourseSubModulePracticalAssessmentResults();
    }

    /**
     * Listing of single practical assessment
     */
    public function getListing(TrainingCourseSubModuleAssessmentListingRequest $request) {
        try {
            // checking for assessment portal feature setting
            $checkAssessment = checkAssessment();
            if($checkAssessment == 1){
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
            // main
            $data = $this->assessmentRepository->getAssessmentResponseListing($request->all());
            if ($request->isExport == 1) {
                $exportData = $data->get()->toArray();
                return $this->assessmentRepository->exportCsv($exportData, $request->exportFields);
            }
            return TrainingCourseSubModulePracticalAssessmentResource::collection($data->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
     /**
     * Listing of single practical assessment
     */
    public function getSelfAssessmentListing(TrainingCourseSubModuleAssessmentListingRequest $request) {
        try {
            // // checking for assessment portal feature setting
            // $checkAssessment = checkAssessment();
            // if($checkAssessment == 1){
            //     return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            // }
            // main
            $data = $this->assessmentRepository->getSelfAssessmentResponseListing($request->all());
            if ($request->isExport == 1) {
                $exportData = $data->get()->toArray();
                return $this->assessmentRepository->exportCsv($exportData, $request->exportFields);
            }
            return TrainingCourseSubModuleSelfAssessmentResource::collection($data->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Specific Assessment Response - View assessment
     */
    public function viewAssessment($submoduleId, $assessorId, $userId) {
        try {
            // checking for assessment portal feature setting
            $checkAssessment = checkAssessment();
            if($checkAssessment == 1){
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
            //main
            $assessor_id = User::where('assessor_id',$assessorId)->value('id');
            $userResponse = $this->assessmentModel->where(['submodule_id' => $submoduleId, 'user_id' => $userId, 'assessor_id'=>$assessor_id, 'type' => 'PA'])
                            ->first();

            //PQMS Progress Calculation Status
            $UserCount = TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submoduleId)->where('assessor_id',$assessor_id)->where('user_id',$userId)->count('id');
            if($UserCount>0){
                $result = DB::table('training_course_submodule_practical_assessment_results')->where('submodule_id', $submoduleId)->where('assessor_id', $assessor_id)->where('user_id', $userId)->value('is_pass');
                if (isset($result) && $result==0 || $result==1) {
                    $status = $result;
                } else {
                    $status = 2;
                }
            }else {
                $status = 3;
            }

            if ($userResponse) {
                $Location = PracticalAssessmentGeoLocation::where(['submodule_id' => $submoduleId, 'user_id' => $userId, 'assessor_id'=>$assessor_id])
                            ->first();
                $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;

                return [
                    "id"=> $userResponse->id,
                    "name"=> $userResponse->user->name,
                    "email" => $userResponse->user->email,
                    "photo" => $userResponse->user->photo_url,
                    "assessor_name" => $userResponse->assessor->name,
                    "assessor_email" => $userResponse->assessor->email,
                    "assessed_by_assessor" => $userResponse->assessed_by_assessor,
                    "assessor_id" => $userResponse->assessor->assessor_id,
                    "assessment_result" => $status,
                    "start_latitude" => isset($Location->start_latitude)?$Location->start_latitude:null,
                    "start_longitude" => isset($Location->start_longitude)?$Location->start_longitude:null,
                    "end_latitude" => isset($Location->end_latitude)?$Location->end_latitude:null,
                    "end_longitude" => isset($Location->end_longitude)?$Location->end_longitude:null,
                    "course_name" => $userResponse->trainingCourse->title ?? null,
                    "manager_email" => $userResponse->masterUser->email ?? null,
                    "updated_at" => !empty($userResponse->updated_at)?date_format($userResponse->updated_at, 'Y-m-d H:i:s'):null,
                ];
            }
            return response()->json(setResponse([], ['message' => __('operator.assessment.not-found')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    /**
     * Get Specific Assessment Response - View assessment
     */
    public function viewSelfAssessment($submoduleId, $userId) {
        try {
            //main
            $assessor_id = $userId;
            $userResponse = $this->assessmentModel->where(['submodule_id' => $submoduleId, 'user_id' => $userId, 'assessor_id'=>$assessor_id,'type'=>'Self'])
                            ->first();

            //PQMS Progress Calculation Status
            $UserCount = TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submoduleId)->where('assessor_id',$assessor_id)->where('user_id',$userId)->count('id');
            if($UserCount>0){
                $result = DB::table('training_course_submodule_practical_assessment_results')->where('submodule_id', $submoduleId)->where('assessor_id', $assessor_id)->where('user_id', $userId)->value('is_pass');
                if (isset($result) && $result==0 || $result==1) {
                    $status = $result;
                } else {
                    $status = 2;
                }
            }else {
                $status = 3;
            }

            if ($userResponse) {
                $Location = PracticalAssessmentGeoLocation::where(['submodule_id' => $submoduleId, 'user_id' => $userId, 'assessor_id'=>$assessor_id])
                            ->first();
                $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;

                return [
                    "id"=> $userResponse->id,
                    "name"=> $userResponse->user->name,
                    "email" => $userResponse->user->email,
                    "photo" => $userResponse->user->photo_url,
                    // "assessor_name" => $userResponse->assessor->name,
                    // "assessor_email" => $userResponse->assessor->email,
                    // "assessed_by_assessor" => $userResponse->assessed_by_assessor,
                    // "assessor_id" => $userResponse->assessor->assessor_id,
                    "assessment_result" => $status,
                    "start_latitude" => isset($Location->start_latitude)?$Location->start_latitude:null,
                    "start_longitude" => isset($Location->start_longitude)?$Location->start_longitude:null,
                    "end_latitude" => isset($Location->end_latitude)?$Location->end_latitude:null,
                    "end_longitude" => isset($Location->end_longitude)?$Location->end_longitude:null,
                    "course_name" => $userResponse->trainingCourse->title ?? null,
                    "manager_email" => $userResponse->masterUser->email ?? null,
                    "updated_at" => !empty($userResponse->updated_at)?date_format($userResponse->updated_at, 'Y-m-d H:i:s'):null,
                ];
            }
            return response()->json(setResponse([], ['message' => __('operator.assessment.not-found')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
/**
     * Get Specific Assessment Response - View assessment
     */
    public function viewPaAssessment($submoduleId, $assessorId, $userId) {
        try {
            //main
            $assessor_id = User::where('assessor_id',$assessorId)->value('id');
            $userResponse = $this->assessmentModel->where(['submodule_id' => $submoduleId, 'user_id' => $userId, 'assessor_id'=>$assessor_id, 'type' => 'PA'])
                            ->first();

	        $categories = PQMSQuestionCategory::select('pqms_submodule_categories.*')
			    ->join('training_course_submodule_practical_assessment_questions as questions', 'questions.category_id', '=', 'pqms_submodule_categories.id')
			    ->where(['pqms_submodule_categories.submodule_id' => $submoduleId, 'pqms_submodule_categories.status' => 'Active'])->whereNull('pqms_submodule_categories.deleted_at')->groupBy('pqms_submodule_categories.id')->get();
	        //Records having main media & with questions
            $assessment = $this->assessmentModel::select('training_course_submodule_practical_assessment_answers.*')->with('question', 'media')
            ->leftjoin('training_course_submodule_practical_assessment_questions', 'training_course_submodule_practical_assessment_questions.id', 'training_course_submodule_practical_assessment_answers.question_id')
            ->where(['training_course_submodule_practical_assessment_answers.submodule_id' => $submoduleId, 'training_course_submodule_practical_assessment_answers.user_id' => $userId, 'assessor_id' => $assessor_id])
            ->whereNotNull('training_course_submodule_practical_assessment_answers.question_id')
            ->orderBy('training_course_submodule_practical_assessment_questions.question_list_order', 'ASC')
            ->orderBy('training_course_submodule_practical_assessment_questions.question_order', 'ASC')
            ->orderBy('training_course_submodule_practical_assessment_questions.id', 'ASC')
            ->groupBy(DB::raw('IFNULL(training_course_submodule_practical_assessment_questions.category_id, training_course_submodule_practical_assessment_questions.id)'))->get();

            //Records having extra media & with no questions
            $assessmentExtra = PracticalAssessmentMedia::where(['submodule_id' => $submoduleId,'assessor_id'=>$assessor_id])->where('media_content','Extra')->whereNull('question_id')->get();
            //PQMS Progress Calculation Status
            $UserCount = TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submoduleId)->where('assessor_id',$assessor_id)->where('user_id',$userId)->count('id');
            if ($userResponse) {
                $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
                $severity=Severity::select('severity_name')->where(['submodule_id' => $submoduleId,'master_user_id'=>$operatorId])->get();
                $TotalSubModuleQuestion=TrainingCourseSubModulePracticalAssessmentQuestion::where('submodule_id',$submoduleId)->pluck('id');
                $TotalGeneralSubModuleQuestion=TrainingCourseSubModulePracticalAssessmentQuestion::where('submodule_id',$submoduleId)->whereNull('category_id')->pluck('id');
                $TotalDeletedQuestion=TrainingCourseSubModulePracticalAssessmentQuestion::where('submodule_id',$submoduleId)->onlyTrashed()->pluck('id');
                $TotalGeneralDeletedQuestion=TrainingCourseSubModulePracticalAssessmentQuestion::where('submodule_id',$submoduleId)->whereNull('category_id')->onlyTrashed()->pluck('id');
                $DeleteQuestionCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submoduleId)->where('assessor_id',$assessor_id)->where('user_id',$userId)->whereIn('question_id',$TotalDeletedQuestion)->distinct('question_id')->count('id');
                $DeleteGeneralQuestionCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submoduleId)->where('assessor_id',$assessor_id)->where('user_id',$userId)->whereIn('question_id',$TotalGeneralDeletedQuestion)->distinct('question_id')->count('id');
                $generalAnsweredCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submoduleId)->where('assessor_id',$assessor_id)->where('user_id',$userId)->whereIn('question_id',$TotalGeneralSubModuleQuestion)->distinct('question_id')->count('id');
                $talisData=[
                    "total_question"=> count($TotalSubModuleQuestion)+ $DeleteQuestionCount,
                    "total_answered_questions"=> $UserCount,
                    "severity"=> $this->GetSeverityCount($severity,$submoduleId,$userId,$operatorId,$assessor_id),
                ];
                $generalTalisData=[
                    "total_question"=> count($TotalGeneralSubModuleQuestion)+ $DeleteGeneralQuestionCount,
                    "total_answered_questions"=> $generalAnsweredCount+$DeleteGeneralQuestionCount,
                    "severity"=> $this->GetGeneralSeverityCount($severity,$submoduleId,$userId,$operatorId,$assessor_id),
                ];
                return [
                    "id"=> $userResponse->id,
                    "overall_question_tallies" => $talisData,
                    "general_question_tallies" => $generalTalisData,
                    "question_list" => TrainingCourseSubModuleAssessmentResponseResource::collection($assessment),
                    "additional_media" => TrainingCourseSubModuleAssessmentExtraResponseResource::collection($assessmentExtra ?? []),
                ];
            }
            return response()->json(setResponse([], ['message' => __('operator.assessment.not-found')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    /**
     * Get Specific Assessment Response - View assessment
     */
    public function viewSaAssessment($submoduleId, $userId) {
        try {
            //main
            $assessor_id = $userId;
            $userResponse = $this->assessmentModel->where(['submodule_id' => $submoduleId, 'user_id' => $userId, 'assessor_id'=>$assessor_id,'type'=>'Self'])
                            ->first();

	        $categories = PQMSQuestionCategory::select('pqms_submodule_categories.*')
			    ->join('training_course_submodule_practical_assessment_questions as questions', 'questions.category_id', '=', 'pqms_submodule_categories.id')
			    ->where(['pqms_submodule_categories.submodule_id' => $submoduleId, 'pqms_submodule_categories.status' => 'Active'])->whereNull('pqms_submodule_categories.deleted_at')->groupBy('pqms_submodule_categories.id')->get();
	        //Records having main media & with questions
            $assessment = $this->assessmentModel::select('training_course_submodule_practical_assessment_answers.*')->with('question', 'media')
            ->leftjoin('training_course_submodule_practical_assessment_questions', 'training_course_submodule_practical_assessment_questions.id', 'training_course_submodule_practical_assessment_answers.question_id')
            ->where(['training_course_submodule_practical_assessment_answers.submodule_id' => $submoduleId, 'training_course_submodule_practical_assessment_answers.user_id' => $userId, 'assessor_id' => $assessor_id])
            ->whereNotNull('training_course_submodule_practical_assessment_answers.question_id')
            ->orderBy('training_course_submodule_practical_assessment_questions.question_list_order', 'ASC')
            ->orderBy('training_course_submodule_practical_assessment_questions.question_order', 'ASC')
            ->orderBy('training_course_submodule_practical_assessment_questions.id', 'ASC')
            ->groupBy(DB::raw('IFNULL(training_course_submodule_practical_assessment_questions.category_id, training_course_submodule_practical_assessment_questions.id)'))->get();

            //Records having extra media & with no questions
            $assessmentExtra = PracticalAssessmentMedia::where(['submodule_id' => $submoduleId,'assessor_id'=>$assessor_id])->where('media_content','Extra')->whereNull('question_id')->get();
            //PQMS Progress Calculation Status
            $UserCount = TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submoduleId)->where('assessor_id',$assessor_id)->where('user_id',$userId)->count('id');
            if ($userResponse) {
                $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
                $severity=Severity::select('severity_name')->where(['submodule_id' => $submoduleId])->get();
                $TotalSubModuleQuestion=TrainingCourseSubModulePracticalAssessmentQuestion::where('submodule_id',$submoduleId)->pluck('id');
                $TotalGeneralSubModuleQuestion=TrainingCourseSubModulePracticalAssessmentQuestion::where('submodule_id',$submoduleId)->whereNull('category_id')->pluck('id');
                $TotalDeletedQuestion=TrainingCourseSubModulePracticalAssessmentQuestion::where('submodule_id',$submoduleId)->onlyTrashed()->pluck('id');
                $TotalGeneralDeletedQuestion=TrainingCourseSubModulePracticalAssessmentQuestion::where('submodule_id',$submoduleId)->whereNull('category_id')->onlyTrashed()->pluck('id');
                $DeleteQuestionCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submoduleId)->where('assessor_id',$assessor_id)->where('user_id',$userId)->whereIn('question_id',$TotalDeletedQuestion)->distinct('question_id')->count('id');
                $DeleteGeneralQuestionCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submoduleId)->where('assessor_id',$assessor_id)->where('user_id',$userId)->whereIn('question_id',$TotalGeneralDeletedQuestion)->distinct('question_id')->count('id');
                $generalAnsweredCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submoduleId)->where('assessor_id',$assessor_id)->where('user_id',$userId)->whereIn('question_id',$TotalGeneralSubModuleQuestion)->distinct('question_id')->count('id');
                $talisData=[
                    "total_question"=> count($TotalSubModuleQuestion)+ $DeleteQuestionCount,
                    "total_answered_questions"=> $UserCount,
                    "severity"=> $this->GetSeverityCount($severity,$submoduleId,$userId,$operatorId,$assessor_id),
                ];
                $generalTalisData=[
                    "total_question"=> count($TotalGeneralSubModuleQuestion)+ $DeleteGeneralQuestionCount,
                    "total_answered_questions"=> $generalAnsweredCount+$DeleteGeneralQuestionCount,
                    "severity"=> $this->GetGeneralSeverityCount($severity,$submoduleId,$userId,$operatorId,$assessor_id),
                ];
                return [
                    "id"=> $userResponse->id,
                    "overall_question_tallies" => $talisData,
                    "general_question_tallies" => $generalTalisData,
                    "question_list" => TrainingCourseSubModuleAssessmentResponseResource::collection($assessment),
                    "additional_media" => TrainingCourseSubModuleAssessmentExtraResponseResource::collection($assessmentExtra ?? []),
                ];
            }
            return response()->json(setResponse([], ['message' => __('operator.assessment.not-found')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    /**
     * @OA\Post(
     *     path="operator/practicalAssessment/getListing",
     *     tags={"Operator - Practical Assessment"},
     *     summary="Single Practical Assessment Listing",
     *     description="Single Practical Assessment Listing",
     *     operationId="getSinglePAListing",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="submodule_id",
     *                     description="Submodule Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 example={"submodule_id": 2174, "per_page": 10, "page": 1, "search_key": "", "sort_by": "name", "order_by": "asc", "filters": {"uname": "", "accessor_name": "", "status": ""}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getPracticalAssessmentListing(CommonListingRequest $request) {
        try {
            // checking for assessment portal feature setting
            $Role=Roles::where('id',auth()->guard('operator')->user()->role_id)->value('master_user_type');
            $checkAssessment = checkAssessment();
            if($checkAssessment == 1 && $Role!='IQA'){
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
            // main
            $data = $this->assessmentRepository->getPracticalAssessmentListing($request->all());
            if ($request->isExport == 1) {
                $exportData = $data->get()->toArray();
                return $this->assessmentRepository->exportPracticalAssessmentCsv($exportData, $request->exportFields);
            }
            return PracticalAssessmentListingResource::collection($data->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    public function getSelfAssessmentResultListing(CommonListingRequest $request) {
        try {
            // checking for assessment portal feature setting
            $Role=Roles::where('id',auth()->guard('operator')->user()->role_id)->value('master_user_type');
            $checkAssessment = checkAssessment();
            if($checkAssessment == 1 && $Role!='IQA'){
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
            // main
            $data = $this->assessmentRepository->getSelfAssessmentResultListing($request->all());
            if ($request->isExport == 1) {
                $exportData = $data->get()->toArray();
                return $this->assessmentRepository->exportPracticalAssessmentCsv($exportData, $request->exportFields);
            }
            return PracticalAssessmentListingResource::collection($data->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="operator/practicalAssessment/details/{id}",
     *     tags={"Operator - Practical Assessment"},
     *     summary="Edit Assessment Response",
     *     description="Edit Assessment Response",
     *     operationId="editAssessmentResponse",
     *     @OA\Parameter(
     *         description="Id of Result",
     *         in="path",
     *         name="resultID",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function editAssessmentResponse($id) {
        try {
            // checking for assessment portal feature setting
            $Role=Roles::where('id',auth()->guard('operator')->user()->role_id)->value('master_user_type');
            $checkAssessment = checkAssessment();
            if($checkAssessment == 1 && $Role!='IQA'){
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }

            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $result = TrainingCourseSubModulePracticalAssessmentResults::where('id', $id)->where('type','PA')->first();
            if($result && $result->master_user_id != $operatorId)
            {
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
            $result = TrainingCourseSubModulePracticalAssessmentResults::where('id', $id)->where('type','PA')->first();
            if($result){
                if(empty($result->trainingCourse)){
                    return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.dosentExist')]))->setStatusCode(Response::HTTP_OK);
                }

                $assessor_id = $result->assessor_id;
                $submoduleId = $result->submodule_id;
                $userId = $result->user_id;
                $moduleId = $result->module_id;
                $trainingCourseID = $result->training_course_id;
                $startAnswerDate = TrainingCourseSubModulePracticalAssessmentAnswer::where('training_course_id', $trainingCourseID)
									->where('module_id', $moduleId)
									->where('submodule_id', $submoduleId)
									->where('assessor_id', $assessor_id)
									->where('user_id', $userId)
									->where('master_user_id', $operatorId)
									->value('created_at');
                if($result->is_result_override==1)
                {
                    $startDate=$result->created_at;
                }else{
                    $startDate=$startAnswerDate;
                }
                $userResponse = $this->assessmentModel->where(['submodule_id' => $submoduleId, 'user_id' => $userId, 'assessor_id'=>$assessor_id])->first();
                if($userResponse){
                    //PQMS Progress Calculation Status
                    $UserCount = TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submoduleId)->where('assessor_id',$assessor_id)->where('user_id',$userId)->distinct('question_id')->count('id');

                    $assessmentCommentsAndMedia = PracticalAssessmentComment::where('result_id',$id)->whereNull('deleted_at')->orderBy('id','ASC')->get();

                    //Assessment result
                    if($UserCount>0){
                        if (isset($result->is_pass) && $result->is_pass==0 || $result->is_pass==1) {
                            $status = $result->is_pass;
                        } else {
                            $status = 2;
                        }
                    }else {
                        $status = 3;
                    }
                    $Location=PracticalAssessmentGeoLocation::where(['submodule_id' => $submoduleId, 'user_id' => $userId, 'assessor_id'=>$assessor_id])->first();
                    $severity=Severity::select('severity_name')->where(['submodule_id' => $submoduleId,'master_user_id'=>$operatorId])->get();
                    return [
                        "id"=> $result->id,
                        "user_id"=> $userResponse->user->id,
                        "name"=> $userResponse->user->name,
                        "email" => $userResponse->user->email,
                        "photo" => $userResponse->user->photo_url,
                        "assessor_user_id" => $userResponse->assessor->id,
                        "assessor_name" => $userResponse->assessor->name,
                        "assessor_email" => $userResponse->assessor->email,
                        "assessed_by_assessor" => $userResponse->assessed_by_assessor,
                        "assessor_id" => $userResponse->assessor->assessor_id,
                        "assessment_result" => $status,
                        "course_name" => $result->trainingCourse->title,
                        "module_name" => $result->module->name,
                        "submodule_id" => $result->subModuleWithTrash->id,
                        "submodule_name" => $result->subModuleWithTrash->submodule_name,
                        "start_date" => (!empty($startDate)) ? date_format($startDate, 'Y-m-d H:i:s') :  null,
                        "completion_date" => ($result->is_pass == 1) ? date_format($result->created_at, 'Y-m-d H:i:s') :  null,
                        "last_activity_date" => date_format($result->created_at, 'Y-m-d H:i:s'),
                        "practical_result" => $result->status,
                        "practical_comment" => PracticalAssessmentCommentResource::collection($assessmentCommentsAndMedia),
                        "start_latitude" => isset($Location->start_latitude)?$Location->start_latitude:null,
                        "start_longitude" => isset($Location->start_longitude)?$Location->start_longitude:null,
                        "end_latitude" => isset($Location->end_latitude)?$Location->end_latitude:null,
                        "end_longitude" => isset($Location->end_longitude)?$Location->end_longitude:null,
                    ];
                }else{
                    return response()->json(setResponse([], ['message' => __('operator.assessment.not-found')]))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
            }else{
                return response()->json(setResponse([], ['message' => __('operator.assessment.not-found')]))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            echo "<pre>";
            print_r($e->getMessage());
            exit;
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

     public function editSelfAssessmentResponse($id) {
        try {
            // checking for assessment portal feature setting
            $Role=Roles::where('id',auth()->guard('operator')->user()->role_id)->value('master_user_type');
            $checkAssessment = checkAssessment();
            if($checkAssessment == 1 && $Role!='IQA'){
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }

            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $result = TrainingCourseSubModulePracticalAssessmentResults::where('id', $id)->where('type','PA')->first();
            if($result && $result->master_user_id != $operatorId)
            {
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
            $result = TrainingCourseSubModulePracticalAssessmentResults::where('id', $id)->where('type','Self')->first();
            if($result){
                if(empty($result->trainingCourse)){
                    return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.dosentExist')]))->setStatusCode(Response::HTTP_OK);
                }

                $assessor_id = $result->assessor_id;
                $submoduleId = $result->submodule_id;
                $userId = $result->user_id;
                $moduleId = $result->module_id;
                $trainingCourseID = $result->training_course_id;
                $startAnswerDate = TrainingCourseSubModulePracticalAssessmentAnswer::where('training_course_id', $trainingCourseID)
									->where('module_id', $moduleId)
									->where('submodule_id', $submoduleId)
									->where('assessor_id', $assessor_id)
									->where('user_id', $userId)
									->where('master_user_id', $operatorId)
									->value('created_at');
                if($result->is_result_override==1)
                {
                    $startDate=$result->created_at;
                }else{
                    $startDate=$startAnswerDate;
                }
                $userResponse = $this->assessmentModel->where(['submodule_id' => $submoduleId, 'user_id' => $userId, 'assessor_id'=>$assessor_id])->first();
                if($userResponse){
                    //PQMS Progress Calculation Status
                    $UserCount = TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submoduleId)->where('assessor_id',$assessor_id)->where('user_id',$userId)->distinct('question_id')->count('id');

                    $assessmentCommentsAndMedia = PracticalAssessmentComment::where('result_id',$id)->whereNull('deleted_at')->orderBy('id','ASC')->get();

                    //Assessment result
                    if($UserCount>0){
                        if (isset($result->is_pass) && $result->is_pass==0 || $result->is_pass==1) {
                            $status = $result->is_pass;
                        } else {
                            $status = 2;
                        }
                    }else {
                        $status = 3;
                    }
                    $Location=PracticalAssessmentGeoLocation::where(['submodule_id' => $submoduleId, 'user_id' => $userId, 'assessor_id'=>$assessor_id])->first();
                    $severity=Severity::select('severity_name')->where(['submodule_id' => $submoduleId,'master_user_id'=>$operatorId])->get();
                    return [
                        "id"=> $result->id,
                        "user_id"=> $userResponse->user->id,
                        "name"=> $userResponse->user->name,
                        "email" => $userResponse->user->email,
                        "photo" => $userResponse->user->photo_url,
                        "assessment_result" => $status,
                        "course_name" => $result->trainingCourse->title,
                        "module_name" => $result->module->name,
                        "submodule_id" => $result->subModuleWithTrash->id,
                        "submodule_name" => $result->subModuleWithTrash->submodule_name,
                        "start_date" => (!empty($startDate)) ? date_format($startDate, 'Y-m-d H:i:s') :  null,
                        "completion_date" => ($result->is_pass == 1) ? date_format($result->created_at, 'Y-m-d H:i:s') :  null,
                        "last_activity_date" => date_format($result->created_at, 'Y-m-d H:i:s'),
                        "practical_result" => $result->status,
                        "practical_comment" => PracticalAssessmentCommentResource::collection($assessmentCommentsAndMedia),
                        "start_latitude" => isset($Location->start_latitude)?$Location->start_latitude:null,
                        "start_longitude" => isset($Location->start_longitude)?$Location->start_longitude:null,
                        "end_latitude" => isset($Location->end_latitude)?$Location->end_latitude:null,
                        "end_longitude" => isset($Location->end_longitude)?$Location->end_longitude:null,
                    ];
                }else{
                    return response()->json(setResponse([], ['message' => __('operator.assessment.not-found')]))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
            }else{
                return response()->json(setResponse([], ['message' => __('operator.assessment.not-found')]))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            echo "<pre>";
            print_r($e->getMessage());
            exit;
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="operator/practicalAssessment/pa_details/{id}",
     *     tags={"Operator - Practical Assessment"},
     *     summary="Edit Assessment Response",
     *     description="Edit Assessment Response",
     *     operationId="PaAssessmentResponse",
     *     @OA\Parameter(
     *         description="Id of Result",
     *         in="path",
     *         name="resultID",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function PaAssessmentResponse($id) {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            //main
            $result = TrainingCourseSubModulePracticalAssessmentResults::where('id', $id)->where('type','PA')->first();
            if($result && $result->master_user_id != $operatorId)
            {
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
            if($result){
                if(empty($result->trainingCourse)){
                    return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.dosentExist')]))->setStatusCode(Response::HTTP_OK);
                }

                $assessor_id = $result->assessor_id;
                $submoduleId = $result->submodule_id;
                $userId = $result->user_id;
                $userResponse = $this->assessmentModel->where(['submodule_id' => $submoduleId, 'user_id' => $userId, 'assessor_id'=>$assessor_id])->first();
                if($userResponse){
                    /* Edited by kishor start */
                    $assessment = $this->assessmentModel::select('training_course_submodule_practical_assessment_answers.*')->with('question', 'media')
                    ->leftjoin('training_course_submodule_practical_assessment_questions', 'training_course_submodule_practical_assessment_questions.id', 'training_course_submodule_practical_assessment_answers.question_id')
                    ->where(['training_course_submodule_practical_assessment_answers.submodule_id' => $submoduleId, 'training_course_submodule_practical_assessment_answers.user_id' => $userId, 'assessor_id' => $assessor_id])
                    ->whereNotNull('training_course_submodule_practical_assessment_answers.question_id')
                    ->orderBy('training_course_submodule_practical_assessment_questions.question_list_order', 'ASC')
                    ->orderBy('training_course_submodule_practical_assessment_questions.question_order', 'ASC')
                    ->orderBy('training_course_submodule_practical_assessment_questions.id', 'ASC')
                    ->groupBy(DB::raw('IFNULL(training_course_submodule_practical_assessment_questions.category_id, training_course_submodule_practical_assessment_questions.id)'))->get();
                    //Records having extra media & with no questions
                    $assessmentExtra = PracticalAssessmentMedia::where(['submodule_id' => $submoduleId,'assessor_id'=>$assessor_id])->where('media_content','Extra')->whereNull('question_id')->get();
                    //PQMS Progress Calculation Status
                    $UserCount = TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submoduleId)->where('assessor_id',$assessor_id)->where('user_id',$userId)->distinct('question_id')->count('id');

                    $severity=Severity::select('severity_name')->where(['submodule_id' => $submoduleId,'master_user_id'=>$operatorId])->get();
                    $TotalSubModuleQuestion=TrainingCourseSubModulePracticalAssessmentQuestion::where('submodule_id',$submoduleId)->pluck('id');
                    $TotalGeneralSubModuleQuestion=TrainingCourseSubModulePracticalAssessmentQuestion::where('submodule_id',$submoduleId)->whereNull('category_id')->pluck('id');
                    $TotalDeletedQuestion=TrainingCourseSubModulePracticalAssessmentQuestion::where('submodule_id',$submoduleId)->onlyTrashed()->pluck('id');
                    $TotalGeneralDeletedQuestion=TrainingCourseSubModulePracticalAssessmentQuestion::where('submodule_id',$submoduleId)->whereNull('category_id')->onlyTrashed()->pluck('id');
                    $DeleteQuestionCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submoduleId)->where('assessor_id',$assessor_id)->where('user_id',$userId)->whereIn('question_id',$TotalDeletedQuestion)->distinct('question_id')->count('id');
                    $DeleteGeneralQuestionCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submoduleId)->where('assessor_id',$assessor_id)->where('user_id',$userId)->whereIn('question_id',$TotalGeneralDeletedQuestion)->distinct('question_id')->count('id');
                    $generalAnsweredCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submoduleId)->where('assessor_id',$assessor_id)->where('user_id',$userId)->whereIn('question_id',$TotalGeneralSubModuleQuestion)->distinct('question_id')->count('id');
                    $talisData=[
                        "total_question"=> count($TotalSubModuleQuestion)+ $DeleteQuestionCount,
                        "total_answered_questions"=> $UserCount,
                        "severity"=> $this->GetSeverityCount($severity,$submoduleId,$userId,$operatorId,$assessor_id),
                    ];
                    $generalTalisData=[
                        "total_question"=> count($TotalGeneralSubModuleQuestion)+ $DeleteGeneralQuestionCount,
                        "total_answered_questions"=> $generalAnsweredCount+$DeleteGeneralQuestionCount,
                        "severity"=> $this->GetGeneralSeverityCount($severity,$submoduleId,$userId,$operatorId,$assessor_id),
                    ];
                    return [
                        "id"=> $result->id,
                        "overall_question_tallies" => $talisData,
                        "general_question_tallies" => $generalTalisData,
                        "question_list" => TrainingCourseSubModuleAssessmentResponseResource::collection($assessment),
                        "additional_media" => TrainingCourseSubModuleAssessmentExtraResponseResource::collection($assessmentExtra ?? []),
                    ];
                }else{
                    return response()->json(setResponse([], ['message' => __('operator.assessment.not-found')]))->setStatusCode(Response::HTTP_OK);
                }
            }else{
                return response()->json(setResponse([], ['message' => __('operator.assessment.not-found')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            echo "<pre>";
            print_r($e->getMessage());
            exit;
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

     public function SaAssessmentResponse($id) {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            //main
            $result = TrainingCourseSubModulePracticalAssessmentResults::where('id', $id)->where('type','Self')->first();
            if($result && $result->master_user_id != $operatorId)
            {
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
            if($result){
                if(empty($result->trainingCourse)){
                    return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.dosentExist')]))->setStatusCode(Response::HTTP_OK);
                }

                $assessor_id = $result->assessor_id;
                $submoduleId = $result->submodule_id;
                $userId = $result->user_id;
                $userResponse = $this->assessmentModel->where(['submodule_id' => $submoduleId, 'user_id' => $userId, 'assessor_id'=>$assessor_id])->first();
                if($userResponse){
                    /* Edited by kishor start */
                    $assessment = $this->assessmentModel::select('training_course_submodule_practical_assessment_answers.*')->with('question', 'media')
                    ->leftjoin('training_course_submodule_practical_assessment_questions', 'training_course_submodule_practical_assessment_questions.id', 'training_course_submodule_practical_assessment_answers.question_id')
                    ->where(['training_course_submodule_practical_assessment_answers.submodule_id' => $submoduleId, 'training_course_submodule_practical_assessment_answers.user_id' => $userId, 'assessor_id' => $assessor_id])
                    ->whereNotNull('training_course_submodule_practical_assessment_answers.question_id')
                    ->orderBy('training_course_submodule_practical_assessment_questions.question_list_order', 'ASC')
                    ->orderBy('training_course_submodule_practical_assessment_questions.question_order', 'ASC')
                    ->orderBy('training_course_submodule_practical_assessment_questions.id', 'ASC')
                    ->groupBy(DB::raw('IFNULL(training_course_submodule_practical_assessment_questions.category_id, training_course_submodule_practical_assessment_questions.id)'))->get();
                    //Records having extra media & with no questions
                    $assessmentExtra = PracticalAssessmentMedia::where(['submodule_id' => $submoduleId,'assessor_id'=>$assessor_id])->where('media_content','Extra')->whereNull('question_id')->get();
                    //PQMS Progress Calculation Status
                    $UserCount = TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submoduleId)->where('assessor_id',$assessor_id)->where('user_id',$userId)->distinct('question_id')->count('id');

                    $severity=Severity::select('severity_name')->where(['submodule_id' => $submoduleId,'master_user_id'=>$operatorId])->get();
                    $TotalSubModuleQuestion=TrainingCourseSubModulePracticalAssessmentQuestion::where('submodule_id',$submoduleId)->pluck('id');
                    $TotalGeneralSubModuleQuestion=TrainingCourseSubModulePracticalAssessmentQuestion::where('submodule_id',$submoduleId)->whereNull('category_id')->pluck('id');
                    $TotalDeletedQuestion=TrainingCourseSubModulePracticalAssessmentQuestion::where('submodule_id',$submoduleId)->onlyTrashed()->pluck('id');
                    $TotalGeneralDeletedQuestion=TrainingCourseSubModulePracticalAssessmentQuestion::where('submodule_id',$submoduleId)->whereNull('category_id')->onlyTrashed()->pluck('id');
                    $DeleteQuestionCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submoduleId)->where('assessor_id',$assessor_id)->where('user_id',$userId)->whereIn('question_id',$TotalDeletedQuestion)->distinct('question_id')->count('id');
                    $DeleteGeneralQuestionCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submoduleId)->where('assessor_id',$assessor_id)->where('user_id',$userId)->whereIn('question_id',$TotalGeneralDeletedQuestion)->distinct('question_id')->count('id');
                    $generalAnsweredCount=TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submoduleId)->where('assessor_id',$assessor_id)->where('user_id',$userId)->whereIn('question_id',$TotalGeneralSubModuleQuestion)->distinct('question_id')->count('id');
                    $talisData=[
                        "total_question"=> count($TotalSubModuleQuestion)+ $DeleteQuestionCount,
                        "total_answered_questions"=> $UserCount,
                        "severity"=> $this->GetSeverityCount($severity,$submoduleId,$userId,$operatorId,$assessor_id),
                    ];
                    $generalTalisData=[
                        "total_question"=> count($TotalGeneralSubModuleQuestion)+ $DeleteGeneralQuestionCount,
                        "total_answered_questions"=> $generalAnsweredCount+$DeleteGeneralQuestionCount,
                        "severity"=> $this->GetGeneralSeverityCount($severity,$submoduleId,$userId,$operatorId,$assessor_id),
                    ];
                    return [
                        "id"=> $result->id,
                        "overall_question_tallies" => $talisData,
                        "general_question_tallies" => $generalTalisData,
                        "question_list" => TrainingCourseSubModuleAssessmentResponseResource::collection($assessment),
                        "additional_media" => TrainingCourseSubModuleAssessmentExtraResponseResource::collection($assessmentExtra ?? []),
                    ];
                }else{
                    return response()->json(setResponse([], ['message' => __('operator.assessment.not-found')]))->setStatusCode(Response::HTTP_OK);
                }
            }else{
                return response()->json(setResponse([], ['message' => __('operator.assessment.not-found')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            echo "<pre>";
            print_r($e->getMessage());
            exit;
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    //Get Severity Count According Submitted Assessment
    public function getSeverityCount($severity,$submoduleId,$userId,$operatorId,$assessor_id){
        $severityData=[];
        if(isset($severity))
                    {
                        $calculated_single = TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submoduleId)->where('assessor_id',$assessor_id)->where('user_id',$userId)->where('question_type','calculated_single')->distinct('question_id')->pluck('question_id');
                        if(count($calculated_single)>0){
                        foreach($severity as $key=> $severity_value){
                            $severityData[$key]['severity_name']=$severity_value->severity_name;
                            $severityData[$key]['severity_count']=0;
                            $answeredOption = TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submoduleId)->where('assessor_id',$assessor_id)->where('user_id',$userId)->where('question_type','calculated_single')->distinct('question_id')->pluck('option_id');
                            if(count($answeredOption)>0){
                            $count=0;
                            foreach($answeredOption as $answer_value){
                            $severityCount=TrainingCourseSubModulePracticalAssessmentOption::where('id',$answer_value)->whereJsonContains('severity',  ['severity_name' => $severity_value->severity_name])->count();
                            if($severityCount>0){
                                $count=$count+$severityCount;
                                $severityData[$key]['severity_count']=$count;
                            }
                            }
                            unset($count);
                            }
                        }
                    }
                    }
        return $severityData;
    }
    //Get Severity Count According Submitted Assessment
    public function GetGeneralSeverityCount($severity,$submoduleId,$userId,$operatorId,$assessor_id){
        $severityData=[];
        if(isset($severity))
                    {
                        $general = TrainingCourseSubModulePracticalAssessmentQuestion::where('submodule_id', $submoduleId)->where('question_type','calculated_single')->whereNull('category_id')->withTrashed()->pluck('id');
                        if(count($general)>0){
                        foreach($severity as $key=> $severity_value){
                            $severityData[$key]['severity_name']=$severity_value->severity_name;
                            $severityData[$key]['severity_count']=0;
                            $answeredOption = TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $submoduleId)->where('assessor_id',$assessor_id)->where('user_id',$userId)->whereIn('question_id',$general)->whereNotNull('option_id')->distinct('question_id')->pluck('option_id');
                            if(count($answeredOption)>0){
                            $count=0;
                            foreach($answeredOption as $answer_value){
                            $severityCount=TrainingCourseSubModulePracticalAssessmentOption::where('id',$answer_value)->whereJsonContains('severity',  ['severity_name' => $severity_value->severity_name])->count();
                            if($severityCount>0){
                                $count=$count+$severityCount;
                                $severityData[$key]['severity_count']=$count;
                            }
                            }
                            unset($count);
                            }
                        }
                    }
                    }
        return $severityData;
    }

    // PDF Export
    public function pdfExport() {
	    try {
            // checking for assessment portal feature setting
            $checkAssessment = checkAssessment();
            $Role=Roles::where('id',auth()->guard('operator')->user()->role_id)->value('master_user_type');
            if($checkAssessment == 1 && $Role!='IQA'){
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }

            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $query = TrainingCourseSubModulePracticalAssessmentResults::whereHas('user', function (Builder $query) {
                $query->whereNull('deleted_at');
            });
            if(isset(request()->ids)){
                $query->whereIn('id',request()->ids);
            }
            $query->where('master_user_id', $operatorId)->whereNotNull('pdf');
            if($Role=='IQA'){
                $authUser = auth()->guard('operator')->user();
                $query->whereIn(DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS.'.user_id', $authUser->IQAAssignAllUsersIds());
            }
            $data = $query->orderBy('id','DESC')->get();
            if($data->count() > 0){
                $pdfFiles = [];
                foreach ($data as $key => $value) {
                    $pdfFiles[] = $value['pdf_url'];
                }
                return response()->json(setResponse(['practial_assessment_pdf'=>$pdfFiles], ['message' => __('operator.assessment.pdfgenerate')]))->setStatusCode(Response::HTTP_OK);
            }else{
                return response()->json(setResponse([], ['message' => __('operator.assessment.pdfNotGenerated')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    public function pdfExportSelfAssessment() {
	    try {
            // checking for assessment portal feature setting
            $checkAssessment = checkAssessment();
            $Role=Roles::where('id',auth()->guard('operator')->user()->role_id)->value('master_user_type');
            if($checkAssessment == 1 && $Role!='IQA'){
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }

            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $query = TrainingCourseSubModulePracticalAssessmentResults::whereHas('user', function (Builder $query) {
                $query->whereNull('deleted_at');
            })->where('type','Self');
            if(isset(request()->ids)){
                $query->whereIn('id',request()->ids);
            }
            $query->where('master_user_id', $operatorId)->whereNotNull('pdf');
            if($Role=='IQA'){
                $authUser = auth()->guard('operator')->user();
                $query->whereIn(DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS.'.user_id', $authUser->IQAAssignAllUsersIds());
            }
            $data = $query->orderBy('id','DESC')->get();
            if($data->count() > 0){
                $pdfFiles = [];
                foreach ($data as $key => $value) {
                    $pdfFiles[] = $value['pdf_url'];
                }
                return response()->json(setResponse(['self_assessment_pdf'=>$pdfFiles], ['message' => __('operator.assessment.pdfgenerate')]))->setStatusCode(Response::HTTP_OK);
            }else{
                return response()->json(setResponse([], ['message' => __('operator.assessment.pdfNotGenerated')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Put(
     *     path="/operator/practicalAssessment/update/{id}",
     *     tags={"Operator - Practical Assessment"},
     *     summary="Update Status and Comment to the Specific assessment of user",
     *     description="Update Status and Comment to the Specific assessment of user",
     *     operationId="update",
     *     @OA\Parameter(
     *         description="Id of assessment to update",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="comment",
     *                     description="comment",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={"comment": "Test Comment", "status": "Approved"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function update(PARequest $request,$id) {
        try {
            $resultDetail = $this->assessmentResultModel->find($id);
            $operatorID = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            if($resultDetail) {
                $this->ResultAdjust($resultDetail,$request->override_assessment_result);
                $resultDetail->update([
                    'status' => $request->status, // Status must be Pending OR Approved OR Rejected
                    'is_pass' => $request->override_assessment_result,
                    'adjust_result' => 'manual',
                    'pdf' => NULL
                ]);

                $comment =  $request->comment ?? null;
                $fileType =  $request->fileType ?? null;
                $mediaImageVideo =  ($request->media) ? $request->media : null;
                // echo $mediaImageVideo;exit;
                $mediaFileName = null;
                if ($mediaImageVideo !='null') {
                    $path='practical_assessment/results/'.$id;
                    $media=S3BucketFileUpload($mediaImageVideo,$path);
                    $mediaFileName = $media;
                }

                if(isset($comment) && !empty($comment) && $comment != 'null'){
                    $addMediaComment = [
                        'result_id' => $id,
                        'comment' => ($comment == 'null') ? null : $comment,
                        'media' => ($mediaFileName == 'null') ? null : $mediaFileName,
                        'fileType' => ($fileType == 'null') ? null : $fileType,
                    ];
                    PracticalAssessmentComment::create($addMediaComment);
                }
                $data = [
                    'master_user_id' => $resultDetail->master_user_id,
                    'user_id' => $resultDetail->user_id,
                    'training_course_id' => $resultDetail->training_course_id,
                    'module_id' => $resultDetail->module_id,
                    'submodule_id' => $resultDetail->submodule_id,
                    'assessor_id' => $resultDetail->assessor_id,
                    'status' => $resultDetail->status,
                    'comment' => ($comment == 'null') ? null : $comment,
                ];
                dispatch(new \App\Jobs\PACommentJob($data));

                $resultPassFail = null;
                if(isset($request->override_assessment_result)){
                    $resultPassFail = ($request->override_assessment_result == 0) ? 'Fail' : 'Pass';
                }

                // Update Assessment Results Logs
                $logsData = [
                    'user_id' => $operatorID,
                    'email' => auth()->guard('operator')->user()->email ?? null,
                    'action' => 'Update',
                    'action_name' => config('constants.system_logs.update_assessment_result_operator'),
                    'extra_info' => get_submodule_name($resultDetail->submodule_id) ?? null,
                    'url' => str_replace(url('/v1/'), '', request()->fullUrl()) ?? null,
                    'is_app_operator_admin' => 2,
                    'is_practical_assessment' => 1 ,
                    'system_section' => 'Practical Assessment',
                    'item_id' => $resultDetail->submodule_id ?? null,
                ];

                custom_system_logs($logsData);
                $this->submoduleProgress($resultDetail->training_course_id,$resultDetail->module_id,$resultDetail->submodule_id,$resultDetail->master_user_id,$resultDetail->user_id,$request->override_assessment_result);
                return response()->json(setResponse($data, ['message' => __('operator.assessment.update')]))->setStatusCode(Response::HTTP_OK);
            }else{
                return response()->json(setResponse([], ['message' => __('operator.assessment.not-found')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    public function submoduleProgress($TrainigCourseID,$ModuleId,$SubModuleId,$masterUserId,$userId,$is_status) {
        TrainingCourseSubmoduleProgress::updateOrCreate(
            [
                'training_course_id' => $TrainigCourseID,
                'module_id' => $ModuleId,
                'submodule_id' => $SubModuleId,
                'master_user_id' => $masterUserId,
                'submodule_type_id' => 16,
                'user_id' => $userId,
            ],
            ['submodule_progress' => ($is_status == 1) ? 100 : 0,'date_time'=>date('Y-m-d H:i:s')]
        );
        // Module progress
        $data['training_course_id']=$TrainigCourseID;
        $data['module_id']=$ModuleId;
        $data['master_user_id']=$masterUserId;
        $data['user_id']=$userId;
        (new TrainingCourseModuleProgress)->calculateModuleProgress($data);
        (new TrainingCourseProgress)->calculateTrainingCourseProgress($data);
    }
    
    public function ResultAdjust($data,$result) {
        try {
            $operatorID = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            if($data) {
                $record=new AdjustResult;
                $record->training_course_id=$data->training_course_id;
                $record->module_id=$data->module_id;
                $record->submodule_id=$data->submodule_id;
                $record->assessor_id=$data->assessor_id;
                $record->user_id=$data->user_id;
                $record->old_result=$data->is_pass;
                $record->new_result=$result;
                $record->override_by=$operatorID;
                $record->save();
                if($data->is_pass != request()->override_assessment_result){
                    // Store System Logs on Override Results
                    $finalResult = (request()->override_assessment_result == 0) ? 'Fail' : 'Pass';
                    $logsData = [
                        'user_id' => $operatorID,
                        'email' => auth()->guard('operator')->user()->email ?? null,
                        'action' => 'Update',
                        'action_name' => config('constants.system_logs.override_assessment_result_operator'),
                        'extra_info' =>  $finalResult ?? null,
                        'url' => str_replace(url('/v1/'), '', request()->fullUrl()) ?? null,
                        'is_app_operator_admin' => 2,
                        'is_practical_assessment' => 1,
                        'system_section' => 'Practical Assessment',
                        'item_id' => $data->submodule_id ?? null,
                    ];
                    custom_system_logs($logsData);
                }
            }else{
                return response()->json(setResponse([], ['message' => __('operator.assessment.not-found')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="operator/practicalAssessment/globalTracking",
     *     tags={"Operator - Practical Assessment"},
     *     summary="Global Tracking Listing",
     *     description="Global Tracking Listing",
     *     operationId="globalTracking",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="submodule_id",
     *                     description="Submodule Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 example={"submodule_id": 2174, "per_page": 10, "page": 1, "search_key": "", "sort_by": "name", "order_by": "asc", "filters": {"uname": "", "accessor_name": "", "status": ""}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function globalTracking() {
        try {
            $data = $this->assessmentRepository->getAssessmentLogsListing(request()->all());
            if($data) {
                return PracticalAssessmentSystemLogsResource::collection($data->paginate(request()->per_page));
            }else{
                return response()->json(setResponse([], ['message' => __('operator.assessment.logs-not-found')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
