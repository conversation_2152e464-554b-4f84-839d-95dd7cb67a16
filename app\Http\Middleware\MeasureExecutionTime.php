<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class MeasureExecutionTime
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);
        $executionTime = microtime(true) - LUMEN_START;
        if ($executionTime > 0) {
            $data = [
                'Request Method' => $request->method(),
                'Request Path' => $request->path(),
                'Request IP' => $request->ip(),
                'Origin' => $request->header('host'),
                'Execution Time' => $executionTime,
            ];
        }
        return $response;
    }
}
