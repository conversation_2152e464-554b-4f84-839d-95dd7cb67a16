<?php

namespace App\Http\Controllers\Admin\v1;

use ZipArchive;
use DBTableNames;
use App\Models\MasterUser;
use App\Models\UserRelation;
use Illuminate\Http\Response;
use App\Models\TrainingCourse;
use App\Models\whiteLabelSetting;
use App\Services\GetStreamService;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use App\Http\Requests\Admin\v1\OperatorRequest;
use App\Http\Requests\Admin\v1\EmailSettingRequest;
use App\Http\Requests\Admin\v1\PDFSettingRequest;
use App\Repositories\Admin\v1\MasterUserRepository;
use App\Http\Requests\Admin\v1\CommonListingRequest;
use App\Http\Requests\Admin\v1\PushNotificationRequest;
use App\Http\Resources\Admin\v1\OperatorDetailResource;
use App\Http\Requests\Admin\v1\PushConfigurationRequest;
use App\Http\Requests\Admin\v1\whiteLabelSettingRequest;
use App\Http\Resources\Admin\v1\OperatorListingResource;
use App\Http\Requests\Admin\v1\DeletedCourseRestoreRequest;
use App\Http\Requests\Admin\v1\CommonBulkStatusChangeRequest;
use App\Http\Requests\Admin\v1\CommonBulkRestoreCourseRequest;
use App\Http\Resources\Admin\v1\DeletedTrainingCourseResource;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Mail;

class OperatorController extends Controller {

    protected $model;
    protected $repository;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
	$this->model = new MasterUser();
	$this->repository = new MasterUserRepository($this->model);
    }

    /**
     * List operators
     *
     * @return \Illuminate\Http\Response
     */
    public function getListing(CommonListingRequest $request) {
	try {
	    $operators = $this->repository->getOperatorsListing($request->all());
	    if ($request->isExport) {
		return $this->repository->exportOperatorCsv($operators->get(), $request->exportFields);
	    }
	    return OperatorListingResource::collection($operators->paginate($request->per_page));
	} catch (\Exception $e) {
	    return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
	}
    }

    /**
     * Add operator
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(OperatorRequest $request) {
	try {
	    return $this->repository->create($request->all());
	} catch (\Exception $e) {
	    return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
	}
    }

    /**
     * Get operator
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id) {
	try {
	    $operator = $this->model->find($id);
	    return ($operator) ?
		    (new OperatorDetailResource($operator)) :
		    response()->json(setErrorResponse(__('admin.operator.not_found')))->setStatusCode(Response::HTTP_NOT_FOUND);
	} catch (\Exception $e) {
	    return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
	}
    }

    /**
     * Update operator
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(OperatorRequest $request, $id) {
	try {
	    return $this->repository->update($request->all(), $id);
	} catch (\Exception $e) {
	    return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
	}
    }

    /**
     * Delete operator
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id) {
	try {
	    if ($id != '1') {
		$operator = $this->model->find($id);
		app(GetStreamService::class)->DeleteOperatorGetStream($id);
		return ($operator) ?
			$this->repository->delete($id) :
			response()->json(setErrorResponse(__('admin.operator.not_found')))->setStatusCode(Response::HTTP_NOT_FOUND);
	    } else {
		response()->json(setErrorResponse(__('admin.operator.default_operator')))->setStatusCode(Response::HTTP_NOT_FOUND);
	    }
	} catch (\Exception $e) {
	    return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
	}
    }

    /**
     * Change operator(s) status
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function changeStatus(CommonBulkStatusChangeRequest $request) {
	try {
	    $ids = $request->ids;
	    if (($key = array_search(1, $ids)) !== false) {

		unset($ids[$key]);
		if (count($ids) <= 0) {
		    return response()->json(setResponse([], ['message' => __('admin.operator.cnt_default_operator')]))->setStatusCode(Response::HTTP_OK);
		} else {
		    $ids = array_values($ids);
		}
	    }
	    $isDisabled = ($request->status === 'Active') ? 0 : 1;
	    $this->model->whereIn('id', $ids)->update(['is_disabled' => $isDisabled, 'modified_by' => auth()->guard('admin')->id(), 'status' => $request->status]);
	    app(GetStreamService::class)->OperatorChangeStatus($ids, $request->status);
	    UserRelation::whereIn('master_user_id', $ids)->update(['is_disabled' => $isDisabled]);
	    if ($isDisabled) {
		$userIds = UserRelation::whereIn('master_user_id', $ids)->where('is_current_operator', 1)->pluck('user_id', 'id')->toArray();
		UserRelation::whereIn('id', array_keys($userIds))->update(['is_current_operator' => 0]);
		$skillbaseId = $this->model->whereUserType('Operator')->whereNotNull('managed_by')->pluck('id')->first();
		UserRelation::whereIn('user_id', array_values($userIds))->where('master_user_id', $skillbaseId)->update(['is_current_operator' => 1]);
	    }
	    return response()->json(setResponse([], ['message' => __('admin.operator.status')]))->setStatusCode(Response::HTTP_OK);
	} catch (\Exception $e) {
	    return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
	}
    }

    /**
     * List deleted course list of specific operator
     *
     * @return \Illuminate\Http\Response
     */
    public function deletedCourseList(DeletedCourseRestoreRequest $request) {
	try {
	    $trainingCourses = $this->repository->getDeletedCourseList($request->all());
	    return DeletedTrainingCourseResource::collection($trainingCourses->paginate($request->per_page));
	} catch (\Exception $e) {
	    return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
	}
    }

    /**
     * Restore Courses
     *
     * @return \Illuminate\Http\Response
     */
    public function restoreCourses(CommonBulkRestoreCourseRequest $request) {
	try {
	    $ids = $request->ids;
	    if (($key = array_search(1, $ids)) !== false) {
		unset($ids[$key]);
		if (count($ids) <= 0) {
		    return response()->json(setResponse([], ['message' => __('admin.operator.cnt_restore_course')]))->setStatusCode(Response::HTTP_OK);
		} else {
		    $ids = array_values($ids);
		}
	    }
	    $courseIds = TrainingCourse::whereIn('id', $ids)->onlyTrashed()->pluck('id')->toArray();
	    TrainingCourse::whereIn('id', array_values($courseIds))->onlyTrashed()->update(['deleted_at' => NULL, 'status' => 'Inactive']);
	    return response()->json(setResponse([], ['message' => __('admin.operator.restore_course')]))->setStatusCode(Response::HTTP_OK);
	} catch (\Exception $e) {
	    return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
	}
    }

    public function whiteLabelSettings(whiteLabelSettingRequest $request) {
	try {
	    if (isset($request->firebase_json)) {
		$file = \Storage::disk('s3')->putFileAs(getJsonPath($request->operator_id), $request->firebase_json, $request->firebase_json->getClientOriginalName());
		$name = explode('/', $file);
		$uploadedImageName = $name[count($name) - 1];
		$jsonFileName = $uploadedImageName;
	    }
	    if ($request->is_white_label_feature_on == 0) {
		$data = [
		    'operator_id' => $request->operator_id,
		    'is_white_label_feature_on' => $request->is_white_label_feature_on,
		    'domain_uri_prefix' => null,
		    'ios_app_store_id' => null,
		    'ios_package_name' => null,
		    'android_package_name' => null,
		    'firebase_key' => null,
		    'firebase_json' => null,
		];
	    } else {
		$settings = whiteLabelSetting::where('operator_id', $request->operator_id)->first();
		$data = [
		    'operator_id' => $request->operator_id,
		    'is_white_label_feature_on' => $request->is_white_label_feature_on,
		    'domain_uri_prefix' => $request->domain_uri_prefix ?? null,
		    'ios_app_store_id' => $request->ios_app_store_id ?? null,
		    'ios_package_name' => $request->ios_package_name ?? null,
		    'android_package_name' => $request->android_package_name ?? null,
		    // 'firebase_key' => $request->firebase_key ?? null,
		];
		// if (isset($request->firebase_json)) {
		//     $data['firebase_json'] = $jsonFileName;
		// } else {
		//     $jsonFileName = $settings->firebase_json;
		//     $data['firebase_json'] = $jsonFileName;
		// }
	    }
	    /* verify deeplinking configuration */
	    if ($request->is_white_label_feature_on) {
		/* Download PEM file from S3 to local Start */
// 		$localDirectory = storage_path('app/public/json/' . $data['operator_id'] . '/');
// 		$localFilePath = $localDirectory . $data['firebase_json'];
// 		if (!file_exists($localFilePath)) {
// 		    if (!is_dir($localDirectory)) {
// 			mkdir($localDirectory, 0755, true);
// 		    }
// 		    $fileContent = file_get_contents(env('CDN_URL') . 'json/' . $data['operator_id'] . '/' . $data['firebase_json']);
// 		    file_put_contents($localFilePath, $fileContent);
// 		}
// 		/* Download PEM file from S3 to local End */
// 		Config::set('firebase.projects.app.dynamic_links.default_domain', $data['domain_uri_prefix']);
// 		Config::set('firebase.projects.app.credentials.file', $localFilePath);
// 		$dynamicLinks = app('firebase.dynamic_links');
// //		$url = "https://skillsbase-api-dev.devpress.net/v1/api/users/contactUs";
// 		$url = url(route('users.contactUs'));

// 		$parameters = [
// 		    'dynamicLinkInfo' => [
// 			'domainUriPrefix' => $data['domain_uri_prefix'],
// 			'link' => $url,
// 			'androidInfo' => [
// 			    'androidPackageName' => $data['android_package_name'],
// 			],
// 			'iosInfo' => [
// 			    'iosBundleId' => $data['ios_package_name'],
// 			],
// 			'navigationInfo' => [
// 			    'enableForcedRedirect' => true,
// 			],
// 		    ],
// 		    'suffix' => [
// 			'option' => 'UNGUESSABLE',
// 		    ],
// 		];
// 		$link = $dynamicLinks->createDynamicLink($parameters);
	    }

	    whiteLabelSetting::updateOrCreate(['operator_id' => $request->operator_id], $data);
	    return response()->json(setResponse($data, ['message' => __('Configuration Setting Added Successfully')]))->setStatusCode(Response::HTTP_OK);
	} catch (\Exception $e) {
	    return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
	}
    }

    public function PushNotificationSettings(PushConfigurationRequest $request) {
	try {
	    if (isset($request->push_certificate)) {
		$file = \Storage::disk('s3')->putFileAs(getPushPath($request->operator_id), $request->push_certificate, $request->push_certificate->getClientOriginalName());
		$name = explode('/', $file);
		$uploadedImageName = $name[count($name) - 1];
		$pushFileName = $uploadedImageName;
	    } else {
		$settings = whiteLabelSetting::where('operator_id', $request->operator_id)->first();
		$pushFileName = $settings->push_certificate;
	    }
	    if ($request->is_push_notification_on == 0) {
		$data = [
		    'operator_id' => $request->operator_id,
		    'is_push_notification_on' => $request->is_push_notification_on,
		    'push_certificate' => null,
		    'push_certificate_path' => null,
		    'ios_passphrase' => null,
		    'apns_topic' => null,
		    'push_url' => null,
		    'android_server_key' => null,
		];
	    } else {
		$Url = env('CDN_URL') . config('constants.aws.push') . '/' . $request->operator_id . '/' . $pushFileName;
		$push_data = array();
		$push_data['push_certificate_name'] = $pushFileName;
		$push_data['push_certificate_path'] = $Url;
		$data = [
		    'operator_id' => $request->operator_id,
		    'is_push_notification_on' => $request->is_push_notification_on,
		    'push_certificate' => $pushFileName,
		    "push_certificate_info" => $push_data,
		    'ios_passphrase' => $request->ios_passphrase ?? null,
		    'apns_topic' => $request->apns_topic ?? null,
		    'push_url' => $request->push_url ?? null,
		    'android_server_key' => $request->android_server_key ?? null,
		];
	    }

	    whiteLabelSetting::updateOrCreate(['operator_id' => $request->operator_id], $data);
	    unset($data['push_url']);
	    return response()->json(setResponse($data, ['message' => __('Configuration Setting Added Successfully')]))->setStatusCode(Response::HTTP_OK);
	} catch (\Exception $e) {
	    return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
	}
    }

    public function EmailSettings(EmailSettingRequest $request) 
	{
		try {
			if (isset($request->logo)) {
				$file = \Storage::disk('s3')->putFileAs(getEmailLogoPath($request->operator_id), $request->logo, $request->logo->getClientOriginalName());
				$name = explode('/', $file);
				$uploadedImageName = $name[count($name) - 1];
				$logoFileName = $uploadedImageName;
			}else {
				$settings = whiteLabelSetting::where('operator_id', $request->operator_id)->first();
				$logoFileName = $settings->logo_name;
			}

			if (isset($request->view_background_image)) {
				$backgroundImagefile = \Storage::disk('s3')->putFileAs(getBackgroundImagePath($request->operator_id), $request->view_background_image, $request->view_background_image->getClientOriginalName());
				$backgroundImageName = explode('/', $backgroundImagefile);
				$uploadedBackgroundImageName = $backgroundImageName[count($backgroundImageName) - 1];
				$backgroundImageFileName = $uploadedBackgroundImageName;
			}else {
				$settings = whiteLabelSetting::where('operator_id', $request->operator_id)->first();
				$backgroundImageFileName = $settings->view_background_image;
			}

			$data = [
				'operator_id' => $request->operator_id,
				'is_email_settings_on' => $request->is_email_settings_on,
				'mail_username' => $request->mail_username ?? null,
				'mail_password' => $request->mail_password ?? null,
				'mail_from_name' => $request->mail_from_name ?? null,
				'mail_from_address' => $request->mail_from_address ?? null,
				'app_name' => $request->app_name ?? null,
				'logo_name' => $logoFileName ?? null,
				'view_background_image' => $backgroundImageFileName ?? null,
				'fallback_link' => $request->fallback_link ?? null,
			];
			if ($request->is_email_settings_on) {
				/* verify email config are correct or not */
				Config::set('mail.mailers.smtp.username', $data['mail_username']);
				Config::set('mail.mailers.smtp.password', $data['mail_password']);
				Config::set('mail.from.address', $data['mail_from_address']);
				Config::set('mail.from.name', $data['mail_from_name']);
			}
			whiteLabelSetting::updateOrCreate(['operator_id' => $request->operator_id], $data);
			return response()->json(setResponse([], ['message' => __('Configuration Setting Added Successfully')]))->setStatusCode(Response::HTTP_OK);
		} catch (\Exception $e) {
			return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
		}
    }

    public function getFirebaseJsonFile($operatorId) 
	{
		$operatorId = decrypt($operatorId);
		$config = whiteLabelSetting::where('operator_id', $operatorId)->first();
		if ($config) {
			$file = 'json/' . $operatorId . '/' . $config->firebase_json;
			$filePath = storage_path('app/public/' . $file);
			if (Storage::disk('public')->exists($file)) {
				return response()->download(Storage::path($filePath));
			} else {
				return response()->json(setErrorResponse(__("File not found.")))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
			}
		} else {
			return response()->json(setErrorResponse(__("File not found.")))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
		}
	}

	public function getPushFile($operatorId) {
		$operatorId = decrypt($operatorId);
		$config = whiteLabelSetting::where('operator_id', $operatorId)->first();
		if ($config) {
			$file = 'push/' . $operatorId . '/' . $config->push_certificate;
			$filePath = storage_path('app/public/' . $file);
			if (Storage::disk('public')->exists($file)) {
			return response()->download(Storage::path($filePath));
			} else {
			return response()->json(setErrorResponse(__("File not found.")))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
			}
		} else {
			return response()->json(setErrorResponse(__("File not found.")))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
		}
    }

	public function PDFSettings(PDFSettingRequest $request) 
	{
		try {
			if (isset($request->short_logo)) {
				$path=getPDFLogoPath($request->operator_id);
                $image=S3BucketFileUpload($request->short_logo,$path);
				$shortLogoFileName = $image;
			}else {
				$settings = whiteLabelSetting::where('operator_id', $request->operator_id)->first();
				$shortLogoFileName = $settings->short_logo;
			}

			if (isset($request->favicon_logo)) {
				$path=getPDFLogoPath($request->operator_id);
                $image=S3BucketFileUpload($request->favicon_logo,$path);
				$faviconLogoFileName = $image;
			}else {
				$settings = whiteLabelSetting::where('operator_id', $request->operator_id)->first();
				$faviconLogoFileName = $settings->favicon_logo;
			}

			if (isset($request->sidebar_logo)) {
				$path=getPDFLogoPath($request->operator_id);
                $image=S3BucketFileUpload($request->sidebar_logo,$path);
				$sidebarLogoFileName = $image;
			}else {
				$settings = whiteLabelSetting::where('operator_id', $request->operator_id)->first();
				$sidebarLogoFileName = $settings->sidebar_logo;
			}

			if (isset($request->footer_background_image)) {
				$path=getPDFLogoPath($request->operator_id);
                $image=S3BucketFileUpload($request->footer_background_image,$path);
				$footerBackgroundImageFileName = $image;
			}else {
				$settings = whiteLabelSetting::where('operator_id', $request->operator_id)->first();
				$footerBackgroundImageFileName = $settings->footer_background_image;
			}

			$data = [
				'operator_id' => $request->operator_id,
				'is_pdf_settings_on' => $request->is_pdf_settings_on,
				'short_logo' => ($request->is_pdf_settings_on == 1) ? $shortLogoFileName : null,
				'favicon_logo' => ($request->is_pdf_settings_on == 1) ? $faviconLogoFileName : null,
				'sidebar_logo' => ($request->is_pdf_settings_on == 1) ? $sidebarLogoFileName : null,
				'fail_color_scheme' => $request->fail_color_scheme ?? null,
				'main_color_scheme' => $request->main_color_scheme ?? null,
				'pass_color_scheme' => $request->pass_color_scheme ?? null,
				'fail_color_scheme' => $request->fail_color_scheme ?? null,
				'site_link' => $request->site_link ?? null,
				'chat_link' => $request->chat_link ?? null,
				'footer_background_image' => ($request->is_pdf_settings_on == 1) ? $footerBackgroundImageFileName : null,
			];
			whiteLabelSetting::updateOrCreate(['operator_id' => $request->operator_id], $data);
			return response()->json(setResponse([], ['message' => __('Configuration Setting Added Successfully.')]))->setStatusCode(Response::HTTP_OK);
		} catch (\Exception $e) {
			return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
		}
    }
}
