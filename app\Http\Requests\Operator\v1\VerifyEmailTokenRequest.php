<?php

namespace App\Http\Requests\Operator\v1;

use App\Http\Requests\CustomFormRequest;

class VerifyEmailTokenRequest extends CustomFormRequest {

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'email' => 'required|email|exists:master_users,email',
            'token' => 'required|min:6|max:6'
        ];
    }
    
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'email.required' => __('Please enter email.'),
            'email.email' => __('Email must be a valid email address (E.g.: <EMAIL>).'),
            'email.exists' => __("You have entered an incorrect email address."),
            'token.required' => __('Token is required.'),
            'token.min' => __('Token must be consist of 6 digits.'),
            'token.max' => __('Token must be consist of 6 digits.'),
        ];
    }

}
