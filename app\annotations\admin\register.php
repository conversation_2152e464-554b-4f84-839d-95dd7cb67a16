<?php
/**
     * @OA\Post(
     *     path="/admin/register",
     *     tags={"Admin - Auth"},
     *     summary="Admin register",
     *     description="Admin register with details",
     *     operationId="adminRegister",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="email",
     *                     description="Email Address",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="password",
     *                     description="Password",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="user_type",
     *                     description="User type:- Admin|Operator|ContentProvider",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="role_id",
     *                     description="Role ID",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="unique_id",
     *                     description="Unique ID",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status:- Active|Inactive",
     *                     type="string"
     *                 ),
     *                 example={"name": "Admin Name", "email": "<EMAIL>", "password": "Indianic@123", "user_type": "Operator", "role_id": 2, "unique_id": "SBOP12345", "status": "Active"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     * )
     */