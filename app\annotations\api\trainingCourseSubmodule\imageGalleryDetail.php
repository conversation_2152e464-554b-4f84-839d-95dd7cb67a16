<?php
 /**
     * @OA\Get(
     *     path="/api/trainingCourseSubmodule/imageGalleryDetail/{id}",
     *     tags={"Mobile - Training Course Submodule"},
     *     summary="Get Training Course Submodule image gallery Detail",
     *     description="Get Training Course Submodule image gallery Detail",
     *     operationId="imageGalleryDetail",
     *     @OA\Parameter(
     *         description="Id of image gallery to fetch",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */