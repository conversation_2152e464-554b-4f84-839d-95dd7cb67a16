<?php

namespace App\Http\Controllers\Admin\v1;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\TrainingCourse;
use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\Admin\v1\VideoThumbnail;
use App\Http\Requests\Admin\v1\MediaRequest;
use App\Http\Requests\Admin\v1\ImageRequest;
class GlobalController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Image Upload
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function uploadImage(ImageRequest $request)
    {
        try {
            $response = [];
            $requestData = $request->all();
            $dataId = isset($requestData['id']) ? $requestData['id'] : '';
            $fromImage = $requestData['type'];
            $uploadedImageName = '';
            if($dataId != ''){
                $courseData = TrainingCourse::find($dataId);
                if ($courseData && $fromImage == 'modules') {
                    
                    $file = \Storage::disk('s3')->put(getTrainingCourseModulePath(), $request->file('image'));
                    $name = explode('/', $file);
                    $uploadedImageName = $name[count($name)-1];
                    $response['image'] = $uploadedImageName;
                    $uploadedImageName = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCourseModulePath(), '/') . '/' . $uploadedImageName;
                    $response['url'] = $uploadedImageName;
                } elseif($courseData && $fromImage == 'submodules') {
                    $file = \Storage::disk('s3')->put(getTrainingCourseSubmodulePath(), $request->file('image'));
                    $name = explode('/', $file);
                    $uploadedImageName = $name[count($name)-1];
                    $response['image'] = $uploadedImageName;
                    $uploadedImageName = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCourseSubmodulePath(), '/') . '/' . $uploadedImageName;
                    $response['url'] = $uploadedImageName;
                } elseif($courseData && $fromImage == 'courses') {
                    $file = \Storage::disk('s3')->put(getTrainingCoursePath(), $request->file('image'));
                    $name = explode('/', $file);
                    $uploadedImageName = $name[count($name)-1];
                    $response['image'] = $uploadedImageName;
                    $uploadedImageName = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCoursePath(), '/') . '/' . $uploadedImageName;
                    $response['url'] = $uploadedImageName;
                } else{
                    return response()->json(setErrorResponse(__('operator.GlobalController.dataNotExist')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            }else{
                if ($fromImage == 'modules') {
                    $file = \Storage::disk('s3')->put(getTrainingCourseModulePath(), $request->file('image'));
                    $name = explode('/', $file);
                    $uploadedImageName = $name[count($name)-1];
                    $response['image'] = $uploadedImageName;
                    $uploadedImageName = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCourseModulePath(), '/') . '/' . $uploadedImageName;
                    $response['url'] = $uploadedImageName;
                } elseif($fromImage == 'submodules') {
                    $file = \Storage::disk('s3')->put(getTrainingCourseSubmodulePath() . '/'. 'submodules', $request->file('image'));
                    $name = explode('/', $file);
                    $uploadedImageName = $name[count($name)-1];
                    $response['image'] = $uploadedImageName;
                    $uploadedImageName = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCourseSubmodulePath(), '/') . '/' . $uploadedImageName;
                    $response['url'] = $uploadedImageName;
                } elseif ($fromImage == 'courses') {
                    $file = \Storage::disk('s3')->put(getTrainingCoursePath(), $request->file('image'));
                    $name = explode('/', $file);
                    $uploadedImageName = $name[count($name)-1];
                    $response['image'] = $uploadedImageName;
                    $uploadedImageName = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCoursePath(), '/') . '/' . $uploadedImageName;
                    $response['url'] = $uploadedImageName;
                } elseif ($fromImage == 'users') {
                    $file = \Storage::disk('s3')->put(getUsersPath(), $request->file('image'));
                    $name = explode('/', $file);
                    $uploadedImageName = $name[count($name)-1];
                    $response['image'] = $uploadedImageName;
                    $uploadedImageName = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getUsersPath(), '/') . '/' . $uploadedImageName;
                    $response['url'] = $uploadedImageName;
                }elseif ($fromImage == 'notification') {
                    $file = \Storage::disk('s3')->put(getNotificationPath(), $request->file('image'));
                    $name = explode('/', $file);
                    $uploadedImageName = $name[count($name)-1];
                    $response['image'] = $uploadedImageName;
                    $uploadedImageName = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getNotificationPath(), '/') . '/' . $uploadedImageName;
                    $response['url'] = $uploadedImageName;
                }elseif ($fromImage == 'product_type') {
			$file = \Storage::disk('s3')->put(moveFileToProductType(), $request->file('image'));
			$name = explode('/', $file);
			$uploadedImageName = $name[count($name) - 1];
			$response['image'] = $uploadedImageName;
			$uploadedImageName = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(moveFileToProductType(), '/') . '/' . $uploadedImageName;
			$response['url'] = $uploadedImageName;
		    }
            }
            return response()->json(setResponse($response, ['message' => __('operator.GlobalController.imageUpload')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Upload Media
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function uploadMedia(MediaRequest $request)
    {   
        $requestData = $request->all();
        try {
            $response = [];
            $dataId = isset($requestData['id']) ? $requestData['id'] : '';
            $uploadedImageName = '';
            
                $courseData = TrainingCourse::find($dataId);
                if($courseData && $requestData['type'] == 'submodules') {
                    $file = \Storage::disk('s3')->put(getTrainingCourseSubmoduleVideoPath(), $request->file('file'));
                    $name = explode('/', $file);
                    $uploadedImageName = $name[count($name)-1];
                    $response['file'] = $uploadedImageName;
                    $uploadedImageName = 'https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCourseSubmoduleVideoPath(), '/') . '/' . $uploadedImageName;
                    $response['url'] = $uploadedImageName;


                   //generate thumnail from mp4 and upload image start
                    if ($requestData['media_type']=='videowiththumb') {
                        
                        $uploadedImageName = $name[count($name)-1];
                        $videoUrl='https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCourseSubmoduleVideoPath(), '/') . '/' . $uploadedImageName;


                        $storageUrl = base_path('storage/thumb');
                        if (!file_exists($storageUrl)) {
                            mkdir($storageUrl);
                            chmod($storageUrl,0777);
                        }
                        
                        $thumb_name = explode('.', $uploadedImageName);
                        $thumb_name =$thumb_name[0].'.jpg';
                        $imgSizes = config('constants.videoguide.thumbnail');   
                        VideoThumbnail::createThumbnail($videoUrl, $storageUrl, $thumb_name, 0, $imgSizes['width'], $imgSizes['height']);
                        
                        chmod(base_path('storage/thumb/'.$thumb_name),0777);
                        $file=base_path('storage/thumb/'.$thumb_name);


                        $file_path='https://s3-' . env('AWS_DEFAULT_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/' . trim(getTrainingCourseSubmoduleThumbnailPath()). '/' . $thumb_name;
                        
                        $s3 = \Storage::disk('s3')->put(getTrainingCourseSubmoduleThumbnailPath(). '/' . $thumb_name, file_get_contents($file));
                            
                        if (file_exists($file)) {
                            unlink($file);
                        }
                        $response['thumbnail'] = $thumb_name;
                        $response['thumbUrl'] = $file_path;
                    }
                    //generate thumnail from mp4 and upload image end
                }
            
            return response()->json(setResponse($response, ['message' => __('operator.GlobalController.fileUpload')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
