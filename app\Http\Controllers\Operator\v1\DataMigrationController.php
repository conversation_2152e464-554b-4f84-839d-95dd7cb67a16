<?php

namespace App\Http\Controllers\Operator\v1;

use App\Models\User;
use App\Models\MasterUser;
use App\Models\UserRelation;
use Illuminate\Http\Response;
use App\Models\TrainingCourse;
use App\Models\HotspotUploaderMedia;
use App\Models\TrainingCourseModules;
use App\Models\TrainingCourseProgress;
use Illuminate\Support\Facades\Storage;
use App\Models\UserAssignTrainingCourses;
use App\Models\TrainingCourseModuleProgress;
use App\Models\TrainingCourseSubmoduleDetails;
use App\Models\TrainingCourseSubmoduleProgress;
use App\Models\TrainingCourseSubModulePhotoHotspot;
use App\Http\Requests\Operator\v1\DataMigrationRequest;
use App\Models\TrainingCourseSubmoduleHotspotUploaderProgress;

class DataMigrationController extends Controller
{

    public function store(DataMigrationRequest $request)
    {
        try {
            // Open the uploaded CSV file
            $file = fopen($request->file('file'), "r");

            // Read the header row
            $header = fgetcsv($file);

            // Validate the header
            if (isset($header) && count($header) === 11) {
                // Get the authenticated operator's ID and find the master user
                $masterUserId = auth()->guard('operator')->id();
                $masterUser = MasterUser::find($masterUserId);
                $dexgreenUserId = MasterUser::where('name', 'Dexgreen')->value('id');

                if ($masterUserId === $dexgreenUserId) {
                    $rowData = [];

                    // Read each row of data from the CSV
                    while (!feof($file)) {
                        $row = fgetcsv($file);
                        if ($row) { // Ensure that the row is not empty
                            $rowData[] = $row;
                        }
                    }

                    // Check if any rows were read
                    if (count($rowData) === 0) {
                        return response()->json(setErrorResponse(__('No data found in the file.')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                    }

                    foreach ($rowData as $value) {
                        if (isset($value) && !empty($value)) {
                            // Check if user already exists
                            $userExists = User::where('email', $value[1])->where('is_migrate_user','!=','Yes')->first();

                             // Get training course ID based on title and master user ID
                             $trainingCourseID = TrainingCourse::where('title', $value[3]) // Adjusted index for TrainingCourseName
                             ->where('master_user_id', $masterUser->id)
                             ->value('id');
                            // If user does not exist, proceed with creation
                            if (empty($userExists)) {
                                // Create or update the user based on email
                                $user = User::updateOrCreate(
                                    ['email' => $value[1]], // Search criteria by email
                                    [
                                        'name' => $value[0],
                                        'password' => $value[2], // Hashing the password
                                        'added_by' => 'Operator',
                                        'status' => 'Active',
                                        'is_migrate_user' => 'Yes',
                                        'email_verified_at' => now(), // Set email verified timestamp
                                    ]
                                );
                                
                                // Create or update the user relation if user exists
                                if ($user) {
                                    UserRelation::updateOrCreate(
                                        [
                                            'user_id' => $user->id,
                                            'master_user_id' => $masterUser->id,
                                        ],
                                        [
                                            'manager_id' => $masterUser->id,
                                            'is_current_operator' => 1,
                                            'manager_email' => $masterUser->email,
                                            'unique_id' => $masterUser->unique_id,
                                        ]
                                    );
                                }
                                $userDetails=$user;
                                if (!empty($trainingCourseID)) {
                                    UserAssignTrainingCourses::updateOrCreate(
                                        [
                                            'user_id' => $userDetails->id,
                                            'training_course_id' => $trainingCourseID,
                                            'master_user_id' => $masterUser->id,
                                        ],
                                        [
                                            'is_manual_course' => 1,
                                        ]
                                    );
                                    if($value[3]=='Multi-Dwelling Units'){
                                        $trainingCourseSubModuleDetail = TrainingCourseSubmoduleDetails::select('training_course_id', 'module_id', 'id')
                                        ->where('training_course_id', $trainingCourseID)
                                        ->where('submodule_type_id', 11)
                                        ->where('submodule_name', 'Photo Demo board')
                                        ->first();
                                        if ($trainingCourseSubModuleDetail) {
                                            if ($value[7] != 'N/A' && $value[8] != 'N/A' && $value[9] != 'N/A') {
                                                $HotspotUploaderProgressStatus = array_map('trim', explode(',', $value[7]));
                                                if (in_array("Pending", $HotspotUploaderProgressStatus)) {
                                                    $setStatus='Pending';
                                                }else{
                                                    $setStatus='Approved';
                                                }
                                                $HotspotUploaderProgress=TrainingCourseSubmoduleHotspotUploaderProgress::updateOrCreate(
                                                    [
                                                        'training_course_id' => $trainingCourseSubModuleDetail->training_course_id,
                                                        'module_id' => $trainingCourseSubModuleDetail->module_id,
                                                        'submodule_id' => $trainingCourseSubModuleDetail->id,
                                                        'master_user_id' => $masterUser->id,
                                                        'user_id' => $userDetails->id,
                                                    ],
                                                    [
                                                        'attempts' => ($value[5] == 'N/A' ? 1 : (int) $value[5]),
                                                        'status' => $setStatus,
                                                        'created_at' => ($value[10] == 'N/A' ? now() : date('Y-m-d H:i:s', strtotime($value[10]))),
                                                        'updated_at' => ($value[10] == 'N/A' ? now() : date('Y-m-d H:i:s', strtotime($value[10]))),
                                                    ]
                                                );
                                                if ($HotspotUploaderProgress) {
                                                        // Process additional data for regions and images
                                                        $status = array_map('trim', explode(',', $value[7]));
                                                        $regionName = array_map('trim', explode(',', $value[8]));
                                                        $media = array_map('trim', explode(',', $value[9]));
                                                        foreach ($regionName as $key => $region) {
                                                            // Get region ID based on submodule and region name
                                                            $regionID = TrainingCourseSubModulePhotoHotspot::where('submodule_id', $trainingCourseSubModuleDetail->id)
                                                                ->where('region_name', trim($region))
                                                                ->value('id');
                                                            $image=array_filter(explode('/', $media[$key]));
                                                            // echo getTrainingCourseSubmodulePath($trainingCourseSubModuleDetail->training_course_id);die;
                                                            Storage::disk('s3')->put(getTrainingCourseSubmodulePath($trainingCourseSubModuleDetail->training_course_id).'/'.end($image), file_get_contents($media[$key]));
                                                            HotspotUploaderMedia::updateOrCreate(
                                                                [
                                                                    'hotspot_uploader_id' => $HotspotUploaderProgress['id'],
                                                                    'photo_hotspot_id' => $regionID,
                                                                ],
                                                                [
                                                                    'media' => end($image),
                                                                    'attempts' => 1,
                                                                    'status' => isset($status[$key]) ? trim($status[$key]) : null,
                                                                ]
                                                            );
                                                        }
                                                    $this->addDefaultCourseProgress($trainingCourseSubModuleDetail->training_course_id,$userDetails->id,$masterUser->id,$trainingCourseSubModuleDetail->module_id,$trainingCourseSubModuleDetail->id,$value[4]);
                                                }
                                            }else{
                                                if($value[4]==100){
                                                    $this->addCSPCourseProgress($trainingCourseID,$userDetails->id,$masterUser->id,$value[4]);
                                                } 
                                            }
                                        }
                                    }
                                    if($value[3]=='CSP & Inside/ Out Cable'){
                                        if($value[4]==100){
                                        $this->addCSPCourseProgress($trainingCourseID,$userDetails->id,$masterUser->id,$value[4]);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    fclose($file); // Close the file after processing

                    return response()->json(['message' => __('Users migrated successfully.')], Response::HTTP_OK);
                } else {
                    return response()->json(setErrorResponse(__('These functionalities are available exclusively for the Dexgreen Operator.')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            } else {
                return response()->json(setErrorResponse(__('Invalid File Format')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function addDefaultCourseProgress($courseId,$userId,$masterUserId,$moduleId,$submoduleId,$progress){
        // Get course with sub module and its modules
        $courseDetails = TrainingCourse::with(['modules','modules.subModules'])->whereId($courseId)->first();
        $result = \DB::transaction(function () use ($courseDetails,$courseId,$userId,$masterUserId,$moduleId,$submoduleId,$progress) {
            // Main Course Entry
            $isExist = TrainingCourseProgress::whereUserId($userId)->whereTrainingCourseId($courseId)->whereMasterUserId($masterUserId)->first();
            if(!$isExist){
                $this->addDefaultProgress($courseDetails,$userId,$masterUserId,$moduleId,$submoduleId,$progress);
            }
            else{
                $this->addDefaultModuleProgress($courseDetails->id,$isExist->id,$masterUserId,$userId,$moduleId,$submoduleId,$progress);
            }
        });
    }
    public function addCSPCourseProgress($courseId,$userId,$masterUserId,$progress){
        // Get course with sub module and its modules
        $courseDetails = TrainingCourse::with(['modules','modules.subModules'])->whereId($courseId)->first();
        $result = \DB::transaction(function () use ($courseDetails,$courseId,$userId,$masterUserId,$progress) {
            // Main Course Entry
            $isExist = TrainingCourseProgress::whereUserId($userId)->whereTrainingCourseId($courseId)->whereMasterUserId($masterUserId)->first();
            if(!$isExist){
                $this->addCSPProgress($courseDetails,$userId,$masterUserId,$progress);
            }
            else{
                $this->addCSPModuleProgress($courseDetails->id,$isExist->id,$masterUserId,$userId,$progress);
            }
        });
    }

    public function addDefaultProgress($courseDetails,$userId,$masterUserId,$moduleId,$submoduleId,$progress){
        $courseEntry = new TrainingCourseProgress;
        $courseEntry->training_course_id = $courseDetails->id;
        $courseEntry->master_user_id = $courseDetails->master_user_id;
        $courseEntry->user_id = $userId;
        $courseEntry->course_progress = $progress;
        $courseEntry->date_time = date('Y-m-d H:i:s');
        $courseEntry->is_new = 0;
        $courseEntry->save();
        if($courseEntry){
            $this->addDefaultModuleProgress($courseDetails->id,$courseEntry->id,$masterUserId,$userId,$moduleId,$submoduleId,$progress);
        }
    }
    public function addCSPProgress($courseDetails,$userId,$masterUserId,$progress){
        $courseEntry = new TrainingCourseProgress;
        $courseEntry->training_course_id = $courseDetails->id;
        $courseEntry->master_user_id = $courseDetails->master_user_id;
        $courseEntry->user_id = $userId;
        $courseEntry->course_progress = $progress;
        $courseEntry->date_time = date('Y-m-d H:i:s');
        $courseEntry->is_new = 0;
        $courseEntry->save();
        if($courseEntry){
            $this->addCSPModuleProgress($courseDetails->id,$courseEntry->id,$masterUserId,$userId,$progress);
        }
    }

    public function addDefaultModuleProgress($training_course_id,$courseProgressId, $operatorId,$userId,$moduleId,$submoduleId,$progress){
                $moduleExist=TrainingCourseModuleProgress::where('training_course_id',$training_course_id)->where('master_user_id',$operatorId)->where('module_id',$moduleId)->where('user_id',$userId)->first();
                if(empty($moduleExist)){
				$courseModuleEntry = new TrainingCourseModuleProgress;
                $courseModuleEntry->training_course_id = $training_course_id;
                $courseModuleEntry->course_progress_id = $courseProgressId;
				$courseModuleEntry->master_user_id = $operatorId;
				$courseModuleEntry->module_id = $moduleId;
				$courseModuleEntry->user_id = $userId;
				$courseModuleEntry->module_progress = $progress;
				$courseModuleEntry->date_time = date('Y-m-d H:i:s');
				$courseModuleEntry->is_new = 0;
				$courseModuleEntry->save();
                if(!empty($courseModuleEntry)){
                    $this->addDefaultSubmoduleProgress($training_course_id, $operatorId,$moduleId,$submoduleId,$userId,$courseModuleEntry->id,$progress);
                }
			}
            else{
                $this->addDefaultSubmoduleProgress($training_course_id, $operatorId,$moduleId,$submoduleId,$userId,$moduleExist->id,$progress);
            }        
    }
    public function addCSPModuleProgress($training_course_id,$courseProgressId, $operatorId,$userId,$progress){
        $moduleList=TrainingCourseModules::select('id')->where('training_course_id',$training_course_id)->get('id')->toArray();
        foreach($moduleList as $module_value){
        $moduleExist=TrainingCourseModuleProgress::where('training_course_id',$training_course_id)->where('master_user_id',$operatorId)->where('module_id',$module_value['id'])->where('user_id',$userId)->first();
        if(empty($moduleExist)){
        $courseModuleEntry = new TrainingCourseModuleProgress;
        $courseModuleEntry->training_course_id = $training_course_id;
        $courseModuleEntry->course_progress_id = $courseProgressId;
        $courseModuleEntry->master_user_id = $operatorId;
        $courseModuleEntry->module_id = $module_value['id'];
        $courseModuleEntry->user_id = $userId;
        $courseModuleEntry->module_progress = $progress;
        $courseModuleEntry->date_time = date('Y-m-d H:i:s');
        $courseModuleEntry->is_new = 0;
        $courseModuleEntry->save();
        if(!empty($courseModuleEntry)){
            $this->addCSPSubmoduleProgress($training_course_id, $operatorId,$module_value['id'],$userId,$progress,$courseModuleEntry->id);
        }
        }
        else{
            $this->addCSPSubmoduleProgress($training_course_id, $operatorId,$module_value['id'],$userId,$progress,$moduleExist->id);
        }
    }        
}
public function addCSPSubmoduleProgress($training_course_id,$operatorId, $moduleId,$userId,$progress,$courseModuleProgressid){
    $subModuleList=TrainingCourseSubmoduleDetails::select('id')->where('module_id',$moduleId)->get()->toArray();
    foreach($subModuleList as $subModule_value){
    $submoduleExist=TrainingCourseSubmoduleProgress::where('training_course_id',$training_course_id)->where('master_user_id',$operatorId)->where('module_id',$moduleId)->where('submodule_id',$subModule_value['id'])->where('user_id',$userId)->first();
        if(empty($submoduleExist)){
            $subModuleDetail=TrainingCourseSubmoduleDetails::select('time_spent','touch_count','total_subdata','status','submodule_type_id')->where('id',$subModule_value['id'])->first()->toArray();
        $courseSubModuleEntry = new TrainingCourseSubmoduleProgress;
        $courseSubModuleEntry->training_course_id = $training_course_id;
        $courseSubModuleEntry->module_progress_id = $courseModuleProgressid;
        $courseSubModuleEntry->master_user_id = $operatorId;
        $courseSubModuleEntry->module_id = $moduleId;
        $courseSubModuleEntry->submodule_id = $subModule_value['id'];
        $courseSubModuleEntry->submodule_type_id = $subModuleDetail['submodule_type_id'];
        $courseSubModuleEntry->user_id = $userId;
        $courseSubModuleEntry->submodule_progress = $progress;
        $courseSubModuleEntry->time_spent = 0;
        $courseSubModuleEntry->total_spent = $subModuleDetail['time_spent'];
        $courseSubModuleEntry->touch_count = 0;
        $courseSubModuleEntry->total_touch_count = $subModuleDetail['touch_count'];
        $courseSubModuleEntry->total_child = $subModuleDetail['total_subdata'];
        $courseSubModuleEntry->visited_child = 0;
        $courseSubModuleEntry->date_time = date('Y-m-d H:i:s');
        $courseSubModuleEntry->is_new = 0;
        $courseSubModuleEntry->status = $subModuleDetail['status'];
        $courseSubModuleEntry->save();
        }
    }        
}
    public function addDefaultSubmoduleProgress($training_course_id,$operatorId, $moduleId,$subModuleid, $userId,$courseModuleProgressid,$progress){
                $submoduleExist=TrainingCourseSubmoduleProgress::where('training_course_id',$training_course_id)->where('master_user_id',$operatorId)->where('module_id',$moduleId)->where('user_id',$userId)->first();
                if(empty($submoduleExist)){
                    $subModuleDetail=TrainingCourseSubmoduleDetails::select('time_spent','touch_count','total_subdata','status')->where('id',$subModuleid)->first()->toArray();
				$courseSubModuleEntry = new TrainingCourseSubmoduleProgress;
                $courseSubModuleEntry->training_course_id = $training_course_id;
                $courseSubModuleEntry->module_progress_id = $courseModuleProgressid;
                $courseSubModuleEntry->master_user_id = $operatorId;
                $courseSubModuleEntry->module_id = $moduleId;
                $courseSubModuleEntry->submodule_id = $subModuleid;
                $courseSubModuleEntry->submodule_type_id = 11;
				$courseSubModuleEntry->user_id = $userId;
                $courseSubModuleEntry->submodule_progress = $progress;
                $courseSubModuleEntry->time_spent = 0;
                $courseSubModuleEntry->total_spent = $subModuleDetail['time_spent'];
                $courseSubModuleEntry->touch_count = 0;
                $courseSubModuleEntry->total_touch_count = $subModuleDetail['touch_count'];
                $courseSubModuleEntry->total_child = $subModuleDetail['total_subdata'];
                $courseSubModuleEntry->visited_child = 0;
				$courseSubModuleEntry->date_time = date('Y-m-d H:i:s');
                $courseSubModuleEntry->is_new = 0;
                $courseSubModuleEntry->status = $subModuleDetail['status'];
                $courseSubModuleEntry->save();
                }        
    }
}
