<?php

/**
     * @OA\Post(
     *     path="/api/trainingCourses/accreditationList",
     *     tags={"Mobile - Training Course"},
     *     summary="Accreditation List Training Course",
     *     description="Accreditation List Training Course",
     *     operationId="accreditationList",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="operator_id",
     *                     description="Operator Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="filter_by",
     *                     description="Filter By",
     *                     type="string",
     *                     enum={"all","this_month","six_months","year"}
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort By",
     *                     type="string",
     *                     enum={"asc","desc","latest","oldest"}
     *                 ),
     *                 example={"operator_id": 4, "filter_by": "all", "sort_by": "asc"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */