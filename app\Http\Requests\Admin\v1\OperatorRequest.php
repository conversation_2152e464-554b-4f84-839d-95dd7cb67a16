<?php

namespace App\Http\Requests\Admin\v1;

use DBTableNames;
use App\Http\Requests\CustomFormRequest;

class OperatorRequest extends CustomFormRequest {

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'name' => 'required|max:150',
            'email' => 'required|email|max:255|unique:' . DBTableNames::MASTER_USERS . ',email,' . request()->id . ',id,deleted_at,NULL',
            'password' => 'sometimes|min:8|max:64|regex:/(?=.*([A-Z]))(?=.*([a-z]))(?=.*([~`\!-@#\$%\^&\*\(\)_\{\}\[\]]))/',
            'status' => 'required|in:Active,Inactive',
            'unique_id' => 'max:15'
        ];
    }
    
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Name is required.',
            'name.max' => 'Name may not be greater than 150 characters.',
            'email.required' => 'Email is required.',
            'email.email' => 'Email must be a valid email address (E.g.: <EMAIL> ).',
            'email.max' => 'Email may not be greater than 255 characters.',
            // 'email.unique' => 'Email has already been taken.',
            // 'unique_id.required' => 'Unique ID is required.',
            'unique_id.max' => 'Unique ID may not be greater than 15 characters.',
            // 'unique_id.unique' => 'Unique ID already exists.',
            'password.required' => 'Please enter password.',
            'password.min' => 'Password must be at least 8 characters.',
            'password.max' => 'Password may not be greater than 20 characters.',
            'password.regex' => 'Password must have atleast one capital, small and special character.',
            'status.required' => 'Status is required.',
            'status.in' => 'Invalid status.',
        ];
    }

}
