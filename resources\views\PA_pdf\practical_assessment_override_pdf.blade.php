<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2//EN">
<html>
@php 
    $main_color = $settings->main_color_scheme ?? '#2E3092';
    $app_name =  $settings->app_name ?? config('constants.app_name');
@endphp
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<meta name="format-detection" content="telephone=no">
		<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=no;">
		<meta http-equiv="X-UA-Compatible" content="IE=9; IE=8; IE=7; IE=EDGE" />
		<title>{{$app_name}}</title>
		<link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
		<style>
			/* latin-ext */
			@font-face {
				font-family: 'Lato';
				font-style: normal;
				font-weight: 400;
				src: url(https://fonts.gstatic.com/s/lato/v20/S6uyw4BMUTPHjxAwXjeu.woff2) format('woff2');
				unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
			}

			/* latin */
			@font-face {
				font-family: 'Lato';
				font-style: normal;
				font-weight: 400;
				src: url(https://fonts.gstatic.com/s/lato/v20/S6uyw4BMUTPHjx4wXg.woff2) format('woff2');
				unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
			}

			/* latin-ext */
			@font-face {
				font-family: 'Lato';
				font-style: normal;
				font-weight: 700;
				src: url(https://fonts.gstatic.com/s/lato/v20/S6u9w4BMUTPHh6UVSwaPGR_p.woff2) format('woff2');
				unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
			}

			/* latin */
			@font-face {
				font-family: 'Lato';
				font-style: normal;
				font-weight: 700;
				src: url(https://fonts.gstatic.com/s/lato/v20/S6u9w4BMUTPHh6UVSwiPGQ.woff2) format('woff2');
				unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
			}

			header {
				position: fixed;
				top: -90px;
				left: 0px;
				right: 0px;
				height: 50px;
				color: black;
				border-bottom: 4px solid blue;
				text-align: center;
				line-height: 35px;
			}

			footer {
				position: fixed;
				bottom: -50px;
				left: 0px;
				right: 0px;
				height: 50px;
				color: black;
				border-top: 4px solid {{$main_color}};
				text-align: center;
				line-height: 35px;
			}

			.page-break {
				page-break-after: always;
				/* height: 50px; */
				opacity: 1;
				border: none;
				box-shadow: 0;
				background-color: #3aa8b6;
			}

			#pagenumber:before {
				content: counter(page);
			}

			.fixed-row td {
				height: 30px;
			}

			.detailstable tr.fixed-row:nth-of-type(odd) {
				background-color: #cccccc !important;
			}

			.category-boxes{ display: table;
				margin-bottom: 10px;
				margin-top:10px;
			}

			.category-boxes td{
				padding: 10px;
				border: 1px solid #000;
				border-radius: 4px;
				margin-right: 10px;
				display: inline-flex;
				text-transform: capitalize;
				font-weight: 400;
				flex-direction: column;
				/* min-width: 150px; */
				margin-top: 10px;
			}
			.category-boxes td span{
				color:{{$main_color}};
				margin-top:8px;
				display:inline-block;
				font-weight: 600; 
			}

			.additonal-media {
				display: table-cell;
				padding: 20px 0px;
			}

			.additonal-media td {
				padding: 5px;
				border: 1px solid #a3a3a3;
				display: inline-table;
			}

			.additonal-media img {
				width: 100px;
				height: 100px;
				object-fit: cover;
				padding : 5px;
			}

			.map-image { width: 100px; height: 150px; }
			.geo-map-image { width: 700px; height: 300px; object-fit: cover; }

			.box {
				height: 12px;
				width: 12px;
				border: 1px solid black;
			}

		</style>
	</head>
	<body style="font-family: 'Lato';background:#FFFFFF">
		<!-- Summary of Assessment -->
        <table width="100%" bgcolor="#FFFFFF" border="0" cellspacing="0" cellpadding="0" align="left;">
            <tr>
                <td align="left" style="font-size: 11px;color: #000; width: 20%;">
                    <img src="{{$pdfLogo['short_logo_url'] ?? url('images/default.png')}}"
                        alt="logo" width="100px">
                </td>
                <td align="center" style="font-size: 11px;color: #000;width: 60%;">
                    <h3 style="font-size: 34px;line-height: 36px;text-align: center;margin: 0;">{{ ($pData['type'] ?? '') === 'PA' ? 'PA' : 'SA' }} Report</h3>
                    <span style="font-size: 16px;padding-top: 20px;display: block;">Page <span
                            id="pagenumber"></span></span>
                </td>
                <td align="right" style="font-size: 11px;color: #000;width: 20%; padding-top:15px;">
                    <h3
                        style="width:250px;background-color: {{ '#00B050' }};font-size: 20px;color: #FFF;padding:12px 0px;margin: 0; text-align: center;border-radius:10px">
                        {{ 'PASSED (manual)' }} </h3>
                </td>
            </tr>
        </table>
        <table width="100%" bgcolor="#FFFFFF" border="0" cellspacing="0" cellpadding="0" align="left;">
            <tr style="width:100%;line-height: 18px;">
                <td align="left" valign="top" style="font-size: 16px;">
					@if (($pData['type'] ?? '') === 'PA')
                    <strong>Centre Name:</strong> {{ $pData['centre_name'] }}
					@endif
                </td>
            </tr>
        </table>
        <table width="100%" bgcolor="#FFFFFF" border="0" cellspacing="0" cellpadding="0" align="left;">
			<tr style="width:100%;line-height: 18px;">
					<td align="left" valign="top" style="font-size: 16px;width:50%;">
						<strong>Training Course:</strong> {{ $pData['course_name'] }}
					</td>
					<td align="right" valign="top" style="font-size: 16px;width:50%;">
						<strong>Date Start:</strong> N/A
					</td>
				</tr>
            <tr style="width:100%;line-height: 18px;">
				<td align="left" valign="top" style="font-size: 16px;width:50%;">
					<strong>Module Name:</strong> {{ $pData['module_name'] }}
				</td>
                <td align="right" valign="top" style="font-size: 16px;width:50%;">
                    <strong>Date Completed:</strong> {{ $pData['completion_date'] }}
                </td>
            </tr>
            <tr style="width:100%;line-height: 18px;">
				<td align="left" valign="top" style="font-size: 16px;width:50%;">
                    <strong>User Email:</strong> {{ $pData['user_email'] }}
                </td>
                <td align="right" valign="top" style="font-size: 16px;width:40%;">
                    <strong>Time Taken:</strong> {{ $pData['time_spent'] }}
                </td>
            </tr>
            <tr style="width:100%;line-height: 18px;">
                <td align="left" valign="top" style="width:50%;">
                        <strong style="font-size: 16px;">Start Location:</strong> <span
                            style="font-size: 12.5px;">N/A</span>
                </td>
                <td align="right" valign="top" style="width:50%;">
                        <strong style="font-size: 16px;">End Location:</strong> <span
                            style="font-size: 12.5px;">N/A</span>
                </td>
            </tr>
        </table>
        <table width="100%" bgcolor="#FFFFFF" border="0" cellspacing="0" cellpadding="0" align="left"
            style="margin-top: 10px;">
            <tr>
                <td style="border-top: 4px solid {{$main_color}};padding: 10px 0; width: 100%;">
                    <h3 style="font-size: 20px;color: {{$main_color}};margin: 0; font-weight: 400;">Result Manually Overridden</h3>
                </td>
            </tr>
        </table>
        <table width="100%" bgcolor="#FFFFFF" border="0" cellspacing="0" cellpadding="0" align="left"
            style="border-top: 2px solid #333333;" class="detailstable">
				<tbody align="center" class="details">
					<tr>
						<td style="display:table;margin: 100px auto;">
							<img src="{{url('images/na-image.png')}}" style="filter: grayscale(100%);" alt="Not Applicable" height="540px" width="540">
						</td>
					</tr>
				</tbody>
        </table>
        <footer>
            <table width="100%" bgcolor="#FFFFFF" border="0" cellspacing="0" cellpadding="0" align="left;">
                <tr>
                    <td align="right" style="font-size: 16px;color: #666666;">Page <span id="pagenumber"></span></td>
                </tr>
            </table>
        </footer>
	</body>
</html>
