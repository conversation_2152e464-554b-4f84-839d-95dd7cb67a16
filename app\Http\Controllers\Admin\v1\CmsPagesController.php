<?php

namespace App\Http\Controllers\Admin\v1;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Repositories\Admin\v1\CmsPagesRepository;
use App\Http\Resources\Admin\v1\CmsPagesResource;
use App\Http\Resources\Admin\v1\CmsPagesListingResource;
use App\Http\Requests\Admin\v1\CmsPagesRequest;
use App\Http\Requests\Admin\v1\CmsPagesUpdateRequest;
use App\Http\Requests\Admin\v1\CmsPagesDeleteRequest;
use App\Http\Requests\Admin\v1\CmsPagesStatusRequest;
use App\Models\CmsPages;
use Illuminate\Http\Response;
use App\Http\Resources\CustomCollection;
use App\Http\Requests\Admin\v1\CommonListingRequest;

class CmsPagesController extends Controller
{
     /*
      |--------------------------------------------------------------------------
      | CmsPagesController Controller
      |--------------------------------------------------------------------------
      |
      | This controller handles authenticating users for the application and
      | redirecting them to your home screen. The controller uses a trait
      | to conveniently provide its functionality to your applications.
      |
     */

    private $model;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->model = new CmsPages();
        $this->repository = new CmsPagesRepository($this->model);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        //
    }

    /**
     * List All CMS Pages
     *
     * @return \Illuminate\Http\Response
     */
    public function getCmsPagesList(CommonListingRequest $request)
    {
        try {
            $cmspages = $this->repository->getCmsPagesListing($request->all());
            return CmsPagesListingResource::collection($cmspages->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Add CMS Page
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(CmsPagesRequest $request)
    {
        try {
            $result = DB::transaction(function () use ($request) {
                $moduleData = $request;
                $this->repository->create($moduleData);
            });
            return response()->json(setResponse([], ['message' => __('admin.CmsPages.add')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get CMS Page Details
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $cmsDetail=CmsPages::whereId($id)->first();
            if ($cmsDetail) {
                $cmsDetail = new CmsPagesResource($cmsDetail);
            }
            $message = !empty($cmsDetail) ? __('admin.CmsPages.found') : __('admin.CmsPages.notFound');
            return response()->json(setResponse($cmsDetail, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Update CMS Page Details
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(CmsPagesUpdateRequest $request, $id)
    {
        try {
            $this->repository->update($request, $id);
            return response()->json(setResponse([], ['message' => __('admin.CmsPages.update')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Delete CMS Pages
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(CmsPagesDeleteRequest $request)
    {   
        try {
            $this->repository->delete($request);
            return response()->json(setResponse([], ['message' => __('admin.CmsPages.delete')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * CMS Pages Change Status
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function changeStatus(CmsPagesStatusRequest $request) 
    {
        try {
            $this->repository->change_status($request);
            return response()->json(setResponse([], ['message' => __('admin.CmsPages.status')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
