<?php

namespace App\Console\Commands;

use App\Jobs\FlushRecordJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\TrainingCourseProgress;
use App\Models\TrainingCourseSubModuleQuiz;
use App\Models\TrainingCourseModuleProgress;
use App\Models\TrainingCourseSubmoduleDetails;
use App\Models\TrainingCourseSubmoduleProgress;
use App\Models\TrainingCourseSubModuleQuizResults;
use App\Models\TrainingCourseSubModulePracticalAssessmentAnswer;
use App\Models\TrainingCourseSubModulePracticalAssessmentOption;
use App\Models\TrainingCourseSubModulePracticalAssessmentResults;
use App\Models\TrainingCourseSubModulePracticalAssessmentQuestion;
use App\Models\TrainingCourseSubModulePracticalAssessmentAnswersJson;

class ProgressCalculation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pqms:progress-calculation';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'PQMS Progress Calculation';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {


    }

}
