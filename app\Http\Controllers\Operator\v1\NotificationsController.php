<?php

namespace App\Http\Controllers\Operator\v1;

use Illuminate\Http\Response;
use App\Models\NotificationOperator;
use App\Http\Resources\Operator\v1\NotificationResource;
use App\Http\Requests\Operator\v1\NotificationRequest;

class NotificationsController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    
    public function __construct() {
        
    }

    /**
     * @OA\Get(
     *     path="/operator/notifications",
     *     tags={"Operator - Web Notification"},
     *     summary="Get Notification Settings List",
     *     description="Get Notification Settings List",
     *     operationId="index",
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function index() {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $settings = NotificationOperator::whereMasterUserId($operatorId)->get();
            return NotificationResource::collection($settings);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * @OA\Post(
     *     path="/operator/notifications",
     *     tags={"Operator - Web Notification"},
     *     summary="Store Notification Settings",
     *     description="Store Notification Settings",
     *     operationId="store",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="notification",
     *                     description="Notification",
     *                     type="array",
     *                     @OA\Items(
     *                     type="object",
     *                          @OA\Property(
     *                              property="id",
     *                              description="Notfication Id",
     *                              type="integer"
     *                          ),
     *                          @OA\Property(
     *                              property="is_on",
     *                              description="Is ON",
     *                              type="integer",
     *                              enum={0, 1}
     *                          )
     *                      )
     *                 ),
     *                 example={"id": 1, "is_on": 1}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function store(NotificationRequest $request) {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $notification = NotificationOperator::where(['id' => $request->id, 'master_user_id' => $operatorId])->first();
            $notification->update(['is_on' => $request->is_on]);
            return response()->json(setResponse([], ['message' => __('operator.notification.updated')]))->setStatusCode(Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
