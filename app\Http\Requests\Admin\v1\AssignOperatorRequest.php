<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;

class AssignOperatorRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {
        return [
            'user_id' => 'required|int',
        ];
    }

    public function messages() : array
    {
        return [
            'user_id.required' => __('User id is required.'),
            'user_id.int' => __('User id must be integer.'),
        ];
    }
}
