<?php

namespace App\Http\Middleware;

use App\Models\MasterUser;
use Closure;
use Illuminate\Support\Facades\Config;

class OverrideMailConfig
{

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next, $guard, $extra = null)
    {
        $isSettingsUpdated = false;
        $operator = null;

        // Determine the operator based on the guard and extra parameters
		if ($request->header('bundle-id') || $request->bundle_id) {
			$header_bundle_id = ($request->header('bundle-id')) ? strtolower($request->header('bundle-id')) : strtolower($request->bundle_id);
			$bundle_ids = array_change_key_case(config('constants.bundle_ids'), CASE_LOWER);
			if (in_array($header_bundle_id, array_keys($bundle_ids))) {
				$operator_id = $bundle_ids[$header_bundle_id];
				$operator = MasterUser::find($operator_id);
			}
		}
		elseif(($guard === 'api' && empty($extra)) || ($guard === 'operator' && empty($extra))) {
            $user = auth()->guard($guard)->user();
            if ($user) {
                if ($guard === 'api') {
                    $operator = $user->user_relation->operator ?? null;
                } elseif ($guard === 'operator') {
                    $operatorId = $user->parent_id ?? $user->id;
                    $operator = MasterUser::find($operatorId);
                }
            }
        } elseif ($guard === 'operator' && $extra === 'smartawards') {
            $operatorId = config('constants.smart_award_operator_id');
            $operator = MasterUser::find($operatorId);
        } elseif ($guard === 'operator' && $extra === 'forgotPassword') {
            $user = MasterUser::where('email', $request->email)->first();
            if ($user) {
                $operatorId = $user->parent_id ?? $user->id;
                $operator = MasterUser::find($operatorId);
            }
        }

        // Set default values for application name and logo URL
        $appName = env('APP_NAME');
        $logoUrl = env('APP_URL') . '/images/logo.png';

        // Update email settings if an operator is found
        if ($operator && isset($operator->emailSettings)) {
            $emailSettings = $operator->emailSettings;

            Config::set('mail.mailers.smtp.transport', env('MAIL_DRIVER'));
            Config::set('mail.mailers.smtp.host', env('MAIL_HOST'));
            Config::set('mail.mailers.smtp.port', env('MAIL_PORT'));
            Config::set('mail.mailers.smtp.username', $emailSettings->mail_username);
            Config::set('mail.mailers.smtp.password', $emailSettings->mail_password);
            Config::set('mail.mailers.smtp.encryption', env('MAIL_ENCRYPTION'));
            Config::set('mail.from.address', $emailSettings->mail_from_address);
            Config::set('mail.from.name', $emailSettings->mail_from_name);
            Config::set('mail.app_name', $emailSettings->app_name);
            Config::set('mail.logo', isset($emailSettings->logo_name)
                ? env('CDN_URL') . config('constants.aws.email_logo') . '/' .
                $emailSettings->operator_id . '/' .
                $emailSettings->logo_name
                : env('APP_URL') . '/images/logo.png');
            Config::set('mail.fallback_link', $emailSettings->fallback_link);
            Config::set('constants.is_email_setting_on', true);
            Config::set('mail.main_color', optional($operator->pdfSettings)->main_color_scheme);

            // Mark settings as updated
            $isSettingsUpdated = true;
        }

        return $next($request);
    }
}