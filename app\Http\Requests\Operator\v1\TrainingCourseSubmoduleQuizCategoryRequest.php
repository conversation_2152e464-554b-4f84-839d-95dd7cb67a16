<?php

namespace App\Http\Requests\Operator\v1;

use App\Http\Requests\CustomFormRequest;

class TrainingCourseSubmoduleQuizCategoryRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array
    {
        return [
            'training_course_id' => 'required|numeric|exists:'.\DBTableNames::TRAINING_COURSE.',id',
            'name' => 'required|max:100',
        ];
    }
    
    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'training_course_id.required' => __('Training Course Id is required.'),
            'training_course_id.numeric' => __('Training Course Id is invalid.'),
            'training_course_id.exists' => __('Training Course not found.'),
            'name.required' => __('Name is required.'),
            'name.max' => __('Name is too long.'),
        ];
    }
    
}
