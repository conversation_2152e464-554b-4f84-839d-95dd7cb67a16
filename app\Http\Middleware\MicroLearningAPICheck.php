<?php

namespace App\Http\Middleware;
use Illuminate\Http\Response;
use Closure;

class MicroLearningAPICheck
{
    public function handle($request, Closure $next)
    {
        // check has api-key in header
        if($request->hasHeader('api-key') && $request->header('api-key') != ''){
            // Check if requested api-key in request header must be matched with the authorization key which is set in ENV
            if($request->header('api-key') == env('MICRO_LEARNING_AUTHORIZATIONs_KEY')){
                // proceed for the next step
                $response = $next($request);
            }else{
                // Unauthorize Access
                return response()->json(setErrorResponse('Unauthorize Access!', Response::HTTP_UNAUTHORIZED))->setStatusCode(Response::HTTP_UNAUTHORIZED);
            }
        }else{
            // Unauthorize Access
            return response()->json(setErrorResponse('Unauthorize Access!', Response::HTTP_UNAUTHORIZED))->setStatusCode(Response::HTTP_UNAUTHORIZED);
        }
        return $response;
    }
}
