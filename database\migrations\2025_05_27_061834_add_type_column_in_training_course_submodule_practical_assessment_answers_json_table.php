<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTypeColumnInTrainingCourseSubmodulePracticalAssessmentAnswersJsonTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('training_course_submodule_practical_assessment_answers_json', function (Blueprint $table) {
            if (!Schema::hasColumn('training_course_submodule_practical_assessment_answers_json', 'type')) {
                $table->string('type')->default('PA')->after('required');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('training_course_submodule_practical_assessment_answers_json', function (Blueprint $table) {
            if (Schema::hasColumn('training_course_submodule_practical_assessment_answers_json', 'type')) {
                $table->dropColumn('type');
            }
        });
    }
}
