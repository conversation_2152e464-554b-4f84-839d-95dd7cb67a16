<?php
/**
     * @OA\Post(
     *     path="/api/trainingCourseSubmodule/sendEmailProduct",
     *     tags={"Mobile - Training Course Submodule"},
     *     summary="Send Kit Overview - Product Email",
     *     description="Send Kit Overview - Product Email",
     *     operationId="sendEmailProduct",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="product_id",
     *                     description="Product Id",
     *                     type="string"
     *                 ),
     *                  @OA\Property(
     *                     property="email",
     *                     description="Email",
     *                     type="string"
     *                 ),
     *                  @OA\Property(
     *                     property="type",
     *                     description="Type KIT_OVERVIEW,PREVIOUS_RESULT",
     *                     type="string"
     *                 ),
     *                 example={"training_course_id": 7, "product_id": "3,4,7,8,10", "email": "<EMAIL>", "type":"KIT_OVERVIEW"}
     *              )
     *          )
     *      ),
     *     security={
     *         {"User": {}}
     *     },
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     * )
     */