<?php

namespace App\Events;

use App\Events\Event;
use Illuminate\Queue\SerializesModels;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Broadcasting\InteractsWithSockets;

class UserEmailVerifyEvent extends Event {

    use Dispatchable,
        InteractsWithSockets,
        SerializesModels;

    public $user;

    public function __construct($user) {        
        $this->user = $user;
    }

}
