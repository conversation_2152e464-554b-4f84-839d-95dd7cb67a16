<?php
/**
     * @OA\Post(
     *     path="/admin/verifyEmailToken",
     *     tags={"Admin - Auth"},
     *     summary="Verify Email token",
     *     description="Verify Email token",
     *     operationId="verifyEmailToken",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="email",
     *                     description="Email",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="token",
     *                     description="Token",
     *                     type="string"
     *                 ),
     *                 example={"email": "<EMAIL>", "token": "829434"}
     *              )
     *          )
     *      ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     * )
     */