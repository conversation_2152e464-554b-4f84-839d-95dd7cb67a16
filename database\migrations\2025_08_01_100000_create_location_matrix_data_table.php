<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLocationMatrixDataTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('location_matrix_data', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('question_id');
            $table->unsignedBigInteger('master_user_id');
            $table->string('region', 191)->nullable()->comment('Primary filter - Region');
            $table->string('territory', 191)->nullable()->comment('Secondary filter - Territory');
            $table->text('exchange')->comment('Final selection - Exchange');
            $table->json('additional_data')->nullable()->comment('Other CSV columns (Address, Map, etc.)');
            $table->timestamps();
            $table->softDeletes();
            
            // Foreign keys
            $table->foreign('question_id')->references('id')->on('training_course_submodule_practical_assessment_questions')->onDelete('cascade');
            $table->foreign('master_user_id')->references('id')->on('master_users')->onDelete('cascade');
            
            // Indexes for performance
            $table->index(['question_id', 'region', 'territory'])->name('idx_location_matrix_question_filters');
            $table->index(['question_id', 'master_user_id'])->name('idx_location_matrix_question_user');
            $table->fullText(['region', 'territory', 'exchange'])->name('idx_location_matrix_fulltext');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('location_matrix_data');
    }
}
