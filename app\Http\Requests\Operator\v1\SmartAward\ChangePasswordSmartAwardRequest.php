<?php

namespace App\Http\Requests\Operator\v1\SmartAward;

use App\Http\Requests\CustomFormRequest;

class ChangePasswordSmartAwardRequest extends CustomFormRequest {

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'email' => 'required',
            'password' => [
                'required',
                'min:8',
                'max:64',
                'regex:/[a-z]/', // must contain at least one lowercase letter
                'regex:/[A-Z]/', // must contain at least one uppercase letter
                'regex:/[0-9]/', // must contain at least one digit
                'regex:/[-@$!%*#?&]/',
            ],
        ];
    }
    
    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'email.required' => __('Email is required.'),
            'password.required' => __('Password is required.'),
            'password.min' => __('Password must be at least 8 characters.'),
            'password.max' => __('Password may not be greater than 20 characters.'),
            'password.regex' => __('Password must have atleast one capital, small, digit and special character.')
        ];
    }
}
