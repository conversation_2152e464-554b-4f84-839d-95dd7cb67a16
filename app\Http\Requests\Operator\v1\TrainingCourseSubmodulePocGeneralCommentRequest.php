<?php

namespace App\Http\Requests\Operator\v1;

use DBTableNames;
use App\Http\Requests\CustomFormRequest;

class TrainingCourseSubmodulePocGeneralCommentRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {
        return [
            'id' => 'required|integer|exists:' . DBTableNames::TRAINING_COURSE_SUBMODULE_HOTSPOT_UPLOADER_PROGRESS . ',id',
            'comment' => 'required',
            'ratings' => 'required|integer|min:1|max:5',
            'data' => 'required|array'
        ];
    }

    public function messages() : array
    {
        return [
            'id.required' => __('Progress id is required.'),
            'id.integer' => __('Progress id must be an integer.'),
            'id.exists' => __('Progress doesn\'t exists.'),
            'comment.required' => __('Comment is required.'),
            'ratings.required' => __('Ratings is required.'),
            'ratings.integer' => __('Ratings must be an integer value.'),
            'ratings.min' => __('Ratings must be between 1 to 5.'),
            'ratings.max' => __('Ratings must be between 1 to 5.'),
            'data.required' => __('data is required.'),
        ];
    }
}
