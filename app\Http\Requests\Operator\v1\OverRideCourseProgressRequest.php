<?php

namespace App\Http\Requests\Operator\v1;

use DBTableNames;
use App\Http\Requests\CustomFormRequest;

class OverRideCourseProgressRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {
        return [
            'training_course_id' => 'required|int|exists:'.DBTableNames::TRAINING_COURSE.',id',
            'user_id' => 'required|int|exists:'.DBTableNames::USERS.',id'
        ];
    }

    public function messages() : array
    {
        return [
            'user_id.required' => __('User Id is required.'),
            'user_id.int' => __('User id must be integer.'),
            'training_course_id.required' => __('Training Course Id is required.'),
            'training_course_id.int' => __('Training Course id must be integer.'),
            'master_user_id.required' => __('Master User Id is required.'),
            'master_user_id.int' => __('Master User id must be integer.')
        ];
    }
}
