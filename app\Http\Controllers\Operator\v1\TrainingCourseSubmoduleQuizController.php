<?php

namespace App\Http\Controllers\Operator\v1;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Models\TrainingCourseSubModuleQuiz;
use App\Models\TrainingCourseSubmoduleProgress;
use App\Models\TrainingCourseSubModuleQuizResults;
use App\Models\TrainingCourseSubModuleQuizCategory;
use App\Models\TrainingCourseSubModuleQuizQuestion;
use App\Http\Requests\Operator\v1\QuizResultRequest;
use App\Models\TrainingCourseSubModuleQuizQuestionOption;
use App\Http\Requests\Operator\v1\ManualQuizResultRequest;
use App\Http\Requests\Operator\v1\ImportQuestionBankRequest;
use App\Http\Requests\Operator\v1\CommonBulkStatusChangeRequest;
use App\Repositories\Operator\v1\TrainingCourseSubModuleQuizRepository;
use App\Http\Requests\Operator\v1\TrainingCourseSubmoduleQuizCategoryRequest;
use App\Http\Resources\Operator\v1\TrainingCourseSubModuleQuizResultResource;
use App\Repositories\Operator\v1\TrainingCourseSubModuleQuizResultRepository;
use App\Http\Requests\Operator\v1\TrainingCourseSubmoduleQuizQuestionsRequest;
use App\Http\Resources\Operator\v1\TrainingCourseSubModuleQuizCategoryResource;
use App\Repositories\Operator\v1\TrainingCourseSubModuleQuizCategoryRepository;
use App\Http\Resources\Operator\v1\TrainingCourseSubModuleQuizPdfResultResource;
use App\Http\Resources\Operator\v1\TrainingCourseSubModuleQuizQuestionsResource;
use App\Http\Resources\Operator\v1\TrainingCourseSubModuleQuizCategoriesResource;
use App\Http\Requests\Operator\v1\TrainingCourseSubModuleQuizResultListingRequest;
use App\Http\Requests\Operator\v1\TrainingCourseSubModuleQuizCategoriesListingRequest;

class TrainingCourseSubmoduleQuizController extends Controller
{
    protected $categoryModel;
    protected $categoryRepository;
    protected $quizModel;
    protected $quizRepository;
    protected $quizResultModel;
    protected $quizResultRepository;

    public function __construct() {
        $this->categoryModel = new TrainingCourseSubModuleQuizCategory();
        $this->categoryRepository = new TrainingCourseSubModuleQuizCategoryRepository($this->categoryModel);
        $this->quizModel = new TrainingCourseSubModuleQuiz();
        $this->quizRepository = new TrainingCourseSubModuleQuizRepository($this->quizModel);
        $this->quizResultModel = new TrainingCourseSubModuleQuizResults();
        $this->quizResultRepository = new TrainingCourseSubModuleQuizResultRepository($this->quizResultModel);
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourseSubmodule/quiz/categories/getListing",
     *     tags={"Operator - Training Course Submodule Quiz"},
     *     summary="Get All Categories",
     *     description="Get All Categories",
     *     operationId="index",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 example={"training_course_id": 1, "per_page": 10, "page": 1, "search_key": "", "sort_by": "name", "order_by": "asc", "filters": {"name": "", "total_subdata": "", "status": ""}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Role Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function index(TrainingCourseSubModuleQuizCategoriesListingRequest $request) {
        try {
            $categories = $this->categoryRepository->getCategoryListing($request->all());
            if ($request->isExport) {
                return $this->categoryRepository->exportCsv($categories->get(), $request->exportFields);
            }
            return TrainingCourseSubModuleQuizCategoriesResource::collection($categories->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourseSubmodule/quiz/categories",
     *     tags={"Operator - Training Course Submodule Quiz"},
     *     summary="Add Category",
     *     description="Add Category",
     *     operationId="store",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="name",
     *                     description="Name",
     *                     type="string"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Category Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function store(TrainingCourseSubmoduleQuizCategoryRequest $request)
    {
        try {
            TrainingCourseSubModuleQuizCategory::create($request->only(['training_course_id', 'name']));
            return response()->json(setResponse([], ['message' => __('operator.quiz.categories.added')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/trainingCourseSubmodule/quiz/categories/{id}",
     *     tags={"Operator - Training Course Submodule Quiz"},
     *     summary="Show Category",
     *     description="Show Category",
     *     operationId="show",
     *     @OA\Parameter(
     *         description="Id of category to fetch",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function show($id)
    {
        try {
            $category = $this->categoryModel->find($id);
            return ($category) ?
                    (new TrainingCourseSubModuleQuizCategoryResource($category)) :
                    response()->json(setErrorResponse(__('operator.quiz.categories.not-found')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Put(
     *     path="/operator/trainingCourseSubmodule/quiz/categories/{id}",
     *     tags={"Operator - Training Course Submodule Quiz"},
     *     summary="Update Category",
     *     description="Update Category",
     *     operationId="update",
     *     @OA\Parameter(
     *         description="Id of category to update",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="name",
     *                     description="Name",
     *                     type="string"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Category Updated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function update(TrainingCourseSubmoduleQuizCategoryRequest $request, $id)
    {
        try {
            $category = $this->categoryModel->where(['id' => $id, 'training_course_id' => $request->training_course_id])->first();
            if ($category) {
                $category->update(['name' => $request->name]);
                return response()->json(setResponse([], ['message' => __('operator.quiz.categories.updated')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setErrorResponse(__('operator.quiz.categories.not-found')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourseSubmodule/quiz/categories/delete",
     *     tags={"Operator - Training Course Submodule Quiz"},
     *     summary="Delete Categories",
     *     description="Delete Categories",
     *     operationId="destroy",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="ids",
     *                     description="Ids of Resources",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 example={"ids": {1, 2}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function destroy(Request $request)
    {
        try {
            $ids = is_array($request->ids)? $request->ids: [];
            $this->categoryModel->destroy($ids);
            return response()->json(setResponse([], ['message' => __('operator.quiz.categories.deleted')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourseSubmodule/quiz/categories/changeStatus",
     *     tags={"Operator - Training Course Submodule Quiz"},
     *     summary="Change Status of Categories",
     *     description="Change Status of Categories",
     *     operationId="changeStatus",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="ids",
     *                     description="ids",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={"ids": {"1","2"}, "status": "Active"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Status changed successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function changeStatus(CommonBulkStatusChangeRequest $request)
    {
        try {
            $this->categoryModel->whereIn('id', $request->ids)->update(['status' => $request->status]);
            return response()->json(setResponse([], ['message' => __('operator.quiz.categories.status-changed')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourseSubmodule/quiz/categories/storeQuestions",
     *     tags={"Operator - Training Course Submodule Quiz"},
     *     summary="Add Questions",
     *     description="Add Questions",
     *     operationId="storeQuestions",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="category_id",
     *                     description="Category Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="questions",
     *                     description="Questions",
     *                     type="array",
     *                     @OA\Items(
     *                     type="object",
     *                          @OA\Property(
     *                              property="question",
     *                              description="Question",
     *                              type="string"
     *                          ),
     *                          @OA\Property(
     *                              property="question_image",
     *                              description="Question Image",
     *                              type="string"
     *                          ),
     *                          @OA\Property(
     *                              property="answer_image",
     *                              description="Answer Image",
     *                              type="string"
     *                          ),
     *                          @OA\Property(
     *                              property="answer_explanation",
     *                              description="Answer Explanation",
     *                              type="string"
     *                          ),
     *                          @OA\Property(
     *                              property="options",
     *                              description="Options",
     *                              type="array",
     *                              @OA\Items(
     *                              type="object",
     *                                  @OA\Property(
     *                                      property="answer",
     *                                      description="Answer",
     *                                      type="string"
     *                                  ),
     *                                  @OA\Property(
     *                                      property="is_correct_answer",
     *                                      description="Is correct answer",
     *                                      type="integer"
     *                                  ),
     *                              )
     *                          )
     *                      )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Questions Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function storeQuestions(TrainingCourseSubmoduleQuizQuestionsRequest $request)
    {
        try {
            \DB::transaction(function () use ($request) {
                $data = $request->all();
                $allQuizQuestions = TrainingCourseSubModuleQuizQuestion::where('category_id',$data['category_id'])->whereNull('deleted_at')->pluck('id')->toArray();
                if(!empty($allQuizQuestions)){
                    TrainingCourseSubModuleQuizCategory::where('id',$data['category_id'])->update(['total_subdata'=>count($data['questions'])]);
                }
                foreach($data['questions'] as $question) {
                    if(isset($question['question_image'])){
                        $parseURL = parse_url($question['question_image']);
                    }else{
                        $parseURL = '';
                    }
                    $url_parts_question = $parseURL;
                    $url_parts_answer = parse_url($question['answer_image']);
                    $category = $this->categoryModel->find($data['category_id']);
                    $question['training_course_id'] = $category->training_course_id;
                    $question['category_id'] = $data['category_id'];

                    if (isset($question['id'])) {
                        $self = TrainingCourseSubModuleQuizQuestion::where('id', $question['id'])->first();
                        unset($allQuizQuestions[array_search($self->id, $allQuizQuestions)]);

                        $quizQuestion = TrainingCourseSubModuleQuizQuestion::find($question['id']);
                        if(isset($url_parts_question['host']) ){
                            $question['question_image'] =  $quizQuestion['question_image'];
                        }
                        if(isset($url_parts_answer['host'])){
                            $question['answer_image'] =  $quizQuestion['answer_image'];
                        }
                        $quizQuestion->update($question);
                    } else {
                        $quizQuestion = TrainingCourseSubModuleQuizQuestion::create($question);
                    }

                    $existingCatQuestionOptions = $quizQuestion->options()->pluck('id')->toArray();
                    foreach($question['options'] as $option) {
                        if (isset($option['id'])) {
                            $quizOption = TrainingCourseSubModuleQuizQuestionOption::find($option['id']);
                            $quizOption->update($option);
                            unset($existingCatQuestionOptions[array_search($option['id'], $existingCatQuestionOptions)]);
                        } else {
                            $option['question_id'] = $quizQuestion->id;
                            TrainingCourseSubModuleQuizQuestionOption::create($option);
                        }
                    }
                    if (count($existingCatQuestionOptions) > 0) {
                        TrainingCourseSubModuleQuizQuestionOption::whereIn('id', $existingCatQuestionOptions)->delete();
                    }
                }
                if (count($allQuizQuestions) > 0) {
                    TrainingCourseSubModuleQuizQuestion::whereIn('id', $allQuizQuestions)->delete();
                }
            });
            return response()->json(setResponse([], ['message' => __('operator.quiz.categories.questions.added')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/trainingCourseSubmodule/quiz/categories/listQuestions/{id}",
     *     tags={"Operator - Training Course Submodule Quiz"},
     *     summary="Show Questions",
     *     description="Show Questions",
     *     operationId="listQuestions",
     *     @OA\Parameter(
     *         description="Id of category to fetch questions",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function listQuestions($id)
    {
        try {
            $category = $this->categoryModel->find($id);
            return ($category) ?
                    TrainingCourseSubModuleQuizQuestionsResource::collection($category->questions):
                    response()->json(setErrorResponse(__('operator.quiz.categories.not-found')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourseSubmodule/quiz/questionBank/import",
     *     tags={"Operator - Training Course Submodule Quiz"},
     *     summary="Import Question Bank",
     *     description="Import Question Bank",
     *     operationId="questionBankImport",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="file",
     *                     description="CSV File",
     *                     type="file"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Questions Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function questionBankImport(ImportQuestionBankRequest $request) {
        try {
            return \DB::transaction(function () use ($request) {
                $response = $this->quizRepository->importQuestionBank($request);
                if (count($response['skippedRows']) > 0) {
                    return response()->json(setErrorResponse(__('operator.quiz.question-bank.not-imported') . implode(',', $response['skippedRows']), [
                        'totalRows' => count($response['totalRows']),
                        'insertedRows' => count($response['insertedRows']),
                        'skippedRows' => count($response['skippedRows']),
                    ]))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
                return response()->json(setResponse([], ['message' => __('operator.quiz.question-bank.imported')]))->setStatusCode(Response::HTTP_OK);
            });
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/trainingCourseSubmodule/quiz/questionBank/export/{id}",
     *     tags={"Operator - Training Course Submodule Quiz"},
     *     summary="Export Question Bank",
     *     description="Export Question Bank",
     *     operationId="questionBankExport",
     *     @OA\Parameter(
     *         description="Id of training course to export the quiz questions",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function questionBankExport($id) {
        try {
            return $this->quizRepository->exportQuestionBank($id);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourseSubmodule/quiz/result/getListing",
     *     tags={"Operator - Training Course Submodule Quiz"},
     *     summary="Get All User Results",
     *     description="Get All User Results",
     *     operationId="getResultListing",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="submodule_id",
     *                     description="Submodule Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                  @OA\Property(
     *                     property="isExport",
     *                     description="for export csv",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 example={"submodule_id": 1, "per_page": 10, "page": 1, "search_key": "", "sort_by": "name", "order_by": "asc", "isExport": 0, "filters": {"name": "", "total_subdata": "", "status": ""}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Role Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getResultListing(TrainingCourseSubModuleQuizResultListingRequest $request) {
        try {
            $results = $this->quizResultRepository->getListing($request->all());
            if ($request->isExport) {
                $exportData = $results->get()->toArray();
                $userQuizResult = [];
                $i=0;
                foreach($exportData as $key => $res){
                    $quiz = TrainingCourseSubModuleQuizResults::where([
                        'submodule_id' => $request->submodule_id,
                        'user_id' => $res['user_id']
                    ])->get();
                    foreach ($quiz as $k => $q) {
                        $userQuizResult[] = $res;
                        $userQuizResult[$i]['pdf'] = $q->pdf_url;
                        $userQuizResult[$i]['attempts'] = $k+1;
                        $i++;
                    }
                }
                return $this->quizResultRepository->exportCsv($userQuizResult, $request->exportFields);
            }
            return TrainingCourseSubModuleQuizResultResource::collection($results->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/trainingCourseSubmodule/quiz/result/markAsPass/{id}",
     *     tags={"Operator - Training Course Submodule Quiz"},
     *     summary="Mark User as Passed",
     *     description="Mark User as Passed",
     *     operationId="markAsPass",
     *     @OA\Parameter(
     *         description="Id of result to mark as pass",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function markAsPass($id) {
        try {
            $result = $this->quizResultModel->find($id);
            if ($result) {
                $result->update(['is_pass' => 1, 'passed_by_operator' => 1]);

                // Quiz Progress
                TrainingCourseSubmoduleProgress::where(['training_course_id'=>$result->training_course_id, 'module_id'=>$result->module_id, 'submodule_id'=>$result->submodule_id, 'user_id'=>$result->user_id])->update(['submodule_progress' => 100]);

                return response()->json(setResponse([], ['message' => __('operator.quiz.result.marked-as-pass')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setResponse([], ['message' => __('operator.quiz.result.not-found')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourseSubmodule/quiz/result/quizResults",
     *     tags={"Operator - Training Course Submodule Quiz"},
     *     summary="Get Quiz Pdf Result",
     *     description="Get Quiz Pdf Result",
     *     operationId="quizResults",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="submodule_id",
     *                     description="Submodule Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="integer"
     *                 ),
     *                  @OA\Property(
     *                     property="module_id",
     *                     description="Module Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="user_id",
     *                     description="User Id",
     *                     type="integer"
     *                 ),
     *                 example={"training_course_id": 1, "module_id": 1, "submodule_id": 1, "user_id" : 1}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */
    public function quizResults(QuizResultRequest $request) {
        try {
            $userQuizResult = [];
            $user = User::find($request->user_id);
            if($user){
                $userQuizResult['id'] = $user->id;
                $userQuizResult['name'] = $user->name;
                $userQuizResult['manager_email'] = $user->userOperator->manager_email ?? '';
                $userQuizResult['unique_id'] = $user->userOperator->unique_id ?? '';
                $userQuizResult['user_group_id'] = $user->userOperator->user_group_id ?? '';
                $userQuizResult['group_name'] = $user->userOperator->group->name ?? '';
            }

            $quiz = TrainingCourseSubModuleQuizResults::where([
                'training_course_id' => $request->training_course_id,
                'module_id' => $request->module_id,
                'submodule_id' => $request->submodule_id,
                'user_id' => $request->user_id
            ])->orderBy('id', 'DESC')->get();
            $userQuizResult['pdf_list'] = TrainingCourseSubModuleQuizPdfResultResource::collection($quiz);
            return response()->json(setResponse($userQuizResult, ['message' => __('operator.quiz.result.pdf-success')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    public function manualPDFGenerate(ManualQuizResultRequest $request) {
        $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
        $user = User::find($request->user_id);
        $results = TrainingCourseSubModuleQuizResults::where(['master_user_id' => $operatorId])
                        ->where('user_id',$user->id)
                        ->where(function($query) {
                            $query->where('pdf','=','')->orWhereNull('pdf');
                        })
                        ->get();
        if(!empty($results)){
            foreach ($results as $key => $result) {
                dispatch(new \App\Jobs\QuizResultPdfSendMailJob($result->id, $request->user_id, $result->correct_answers, $user->user_relation,'OPERATOR',$result->late_submission));
            }
            return response()->json(setResponse([], ['message' => __('operator.quiz.manualPDFGenerate')]))->setStatusCode(Response::HTTP_OK);
        }else{
            return response()->json(setResponse([], ['message' => __('operator.quiz.pdfAlreadyGenerated')]))->setStatusCode(Response::HTTP_OK);
        }
    }
}
