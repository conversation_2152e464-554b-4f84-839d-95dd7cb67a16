<?php
/**
     * @OA\Get(
     *     path="/admin/resendPassword/{type}/{id}",
     *     tags={"Admin - Resend Password"},
     *     summary="Resend Password to User/Operator",
     *     description="Resend Password to User/Operator",
     *     operationId="resendPassword",
     *     @OA\Parameter(
     *         description="type :- users,operators",
     *         name="type",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string",
     *             enum={"users", "operators"}
     *         )
     *     ),
     *     @OA\Parameter(
     *         description="id",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */