<?php

namespace App\Http\Requests\Operator\v1;

use DBTableNames;
use App\Http\Requests\CustomFormRequest;
use App\Models\TrainingCourseSubmoduleDetails;
use App\Models\TrainingCourseSubModuleFeedbackQuestion;
use App\Models\TrainingCourseSubModulePracticalAssessmentQuestion;

class TrainingCourseSubmoduleDetailsRequest extends CustomFormRequest
{
    
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {
        $submoduleTypeId = request()->submodule_type_id;
        \Validator::extend('name_exists',
            function($attribute, $value, $parameters)
            {
                $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
                if(isset($parameters[0]) && !is_null($parameters[0])){
                    $existModule = TrainingCourseSubmoduleDetails::where('submodule_name',$value)->whereTrainingCourseId($parameters[1])->whereModuleId($parameters[2])->where('id','!=', $parameters[0])->first();
                }else{
                    $existModule = TrainingCourseSubmoduleDetails::where('submodule_name',$value)->whereTrainingCourseId($parameters[1])->whereModuleId($parameters[2])->first();
                }
                return $existModule ? false : true;
            }
        );
        $rules = [
            "training_course_id" => "required|integer",
            "module_id" => "required|integer",
            "submodule_type_id" => "required|integer|min:1|max:19",
            "submodule_name" => "required|name_exists:".$this->id.",".$this->training_course_id.",".$this->module_id,
            "thumbnail" => "required",
            "description" => "required",
            "submodule_lock" => "required",
            "submodule_complete" => "required_if:submodule_lock,1|nullable",
            "duration" => "required_if:submodule_complete,1|nullable",
            "duration_type" => "required_if:submodule_complete,1|nullable|in:Hours,Days",
            "unlock_datetime" => "required_if:submodule_complete,0|nullable|date_format:Y-m-d H:i:s",
            "enable_time_spend" => "required_unless:submodule_type_id,2",
            "completion_percentage" => "required_if:(enable_time_spend,1,submodule_type_id,!2,!17)",
            "condition" => "required_if:enable_time_spend,1",
            "time_spent" => "required_if:enable_time_spend,1",//|integer|min:5
            "status" => "required"
        ];
        $submoduleValidations = [];
        if ($submoduleTypeId == 1) { // For title slide submodule validation
            $submoduleValidations = [
                "title" => "required",
                "subtitle" => "required",
                "button_text" => "required",
            ];
        } else if ($submoduleTypeId == 2) { // For 360 url submodule validation
            $submoduleValidations = [
                "touch_count" => "required|integer|between:1,100",
                "url_360" => "required",
            ];
        } else if ($submoduleTypeId == 3) { // For Upload Video
            $submoduleValidations = [
                "resource_type" => "required",
                "resource_id" => "required",
            ];
        } else if ($submoduleTypeId == 4) { // For Image Gallery submodule validation
            $submoduleValidations = [
                'galleries' => "required|array",
                'galleries.*.image' => "required",
                'galleries.*.caption' => "required",
            ];
        } else if ($submoduleTypeId == 5) { // For product listing submodule validation
            $submoduleValidations = [
                'products' => "required|array",
                'products.*.name' => "required|max:255",
                'products.*.code' => "max:40",
                // 'products.*.code' => "required|max:30",
                'products.*.image' => "required|max:255",
            ];
        } else if ($submoduleTypeId == 6) { // For Happy Unhappy submodule validation
            $submoduleValidations = [
                'happyunhappy' => "required|array",
                'happyunhappy.*.thumbnail' => "required",
                'happyunhappy.*.happy_resource_type' => "required",
                'happyunhappy.*.happy_resource_id' => "required",
                'happyunhappy.*.happy_description' => "required",
                'happyunhappy.*.unhappy_resource_type' => "required",
                'happyunhappy.*.unhappy_resource_id' => "required",
                'happyunhappy.*.unhappy_description' => "required",
            ];
        } else if ($submoduleTypeId == 7) { // For course feedback questions submodule validation
            $submoduleValidations = [
                'questions' => "required|array|min:1",
                'questions.*.question' => "required",
                'questions.*.question_type' => "required|in:". implode(',', TrainingCourseSubModuleFeedbackQuestion::QUESTION_TYPES),
                'questions.*.is_required' => "required|in:0,1",
                'questions.*.options' => "required_if:questions.*.question_type,single,questions.*.question_type,multiple|array|min:1",
            ];
        } else if ($submoduleTypeId == 8) { // For video guide submodule validation
            $submoduleValidations = [
                'video_guide' => "required|array|min:1",
                'video_guide.*.title' => "required|max:255",
                'video_guide.*.resource_type' => "required|in:youtube,vimeo,source",
                'video_guide.*.resource_id' => "required_unless:video_guide.*.resource_type,null|max:255",
                'video_guide.*.resource_thumbnail' => "required_if:video_guide.*.resource_type,source|max:150",
                'video_guide.*.source_title' => "max:255",
                'video_guide.*.is_autoplay' => "in:" . true . "," . false,
                'video_guide.*.start_time' => "integer|nullable",
                'video_guide.*.end_time' => "integer|nullable",
                'video_guide.*.steps' => "required_unless:video_guide.*.title,null|array|min:1",
                'video_guide.*.steps.*.title' => "required_unless:video_guide.*.title,null|max:255",
                'video_guide.*.steps.*.resource_type' => "required_unless:video_guide.*.title,null|in:youtube,vimeo,source,image",
                'video_guide.*.steps.*.resource_id' => "required_unless:video_guide.*.steps.*.resource_type,null|max:255",
                'video_guide.*.steps.*.source_title' => "max:255",
                'video_guide.*.steps.*.is_autoplay' => "in:" . true . "," . false,
                'video_guide.*.steps.*.start_time' => "integer|nullable",
                'video_guide.*.steps.*.end_time' => "integer|nullable",
            ];
        } else if ($submoduleTypeId == 9) { // For image hotspot submodule validation
            $submoduleValidations = [
                'hotspot_image' => "required",
                'hotspots' => "required|array|min:1",
                'hotspots.*.section_type' => 'required|in:image,video,nomedia',
                'hotspots.*.region_image' => 'required',
                'hotspots.*.resource_type' => 'required_if:hotspots.*.section_type,video|in:youtube,vimeo,source',
                'hotspots.*.resource_id' => 'required_if:hotspots.*.section_type,image,video|max:255',
                'hotspots.*.source_title' => 'required_if:hotspots.*.resource_type,source|max:255',
                'hotspots.*.caption' => 'required',
                'hotspots.*.position' => 'required',
                'hotspots.*.position.x' => 'required_unless:hotspots.*.position,null|integer',
                'hotspots.*.position.x2' => 'required_unless:hotspots.*.position,null|integer',
                'hotspots.*.position.y' => 'required_unless:hotspots.*.position,null|integer',
                'hotspots.*.position.y2' => 'required_unless:hotspots.*.position,null|integer',
                'hotspots.*.position.height' => 'required_unless:hotspots.*.position,null|integer',
                'hotspots.*.position.width' => 'required_unless:hotspots.*.position,null|integer',
                'hotspots.*.is_autoplay' => "in:" . true . "," . false,
                'hotspots.*.start_time' => 'integer|nullable',
                'hotspots.*.end_time' => 'integer|nullable',
            ];
        } else if ($submoduleTypeId == 10) { // For Submodule confirmation section validation
            $submoduleValidations = [
                'title' => "required",
                'button1_text' => "required",
                'button2_text' => "required",
                'confirmation_lock' => "required",
            ];
        } else if ($submoduleTypeId == 11) { // For photo hotspot submodule validation
            $submoduleValidations = [
                'poc_enable' => "required|in:0,1",
                'hotspot_photo' => "required",
                'photoHotspots' => "required|array|min:1",
                'photoHotspots.*.region_name' => 'required',
                'photoHotspots.*.region_image' => 'required',
                'photoHotspots.*.position' => 'required',
                'photoHotspots.*.position.x' => 'required_unless:photoHotspots.*.position,null|integer',
                'photoHotspots.*.position.x2' => 'required_unless:photoHotspots.*.position,null|integer',
                'photoHotspots.*.position.y' => 'required_unless:photoHotspots.*.position,null|integer',
                'photoHotspots.*.position.y2' => 'required_unless:photoHotspots.*.position,null|integer',
                'photoHotspots.*.position.height' => 'required_unless:photoHotspots.*.position,null|integer',
                'photoHotspots.*.position.width' => 'required_unless:photoHotspots.*.position,null|integer',
            ];
        } else if ($submoduleTypeId == 12) { // For quiz submodule validation
            $submoduleValidations = [
                'quiz_duration' => "required|numeric|max:240",
                'auto_reset_duration_on_fail' => "required|numeric|max:240",
                'categories' => "required|array|min:1",
                'passing_criteria' => "required|numeric|min:1|max:100",
                'is_notify' => "required|in:0,1",
                'failed_attempts' => "nullable|required_if:is_notify,1|min:1|max:100",
                'max_attempts_before_fail' => 'numeric|between:1,10|nullable'
            ];
        } else if ($submoduleTypeId == 13) { // For Submodule job validation
            $submoduleValidations = [
                'jobs' => "required|array",
                'jobs.*.upload_type' => "required",
                //'job_topic' => "required|unique:" . DBTableNames::TRAINING_COURSE_SUBMODULE_DETAILS,
                'job_topic' => "required",
                'job_no_required' => "required",
                'multiple_job_enable' => "required",
            ];
        } else if ($submoduleTypeId == 14) { // For Confirmation box submodule validation
            $submoduleValidations = [
                'confirmation_box' => "required|array",
                'confirmation_box.*.image' => "required",
                'confirmation_box.*.title' => "required",
            ];
        } else if ($submoduleTypeId == 15) { // For Document Viewer submodule validation
            $submoduleValidations = [
                "resource_type" => "required",
                "resource_id" => "required",
            ];
        }
        else if ($submoduleTypeId == 16) { // For Practical Assessment submodule validation
            $submoduleValidations = [
                // PA valdiation rule for general questions
                'question_list' => 'required|array|min:1',
                'question_list.*.type' => 'required|in:general,category',
                'question_list.*.question_list_order' => 'required',
                'question_list.*.question' => "required_if:question_list.*.type,general",
                'question_list.*.question_type' => "required_if:question_list.*.type,general|in:" . implode(',', TrainingCourseSubModulePracticalAssessmentQuestion::QUESTION_TYPES),
                'question_list.*.is_required' => 'required_if:question_list.*.type,general|boolean',
                'question_list.*.question_order' => 'required_if:question_list.*.type,general|integer',
                'question_list.*.options' => "required_if:question_list.*.question_type,single,question_list.*.question_type,multiple|array|min:1",

                // PA valdiation rule for category questions
                'question_list.*.category_name' => 'required_if:question_list.*.type,category|string',
                'question_list.*.question.*.question' => 'required_if:question_list.*.type,category',
                'question_list.*.question.*.question_type' => "required_if:question_list.*.type,category|in:" . implode(',', TrainingCourseSubModulePracticalAssessmentQuestion::QUESTION_TYPES),
                'question_list.*.question.*.is_required' => 'required|boolean',
                'question_list.*.question.*.options' => "required_if:question_list.*.question.*.question_type,single,question_list.*.question.*.question_type,multiple|array|min:1",
                'question_list.*.question.*.question_order' => 'required|integer',
            ];
        }
        else if ($submoduleTypeId == 19) { // For Practical Assessment submodule validation
            $submoduleValidations = [
                // PA valdiation rule for general questions
                'question_list' => 'required|array|min:1',
                'question_list.*.type' => 'required|in:general,category',
                'question_list.*.question_list_order' => 'required',
                'question_list.*.question' => "required_if:question_list.*.type,general",
                'question_list.*.question_type' => "required_if:question_list.*.type,general|in:" . implode(',', TrainingCourseSubModulePracticalAssessmentQuestion::QUESTION_TYPES),
                'question_list.*.is_required' => 'required_if:question_list.*.type,general|boolean',
                'question_list.*.question_order' => 'required_if:question_list.*.type,general|integer',
                'question_list.*.options' => "required_if:question_list.*.question_type,single,question_list.*.question_type,multiple|array|min:1",

                // PA valdiation rule for category questions
                'question_list.*.category_name' => 'required_if:question_list.*.type,category|string',
                'question_list.*.question.*.question' => 'required_if:question_list.*.type,category',
                'question_list.*.question.*.question_type' => "required_if:question_list.*.type,category|in:" . implode(',', TrainingCourseSubModulePracticalAssessmentQuestion::QUESTION_TYPES),
                'question_list.*.question.*.is_required' => 'required|boolean',
                'question_list.*.question.*.options' => "required_if:question_list.*.question.*.question_type,single,question_list.*.question.*.question_type,multiple|array|min:1",
                'question_list.*.question.*.question_order' => 'required|integer',
            ];
        } else if ($submoduleTypeId == 17) { // For 360 url submodule validation
            $submoduleValidations = [
                "scorm_zip_name" => "required",
            ];
        }

        return array_merge($rules, $submoduleValidations);
    }

    public function messages() : array
    {
        return [
            "training_course_id.required" => __("Training course Id is required."),
            "training_course_id.integer" => __("Training course Id must be an integer."),
            "module_id.required" => __("Training course module id is required."),
            "module_id.integer" => __("Training course module id must be an integer."),
            "submodule_type_id.required" => __("Training course submodule type is required."),
            "submodule_type_id.integer" => __("Training course submodule type id must be an integer."),
            "submodule_type_id.min" => __("Training course submodule type id may not be smaller than 1."),
            "submodule_type_id.max" => __("Training course submodule type id may not be greater than 16."),
            "submodule_name.required" => __("SubModule name is required."),
            "submodule_name.name_exists" => __("Submodule name already exists"),
            "thumbnail.required" => __("Thumbnail is required."),
            "description.required" => __("Description is required."),
            "submodule_lock.required_if" => __("Submodule lock is required."),
            "submodule_complete.required_if" => __("Submodule complete course is required."),
            "duration.required_if" => __("Duration is required."),
            "duration_type.required_if" => __("Duration type is required."),
            "unlock_datetime.required_if" => __("Unlock datetime is required."),
            "unlock_datetime.date_format" => __("Unlock datetime must be in Y-M-D H:M:S format."),
            "enable_time_spend.required_unless" => __("Enable time Spend is required."),
            "completion_percentage.required_if" => __("Completion Percentage is required."),
            "condition.required_if" => __("Condition is required."),
            "time_spent.required_if" => __("Time spent is required."),
            "time_spent.integer" => __("Time spent must be a numeric value."),
            "time_spent.min" => __("Time spent must be greater than or equal to 5."),
            "status.required" => __("Status is required."),
            
            // For title slide submodule validation
            "title.required" => __("Title is required."),
            "subtitle.required" => __("Subtitle is required."),
            "button_text.required" => __("Button text is required."),
            
            // For 360 url submodule validation
            "url_360.required" => __("360 url is required."),
            "touch_count.required" => __("Touch count is required."),
            "touch_count.integer" => __("Touch count must be a numeric value."),
            "touch_count.between" => __("Touch count must be between 1 and 100."),

            // For SCORM submodule validation
            "scorm_zip_name.required" => __("Zip file is required."),
            
            // For product listing submodule validation messages
            'products.required' => __("Product is required."),
            'products.*.name.required' => __("Product name is required."),
            'products.*.name.max' => __("Product name is too long."),
            // 'products.*.code.required' => __("Product code is required."),
            'products.*.code.max' => __("Product code is too long."),
            'products.*.image.required' => __("Product image is required."),
            'products.*.image.max' => __("Product image name is too long."),
            
            // For video guide submodule validation
            'video_guide.required' => __("Video is required."),
            'video_guide.*.title.required' => __("Title is required."),
            'video_guide.*.title.max' => __("Title is too long."),
            'video_guide.*.resource_type.required' => __("Resource Type is required."),
            'video_guide.*.resource_type.in' => __("Invalid resource type."),
            'video_guide.*.resource_id.required_unless' => __("Resource ID is required."),
            'video_guide.*.resource_id.max' => __("Resource ID is too long."),
            'video_guide.*.resource_thumbnail.required_if' => __("Resource thumbnail is required."),
            'video_guide.*.resource_thumbnail.max' => __("Resource thumbnail is too long."),
            //'video_guide.*.source_title.required_if' => __("Source title is required."),
            'video_guide.*.source_title.max' => __("Source title is too long."),
            'video_guide.*.is_autoplay.in' => __("Invalid autoplay value."),
            'video_guide.*.start_time.integer' => __("Start time must be a numeric value."),
            'video_guide.*.end_time.integer' => __("End time must be a numeric value."),
            'video_guide.*.steps.required_unless' => __("Steps are required."),
            'video_guide.*.steps.*.title.required_unless' => __("Title is required."),
            'video_guide.*.steps.*.title.max' => __("Title is too long."),
            'video_guide.*.steps.*.resource_type.required_unless' => __("Resource Type is required."),
            'video_guide.*.steps.*.resource_type.in' => __("Invalid resource type."),
            'video_guide.*.steps.*.resource_id.required_unless' => __("Resource ID is required."),
            'video_guide.*.steps.*.resource_id.max' => __("Resource ID is too long."),
            //'video_guide.*.steps.*.source_title.required_if' => __("Source title is required."),
            'video_guide.*.steps.*.source_title.max' => __("Source title is too long."),
            'video_guide.*.steps.*.is_autoplay.in' => __("Invalid autoplay value."),
            'video_guide.*.steps.*.start_time.integer' => __("Start time must be a numeric value."),
            'video_guide.*.steps.*.end_time.integer' => __("End time must be a numeric value."),
            
            // For course feedback questions submodule validation
            'questions.required' => __("Question is required."),
            'questions.array' => __("Question must be an array."),
            'questions.*.question.required' => __("Question is required."),
            'questions.*.question_type.required' => __("Question type is required."),
            'questions.*.question_type.in' => __("Invalide question type."),
            'questions.*.is_required.required' => __("Select question is required or not."),
            'questions.*.is_required.in' => __("Invalide required type."),
            'questions.*.options.required_if' => __("Option is required."),
            'questions.*.options.array' => __("Option must be an array."),
            
            // For image hotspot submodule validation
            'hotspot_image.required' => __("Hotspot image is required."),
            'hotspots.required' => __("Hotspot section is required."),
            'hotspots.array' => __("Hotspot must be an array."),
            'hotspots.*.section_type.required' => __("Section type is required."),
            'hotspots.*.section_type.in' => __("Section type must be either image, video or nomedia."),
            'hotspots.*.region_image.required' => __("Region image is required."),
            'hotspots.*.resource_type.required_if' => __("Resource type is requied."),
            'hotspots.*.resource_type.in' => __("Invalid resource type."),
            'hotspots.*.resource_id.required' => __("Resource ID is required."),
            'hotspots.*.resource_id.max' => __("Resource ID is too long."),
            'hotspots.*.source_title.required_if' => __("Source title is required."),
            'hotspots.*.source_title.max' => __("Source title is too long."),
            'hotspots.*.caption.required' => __("Caption is required."),
            'hotspots.*.position.required' => __("Position is required."),
            'hotspots.*.position.x.required_unless' => __("X coordinate of section is required."),
            'hotspots.*.position.x.integer' => __("X coordinate must be a numeric value."),
            'hotspots.*.position.x2.required_unless' => __("X2 coordinate of section is required."),
            'hotspots.*.position.x2.integer' => __("X2 coordinate must be a numeric value."),
            'hotspots.*.position.y.required_unless' => __("Y coordinate of section is required."),
            'hotspots.*.position.y.integer' => __("Y coordinate must be a numeric value."),
            'hotspots.*.position.y2.required_unless' => __("Y2 coordinate of section is required."),
            'hotspots.*.position.y2.integer' => __("Y2 coordinate must be a numeric value."),
            'hotspots.*.position.height.required_unless' => __("Height coordinate of section is required."),
            'hotspots.*.position.height.integer' => __("Height coordinate must be a numeric value."),
            'hotspots.*.position.width.required_unless' => __("Width coordinate of section is required."),
            'hotspots.*.position.width.integer' => __("Width coordinate must be a numeric value."),
            'hotspots.*.is_autoplay.in' => __("Invalid autoplay value."),
            'hotspots.*.start_time.integer' => __("Start time must be a numeric value."),
            'hotspots.*.end_time.integer' => __("End time must be a numeric value."),
            
            // For quiz submodule validation
            'quiz_duration.required' => __("Duration is required."),
            'quiz_duration.numeric' => __("Duration must be a numeric value."),
            //'quiz_duration.min' => __("Duration must be atleast 1 minute."),
            'quiz_duration.max' => __("Duration may not be greater than 240 minutes."),
            'auto_reset_duration_on_fail.required' => __("Auto reset duration is required."),
            'auto_reset_duration_on_fail.numeric' => __("Auto reset duration must be a numeric value."),
            'auto_reset_duration_on_fail.max' => __("Auto reset duration may not be greater than 240 minutes."),
            'categories.required' => __("Categories are required."),
            'categories.array' => __("Categories must be an array"),
            'passing_criteria.required' => __("Passing criteria is required."),
            'passing_criteria.numeric' => __("Passing criteria must be a numeric value."),
            'passing_criteria.min' => __("Passing criteria must be atleast 1%."),
            'passing_criteria.max' => __("Passing criteria may not be greater than 100%."),
            'is_notify.required' => __("Please select either Yes or No."),
            'is_notify.in' => __("Invalid value."),
            'failed_attempts.required_if' => __("Failed attempts is required."),
            'failed_attempts.min' => __("Failed attempts must be atleast 1."),
            'failed_attempts.max' => __("Failed attempts may not be greater than 100."),
            'max_attempts_before_fail.numeric'  => __("Attempts must be a numeric value."),
            'max_attempts_before_fail.between' => __("Attempts must be between 1 and 10."),
            // For photo hotspot submodule validation
            'poc_enable.required' => __("Please select either yes or no."),
            'poc_enable.in' => __("Invalid selection."),
            'hotspot_photo.required' => __("Hotspot photo is required"),
            'photoHotspots.required' => __("Hotspot section is required."),
            'photoHotspots.array' => __("Hotspot must be an array."),
            'photoHotspots.*.region_name.required' => __("Region name is required."),
            'photoHotspots.*.region_image.required' => __("Region image is required."),
            'photoHotspots.*.position.required' => __("Position is required."),
            'photoHotspots.*.position.x.required_unless' => __("X coordinate of section is required."),
            'photoHotspots.*.position.x.integer' => __("X coordinate must be a numeric value."),
            'photoHotspots.*.position.x2.required_unless' => __("X2 coordinate of section is required."),
            'photoHotspots.*.position.x2.integer' => __("X2 coordinate must be a numeric value."),
            'photoHotspots.*.position.y.required_unless' => __("Y coordinate of section is required."),
            'photoHotspots.*.position.y.integer' => __("Y coordinate must be a numeric value."),
            'photoHotspots.*.position.y2.required_unless' => __("Y2 coordinate of section is required."),
            'photoHotspots.*.position.y2.integer' => __("Y2 coordinate must be a numeric value."),
            'photoHotspots.*.position.height.required_unless' => __("Height coordinate of section is required."),
            'photoHotspots.*.position.height.integer' => __("Height coordinate must be a numeric value."),
            'photoHotspots.*.position.width.required_unless' => __("Width coordinate of section is required."),
            'photoHotspots.*.position.width.integer' => __("Width coordinate must be a numeric value."),
            
            
            "resource_type.required" => __("Resource type is required."),
            "resource_id.required" => __("Resource id is required."),

            // For image gallery submodule validation messages
            'galleries.required' => __("Gallery is required."),
            'galleries.*.image.required' => __("Gallery image is required."),
            'galleries.*.caption.required' => __("Gallery caption is required."),

            // For Submodule confirmation section validation messages
            'button1_text.required' => __("Button 1 text is required."),
            'button2_text.required' => __("Button 2 text is required."),
            'confirmation_lock.required' => __("Confirmation lock is required."),

            // For Submodule Happy Unhappy section validation messages
            'happyunhappy.required' => __("Happy unhappy data is required required."),
            'happyunhappy.*.thumbnail.required' => __("Happy unhappy thumbnail is required."),
            'happyunhappy.*.happy_resource_type.required' => __("Happy resource type is required."),
            'happyunhappy.*.happy_resource_id.required' => __("Happy resource id is required."),
            'happyunhappy.*.happy_description.required' => __("Happy description is required."),
            'happyunhappy.*.unhappy_resource_type.required' => __("Unhappy resource type is required."),
            'happyunhappy.*.unhappy_resource_id.required' => __("Unhappy resource id is required."),
            'happyunhappy.*.unhappy_description.required' => __("Unhappy description is required."),

            // For Conformation Box submodule validation messages
            'confirmation_box.required' => __("Confirmation box is required."),
            'confirmation_box.*.image.required' => __("Confirmation box image is required."),
            'confirmation_box.*.title.required' => __("Confirmation box title is required."),

            // For Submodule job section validation messages

            'jobs.required' => __("Job is required."),
            'jobs.*.upload_type.required' => __("Job upload type is required."),
            'job_topic.required' => __("Job topic is required."),
            'job_topic.unique' => __("Job topic already exists."),
            'job_no_required.required' => __("Job no. is required."),

            // PA valdiation message for general questions
            'question_list.required' => 'The questions is required.',
            'question_list.array' => 'The questions must be an array.',
            'question_list.min' => 'At least one question is required.',
            'question_list.*.type.required' => 'Each question must have a type specified as either general or category.',
            'question_list.*.type.in' => 'Invalid question type specified. The type must be either general or category.',
            'question_list.*.question_list_order.required' => 'Question list order is required.',
            'question_list.*.question.required_if' => 'A question is required for the general type.',
            'question_list.*.question_type.required_if' => 'The question type is required for the general type.',
            'question_list.*.question_type.in' => 'Invalid question type specified for the general type.',
            'question_list.*.is_required.required_if' => 'The "is_required" field is required for the general type.',
            'question_list.*.is_required.boolean' => 'The "is_required" field must be a boolean value for the general type.',
            'question_list.*.question_order.required_if' => 'The question order is required for the general type.',
            'question_list.*.question_order.integer' => 'The question order must be an integer for the general type.',
            'question_list.*.options.required_if' => 'Options are required for single or multiple choice questions.',
            'question_list.*.options.array' => 'The options must be provided as an array for single or multiple choice questions.',
            'question_list.*.options.min' => 'At least one option is required for single or multiple choice questions.',

            // PA valdiation message for category questions
            'question_list.*.category_name.required_if' => 'Category name is required for questions of type category.',
            'question_list.*.category_name.string' => 'Category name must be a string.',
            'question_list.*.question.*.question.required_if' => 'A question is required for category type questions.',
            'question_list.*.question.*.question_type.required_if' => 'The question type is required for category type questions.',
            'question_list.*.question.*.question_type.in' => 'Invalid question type specified for category type questions.',
            'question_list.*.question.*.is_required.required' => 'The "is_required" field is required for category type questions.',
            'question_list.*.question.*.is_required.boolean' => 'The "is_required" field must be a boolean value for category type questions.',
            'question_list.*.question.*.options.required_if' => 'Options are required for single or multiple choice questions within the category.',
            'question_list.*.question.*.options.array' => 'The options must be provided as an array for single or multiple choice questions within the category.',
            'question_list.*.question.*.options.min' => 'At least one option is required for single or multiple choice questions within the category.',
            'question_list.*.question.*.question_order.required' => 'The question order is required for category type questions.',
            'question_list.*.question.*.question_order.integer' => 'The question order must be an integer for category type questions.'
        ];
    }
}
