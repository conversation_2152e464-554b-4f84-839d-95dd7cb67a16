<?php

namespace App\Http\Requests\Admin\v1;
use App\Http\Requests\CustomFormRequest;

class TrainingCourseSubmoduleDetailsRequest extends CustomFormRequest
{
    
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {
        return [
            "training_course_id" => "required",
            "module_id" => "required",
            "submodule_type_id" => "required",
            "submodule_name" => "required",
            "thumbnail" => "required",
            "description" => "required",
            "url_360" => "required_if:submodule_type_id,==,2",
            "title" => "required_if:submodule_type_id,==,1",
            "subtitle" => "required_if:submodule_type_id,==,1",
            "button_text" => "required_if:submodule_type_id,==,1",
            "module_lock" => "required",
            //"submodule_complete" => "required_if:module_lock,==,1",
            //"duration" => "required_if:module_lock,==,1",
            //"duration_type" => "required_if:module_lock,==,1",
            //"unlock_datetime" => "required_if:module_lock,==,1",
            "enable_time_spend" => "required",
            //"completion_percentage" => "required_if:enable_time_spend,==,1",
            //"condition" => "required_if:enable_time_spend,==,1",
            //"time_spent" => "required_if:enable_time_spend,==,1",
            //"time_spent_type" => "required_if:enable_time_spend,==,1",
            //"touch_count" => "required",
            "status" => "required",
        ];
    }

    public function messages() : array
    {
        return [
            "training_course_id.required" => __("Training course id is required."),
            "module_id.required" => __("Training course module id is required."),
            "submodule_type_id.required" => __("Training course submodule type is required."),
            "submodule_name.required" => __("SubModule name is required."),
            "thumbnail.required" => __("Thumbnail is required."),
            "description.required" => __("Description is required."),
            "url_360.required_if" => __("360 url is required."),
            "title.required_if" => __("Title is required."),
            "subtitle.required_if" => __("Subtitle is required."),
            "button_text.required_if" => __("Button text is required."),
            "module_lock.required" => __("Module lock is required."),
            "submodule_complete.required" => __("Submodule complete course is required."),
            "duration.required" => __("Duration is required."),
            "duration_type.required" => __("Duration type is required."),
            "unlock_datetime.required" => __("Unlock datetime is required."),
            "enable_time_spend.required" => __("Enable time spend is required."),
            "completion_percentage.required" => __("Completion Percentage is required."),
            "condition.required" => __("Condition is required."),
            "time_spent.required" => __("Time spent is required."),
            "time_spent_type.required" => __("Time spent type is required."),
            //"touch_count.required" => __("Touch Count is required."),
            "status.required" => __("Status is required."),
        ];
    }
}
