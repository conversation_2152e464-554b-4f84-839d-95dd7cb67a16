<?php

namespace App\Http\Controllers\Operator\v1;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Models\UserRelation;
use App\Models\TrainingCourse;
use App\Models\User;
use App\Models\JobProgressMedia;
use App\Models\TrainingCourseSubmoduleJobProgress;
use App\Http\Resources\Operator\v1\NotStartedAnyCourseResource;
use App\Http\Resources\Operator\v1\LowCompletionReportResource;
use App\Http\Resources\Operator\v1\PartialCompletionReportResource;
use App\Http\Requests\Operator\v1\CommonNeverReportRequest;
use App\Http\Requests\Operator\v1\CommonListingRequest;
use App\Http\Requests\Operator\v1\SendReminderRequest;
use DBTableNames;
use App\Repositories\Operator\v1\TrainingCourseSubModuleJobProgressRepository;
use App\Repositories\Operator\v1\TrainingCourseSubModuleJobProgressMediaRepository;
use App\Models\TrainingCourseProgress;
use Illuminate\Database\Eloquent\Builder;
use App\Http\Resources\CustomCollection;
use DB;
class ReportsController extends Controller
{

    public function __construct() {

        $this->jobsMediaModel = new JobProgressMedia();
        $this->jobsProgressModel = new TrainingCourseSubmoduleJobProgress();
        $this->jobsMediaRepository = new TrainingCourseSubModuleJobProgressMediaRepository($this->jobsMediaModel);
        $this->jobsProgressRepository = new TrainingCourseSubModuleJobProgressRepository($this->jobsProgressModel);
    }


    /**
     * @OA\Post(
     *     path="/operator/reports/commonNeverReport",
     *     tags={"Operator - Reports"},
     *     summary="Not Started Any Course / Never Logged In  Report",
     *     description="Not Started Any Course / Never Logged In  Report",
     *     operationId="commonNeverReport",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="isExport",
     *                     description="for export csv",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="exportFields",
     *                     description="for export csv fields",
     *                     type="string"
     *                 ),
     *                  @OA\Property(
     *                     property="type",
     *                     description="(NotStartedAnyCourse / NeverLoggedIn)",
     *                     type="string"
     *                 ),
     *                 example={"type": "NeverLoggedIn", "per_page": 10, "page": 1, "search_key": "", "sort_by": "created_at", "order_by": "asc", "filters": {"last_logged_in_at": "", "user_name": "", "group_name": "", "created_at": ""}, "isExport": 0, "exportFields": {"user_name", "group_name", "created_at", "last_logged_in_at"}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function commonNeverReport(CommonNeverReportRequest $request) {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $data = $request->all();
            if(isset($data['type']) && $data['type'] == 'NotStartedAnyCourse'){
                $selectColumns = [ 
                    DBTableNames::USER_RELATIONS . '.id',
                    DBTableNames::USER_RELATIONS . '.user_id', 
                    DBTableNames::USER_RELATIONS . '.master_user_id', 
                    DBTableNames::USER_RELATIONS . '.user_group_id',
                    DBTableNames::USER_RELATIONS . '.created_at',
                    'users.last_logged_in_at',
                    'groups.name as group_name',
                    'users.name as user_name',
                    'users.email as email',
                    \DB::raw("(select sum(`training_course_progress`.`course_progress`) from `training_course_progress` where `training_course_progress`.`user_id` = `user_relations`.`user_id`) as progress")
                ];
            }else{
                $selectColumns = [ 
                    DBTableNames::USER_RELATIONS . '.id',
                    DBTableNames::USER_RELATIONS . '.user_id', 
                    DBTableNames::USER_RELATIONS . '.master_user_id', 
                    DBTableNames::USER_RELATIONS . '.user_group_id',
                    DBTableNames::USER_RELATIONS . '.created_at',
                    'users.last_logged_in_at',
                    'groups.name as group_name',
                    'users.name as user_name',
                    'users.email as email',
                ];
            }
            
            $query = UserRelation::leftJoin(DBTableNames::USERS, DBTableNames::USER_RELATIONS . '.user_id', '=', 'users.id')
                                    ->leftJoin(DBTableNames::USER_GROUPS, DBTableNames::USER_RELATIONS . '.user_group_id', '=', 'groups.id')
                                    ->select($selectColumns);
            // Show only assigned users if sub-operator has not permission to view all users data
            if (!canViewAllUsersData(auth()->guard('operator')->user()->role_id)) {
                $query = $query->whereIn(DBTableNames::USERS. '.id', getAssignedUsersId());
            }
        
            // Searching
            $search = isset($data['search_key']) ? trim($data['search_key']) : "";
            $searchFields = ['users.last_logged_in_at', 'groups.name', 'users.name', 'users.email'];
            if (!empty($search)) {
                $query = $query->where(function($query) use ($searchFields, $search) {
                            foreach ($searchFields as $key => $field) {
                                $query->orWhere($field, 'LIKE', '%'.$search.'%');
                            }
                        });
            }

            /* Advance Filters */
            if (isset($data['filters']) && count($data['filters']) > 0) {
                if (isset($data['filters']['user_name']) && $data['filters']['user_name'] != "") {
                    $query = $query->where('users.name', 'LIKE', '%' . $data['filters']['user_name'] . '%');
                }
                if (isset($data['filters']['email']) && $data['filters']['email'] != "") {
                    $query = $query->where('users.email', 'LIKE', '%' . $data['filters']['email'] . '%');
                }
                if (isset($data['filters']['group_name']) && $data['filters']['group_name'] != "") {
                    $query = $query->where('groups.name', 'LIKE', '%' . $data['filters']['group_name'] . '%');
                }
                if (isset($data['filters']['created_at']) && $data['filters']['created_at'] != "") {
                    $query = $query->whereDate(DBTableNames::USER_RELATIONS.'.created_at', '=', $data['filters']['created_at']);
                }
                if (isset($data['filters']['last_logged_in_at']) && $data['filters']['last_logged_in_at'] != "") {
                    $query = $query->whereDate('users.last_logged_in_at', $data['filters']['last_logged_in_at']);
                }
            }
            $query->where(DBTableNames::USER_RELATIONS.'.master_user_id', $operatorId);

            // Sorting
            $sort = DBTableNames::USER_RELATIONS . '.created_at'; // Default sort by ID
            $sortingKeys = [
                'user_name' => 'user_name',
                'email' => 'email',
                'group_name' => 'group_name',
                'created_at' => DBTableNames::USER_RELATIONS . '.created_at',
                'last_logged_in_at' => 'last_logged_in_at',           
            ];
            if (isset($data['sort_by']) && $data['sort_by'] != "") {
                if (array_key_exists($data['sort_by'], $sortingKeys)) {
                    $sort = $sortingKeys[$data['sort_by']];
                }
            }
            
            // Ordering (Default DESC)
            $order = isset($data['order_by']) ? $data['order_by'] : 'DESC';
            
            // Apply Sorting and Ordering
            if (in_array($sort, $sortingKeys)) {
                $query = $query->orderBy($sort, $order);
            }
            if(isset($data['type']) && $data['type'] == 'NotStartedAnyCourse'){
                $query->having('progress', '=', 0);
            }else{
                $query->havingRaw('last_logged_in_at IS NULL');
            }
            
            $userData = $query;
            if ($request->isExport) {
                return $this->notStartedAnyCourseCsv($userData->get(), $request->exportFields);
            }
            return NotStartedAnyCourseResource::collection($userData->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function notStartedAnyCourseCsv($userData, $fields = null) {
        try {
            $columns = ['user_name' => 'User Name','email' => 'Email', 'group_name' => 'Group Name', 'created_at' => 'Registered At', 'last_logged_in_at' => 'Last Login'];
            $exportFields = array_flip((empty($fields))? array_keys($columns): $fields);
            $fileName = '../storage/' . time() . '-' . randomString() . '.csv';
            $fp = fopen($fileName, 'w');
            fputcsv($fp, array_intersect_key($columns, $exportFields));

            foreach ($userData as $raw) {
                $output = [
                    "user_name" => $raw->user_name,
                    "email" => $raw->email,
                    "group_name" => $raw->group_name,
                    "created_at" => !is_null($raw->created_at) ? date('Y-m-d H:i:s',strtotime($raw->created_at)) : '',
                    "last_logged_in_at" => !is_null($raw->last_logged_in_at) ? date('Y-m-d H:i:s',strtotime($raw->last_logged_in_at)) : '',
                ];
                
                fputcsv($fp, array_intersect_key($output, $exportFields));
            }
            return response()->download($fileName, time() . '-NotProgressCourse.csv', csvHeaders())->deleteFileAfterSend(true);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/reports/lowCompletionRate",
     *     tags={"Operator - Reports"},
     *     summary="Low Completion Course Report",
     *     description="Low Completion Course Report",
     *     operationId="lowCompletionRate",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="isExport",
     *                     description="for export csv",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="exportFields",
     *                     description="for export csv fields",
     *                     type="string"
     *                 ),
     *                 example={"per_page": 10, "page": 1, "search_key": "", "sort_by": "created_at", "order_by": "asc", "filters": {"last_logged_in_at": "", "user_name": "", "group_name": "", "created_at": ""}, "isExport": 0, "exportFields": {"user_name", "group_name", "created_at", "last_logged_in_at"}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */

    public function lowCompletionRate(CommonListingRequest $request) {
        try {
            $data = $request->all();
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $selectColumns = [ 
                DBTableNames::USER_RELATIONS . '.id',
                DBTableNames::USER_RELATIONS . '.user_id', 
                DBTableNames::USER_RELATIONS . '.master_user_id', 
                DBTableNames::USER_RELATIONS . '.user_group_id',
                DBTableNames::USER_RELATIONS . '.created_at',
                'users.last_logged_in_at',
                'groups.name as group_name',
                'users.name as user_name',
                'users.email as email',
                \DB::raw("(select count(`training_course_progress`.`id`) from `training_course_progress` where `training_course_progress`.`user_id` = `user_relations`.`user_id`) as total_course"),
                \DB::raw("(select count(`training_course_progress`.`id`) from `training_course_progress` where `training_course_progress`.`user_id` = `user_relations`.`user_id` and `training_course_progress`.`course_progress` > 0) as started_course"),
                \DB::raw("(select count(`training_course_progress`.`id`) from `training_course_progress` where `training_course_progress`.`user_id` = `user_relations`.`user_id` and `training_course_progress`.`course_progress` >= 100) as completed_course"),
                \DB::raw("ROUND((select count(`training_course_progress`.`id`) from `training_course_progress` where `training_course_progress`.`user_id` = `user_relations`.`user_id` and `training_course_progress`.`course_progress` >= 100)*100/(select count(`training_course_progress`.`id`) from `training_course_progress` where `training_course_progress`.`user_id` = `user_relations`.`user_id`),0) as average_completion")
            ]; 
            
            $query = UserRelation::whereHas('user', function (Builder $qu) {
                        $qu->whereNull('deleted_at');
                        })->leftJoin(DBTableNames::USERS, DBTableNames::USER_RELATIONS . '.user_id', '=', 'users.id')
                        ->leftJoin(DBTableNames::USER_GROUPS, DBTableNames::USER_RELATIONS . '.user_group_id', '=', 'groups.id')
                        ->select($selectColumns);

            // Show only assigned users if sub-operator has not permission to view all users data
            if (!canViewAllUsersData(auth()->guard('operator')->user()->role_id)) {
                $query = $query->whereIn(DBTableNames::USERS. '.id', getAssignedUsersId());
            }
            // Searching
            $search = isset($data['search_key']) ? trim($data['search_key']) : "";
            
            $query->where(DBTableNames::USER_RELATIONS.'.master_user_id', $operatorId);
            /* Advance Filters */
            if (isset($data['filters']) && count($data['filters']) > 0) {
                if (isset($data['filters']['user_name']) && $data['filters']['user_name'] != "") {
                    $query = $query->where('users.name', 'LIKE', '%' . $data['filters']['user_name'] . '%');
                }
                if (isset($data['filters']['email']) && $data['filters']['email'] != "") {
                    $query = $query->where('users.email', 'LIKE', '%' . $data['filters']['email'] . '%');
                }
                if (isset($data['filters']['group_name']) && $data['filters']['group_name'] != "") {
                    $query = $query->where('groups.name', 'LIKE', '%' . $data['filters']['group_name'] . '%');
                }
                if (isset($data['filters']['created_at']) && $data['filters']['created_at'] != "") {
                    $query = $query->whereDate(DBTableNames::USER_RELATIONS.'.created_at', '=', $data['filters']['created_at']);
                }
                if (isset($data['filters']['last_logged_in_at']) && $data['filters']['last_logged_in_at'] != "") {
                    $query = $query->whereDate('users.last_logged_in_at', $data['filters']['last_logged_in_at']);
                }
            }
            
            // Sorting
            $sort = DBTableNames::USER_RELATIONS . '.id'; // Default sort by ID
            $sortingKeys = [
                'user_name' => 'user_name',
                'email' => 'email',
                'group_name' => 'group_name',
                'created_at' => DBTableNames::USER_RELATIONS . '.created_at',
                'last_logged_in_at' => 'last_logged_in_at',
                'total_course' => 'total_course',
                'started_course' => 'started_course',
                'completed_course' => 'completed_course',
                'average_completion' => 'average_completion',       
            ];
            if (isset($data['sort_by']) && $data['sort_by'] != "") {
                if (array_key_exists($data['sort_by'], $sortingKeys)) {
                    $sort = $sortingKeys[$data['sort_by']];
                }
            }
            
            // Ordering (Default DESC)
            $order = isset($data['order_by']) ? $data['order_by'] : 'DESC';
            
            // Apply Sorting and Ordering
            if (in_array($sort, $sortingKeys)) {
                $query = $query->orderBy($sort, $order);
            }
            
            
            if (empty($search)) {
                $query->having('average_completion', "<=", 30);
            }else{
                $query->having('average_completion', "<=", 30);
                $query->havingRaw("(total_course like '%".$search."%' or started_course like '%".$search."%' or completed_course like '%".$search."%' or average_completion like '%".$search."%' or users.last_logged_in_at like '%".$search."%' or users.email like '%".$search."%' or  users.name like '%".$search."%' or groups.name like '%".$search."%')");
            }
            
            if (isset($data['filters']['total_course']) && $data['filters']['total_course'] != "") {
                $query = $query->having('total_course',"=",$data['filters']['total_course']);
            }
            if (isset($data['filters']['started_course']) && $data['filters']['started_course'] != "") {
                $query = $query->having('started_course',"=",$data['filters']['started_course']);
            }
            if (isset($data['filters']['completed_course']) && $data['filters']['completed_course'] != "") {
                $query = $query->having('completed_course',"=",$data['filters']['completed_course']);
            }
            if (isset($data['filters']['average_completion']) && $data['filters']['average_completion'] != "") {
                $query = $query->having('average_completion',"=",$data['filters']['average_completion']);
            }
            $userData = $query;
            if ($request->isExport) {
                return $this->lowReportCsv($userData->get(), $request->exportFields);
            }
            return LowCompletionReportResource::collection($userData->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function lowReportCsv($userData, $fields = null) {
        try {
            $columns = ['user_name' => 'User Name', 'email'=> 'Email','group_name' => 'Group Name', 'created_at' => 'Registered At', 'total_course' => 'Total Course', 'started_course' => 'Started Course', 'completed_course' => 'Completed Course', 'average_completion' => 'Average Completion Course', 'last_logged_in_at' => 'Last Login'];
            $exportFields = array_flip((empty($fields))? array_keys($columns): $fields);
            $fileName = '../storage/' . time() . '-' . randomString() . '.csv';
            $fp = fopen($fileName, 'w');
            fputcsv($fp, array_intersect_key($columns, $exportFields));

            foreach ($userData as $raw) {
                $output = [
                    "user_name" => $raw->user_name,
                    "email" => $raw->email,
                    "group_name" => $raw->group_name,
                    "created_at" => !is_null($raw->created_at) ? date('Y-m-d H:i:s',strtotime($raw->created_at)) : '',
                    "total_course" => $raw->total_course,
                    "started_course" => $raw->started_course,
                    "completed_course" => $raw->completed_course,
                    "average_completion" => $raw->average_completion,  
                    "last_logged_in_at" => !is_null($raw->last_logged_in_at) ? $raw->last_logged_in_at : '',
                ];
                
                fputcsv($fp, array_intersect_key($output, $exportFields));
            }
            return response()->download($fileName, time() . '-LowCompletionRateReport.csv', csvHeaders())->deleteFileAfterSend(true);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/reports/partialCompletion",
     *     tags={"Operator - Reports"},
     *     summary="Partial Completion Course Report",
     *     description="Partial Completion Course Report",
     *     operationId="partialCompletion",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="isExport",
     *                     description="for export csv",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="exportFields",
     *                     description="for export csv fields",
     *                     type="string"
     *                 ),
     *                 example={"per_page": 10, "page": 1, "search_key": "", "sort_by": "last_visited_at", "order_by": "asc", "filters": {"last_logged_in_at": "", "user_name": "", "group_name": "", "last_visited_at": "", "course_progress" : "", "course_name" : ""}, "isExport": 0, "exportFields": {"user_name", "group_name", "created_at", "last_visited_at", "course_progress"}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function partialCompletion(CommonListingRequest $request) {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $data = $request->all();
            $selectColumns = [ 
                DBTableNames::TRAINING_COURSE_PROGRESS . '.id',
                DBTableNames::TRAINING_COURSE_PROGRESS . '.user_id', 
                DBTableNames::TRAINING_COURSE_PROGRESS . '.master_user_id', 
                DBTableNames::TRAINING_COURSE_PROGRESS . '.training_course_id',
                DBTableNames::TRAINING_COURSE_PROGRESS . '.last_visited_at',
                DBTableNames::TRAINING_COURSE_PROGRESS . '.course_progress',
                'training_course.title as course_name',
                'user_relations.user_group_id',
                'groups.name as group_name',
                'users.name as user_name',
                'users.last_logged_in_at',
                'users.email as email',
                \DB::raw("now() - INTERVAL ".env('REPORT_TIME_PERIOD_DAYS')." day as checkDate")
            ];        
            $query = TrainingCourseProgress::whereHas('user', function (Builder $query) {
                $query->whereNull('deleted_at');
            })->leftJoin(DBTableNames::USERS, DBTableNames::TRAINING_COURSE_PROGRESS . '.user_id', '=', 'users.id')
                                            ->leftJoin(DBTableNames::USER_RELATIONS, function($join) use ($operatorId){
                                                $join->on(DBTableNames::TRAINING_COURSE_PROGRESS . '.user_id', '=', 'user_relations.user_id');
                                                $join->on(DBTableNames::TRAINING_COURSE_PROGRESS . '.master_user_id', '=', \DB::raw($operatorId));
                                            })
                                            ->leftJoin(DBTableNames::USER_GROUPS, DBTableNames::USER_RELATIONS . '.user_group_id', '=', 'groups.id')
                                            ->leftJoin(DBTableNames::TRAINING_COURSE, DBTableNames::TRAINING_COURSE_PROGRESS . '.training_course_id', '=', 'training_course.id')
                                            ->select($selectColumns);
            // Show only assigned users if sub-operator has not permission to view all users data
            if (!canViewAllUsersData(auth()->guard('operator')->user()->role_id)) {
                $query = $query->whereIn(DBTableNames::USERS. '.id', getAssignedUsersId());
            }
            // Searching
            $search = isset($data['search_key']) ? trim($data['search_key']) : "";
            $searchFields = [DBTableNames::TRAINING_COURSE_PROGRESS . '.last_visited_at',
            'training_course.title',
            'groups.name', 
            'users.name',
            'users.email',
            'users.last_logged_in_at'];
            if (!empty($search)) {
                $query = $query->where(function($query) use ($searchFields, $search) {
                            foreach ($searchFields as $key => $field) {
                                $query->orWhere($field, 'LIKE', '%'.$search.'%');
                            }
                        });
            }
            $query->where(DBTableNames::TRAINING_COURSE_PROGRESS.'.master_user_id', $operatorId);
            $query->where(DBTableNames::TRAINING_COURSE_PROGRESS.'.course_progress', '>', 0);
            $query->where(DBTableNames::TRAINING_COURSE_PROGRESS.'.course_progress', '<', 100);
            /* Advance Filters */
            if (isset($data['filters']) && count($data['filters']) > 0) {
                if (isset($data['filters']['user_name']) && $data['filters']['user_name'] != "") {
                    $query = $query->where('users.name', 'LIKE', '%' . $data['filters']['user_name'] . '%');
                }
                if (isset($data['filters']['email']) && $data['filters']['email'] != "") {
                    $query = $query->where('users.email', 'LIKE', '%' . $data['filters']['email'] . '%');
                }
                if (isset($data['filters']['group_name']) && $data['filters']['group_name'] != "") {
                    $query = $query->where('groups.name', 'LIKE', '%' . $data['filters']['group_name'] . '%');
                }
                if (isset($data['filters']['course_name']) && $data['filters']['course_name'] != "") {
                    $query = $query->where('training_course.title', 'LIKE', '%' . $data['filters']['course_name'] . '%');
                }
                if (isset($data['filters']['last_visited_at']) && $data['filters']['last_visited_at'] != "") {
                    $query = $query->whereDate('last_visited_at', $data['filters']['last_visited_at']);
                }
                if (isset($data['filters']['course_progress']) && $data['filters']['course_progress'] != "") {
                    $query = $query->where('course_progress', '<=', $data['filters']['course_progress']);
                }
                if (isset($data['filters']['last_logged_in_at']) && $data['filters']['last_logged_in_at'] != "") {
                    $query = $query->whereDate('users.last_logged_in_at', $data['filters']['last_logged_in_at']);
                }
            }
            
            // Sorting
            $sort = 'last_visited_at'; // Default sort by ID
            $sortingKeys = [
                'user_name' => 'user_name',
                'email' => 'email',
                'group_name' => 'group_name',
                'last_visited_at' => 'last_visited_at',
                'course_name' => 'course_name',  
                'course_progress' => 'course_progress'
            ];
            if (isset($data['sort_by']) && $data['sort_by'] != "") {
                if (array_key_exists($data['sort_by'], $sortingKeys)) {
                    $sort = $sortingKeys[$data['sort_by']];
                }
            }
            
            // Ordering (Default DESC)
            $order = isset($data['order_by']) ? $data['order_by'] : 'DESC';
            
            // Apply Sorting and Ordering
            if (in_array($sort, $sortingKeys)) {
                $query = $query->orderBy($sort, $order);
            }
            $query->groupBy(DBTableNames::TRAINING_COURSE_PROGRESS.'.id');       
            $query->havingRaw('checkDate > last_visited_at');
            $userData = $query;
            
            if ($request->isExport) {
                return $this->partialReportCsv($userData->get(), $request->exportFields);
            }
            return PartialCompletionReportResource::collection($userData->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function partialReportCsv($userData, $fields = null) {
        try {
            $columns = ['user_name' => 'User Name','email' => 'Email', 'group_name' => 'Group Name', 'course_name' => 'Training Course', 'course_progress' => 'Completion', 'last_visited_at' => 'Last Login'];
            $exportFields = array_flip((empty($fields))? array_keys($columns): $fields);
            $fileName = '../storage/' . time() . '-' . randomString() . '.csv';
            $fp = fopen($fileName, 'w');
            fputcsv($fp, array_intersect_key($columns, $exportFields));

            foreach ($userData as $raw) {
                $output = [
                    "user_name" => $raw->user_name,
                    "email" => $raw->email,
                    "group_name" => $raw->group_name,
                    "course_name" => $raw->course_name,
                    "course_progress" => $raw->course_progress,
                    "last_visited_at" => !is_null($raw->last_logged_in_at) ? $raw->last_logged_in_at : '',
                ];
                
                fputcsv($fp, array_intersect_key($output, $exportFields));
            }
            
            return response()->download($fileName, time() . '-PartialCourseReport.csv', csvHeaders())->deleteFileAfterSend(true);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/reports/sendReminderNotification",
     *     tags={"Operator - Reports"},
     *     summary="Send Push and Email Reminder Notification",
     *     description="Send Push and Email Reminder Notification",
     *     operationId="sendReminderNotification",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="user_id",
     *                     description="User Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="type",
     *                     description="NeverLoggedIn / PartialCompletion",
     *                     type="string"
     *                 ),
     *                 example={"user_id": 1, "training_course_id": 1, "type": "NeverLoggedIn/PartialCompletion"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */

    public function sendReminderNotification(SendReminderRequest $request) {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $courseData = TrainingCourse::find($request->training_course_id);
            $userData = User::find($request->user_id);
            if($request->type == 'NeverLoggedIn'){
                $message = "Please login and start your course progress";
                $payload['type'] = "NeverLoggedIn";
                $payload['type_id'] = $userData->id;
            }else{
                $message = "Visit & Complete Your (".$courseData->title.") Course";
                $payload['type'] = "CourseComplete";
                $payload['type_id'] = $courseData->id;
                $payload['image_url'] = $courseData->image_url;
            }
            $payload['title'] = $message;
            $payload['description'] = $message;
            $payload['master_user_id'] = $operatorId;
            $data = [
                'alert' => $message,
                "mutable-content" => 1,
                'data' => $payload,
                'sound' => 'default'
            ];
            $reportReminderNotification = (new \App\Jobs\ReportReminderNotification($userData, $request->type, $courseData, $data))->delay(env('QUEUE_JOB_DELAY_TIME'));
            dispatch($reportReminderNotification);
            return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.notificationSent')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }     
    }

      /**
     * @OA\Post(
     *     path="/operator/reports/costSavedForJobsReport",
     *     tags={"Operator - Reports"},
     *     summary="Get Cost Saved For Jobs Report",
     *     description="Get Cost Saved For Jobs Report",
     *     operationId="costSavedForJobsReport",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="isExport",
     *                     description="for export csv",
     *                     type="integer"
     *                 ),
     *                 example={"per_page": 10, "page": 1, "search_key": "", "sort_by": "course_name", "order_by": "asc", "filters": {"course_name": "", "user_name":"","group_name": "", "created_at": "", "status": ""}, "isExport" : 0}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="User Group list genetated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */ 
    public function costSavedForJobsReport(CommonListingRequest $request)
    {   
        try {
            $data =  $request->all();
            $perPage = isset($request->per_page) ? $request->per_page : 10;
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            if($request->filters['course_id']){
                $CourseId = $request->filters['course_id'];
               }else{
                   $CourseId = 0;
               }
            $selectColumns = [
                DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS. '.id',
                DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS. '.comments',
                DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS. '.operator_comments',
                DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS. '.training_course_id',
                DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS. '.status',
                DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS. '.created_at',
                DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS. '.job_no',
                'training_course.title as course_name',
                'training_course.primary_image',
                'groups.name as group_name',
                'users.name as user_name',
                'training_course_submodule_details.submodule_name',
                'training_course_submodule_details.job_topic',
                'training_course_submodule_details.cost',
                'users.email as user_email',
                'user_relations.unique_id',
                'master_users.email',
                // DB::raw('(training_course_submodule_details.cost * count(job_progress_media.job_progress_id)) as total_cost'),
            ];

            $query = TrainingCourseSubmoduleJobProgress::
                join(DBTableNames::USERS, DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS . '.user_id', '=', 'users.id')
                ->leftJoin(DBTableNames::JOB_PROGRESS_MEDIA, DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS . '.id', '=', 'job_progress_media.job_progress_id')
                ->join(DBTableNames::USER_RELATIONS, function ($join)use($operatorId) {
                    $join->on(DBTableNames::USER_RELATIONS . '.user_id', '=', 'users.id');
                    $join->where(DBTableNames::USER_RELATIONS . '.master_user_id', '=', $operatorId);
                })
                ->leftJoin(DBTableNames::USER_GROUPS, DBTableNames::USER_RELATIONS . '.user_group_id', '=', 'groups.id')
                ->join(DBTableNames::TRAINING_COURSE, function ($join)use($CourseId) {
                    $join->on(DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS . '.training_course_id', '=', 'training_course.id');
                     $join->where(DBTableNames::TRAINING_COURSE . '.id', '=', $CourseId);
                })
                ->join(DBTableNames::TRAINING_COURSE_SUBMODULE_DETAILS,DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS.'.submodule_id','=','training_course_submodule_details.id')
                ->join(DBTableNames::MASTER_USERS, DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS . '.master_user_id', '=', 'master_users.id')
                ->where('training_course_submodule_job_progress.status','=', 'Approved')
                ->groupBy(DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS.'.id')
                ->select($selectColumns);  

            // Searching
            $search = isset($data['search_key']) ? $data['search_key'] : "";
            $searchFields = ['training_course_submodule_job_progress.status',
            'training_course.title',
            'groups.name', 
            'users.name',
            'training_course_submodule_job_progress.created_at',
            'training_course_submodule_details.submodule_name',
            'training_course_submodule_details.job_topic',
            'training_course_submodule_details.cost',
            ];

            if (!empty($search)) {
                $query = $query->where(function($query) use ($searchFields, $search) {
                            foreach ($searchFields as $key => $field) {
                                $query->orWhere($field, 'LIKE', '%'.$search.'%');
                            }
                        });
            }
            // Show only assigned users if sub-operator has not permission to view all users data
            if (!canViewAllUsersData(auth()->guard('operator')->user()->role_id)) {
                $query = $query->whereIn(DBTableNames::USERS. '.id', getAssignedUsersId());
            }
            $query->where(DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS.'.master_user_id', $operatorId);
            $query->where(DBTableNames::USERS.'.deleted_at',null);
            $query->where(DBTableNames::TRAINING_COURSE_SUBMODULE_DETAILS.'.deleted_at',null);
            $query->where(DBTableNames::TRAINING_COURSE.'.deleted_at',null);
            /* Advance Filters */
            if (isset($data['filters']) && count($data['filters']) > 0) {
                if (isset($data['filters']['unique_id']) && $data['filters']['unique_id'] != "") {
                    $query = $query->where('user_relations.unique_id', 'LIKE', '%' . $data['filters']['unique_id'] . '%');
                }
                if (isset($data['filters']['job_no']) && $data['filters']['job_no'] != "") {
                    $query = $query->where('training_course_submodule_job_progress.job_no', 'LIKE', '%' . $data['filters']['job_no'] . '%');
                }
                if (isset($data['filters']['user_name']) && $data['filters']['user_name'] != "") {
                    $query = $query->where('users.name', 'LIKE', '%' . $data['filters']['user_name'] . '%');
                }
                if (isset($data['filters']['group_name']) && $data['filters']['group_name'] != "") {
                    $query = $query->where('groups.name', 'LIKE', '%' . $data['filters']['group_name'] . '%');
                }
                if (isset($data['filters']['course_id']) && $data['filters']['course_id'] != "") {
                    $query = $query->where('training_course.id', 'LIKE', '%' . $data['filters']['course_id'] . '%');
                }
                if (isset($data['filters']['created_at']) && $data['filters']['created_at'] != "") {
                    $query = $query->whereDate('training_course_submodule_job_progress.created_at', $data['filters']['created_at']);
                }
                if (isset($data['filters']['status']) && $data['filters']['status'] != "") {
                    $query = $query->where('training_course_submodule_job_progress.status', 'LIKE', '%' . $data['filters']['status'] . '%');
                }
                if (isset($data['filters']['submodule_name']) && $data['filters']['submodule_name'] != "") {
                    $query = $query->where('training_course_submodule_details.submodule_name', 'LIKE', '%' . $data['filters']['submodule_name'] . '%');
                }
                if (isset($data['filters']['job_topic']) && $data['filters']['job_topic'] != "") {
                    $query = $query->where('training_course_submodule_details.job_topic', 'LIKE', '%' . $data['filters']['job_topic'] . '%');
                }
                if (isset($data['filters']['cost']) && $data['filters']['cost'] != "") {
                    $query = $query->having('cost', 'LIKE', '%' . $data['filters']['cost'] . '%');
                }
            }
            
            // Sorting
            $sort = DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS . '.id'; // Default sort by ID
            $sortingKeys = [
                'user_name' => 'user_name',
                'group_name' => 'group_name',
                'created_at' => 'created_at',
                'course_name' => 'course_name',  
                'course_progress' => 'course_progress',
                'status' => 'status',
                'submodule_name' => 'submodule_name',  
                'job_topic' => 'job_topic',
                'job_no' => 'job_no',
                'unique_id' => 'unique_id',
                'cost' => 'cost'
            ];
            if (isset($data['sort_by']) && $data['sort_by'] != "") {
                if (array_key_exists($data['sort_by'], $sortingKeys)) {
                    $sort = $sortingKeys[$data['sort_by']];
                }
            }
            
            // Ordering (Default DESC)
            $order = isset($data['order_by']) ? $data['order_by'] : 'DESC';
            
            // Apply Sorting and Ordering
            if (in_array($sort, $sortingKeys)) {
                $query = $query->orderBy($sort, $order);
            }else{
                $query = $query->orderBy(DBTableNames::TRAINING_COURSE_SUBMODULE_JOB_PROGRESS. '.created_at', 'DESC');
            }
            //  dd($query->toSql());
            if ($request->isExport) {
                return $this->jobsMediaRepository->exportCsv(new CustomCollection($query->get(), 'App\Http\Resources\Operator\v1\CostReportingResource'), $request->exportFields);
            }else{
                $jobs = $query->paginate($perPage);

            }
            $costSavedForSubmodules = [];
            $allSubmoduleCostData = $this->jobsProgressRepository->getCostSavedAllUsers($CourseId);

            foreach($allSubmoduleCostData as $key=>$jobList){ 
                     $costSavedForSubmodules[$key]['submodule_name'] = $jobList['submodule_name'];
                     $costSavedForSubmodules[$key]['cost'] = $jobList['cost'] * $jobList['media_count'];     
            }
                

            $costSavedByGroups = [];    
            $allGroupCostData = $this->jobsProgressRepository->getCostSavedByGroups($CourseId);
            $totalCostsSaved = 0;
            foreach($allGroupCostData as $costKey=>$costData){ 
                $totalCostsSaved+= $costData['cost'];
            }
             
            $data = new CustomCollection($jobs, 'App\Http\Resources\Operator\v1\CostReportingResource');
            return response()->json(setResponse([
                                            'job_list' => $data,
                                            'cost_saved_by_users_for_submodules' => $costSavedForSubmodules,
                                            'cost_saved_by_unique_id_groups' => $allGroupCostData,
                                            'total_costs_saved' => $totalCostsSaved
                                            ]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
