<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\CustomDeeplink;
use App\Services\DeeplinkService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Response;

class DeeplinkController extends Controller
{
    private DeeplinkService $deeplinkService;

    public function __construct(DeeplinkService $deeplinkService)
    {
        $this->deeplinkService = $deeplinkService;
    }

    /**
     * Resolve and redirect deeplink.
     */
    public function resolve(Request $request, string $shortCode)
    {
        // Validate short code format
        if (!$this->deeplinkService->isValidShortCode($shortCode)) {
            return redirect()->to($this->getDefaultFallback());
        }

        $deeplink = $this->deeplinkService->resolveDeeplink($shortCode);

        if (!$deeplink || $deeplink->isExpired()) {
            Log::warning('Deeplink not found or expired', [
                'short_code' => $shortCode,
                'user_agent' => $request->userAgent(),
                'ip' => $request->ip()
            ]);
            
            return redirect()->to($this->getDefaultFallback());
        }

        // Track click with metadata
        $this->deeplinkService->trackClick($shortCode, [
            'user_agent' => $request->userAgent(),
            'ip_address' => $request->ip(),
            'referer' => $request->header('referer'),
            'timestamp' => now()->toISOString(),
        ]);

        $platform = $this->detectPlatform($request);

        // For iOS, return a special redirect page instead of direct redirect
        if ($platform === 'ios' && $deeplink->ios_package_name) {    
            $whitelabel = \DB::table('white_label_settings')->where('operator_id', $deeplink->operator_id)->first();
            $WhiteLabelDetail=getWhitelabelPDFLogoPath($whitelabel);
            $backgroundImage = $WhiteLabelDetail['background_image'];
            $appName = (isset($whitelabel->app_name) && !is_null($whitelabel->app_name) && trim($whitelabel->app_name) !== '') ? $whitelabel->app_name : 'SkillsBase';
            $buttonColor = $WhiteLabelDetail['button_color'];
            $shortLogo = $WhiteLabelDetail['short_logo_url'];
            $deeplinkUrl = $this->buildIosUrl($deeplink, $request);
            if($shortLogo == null){
                $shortLogo = url('images/default.png');
            }else{
                $shortLogo = $shortLogo;
            }
            if($backgroundImage == null){
                $backgroundImage = url('images/background.jpg');
            }else{
                $backgroundImage = $backgroundImage;
            }
            // App Store fallback URL
            if(strtolower($appName)=='dexgreen'){
                $appStoreUrl = 'https://apps.apple.com/us/app/dexgreen/id' . $deeplink->ios_app_store_id;
                $fallbackUrl = 'https://apps.apple.com/search?term=DexGreen';
            }
            elseif(strtolower($appName)=='aflglobal' || strtolower($appName)=='afl global'){
                $appStoreUrl = 'https://apps.apple.com/us/app/afl-global/id' . $deeplink->ios_app_store_id;
                $fallbackUrl = 'https://apps.apple.com/search?term=AFLGlobal';
            }
            else{
                $appStoreUrl = 'https://apps.apple.com/us/app/skillsbase/id' . $deeplink->ios_app_store_id;
                $fallbackUrl = 'https://apps.apple.com/search?term=SkillsBase';
            }
            return view('ios_deeplink', compact('deeplinkUrl', 'appStoreUrl', 'deeplink', 'fallbackUrl', 'appName','whitelabel','buttonColor','backgroundImage','shortLogo'));
        }

        $redirectUrl = $this->buildRedirectUrl($deeplink, $platform, $request);

        Log::info('Deeplink resolved', [
            'short_code' => $shortCode,
            'platform' => $platform,
            'redirect_url' => $redirectUrl,
            'operator_id' => $deeplink->operator_id
        ]);

        return redirect()->to($redirectUrl);
    }
    /**
     * Extract deeplink from request (admin endpoint).
     */
    public function extractDeeplink(Request $request)
    {
        $data = $request->only(['link']);
        $rules = ['link' => 'required|url'];
        $messages = [
            'link.required' => 'Link is Required.',
            'link.url' => 'Link must be a valid URL (E.g.: https://example.com).'
        ];
        $validator = Validator::make($data, $rules, $messages);
        if ($validator->fails()) {
            $err = implode('<br/>', $validator->errors()->all());
            return response()->json(setErrorResponse($err, Response::HTTP_BAD_REQUEST))->setStatusCode(Response::HTTP_BAD_REQUEST);
        }

        try {
            $lastValue = substr($request->link, strrpos($request->link, '/') + 1);
			$deeplink=CustomDeeplink::where('short_code', $lastValue)->first();
            if (!$deeplink) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid deeplink or unable to extract deeplink'
                ], 400);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'deeplink' => $deeplink->target_url ?? null
                ]
            ], 200);

        } catch (\Exception $e) {
            Log::error('Failed to extract deeplink', [
                'link' => $request->link,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing the deeplink'
            ], 500);
        }
    }   

    /**
     * Get deeplink statistics (admin endpoint).
     */
    public function getStats(Request $request, string $shortCode): JsonResponse
    {
        $deeplink = CustomDeeplink::where('short_code', $shortCode)->first();

        if (!$deeplink) {
            return response()->json(['error' => 'Deeplink not found'], 404);
        }

        return response()->json([
            'short_code' => $deeplink->short_code,
            'click_count' => $deeplink->click_count,
            'last_clicked_at' => $deeplink->last_clicked_at,
            'created_at' => $deeplink->created_at,
            'is_active' => $deeplink->is_active,
            'expires_at' => $deeplink->expires_at,
            'deeplink_type' => $deeplink->deeplink_type,
            'operator_id' => $deeplink->operator_id,
        ]);
    }

    /**
     * Create new deeplink (admin endpoint).
     */
    public function create(Request $request): JsonResponse
    {
        $request->validate([
            'target_url' => 'required|url',
            'operator_id' => 'nullable|integer|exists:master_users,id',
            'type' => 'required|string|max:50',
            'entity_id' => 'nullable|integer',
            'entity_type' => 'nullable|string|max:50',
            'fallback_url' => 'nullable|url',
            'expires_at' => 'nullable|date|after:now',
        ]);

        try {
            $deeplinkUrl = $this->deeplinkService->generateDeeplink([
                'target_url' => $request->target_url,
                'operator_id' => $request->operator_id,
                'type' => $request->type,
                'entity_id' => $request->entity_id,
                'entity_type' => $request->entity_type,
                'fallback_url' => $request->fallback_url,
                'expires_at' => $request->expires_at,
                'metadata' => [
                    'created_by' => 'admin_api',
                    'created_at' => now()->toISOString()
                ]
            ]);

            return response()->json([
                'success' => true,
                'deeplink_url' => $deeplinkUrl,
                'message' => 'Deeplink created successfully'
            ], 201);

        } catch (\Exception $e) {
            Log::error('Failed to create deeplink', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create deeplink'
            ], 500);
        }
    }

    /**
     * Detect platform from user agent.
     */
    private function detectPlatform(Request $request): string
    {
        $userAgent = strtolower($request->userAgent());
        
        if (strpos($userAgent, 'iphone') !== false || strpos($userAgent, 'ipad') !== false) {
            return 'ios';
        } elseif (strpos($userAgent, 'android') !== false) {
            return 'android';
        }
        
        return 'web';
    }

    /**
     * Build redirect URL based on platform.
     */
    private function buildRedirectUrl(CustomDeeplink $deeplink, string $platform, Request $request): string
    {
        switch ($platform) {
            case 'ios':
                if ($deeplink->ios_package_name) {
                    return $this->buildIosUrl($deeplink, $request);
                }
                break;
                
            case 'android':
                if ($deeplink->android_package_name) {
                    // return $this->buildAndroidUrl($deeplink, $request);
                    return $this->buildIosUrl($deeplink, $request);
                }
                break;
        }

        // Fallback to web URL
        return $deeplink->fallback_url ?: $deeplink->target_url;
    }

    /**
     * Build iOS deep link URL.
     */
    private function buildIosUrl(CustomDeeplink $deeplink, Request $request): string
    {
        // $scheme = str_replace('.', '', $deeplink->ios_package_name);
        $scheme = str_replace('.', '', $deeplink->universal_link);
        $encodedUrl = urlencode($deeplink->target_url);
        
        if(!empty(parse_url($deeplink->target_url, PHP_URL_QUERY))) {
            return $scheme . '://'.$encodedUrl;
        }
        // Add query parameters for tracking
        $params = [
            'url' => $deeplink->target_url,
            'type' => $deeplink->deeplink_type,
            'id' => $deeplink->entity_id,
        ];

        if ($deeplink->entity_id) {
            $params['entity_id'] = $deeplink->entity_id;
        }

        $queryString = http_build_query($params);        

        return $scheme . '://'.$encodedUrl.'?' . $queryString;
    }

    /**
     * Build Android deep link URL.
     */
    private function buildAndroidUrl(CustomDeeplink $deeplink, Request $request): string
    {
        $encodedUrl = urlencode($deeplink->target_url);
        
        $params = [
            'url' => $deeplink->target_url,
            'type' => $deeplink->deeplink_type,
        ];

        if ($deeplink->entity_id) {
            $params['entity_id'] = $deeplink->entity_id;
        }

        $queryString = http_build_query($params);
        
        return 'intent://deeplink?' . $queryString . 
               '#Intent;package=' . $deeplink->android_package_name . 
               ';scheme=https;end';
    }

    /**
     * Get default fallback URL.
     */
    private function getDefaultFallback(): string
    {
        return config('deeplinks.default_fallback', 'https://skillsbase.io');
    }

    /**
     * Health check endpoint.
     */
    public function health(): JsonResponse
    {
        return response()->json([
            'status' => 'ok',
            'service' => 'custom_deeplinks',
            'timestamp' => now()->toISOString()
        ]);
    }
}
