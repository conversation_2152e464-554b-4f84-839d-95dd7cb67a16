<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;

class whiteLabelSettingRequest extends CustomFormRequest {

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array {
	$request = $this;
	$rules = [
	    'operator_id' => 'required|int',
	    'is_white_label_feature_on' => 'required|boolean'
	];
	if (isset($request->is_white_label_feature_on) && $request->is_white_label_feature_on) {
	    $rules = array_merge($rules, [
		'domain_uri_prefix' => 'required',
		'ios_app_store_id' => 'required',
		'ios_package_name' => 'required',
		'android_package_name' => 'required',
		// 'firebase_key' => 'required',
		// 'firebase_json' => 'required_if:firebase_json,null|file|mimes:json',
	    ]);
	}
	return $rules;
    }

    public function messages(): array {
	return [
	    'operator_id.required' => __('Operator Id is required.'),
	    'is_white_label_feature_on.required' => __('Feature setting is required.'),
	    'domain_uri_prefix.required' => __('Domain uri prefix is required.'),
	    'ios_app_store_id.required' => __('Ios app store id is required.'),
	    'ios_package_name.required' => __('Ios package name is required.'),
	    'android_package_name.required' => __('Android package name is required.'),
        // 'firebase_key.required' => __('Firebase Key is required.'),
	];
    }

}
