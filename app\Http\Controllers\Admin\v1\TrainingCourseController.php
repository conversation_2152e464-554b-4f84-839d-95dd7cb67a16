<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Response;
use App\Http\Requests\Admin\v1\CommonListingRequest;
use App\Http\Requests\Admin\v1\CommonBulkStatusChangeRequest;
use App\Http\Requests\Admin\v1\ChangeCourseStatusRequest;
use App\Http\Requests\Admin\v1\DeleteCourseRequest;
use App\Http\Requests\Admin\v1\TrainingCourseOperatorRequest;
use App\Models\TrainingCourse;
use App\Models\RequestTrainingCourse;
use App\Repositories\Admin\v1\TrainingCourseRepository;
use App\Http\Resources\Admin\v1\TrainingCourseResource;
use App\Http\Resources\Admin\v1\TrainingCourseRequestResource;
use App\Http\Resources\Admin\v1\TrainingCourseOperatorListingResource;
use App\Models\MasterUser;
use App\Models\UserRelation;
use App\Http\Requests\Admin\v1\TrainingCourseDuplicateRequest;
use App\Services\DeeplinkService;

class TrainingCourseController extends Controller {

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    private $model;
    private $repository;

    public function __construct() {
        $this->deeplinkService =new DeeplinkService();
        $this->model = new TrainingCourse();
        $this->repository = new TrainingCourseRepository($this->model);
    }

    /**
     * List Training Course
     *
     * @return \Illuminate\Http\Response
     */
    public function getListing(CommonListingRequest $request) {
        try {
            $trainingCourses = $this->repository->getTrainingCourseListing($request->all());
            if ($request->isExport) {
                return $this->repository->exportCsv($trainingCourses->get(), $request->exportFields);
            }
            return TrainingCourseResource::collection($trainingCourses->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * List Training Course Request
     *
     * @return \Illuminate\Http\Response
     */
    public function getCourseRequests(CommonListingRequest $request) {
        try{
            $trainingCourses = $this->repository->getTrainingCourseRequestListing($request->all());
            if ($request->isExport) {
                return $this->repository->exportRequestCsv($trainingCourses->get(), $request->exportFields);
            }
            return TrainingCourseRequestResource::collection($trainingCourses->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Change Training Course(s) status
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function changeStatus(CommonBulkStatusChangeRequest $request) {
        try {
            $this->model->whereIn('id', $request->ids)->update(['status' => $request->status]);
            return response()->json(setResponse([], ['message' => __('admin.TrainingCourse.status')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Delete Training Course(s) Request
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function changeRequestStatus(ChangeCourseStatusRequest $request) {
        try {
            RequestTrainingCourse::whereIn('id', $request->ids)->update(['status' => $request->status, 'admin_id' => auth()->guard('admin')->id()]);
            return response()->json(setResponse([], ['message' => __('admin.TrainingCourse.requestStatus')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }  
    
    /**
     * Delete course request
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function deleteCourseRequest(DeleteCourseRequest $request) {
        try{
            RequestTrainingCourse::destroy($request->ids);
            return response()->json(setResponse([], ['message' => __('admin.TrainingCourse.requestDeleted')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }  

    /**
     * Operator List
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function operatorsList(TrainingCourseOperatorRequest $request) {
        try {
            $operators = $this->repository->getOperatorsListing($request->all());
            return TrainingCourseOperatorListingResource::collection($operators->get());
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    } 

    /**
     * @OA\Post(
     *     path="/operator/trainingCourse/duplicateCourse",
     *     tags={"Admin - Operators Management"},
     *     summary="Duplicate Training Course",
     *     description="Duplicate Training Course",
     *     operationId="duplicateCourse",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="master_user_id",
     *                     description="Operator Id",
     *                     type="integer"
     *                 ),
     *                 example={"training_course_id": 282, "master_user_id": 5}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */
    public function duplicateCourse(TrainingCourseDuplicateRequest $request){
        try {
            $result = \DB::transaction(function () use ($request) {
                $id = $request->training_course_id;
                $operatorId = $request->master_user_id;
                $operator = MasterUser::find($request->master_user_id);

                $trainingCourse = TrainingCourse::find($request->training_course_id);

                $imageName = '';
                if(!empty($trainingCourse->primary_image)){
                    $image = explode('.',$trainingCourse->primary_image);
                    $imageName = $image[0].rand().'.'.$image[1];
                }

                // Get display order
                $existingTrainingCourses = $this->model->whereIn('master_user_id', getTeamMembers())->orderBy('display_order', 'DESC')->first();
               
                $displayOrder = ($existingTrainingCourses)? $existingTrainingCourses->display_order+1: 1;

                $newTrainingCourse = $trainingCourse->replicate()->fill([
                    'title' => $trainingCourse->title,
                    'status' => 'Inactive',
                    'display_order' => $displayOrder,
                    'is_copy' => 1,
                    'primary_image' => $imageName,
                    'copy_from' => $id,
                    'total_modules' => 0,
                    'master_user_id' => $operatorId,
                ]);

                $newTrainingCourse->save();

                if(!empty($trainingCourse->primary_image)) {
                    \Storage::disk('s3')->copy(getTrainingCoursePath($id)."/".$trainingCourse->primary_image, getTrainingCoursePath($newTrainingCourse->id)."/".$imageName);
                }

                // Generate QR Code of New Training Course
                $actualURL = url(route('trainingCourse.show', ['id' => $newTrainingCourse->id])). '?type=trainingCourse&id=' . $newTrainingCourse->id;
                $deeplinkUrl =$this->deeplinkService->generateDeeplink([
                                    'target_url' => $actualURL,
                                    'operator_id' => $newTrainingCourse->master_user_id,
                                    'type' => 'training_course',
                                    'entity_id' => $newTrainingCourse->id,
                                    'entity_type' => 'TrainingCourse'
                                ]);
                $dynamicUrl=$deeplinkUrl;
                // $dynamicUrl = generateFirebaseDeepLink($actualURL, $newTrainingCourse->master_user_id) . '?type=trainingCourse&id=' . $newTrainingCourse->id;

                $qrCode = generateQRCode($dynamicUrl);
                $file = storage_path('/qrcodes/') . $qrCode;
                \Storage::disk('s3')->put(getTrainingCoursePath($newTrainingCourse->id). '/' . $qrCode, file_get_contents($file));
                $newTrainingCourse->qr_code = $qrCode;
                $newTrainingCourse->save();

                // Dispatch Duplicate Training Course Job
                $this->dispatch(new \App\Jobs\DuplicateTrainingCourseJob($trainingCourse->id, $newTrainingCourse->id, 0, $operator));

                // Assign Default Courses of operators to the users
                if($newTrainingCourse->is_default == 1){
                    $allUsers = UserRelation::whereMasterUserId($newTrainingCourse->master_user_id)->get();
                    $defaultOperatorCoursesJob = (new \App\Jobs\AssignDefaultCoursesJob($newTrainingCourse, $allUsers))->delay(3);
                    dispatch($defaultOperatorCoursesJob);
                }
            });
            return response()->json(setResponse([], ['message' => __('admin.TrainingCourse.duplicate')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
