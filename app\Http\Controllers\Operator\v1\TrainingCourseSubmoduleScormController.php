<?php

namespace App\Http\Controllers\Operator\v1;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Models\ScormProgress;
use App\Http\Requests\Operator\v1\TrainingCourseSubModuleScormRequest;
use App\Repositories\Operator\v1\TrainingCourseSubModuleScormRepository;
use App\Http\Resources\Operator\v1\TrainingCourseSubModuleScormListingResource;

class TrainingCourseSubmoduleScormController extends Controller
{
    protected $scormProgressModel;
    protected $scormRepository;

    public function __construct() {
        $this->scormProgressModel = new ScormProgress();
        $this->scormRepository = new TrainingCourseSubModuleScormRepository($this->scormProgressModel);
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourseSubmodule/scorm/userResponse/getListing",
     *     tags={"Operator - Training Course Submodule SCORM"},
     *     summary="Get All SCORM User Responses",
     *     description="Get All SCORM User Responses",
     *     operationId="index",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="submodule_id",
     *                     description="Submodule Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 example={"submodule_id": 1059, "per_page": 10, "page": 1, "search_key": "", "sort_by": "name", "order_by": "asc", "filters": {"name": "", "total_subdata": "", "status": ""}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Role Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getListing(TrainingCourseSubModuleScormRequest $request) {
        try {
            $scormList = $this->scormRepository->getListing($request->all());
            if ($request->isExport) {
                return $this->scormRepository->exportCsv($scormList->get(), $request->exportFields);
            }
            return TrainingCourseSubModuleScormListingResource::collection($scormList->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

}
