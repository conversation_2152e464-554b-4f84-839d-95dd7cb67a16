<?php

namespace App\Http\Requests\Operator\v1;

use App\Models\Industry;
use App\Models\MasterUser;
use App\Models\Certificate;
use App\Models\TrainingCourse;
use App\Http\Requests\CustomFormRequest;

class UpdateCertificateRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array
    {
        \Validator::extend('training_course_exists',
            function($attribute, $value, $parameters)
            {
                if(isset($parameters[0]) && !is_null($parameters[0])){
                    $existCourse = TrainingCourse::where('id',$parameters[0])->first();
                    if(!empty($existCourse))
                    {
                        return true;
                    }else{
                        return false;
                    }
                }
            }
        );
        $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
        $exits=Certificate::where('master_user_id',$operatorId)->where('training_course_id',$this->training_course_id)->first();
        return [
            'training_course_id' => 'required|training_course_exists:'.$this->training_course_id,
            'image' => empty($this->primary_background_image) && $this->custom_background=='true'?'required':'',
            'logo_image' => empty($this->logo_image_url) && $this->custom_background=='false'?'required':'',
            'certificate_title' => 'required',
            'certificate_name' => 'required',
            'certificate_body' => 'required',
            'landscape_portrait' => 'required',
            'email_engineers' => 'required|in:TRUE,true,FALSE,false',
            'email_managers' => 'required|in:TRUE,true,FALSE,false',
            'custom_background' => 'required|in:TRUE,true,FALSE,false',
            'cert_never_expires' => 'required|in:TRUE,true,FALSE,false',
            'other_email' => 'required',
            'expiry_date' => $this->cert_never_expires=='false'?'required':'',
        ];
    }

    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'master_user_id.required' => __('Master User is required.'),
            'master_user_id.master_user_exists' => __('Master User Not Exits in Our Record.'),
            'training_course_id.required' => __('Training Course is required.'),
            'training_course_id.training_course_exists' => __('Training Course Not Exits in Our Record.'),
        ];
    }

}
