<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAllowSaOverrideColumnInTrainingCourseSubmoduleDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('training_course_submodule_details', function (Blueprint $table) {
            $table->tinyInteger('allow_sa_override')->default(0)->after('remove_progress_calculation');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('training_course_submodule_details', function (Blueprint $table) {
            $table->dropColumn('allow_sa_override');
        });
    }
}
