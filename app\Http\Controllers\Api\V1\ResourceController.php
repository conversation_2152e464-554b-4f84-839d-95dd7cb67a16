<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\Product;
use App\Models\Resources;
use App\Models\News;
use App\Http\Requests\V1\ResourceListingRequest;
use App\Http\Resources\V1\ResourceListingResource;
use App\Http\Resources\V1\ResourceDetailResource;

class ResourceController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        
    }
        
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(ResourceListingRequest $request) {
        try {
            if ($request->product_id) {
                $product = Product::find($request->product_id);
                $resources = $product->resources()->active();
            } else if ($request->news_id) {
                $news = News::find($request->news_id);
                $resources = $news->resources()->active();
            } else {
                $user = auth()->user();
                $resources = Resources::active()->where('master_user_id', $user->user_relation->master_user_id);
            }
            if (($request->type) && $request->type !== 'all') {
                $resources = $resources->whereIn('resource_type', ($request->type === 'video')? ['youtube', 'vimeo', 'source']: [$request->type]);
            }
            if (in_array($request->sort_by, ['asc', 'desc', 'latest', 'oldest'])) {
                $resources = ($request->sort_by === 'asc' || $request->sort_by === 'desc')?
                        $resources->orderBy('name', $request->sort_by):
                        $resources->{$request->sort_by}();
            }
            return ResourceListingResource::collection($resources->paginate(config('constants.mobile_app.per_page')));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id) {
        try {
            $resource = Resources::find($id);
            return ($resource)?
                    (new ResourceDetailResource($resource)):
                    response()->json(setErrorResponse(__('operator.resources.not-found')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
