<?php

namespace App\Http\Requests\Operator\v1;

use App\Http\Requests\CustomFormRequest;

class VimeoMp4UrlRequest extends CustomFormRequest {

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'vimeo_id' => 'required',
        ];
    }
    
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'vimeo_id.required' => __('Vimeo id is required')
        ];
    }

}
