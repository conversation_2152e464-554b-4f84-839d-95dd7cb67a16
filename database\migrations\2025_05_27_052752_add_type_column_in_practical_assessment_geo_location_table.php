<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTypeColumnInPracticalAssessmentGeoLocationTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('practical_assessment_geo_location', function (Blueprint $table) {
            if (!Schema::hasColumn('practical_assessment_geo_location', 'type')) {
                $table->string('type')->default('PA')->after('end_longitude');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('practical_assessment_geo_location', function (Blueprint $table) {
            if (Schema::hasColumn('practical_assessment_geo_location', 'type')) {
                $table->dropColumn('type');
            }
        });
    }
}
