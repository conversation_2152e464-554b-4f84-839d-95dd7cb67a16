<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsResultOverrideColumnInTrainingCourseProgressTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('training_course_progress', 'is_result_override')) {
            Schema::table('training_course_progress', function (Blueprint $table) {
                try {
                    $table->enum('is_result_override', ['Default', 'OverRide'])->default('Default')->after('is_certificate_generated');
                } catch (Exception $e) {
                    print_r("Migration Error in --- Alter-training_course_progress-Table -- Up --->\n" . $e);
                }
            });
        }
        if (!Schema::hasColumn('training_course_module_progress', 'is_result_override')) {
            Schema::table('training_course_module_progress', function (Blueprint $table) {
                try {
                    $table->enum('is_result_override', ['Default', 'OverRide'])->default('Default')->after('status');
                } catch (Exception $e) {
                    print_r("Migration Error in --- Alter-training_course_module_progress-Table -- Up --->\n" . $e);
                }
            });
        }
        if (!Schema::hasColumn('training_course_submodule_progress', 'is_result_override')) {
            Schema::table('training_course_submodule_progress', function (Blueprint $table) {
                try {
                    $table->enum('is_result_override', ['Default', 'OverRide'])->default('Default')->after('status');
                } catch (Exception $e) {
                    print_r("Migration Error in --- Alter-training_course_submodule_progress-Table -- Up --->\n" . $e);
                }
            });
        }
        if (!Schema::hasColumn(DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS,'is_result_override')) {
            Schema::table(DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS, function (Blueprint $table) {
                $table->boolean('is_result_override')->default(0)->after('late_submission');
            });
        }
        if (!Schema::hasColumn(DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS, 'is_result_override')) {
            Schema::table(DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS, function (Blueprint $table) {
                $table->boolean('is_result_override')->default(0)->after('is_pass');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('training_course_progress', function (Blueprint $table) {
            $table->dropColumn('is_result_override');
        });
        Schema::table('training_course_module_progress', function (Blueprint $table) {
            $table->dropColumn('is_result_override');
        });
        Schema::table('training_course_submodule_progress', function (Blueprint $table) {
            $table->dropColumn('is_result_override');
        });
        Schema::table(DBTableNames::TRAINING_COURSE_SUBMODULE_QUIZ_RESULTS, function (Blueprint $table) {
            $table->dropColumn('is_result_override');
        });
        Schema::table(DBTableNames::TRAINING_COURSE_SUBMODULE_PRACTICAL_ASSESSMENT_RESULTS, function (Blueprint $table) {
            $table->dropColumn('is_result_override');
        });
    }
}
