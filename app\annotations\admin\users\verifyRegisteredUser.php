<?php
/**
     * @OA\Post(
     *     path="/admin/users/verifyRegisteredUser",
     *     tags={"Admin - Users"},
     *     summary="verify Registered User By Admin Panel",
     *     description="verify Registered User By Admin Panel",
     *     operationId="index",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="user_id",
     *                     description="User Id",
     *                     type="integer"
     *                 ),
     *                 example={"user_id": 1}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Role Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */ 