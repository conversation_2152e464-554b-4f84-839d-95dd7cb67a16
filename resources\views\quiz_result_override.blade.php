<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2//EN">
<html>
@php 
    $pass_color = $settings->pass_color_scheme ?? '#00B050';
    $fail_color = $settings->fail_color_scheme ?? '#ED1C24';
    $main_color = $settings->main_color_scheme ?? '#2E3092';
    $app_name =  $settings->app_name ?? config('constants.app_name');
@endphp
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="format-detection" content="telephone=no">
    <meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=no;">
    <meta http-equiv="X-UA-Compatible" content="IE=9; IE=8; IE=7; IE=EDGE" />
    <title>{{$app_name}}</title>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
    <style>
        /* latin-ext */
        @font-face {
            font-family: 'Lato';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/lato/v20/S6uyw4BMUTPHjxAwXjeu.woff2) format('woff2');
            unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
        }

        /* latin */
        @font-face {
            font-family: 'Lato';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/lato/v20/S6uyw4BMUTPHjx4wXg.woff2) format('woff2');
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }

        /* latin-ext */
        @font-face {
            font-family: 'Lato';
            font-style: normal;
            font-weight: 700;
            src: url(https://fonts.gstatic.com/s/lato/v20/S6u9w4BMUTPHh6UVSwaPGR_p.woff2) format('woff2');
            unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
        }

        /* latin */
        @font-face {
            font-family: 'Lato';
            font-style: normal;
            font-weight: 700;
            src: url(https://fonts.gstatic.com/s/lato/v20/S6u9w4BMUTPHh6UVSwiPGQ.woff2) format('woff2');
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }

        header {
            position: fixed;
            top: -90px;
            left: 0px;
            right: 0px;
            height: 50px;
            color: black;
            border-bottom: 4px solid blue;
            text-align: center;
            line-height: 35px;
        }

        footer {
            position: fixed;
            bottom: -50px;
            left: 0px;
            right: 0px;
            height: 50px;
            color: black;
            border-top: 4px solid {{$main_color}};
            text-align: center;
            line-height: 35px;
        }

        .page-break {
            page-break-after: always;
            height: 0;
            opacity: 0;
            border: none;
            box-shadow: 0;
            background-color: transparent;
        }

        #pagenumber:before {
            content: counter(page);
        }

        .page-header-space-40 {
            height: 20px;
        }

        .fixed-row td {
            height: 30px;
        }
    </style>
</head>
<?php
    $titleLength = strlen($data->subModuleName);
    $lineHeight = "15px";
    if($titleLength <= 35){
        $fontSize = "32px";
        $lineHeight = "24px";
    }else if($titleLength > 35 && $titleLength <=75){
        $fontSize = "24px";
        $lineHeight = "24px";
    }else if($titleLength > 75 && $titleLength <=140){
        $fontSize = "18px";
    }else if($titleLength > 140 && $titleLength <=225){
        $fontSize = "14px";
    }else{
        $fontSize = "12px";
    }

    $training_course_title = strlen($data->trainingCourse->title) > 65 ? substr($data->trainingCourse->title,0,65)."..." : $data->trainingCourse->title;
    $module_name = strlen($data->module->name) > 65 ? substr($data->module->name,0,65)."..." : $data->module->name;
?>
<body style="font-family: 'Lato';background:#FFFFFF">
    <header style="padding-top: 60px;">
        <table width="100%" bgcolor="#FFFFFF" border="0" cellspacing="0" cellpadding="0" align="left;">
            <tr>
                <td align="left" style="font-size: 13px;color: #000; width: 10%;">
                <img src="{{$pdfLogo['short_logo_url'] ?? url('images/default.png')}}" alt="logo" width="100px">
                </td>
                <td align="center" style="font-size: 13px;color: #000;width: 60%;">
                    <h3 style="font-size: {{$fontSize}};line-height: {{$lineHeight}};text-align: center;margin: 0;">{{ @$data->subModuleName }}</h3>
                    <span style="font-size: 18px;">Page <span id="pagenumber"></span></span>
                </td>
                <td align="right" style="font-size: 13px;color: #000;width: 30%; padding-top:15px;">
                    @php 
                        $bg_color = ($data->is_pass == 1? $pass_color : $fail_color) ;
                    @endphp
                    <h3
                        style="background-color: {{ $bg_color }};font-size: 24px;color: #FFF;padding:8px 0px;margin: 0; text-align: center;">
                        {{ 'PASS' }}
                    </h3>
                    <p style="margin: 0; padding: 0px; font-size: 17px; text-align: center;">{{ $data->email }}</p>
                </td>
            </tr>
        </table>
    </header>

    <footer>
        <table width="100%" bgcolor="#FFFFFF" border="0" cellspacing="0" cellpadding="0" align="left;">
            <tr>
                <td align="center" style="font-size: 18px;color: #666666;">Page <span id="pagenumber"></span></td>
                <td align="right" style="font-size: 18px;color: #666666;"><span>Continued on next page…</span></td>
            </tr>
        </table>
    </footer>

    <table width="100%" bgcolor="#FFFFFF" border="0" cellspacing="0" cellpadding="0" align="left"
        style="padding-top: 80px;margin-top: 10px;">
        <tr style="width:100%;line-height: 15px;">
            <td align="left" valign="top" style="font-size: 16px;width:50%;">
                <strong>Date Completed:</strong> {{ $data->created_at }}
            </td>
            <td align="left" valign="top" style="font-size: 16px;width:50%;">
                <strong>Training Course:</strong> {{ $training_course_title }}
            </td>
        </tr>
        <tr style="width:100%;line-height: 15px;">
            <td align="left" valign="top" style="font-size: 16px;width:50%;">
                <strong>Time Taken:</strong> {{ $data->duration }}
                @if($data->late_submission == 1) 
                    <span style="color:{{$fail_color}};"> [ Late Submission ] </span>
                @else  
                    <span style="color:{{$pass_color}};"> [ On-Time Submission ] </span>
                @endif
            </td>
            <td align="left" valign="top" style="font-size: 16px;width:50%;">
                <strong>Module:</strong> {{ $module_name }}
            </td>
        </tr>
        <tr>
            <td height="10px"></td>
        </tr>
        <tr>
            <td colspan="3" style="border-top: 4px solid {{$main_color}};padding: 10px 0;">
                <h3 style="font-size: 24px;color: {{$main_color}};margin: 0;">Result Manually Overridden</h3>
            </td>
        </tr>
        <tr>
            <td colspan="3" style="border-top: 2px solid #333333;">
            <table width="100%" bgcolor="#FFFFFF" border="0" cellspacing="0" cellpadding="0" align="left"
                    style=" padding: 10px 0px;margin: 100px auto;">
                    <tr>
                        <td>
                            <div class="page-header-space-40"></div>
                        </td>
                    </tr>
                    <tbody align="center">
                        <tr>
                            <td>
                                <img src="{{url('images/na-image.png')}}" style="filter: grayscale(100%);" alt="Not Applicable" height="540px" width="540">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>