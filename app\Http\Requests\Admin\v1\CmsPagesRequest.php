<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;
use DBTableNames;

class CmsPagesRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array
    {
        return [
            'title' => 'required|max:150',
            'slug' => 'required|max:255|unique:' . DBTableNames::CMS_PAGES . ',slug,{$id},id,deleted_at,NULL',
            'status' => 'required|in:Active,Inactive',
            'body' => 'required',
        ];
    }
    
    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'title.required' => __('Please enter title.'),
            'slug.required' => __('Please enter slug.'),
            'slug.unique' => __('Please enter unique slug.'),
            'status.required' => __('Please select status.'),
            'status.in' => __('Invalid status.'),
            'body.required' => __('Please enter body.'),
        ];
    }
    
}
