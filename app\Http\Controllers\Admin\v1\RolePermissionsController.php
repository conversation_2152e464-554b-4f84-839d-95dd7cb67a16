<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use App\Models\Roles;
use App\Models\RolePermissions;
use App\Repositories\Admin\v1\RolesRepository;
use App\Repositories\Admin\v1\RolePermissionsRepository;
use App\Http\Resources\Admin\v1\RolesResource;
use App\Http\Resources\Admin\v1\ShowRolesResource;
use App\Http\Resources\Admin\v1\RolePermissionsResource;
use App\Http\Requests\Admin\v1\RolesRequest;
use App\Http\Requests\Admin\v1\CommonListingRequest;
use App\Http\Requests\Admin\v1\RolePermissionsRequest;
use App\Http\Requests\Admin\v1\RolePermissionsDeleteRequest;
use App\Http\Requests\Admin\v1\RolePermissionsStatusRequest;

class RolePermissionsController extends Controller
{
    private $model;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->role = new Roles();
        $this->role_repository = new RolesRepository($this->role);
        $this->rolespermissions = new RolePermissions();
        $this->role_permission_repository = new RolePermissionsRepository($this->rolespermissions);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Get All Roles List
     *
     * @return \Illuminate\Http\Response
     */
    public function getRoleList(CommonListingRequest $request) {
        try{
            $roles = $this->role_repository->getRolesListing($request->all());
            return RolesResource::collection($roles->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Add Roles
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(RolesRequest $request)
    {
        try {
            $result = DB::transaction(function () use ($request) {
                $moduleData = $request;
                $this->role_repository->create($moduleData);
            });
            return response()->json(setResponse([], ['message' => __('admin.RolePermission.add')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Show Role Details
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $rolesDetail=Roles::findOrFail($id);
            $permissionList=RolePermissions::where('role_id',$id)->get();
            $data=[];
            if (count($permissionList)>0) {
                foreach ($permissionList as $key => $perm) {
                    $record=[];
                    $record['view']=($perm->view==1)?true:false;
                    $record['add']=($perm->add==1)?true:false;
                    $record['delete']=($perm->delete==1)?true:false;
                    $record['edit']=($perm->edit==1)?true:false;
                    $data[$perm->module_name]=$record;

                }
            }
            $rolesDetail['permissionList']=$data;
            $rolesDetail = new ShowRolesResource($rolesDetail);
            $message = !empty($rolesDetail) ? __('admin.RolePermission.found') : __('admin.RolePermission.notFound');
            return response()->json(setResponse($rolesDetail, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update Role details
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(RolesRequest $request, $id)
    {
        try{
            $this->role_repository->update($request, $id);
            return response()->json(setResponse([], ['message' => __('admin.RolePermission.update')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Delete ROle
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(RolePermissionsDeleteRequest $request)
    {
        try{
            $this->role_repository->delete($request);
            return response()->json(setResponse([], ['message' => __('admin.RolePermission.delete')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Role Change Status
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function changeStatus(RolePermissionsStatusRequest $request)
    {
        try {
            $this->role_repository->change_status($request);
            return response()->json(setResponse([], ['message' => __('admin.RolePermission.status')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Display a listing of all roles.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function roleFieldsList(Request $request)
    {
        try {
            $roleTemp = Roles::orderBy('name');
            //Sorting
            if($request->has('filter')){
                $filterData=$request->filter;
                foreach ($filterData as $key => $filter) {
                    foreach ($filter as $fkey => $value) {
                        $roleTemp->where('name', 'like', '%' . $value . '%');
                    }
                }
            }
            $roleTemp = $roleTemp->pluck('name');
            $message = !empty($roleTemp) ? __('admin.RolePermission.found') : __('admin.RolePermission.notFound');
            return response()->json(setResponse($roleTemp, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Display All Role Module List.
     *
     * @return \Illuminate\Http\Response
     */
    public function roleModuleList(Request $request)
    {
        try {
            $moduleList=config('constants.admin_permission_modules');
            foreach($moduleList as $list)
            {
                $data[$list]=
                    array (
                        'view' => false,
                        'edit' => false,
                        'add' => false,
                        'delete' => false
                    );
            }
            return response()->json(setResponse($data))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
