<?php

namespace App\Http\Requests\Operator\v1;

use App\Http\Requests\CustomFormRequest;

class TrainingCourseSingleModuleCourseStatusRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array
    {
        return [
            'id' => 'required|numeric|exists:'.\DBTableNames::TRAINING_COURSE.',id',
            'single_module_course' => 'boolean',
            'single_submodule' => 'boolean',
        ];
    }
    
    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'id.required' => __('Course id is required.'),
            'id.numeric' => __('Course id is invalid.')
        ];
    }
    
}
