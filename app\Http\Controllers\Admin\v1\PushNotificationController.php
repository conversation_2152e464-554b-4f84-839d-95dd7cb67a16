<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Requests\Admin\v1\PushNotificationRequest;
use App\Http\Requests\Admin\v1\PushNotificationDeleteRequest;
use App\Models\User;
use App\Models\Notifications;
use App\Repositories\Admin\v1\PushNotificationRepository;
use App\Http\Resources\Admin\v1\PushNotificationResource;
use DB;

class PushNotificationController extends Controller
{
    protected $model;
    protected $pushNotificationRepository; 
    
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->model = new Notifications();
        $this->pushNotificationRepository = new PushNotificationRepository($this->model);
    }

    /**
     * Send Push Notification
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(PushNotificationRequest $request)
    {
        try {
            $adminId = auth()->guard('admin')->id();
            $registeredUser = User::where('email',$request->email)->whereNull('deleted_at')->first();
            if(isset($registeredUser)){
                if($registeredUser->device_token){
                    
                    $message = $request->title;
                    $mutable = 1;
                    $payload['type'] = "Media";
                    $payload['title'] = $request->title;
                    $payload['description'] = $request->message;
                    if($request->image_url){
                        $payload['image'] = $request->image_url;   
                    }
                    
                    $payload['master_user_id'] = $adminId;
                    $data = [
                        'alert' => $message,
                        "mutable-content" => $mutable,
                        'data' => $payload,
                        'sound' => 'default'
                    ];

                    $extra['user_id'] = $registeredUser->id;
                    $extra['master_user_id'] = $adminId;
                    $extra['title'] = $request->title;
                    $extra['message'] =  $request->message;
                    if($request->image){
                        $extra['image'] = $request->image;
                    }
                    $extra['type'] = 'Media';
                    $extra['notification_status'] = 'Media';
                    $extra['created_at'] = date('Y-m-d H:i:s');
                    $extra['updated_at'] = date('Y-m-d H:i:s');

                    Notifications::insert($extra);

                    // Dispatch Change Password Notification Job
                    $changePasswordJob = (new \App\Jobs\PushNotification($registeredUser, $data, $extra))->delay(3);
                    dispatch($changePasswordJob);
                    return response()->json(setResponse([], ['message' => __('admin.PushNotification.sendNotification')]))->setStatusCode(Response::HTTP_OK);
                }else{
                    return response()->json(setErrorResponse(__('admin.PushNotification.deviceTokenNotFound')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            }else{
                return response()->json(setErrorResponse(__('admin.UserController.not_found')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }  
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/admin/pushNotification",
     *     tags={"Admin - Send Push Notification"},
     *     summary="Send Push Notification",
     *     description="Send Push Notification",
     *     operationId="getNoticationListing",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 example={"page": 1,"per_page": 10,"search_key": "message","sort_by": "id","order_by": "asc"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */
    public function getNoticationListing(Request $request) {
        try {
            $getMediaNotification = $this->pushNotificationRepository->getListing($request->all());
            return PushNotificationResource::collection($getMediaNotification->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }    
    }

    /**
     * @OA\Post(
     *     path="/admin/pushNotification/delete",
     *     tags={"Admin - Send Push Notification"},
     *     summary="Delete Push Notification",
     *     description="Delete Push Notification",
     *     operationId="pushNotificationDelete",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="ids",
     *                     description="Ids of notification",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 example={"ids": {4,6}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */
    public function delete(PushNotificationDeleteRequest $request) {
        try {
            $ids = is_array($request->ids)? $request->ids: [];
            $this->model->destroy($ids);
            return response()->json(setResponse([], ['message' => __('admin.PushNotification.deleteNotification')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

}
