<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use App\Models\Configuration;

class CheckMaintenanceModeMiddleware {

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next, $guard) {
	
	$configuration = Configuration::find(1);
	if ($configuration->is_maintenance_mode == 1) {
	    $authUser = auth()->guard($guard)->user();
	    if ($authUser && $authUser->is_maintenance_mode_allowed == 1) {
		$response = $next($request);
		return $response;
	    }
	    return response()->json(setErrorResponse(__('admin.configuration.maintenance_mode_on'), Response::HTTP_SERVICE_UNAVAILABLE))->setStatusCode(Response::HTTP_SERVICE_UNAVAILABLE);
	}
	$response = $next($request);
	return $response;
    }

}
