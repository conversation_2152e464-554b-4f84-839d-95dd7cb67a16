<?php

namespace App\Http\Requests\Operator\v1;

use App\Http\Requests\CustomFormRequest;

class UnAssignedUsersLinkRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array
    {
        return [
            'user_id' => 'required|numeric',
            'training_course_id' => 'required|numeric',
            'invite_link_id' => 'required|integer|exists:' . \DBTableNames::TRAINING_COURSE_INVITE . ',id',
            // 'user_group_id' =>'required'
        ];
    }
    
    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'user_id.required' => __('User id is require'),
            'invite_link_id.required' => __('Invite link id is require'),
            'user_id.numeric' => __('User id is must be number only'),
            'training_course_id.required' => __('Training course id is require'),
            'training_course_id.numeric' => __('Training course id is must be number only')

        ];
    }
    
}
