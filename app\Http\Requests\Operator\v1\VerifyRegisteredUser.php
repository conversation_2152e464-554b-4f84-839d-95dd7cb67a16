<?php

namespace App\Http\Requests\Operator\v1;

use App\Http\Requests\CustomFormRequest;

class VerifyRegisteredUser extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {
        return [
            'user_id' => 'required|int'
        ];
    }

    public function messages() : array
    {
        return [
            'user_id.required' => __('User Id is required.'),
            'user_id.int' => __('User id must be integer.')
        ];
    }
}
