<?php

namespace App\Http\Controllers\Admin\v1;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\FeatureSettings;
use App\Models\Features;
use App\Models\Roles;
use App\Models\RolePermissions;
use App\Http\Requests\Admin\v1\FeatureSettingsRequest;
use DBTableNames;

class FeatureSettingsController extends Controller
{

    /**
     * Get All Features Listing
     *
     * @return \Illuminate\Http\Response
     */
    public function getFeatureSettings($operatorId) { 
        try {
            $featureData = Features::where('status', 'Active')->get();
            foreach($featureData as $key =>  $fData){
                $featureSetting = FeatureSettings::where('master_user_id',$operatorId)
                                                    ->where('feature_id',$fData->id)->first();
                if(empty($featureSetting)){
                    $featureArray = [
                        'master_user_id' => $operatorId,
                        'feature_id' => $fData->id,
                        'is_feature_on' => 0,
                        'updated_by' => $operatorId
                
                    ];
                    FeatureSettings::create($featureArray); 
                }
                
            }
        
            $data = FeatureSettings::select('features.id','features.title','feature_settings.is_feature_on')
                ->leftjoin(DBTableNames::FEATURES, DBTableNames::FEATURE_SETTINGS . '.feature_id', '=', 'features.id')
                ->where(DBTableNames::FEATURE_SETTINGS.'.master_user_id', $operatorId)
                ->where(DBTableNames::FEATURES.'.status', 'Active')
                ->get();
        
            return response()->json(setResponse($data))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Features Status Change
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function changeFeaturestatus(FeatureSettingsRequest $request){
        try {
            $feature = FeatureSettings::with('feature')->where('master_user_id', $request->master_user_id)
                                                    ->where('feature_id', $request->feature_id)
                                                    ->first();   
            $newStatus = $request->is_feature_on;
            $roles = Roles::with('permissionList')->where('parent_id', $request->master_user_id)->get();
            if(!$newStatus){
                foreach($roles as $role){
                    foreach($role->permissionList as $rName){
                        if($rName->module_name == $feature->feature->permission_key){
                            RolePermissions::where('module_name', $rName->module_name)->where('role_id',$role->id)->delete();
                        }
                    } 
                }
            }else{
                foreach($roles as $role){
                    $arr = [
                        'role_id' => $role->id,
                        'module_name' => $feature->feature->permission_key,
                        'view' => 0,
                        'add' => 0,
                        'edit' => 0,
                        'delete' => 0,
                    ];

                    RolePermissions::insert($arr);
                }
            }
            if($feature){
                $feature->update(['is_feature_on' => $request->is_feature_on]);
            }else{
                $new = FeatureSettings::create($request->all());
            }
            return response()->json(setResponse([], ['message' => __('admin.operator.feature_status')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
