<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\MasterUser;
use App\Models\User;
use App\Http\Resources\Admin\v1\AdminProfileResource;
use App\Http\Requests\Admin\v1\ChangePasswordRequest;
use App\Http\Requests\Admin\v1\AdminProfileRequest;
use App\Repositories\Admin\v1\MasterUserRepository;
use App\Notifications\OperatorResendPasswordNotification;

class UserController extends Controller
{
   
    protected $model;
    protected $user_repository; 
    
     /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->model = new MasterUser();
        $this->user_repository = new MasterUserRepository($this->model);
    }

    /**
     * Master User change password process
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function changePassword(ChangePasswordRequest $request) {
        try {
            Auth::guard('admin')->user()->update(['password' => bcrypt($request->password)]);
            return response()->json(setResponse([], ['message' => __('admin.UserController.passwordReset')]))
                            ->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Edit Account
     *
     * @return \Illuminate\Http\Response
     */
    public function myAccount() {
        try {
            $admin = auth()->guard('admin')->user();
            return response()->json(setResponse(new AdminProfileResource($admin)))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * Update Account
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateMyAccount(AdminProfileRequest $request) {
        try {
            $data = $request->only(['company_name', 'contact_no', 'address', 'latitude', 'longitude', 'website', 'enable_manager_email', 'enable_unique_id', 'unique_id_name', 'enable_custom_contact_info']);
            Auth::guard('admin')->user()->update($data);
            return response()->json(setResponse([], ['message' => __('admin.UserController.profileUpdate')]))
                            ->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * Resend Password to User/Operator
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function resendPassword($type, $id) {
        try {
            if (in_array($type, ['users', 'operators'])) {
                $user = ($type === 'users')?
                        User::find($id):
                        $this->model->find($id);
                if ($user) {
                    //Checking for email address if smartawards contains only digits before @
                    $smartAwardsMailSendFlag = smartAwardsEmailSendResctrict($user->email);
                    $password = getRandomPassword();
                    $user->update(['password' => bcrypt($password)]);
                    if($smartAwardsMailSendFlag == 1){
                        $user->notify(new OperatorResendPasswordNotification($password,$type));
                    }
                    return response()->json(setResponse([], ['message' => __('admin.operator.resend_password')]))->setStatusCode(Response::HTTP_OK);
                }
                return response()->json(setErrorResponse(__('admin.'.(($type === 'users')? 'UserController': 'operator').'.not_found')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
            return response()->json(setErrorResponse(__('admin.operator.invalid-type')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    } 
}
