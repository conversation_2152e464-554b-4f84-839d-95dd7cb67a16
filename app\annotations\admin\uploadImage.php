<?php
/**
     * @OA\Post(
     *     path="/admin/uploadImage",
     *     tags={"Admin - Global Methods - Media Upload"},
     *     summary="Image Upload",
     *     description="Image Upload",
     *     operationId="uploadImage",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="type",
     *                     description="types : modules / submodules / courses / users",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="id",
     *                     description="[ modules / submodules / courses: Training Course Id, profile: User Id]",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                      description="Image",
     *                      property="image",
     *                      type="file",
     *                 ),
     *                 example={"type": "Theory","training_course_id":1 ,"image":"imge.png"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */