<?php

namespace App\Http\Controllers\Operator\v1;

use Carbon\Carbon;
use App\Models\User;
use App\Models\UserRelation;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\TrainingCourse;
use App\Models\whiteLabelSetting;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\TrainingCourseInvite;
use App\Models\AssignTrainingCourses;
use App\Models\RequestTrainingCourse;
use App\Models\TrainingCourseModules;
use App\Models\UserCourseCertificate;
use App\Models\TrainingCourseProgress;
use App\Http\Resources\CustomCollection;
use App\Models\UserAssignTrainingCourses;
use App\Models\TrainingCourseModuleProgress;
use App\Models\TrainingCourseSubmoduleDetails;
use App\Models\TrainingCourseSubmoduleProgress;
use App\Http\Requests\Operator\v1\CourseRequest;
use App\Models\TrainingCourseSubmoduleJobProgress;
use App\Models\TrainingCourseSubModuleQuizResults;
use App\Http\Requests\Operator\v1\StoreLinkRequest;
use App\Http\Requests\Operator\v1\AddModulesRequest;
use App\Models\TrainingCourseSubmoduleGalleryProgress;
use App\Http\Requests\Operator\v1\CommonListingRequest;
use App\Http\Requests\Operator\v1\ResetProgressRequest;
use App\Http\Requests\Operator\v1\TrainingCourseRequest;
use App\Http\Requests\Operator\v1\UnAssignedUsersRequest;
use App\Models\TrainingCourseSubmoduleVideoGuideProgress;
use App\Http\Resources\Operator\v1\TrainingCourseResource;
use App\Models\TrainingCourseSubmoduleProductListProgress;
use App\Repositories\Operator\v1\TrainingCourseRepository;
use App\Http\Requests\Operator\v1\DeleteInviteLinksRequest;
use App\Http\Requests\Operator\v1\GetAllLinkListingRequest;
use App\Models\TrainingCourseSubmoduleHappyUnhappyProgress;
use App\Http\Requests\Operator\v1\UnAssignedUsersLinkRequest;
use App\Http\Resources\Operator\v1\GetAllLinkListingResource;
use App\Models\TrainingCourseSubModuleFeedbackQuestionAnswer;
use App\Http\Requests\Operator\v1\TrainingCourseSelectRequest;
use App\Http\Requests\Operator\v1\TrainingCourseStatusRequest;
use App\Models\TrainingCourseSubmoduleHotspotUploaderProgress;
use App\Models\TrainingCourseSubmoduleImageWithHotspotProgress;
use App\Http\Resources\Operator\v1\TrainingCourseDetailResource;
use App\Models\TrainingCourseSubmoduleConfirmationBoxesProgress;
use App\Http\Requests\Operator\v1\ChangeAssignedLinkStatusRequest;
use App\Http\Requests\Operator\v1\ViewAssignedUsersListingRequest;
use App\Models\TrainingCourseSubmoduleVideoGuideIndividualProgress;
use App\Http\Requests\Operator\v1\ViewAssignedUsersLinkListingRequest;
use App\Http\Resources\Operator\v1\AssignedUsersToTrainingCourseResource;
use App\Http\Requests\Operator\v1\TrainingCourseSingleModuleCourseStatusRequest;
use App\Services\DeeplinkService;


class TrainingCourseController extends Controller {

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    private $model;
    private $repository;
    
    public function __construct() {
        $this->deeplinkService =new DeeplinkService();
        $this->model = new TrainingCourse();
        $this->repository = new TrainingCourseRepository($this->model);
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourse/list",
     *     tags={"Operator - Training Course"},
     *     summary="List Training Course",
     *     description="List Training Course",
     *     operationId="list",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="title",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="endDate",
     *                     description="End Date",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="startDate",
     *                     description="Start Date",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by latest, oldest, asc [A-Z], desc [Z-A], modified [last modified]",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 example={"per_page": 10, "page": 1, "title": "", "sort_by": "latest", "order_by": "asc", "startDate": "2021-07-01", "endDate": "2021-09-30"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function index(CommonListingRequest $request) {
        try {
            $trainingCourseList = TrainingCourse::select('id','title','description','primary_image','master_user_type','status', 'is_copy', 'total_modules', 'publish_now', 'created_at', 'updated_at','is_default', 'is_preselected')->whereIn('master_user_id', getTeamMembers());
            // All three data
            if($request->has('title') && ($request->has('startDate') && $request->has('endDate'))){
                $title = $request->title;
                $startDate = date("Y-m-d",strtotime($request->startDate));
                $endDate = date("Y-m-d",strtotime($request->endDate));
                $trainingCourseList = $trainingCourseList->where('title','LIKE','%'.$title.'%')
                                        ->whereDate('created_at','>=',$startDate)
                                        ->whereDate('created_at','<=',$endDate);
            }else if($request->has('startDate') && $request->has('endDate')){
                $startDate = date("Y-m-d",strtotime($request->startDate));
                $endDate = date("Y-m-d",strtotime($request->endDate));
                $trainingCourseList = $trainingCourseList->whereDate('created_at','>=',$startDate)
                                        ->whereDate('created_at','<=',$endDate);
            }else if($request->has('title')){
                $title = $request->title;
                $trainingCourseList = $trainingCourseList->where('title','LIKE','%'.$title.'%');
            }

            //Sorting
            if($request->has('sort_by')) {
                $sortBy = $request->input('sort_by');
                $trainingCourseList = (
                    ($sortBy == 'modified') ?
                        $trainingCourseList->orderByDesc('updated_at') : (
                            ($sortBy == 'asc') ?
                                $trainingCourseList->orderBy('title') : (
                                    ($sortBy == 'desc') ?
                                        $trainingCourseList->orderByDesc('title') : (
                                            ($sortBy == 'oldest') ?
                                                $trainingCourseList->orderBy('id') :
                                                $trainingCourseList->orderByDesc('id')
                                        )
                                )
                            )
                    );
            }
            else{
                $trainingCourseList =  $trainingCourseList->orderByDesc('id');
            }
            if ($request->isExport) {
                return $this->repository->exportCsv($trainingCourseList->paginate($request->per_page));
            }
            $trainingCourseList = $trainingCourseList->paginate($request->per_page);
            return new CustomCollection($trainingCourseList, 'App\Http\Resources\Operator\v1\TrainingCourseResource');
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    /**
     * @OA\Post(
     *     path="/operator/trainingCourse",
     *     tags={"Operator - Training Course"},
     *     summary="Store Training Course",
     *     description="Store Training Course",
     *     operationId="store",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="title",
     *                     description="Title",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="description",
     *                     description="Description",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                      description="Primary Image",
     *                      property="primary_image",
     *                      type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="display_order",
     *                     description="Display Order",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="keywords",
     *                     description="Keywords",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 @OA\Property(
     *                     property="publish_now",
     *                     description="Publish Now",
     *                     type="boolean",
     *                     enum={true, false}
     *                 ),
     *                 @OA\Property(
     *                     property="schedule_at",
     *                     description="Schedule At",
     *                     type="string"
     *                 ),
     *                 example={"title": "Sample Course", "description": "Sample Description", "display_order": "1", "keywords": "none", "status": "Active", "publish_now": false, "schedule_at": "2021-04-04 10:10:10", "primary_image": "image.png"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function store(TrainingCourseRequest $request) {
        try {
            $publishNow = ($request->input('publish_now') == true ? 1 : 0);
            $request->request->add(['publish_now' => $publishNow]);
            $this->repository->create($request);
            return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.add')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/trainingCourse/{id}",
     *     tags={"Operator - Training Course"},
     *     summary="Show Training Course",
     *     description="Show Training Course",
     *     operationId="show",
     *     @OA\Parameter(
     *         description="Id of training course to fetch",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function show($id) {
        try {
            $course = $this->model->find($id);
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $courseList =  $this->model->where('master_user_id', $operatorId)->pluck('id')->toArray();
            if(in_array($id, $courseList)){
                return ($course) ?
                    (new TrainingCourseDetailResource($course)) :
                    response()->json(setErrorResponse(__('operator.TrainingCourse.dosentExist')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }else{
                return response()->json(setErrorResponse(__('You cannot access this record')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Put(
     *     path="/operator/trainingCourse/{id}",
     *     tags={"Operator - Training Course"},
     *     summary="Update Training Course",
     *     description="Update Training Course",
     *     operationId="update",
     *     @OA\Parameter(
     *         description="Id of training course to update",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="title",
     *                     description="Title",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="description",
     *                     description="Description",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                      description="Primary Image",
     *                      property="primary_image",
     *                      type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="display_order",
     *                     description="Display Order",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="keywords",
     *                     description="Keywords",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 @OA\Property(
     *                     property="publish_now",
     *                     description="Publish Now",
     *                     type="string",
     *                     enum={0, 1}
     *                 ),
     *                 @OA\Property(
     *                     property="schedule_at",
     *                     description="Schedule At",
     *                     type="string"
     *                 ),
     *                 example={"title": "Sample Course", "description": "Sample Description", "display_order": "1", "keywords": "none", "status": "Active", "publish_now": false, "schedule_at": "2021-04-04 10:10:10", "primary_image": "image.png"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function update(TrainingCourseRequest $request, $id) {
        try {
            $this->repository->update($request, $id);
            return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.update')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/trainingCourse/getCourseDisplayOrders",
     *     tags={"Operator - Training Course"},
     *     summary="Get Training Course Display Orders",
     *     description="Get Training Course Display Orders",
     *     operationId="getCourseDisplayOrders",
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getCourseDisplayOrders() {

        try {
            $courses = [];
            $defaultOrder = [ 0 => [
                'id' => 0,
                'display_order' => 1,
                'title' => 'New Course',
                'primary_image' => url('/images/default.png'),
            ],];

            $existingTrainingCourses = TrainingCourse::select('id', 'display_order', 'title', 'primary_image')->whereIn('master_user_id', getTeamMembers())->orderBy('display_order')->get();
            foreach ($existingTrainingCourses as $course) {
                $courses[] = [
                    'id' => $course->id,
                    'display_order' => $course->display_order,
                    'title' => $course->title,
                    'primary_image' => $course->primary_image_url
                ];
            }

            if($courses){
                $lastRecord =  end($courses);
                $defaultOrder['0']['display_order'] = $lastRecord['display_order'] + 1;
            }
            $displayOrderList = array_merge($courses, $defaultOrder);

            return response()->json(setResponse($displayOrderList, ['message' => __('operator.TrainingCourse.displayOrderFound')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }


    /**
     * @OA\Delete(
     *     path="/operator/trainingCourse/{id}",
     *     tags={"Operator - Training Course"},
     *     summary="Delete  Training Course",
     *     description="Delete  Training Course",
     *     operationId="delete",
     *     @OA\Parameter(
     *         description="id",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function destroy($id)
    {
        try {
            \DB::transaction(function () use ($id) {
                $this->repository->delete($id);
            });
            return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.delete')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourse/changeStatus",
     *     tags={"Operator - Training Course"},
     *     summary="Training Course Change Status",
     *     description="Training Course Change Status",
     *     operationId="changeStatus",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="id",
     *                     description="id",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={"id": 1, "status": "Active"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function changeStatus(TrainingCourseStatusRequest $request) {
        try {
            $course = $this->model->find($request->id);
            $oldStatus = $course->status;
            $newStatus = $request->status;
            $course->update(['status' => $request->status]);
            if(($newStatus == 'Active' && $oldStatus == 'Inactive') && $course->publish_now == '1'){
                TrainingCourse::sendChangeStatusCourseNotification($request->id, $course);
            }
            return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.status')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

        /**
     * @OA\Get(
     *     path="/operator/trainingCourse/preSelect/{id}",
     *     tags={"Select Training Course"},
     *     summary="Select Training Course",
     *     description="Select Training Course",
     *     operationId="selectCourse",
     *     @OA\Parameter(
     *         description="select training course",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function preselectCourse($id) {
        try {
            TrainingCourse::select('id')
                            ->whereHas('subModules', function($q){
                                $q->where('submodule_type_id',13);
                            })
                            ->whereIn('master_user_id', getTeamMembers())
                            ->update(['is_preselected' => 0]);

            TrainingCourse::where('id',$id)
                            ->whereHas('subModules', function($q){
                                $q->where('submodule_type_id',13);
                            })
                            ->whereIn('master_user_id', getTeamMembers())
                            ->update(['is_preselected' => 1]);

            return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.selectCourse')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    /**
     * @OA\Get(
     *     path="/operator/trainingCourse/duplicateCourse/{id}",
     *     tags={"Operator - Training Course"},
     *     summary="Duplicate Training Course",
     *     description="Duplicate Training Course",
     *     operationId="duplicateCourse",
     *     @OA\Parameter(
     *         description="Id of training course to duplicate",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function duplicateCourse($id){
        try {
            $result = \DB::transaction(function () use ($id) {
                $trainingCourse = TrainingCourse::find($id);

                $imageName = '';
                if(!empty($trainingCourse->primary_image)){
                    $image = explode('.',$trainingCourse->primary_image);
                    $imageName = $image[0].rand().'.'.$image[1];
                }

                // Get display order
                $existingTrainingCourses = $this->model->whereIn('master_user_id', getTeamMembers())->orderBy('display_order', 'DESC')->first();
                $displayOrder = ($existingTrainingCourses)? $existingTrainingCourses->display_order+1: 1;

                $newTrainingCourse = $trainingCourse->replicate()->fill([
                    'title' => $trainingCourse->title .' - copy',
                    'status' => 'Inactive',
                    'display_order' => $displayOrder,
                    'is_copy' => 1,
                    'primary_image' => $imageName,
                    'copy_from' => $id,
                    'total_modules' => 0,
                ]);
                $newTrainingCourse->save();

                if(!empty($trainingCourse->primary_image)) {
                    \Storage::disk('s3')->copy(getTrainingCoursePath($id)."/".$trainingCourse->primary_image, getTrainingCoursePath($newTrainingCourse->id)."/".$imageName);
                }

                // Generate QR Code of New Training Course
                $actualURL = url(route('trainingCourse.show', ['id' => $newTrainingCourse->id])). '?type=trainingCourse&id=' . $newTrainingCourse->id;
                // $dynamicUrl = generateFirebaseDeepLink($actualURL, $newTrainingCourse->master_user_id) . '?type=trainingCourse&id=' . $newTrainingCourse->id;
                $deeplinkUrl =$this->deeplinkService->generateDeeplink([
                                    'target_url' => $actualURL,
                                    'operator_id' => $newTrainingCourse->master_user_id,
                                    'type' => 'training_course',
                                    'entity_id' => $newTrainingCourse->id,
                                    'entity_type' => 'training_course',
                                ]);
                $dynamicUrl=$deeplinkUrl;
                $qrCode = generateQRCode($dynamicUrl);
                $file = storage_path('/qrcodes/') . $qrCode;
                \Storage::disk('s3')->put(getTrainingCoursePath($newTrainingCourse->id). '/' . $qrCode, file_get_contents($file));
                $newTrainingCourse->qr_code = $qrCode;
                $newTrainingCourse->save();

                // Dispatch Duplicate Training Course Job
                $this->dispatch(new \App\Jobs\DuplicateTrainingCourseJob($trainingCourse->id, $newTrainingCourse->id, 0, auth()->guard('operator')->user()));


                // Assign Training course to users when course added by default operator

                // Assign Default Courses of operators to the users
                if($newTrainingCourse->is_default == 1){
                    $allUsers = UserRelation::whereMasterUserId($newTrainingCourse->master_user_id)->get();
                    $defaultOperatorCoursesJob = (new \App\Jobs\AssignDefaultCoursesJob($newTrainingCourse, $allUsers))->delay(3);
                    dispatch($defaultOperatorCoursesJob);
                }
            });
            return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.duplicate')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourse/addModules",
     *     tags={"Operator - Training Course"},
     *     summary="Add modules from other Training Course",
     *     description="Add modules from other Training Course",
     *     operationId="addModules",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="trainingCourseId",
     *                     description="Training Course Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="modulesId",
     *                     description="Modules Id",
     *                     type="array",
     *                     @OA\Items()
     *                 ),
     *                 example={"trainingCourseId": 1, "modulesId": {1, 5, 10, 15}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function addModules(AddModulesRequest $request) {
        try {
            $trainingCourse = TrainingCourse::find($request->trainingCourseId);
            if($trainingCourse->single_module_course == 1){
                $requestedModules = count($request->modulesId);
                if($requestedModules > 1){
                    return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.addSingleModule')]))->setStatusCode(Response::HTTP_OK);
                }
                $moduleCount = TrainingCourseModules::where('training_course_id',$request->trainingCourseId)->whereNull('deleted_at')->count();
                if($moduleCount == 0){
                    if($trainingCourse->single_submodule == 1){
                        $subModuleCount = TrainingCourseSubmoduleDetails::where('module_id',$request->modulesId[0])->whereNull('deleted_at')->count();
                        if($subModuleCount > 1){
                            return response()->json(setErrorResponse(__('operator.TrainingCourse.addSingleSubModule')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                        }else{
                            $submoduleDetails = TrainingCourseSubmoduleDetails::where('module_id',$request->modulesId[0])
                                                ->where(function($query) {
                                                    $query->orWhere('submodule_type_id', config('constants.submodule_types.question_bank_quiz'))
                                                        ->orWhere('submodule_type_id', config('constants.submodule_types.practical_assessment'));
                                                })
                                                ->whereNull('deleted_at')->first();
                            if(!empty($submoduleDetails)){
                                dispatch(new \App\Jobs\AddModulesToTrainingCourseJob($request->trainingCourseId, $request->modulesId, auth()->guard('operator')->user()));
                            return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.modulesAdded')]))->setStatusCode(Response::HTTP_OK);
                            }else{
                                return response()->json(setErrorResponse(__('operator.TrainingCourse.addOnlyQuizPASubModule')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                            }
                        }
                    }
                } else{
                    return response()->json(setErrorResponse(__('operator.TrainingCourseModule.singleModuleCourseError')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }  
            }else{
                dispatch(new \App\Jobs\AddModulesToTrainingCourseJob($request->trainingCourseId, $request->modulesId, auth()->guard('operator')->user()));
                return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.modulesAdded')]))->setStatusCode(Response::HTTP_OK);
            } 
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourse/viewAssignedUsers",
     *     tags={"Operator - Training Course"},
     *     summary="View assigned users to Training Course",
     *     description="View assigned users to Training Course",
     *     operationId="viewAssignedUsers",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="isExport",
     *                     description="for export csv",
     *                     type="integer"
     *                 ),
     *                 example={"training_course_id": 1, "per_page": 10, "page": 1, "search_key": "", "sort_by": "name", "order_by": "asc", "filters": {"uname": "", "uemail": "", "gname": "", "manager_email": "", "unique_id": ""}, "isExport": 0}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function viewAssignedUsers(ViewAssignedUsersListingRequest $request) {
        try {
            $users = $this->repository->getAssignedUsersListing($request->all());
            if ($request->isExport) {
                return $this->repository->exportAssignedUsers($users->get());
            }
            return AssignedUsersToTrainingCourseResource::collection($users->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourse/viewAssignedGroupUsers",
     *     tags={"Operator - Training Course"},
     *     summary="View assigned users to group Training Course",
     *     description="View assigned users to group Training Course",
     *     operationId="viewAssignedGroupUsers",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="isExport",
     *                     description="for export csv",
     *                     type="integer"
     *                 ),
     *                 example={"training_course_id": 1, "per_page": 10, "page": 1, "search_key": "", "sort_by": "name", "order_by": "asc", "filters": {"uname": "", "uemail": "", "gname": "", "manager_email": "", "unique_id": ""}, "isExport": 0}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function viewAssignedGroupUsers(ViewAssignedUsersListingRequest $request) {
        try {
            $users = $this->repository->getAssignedGroupUsersListing($request->all());
            if ($request->isExport) {
                return $this->repository->exportAssignedUsers($users->get());
            }
            return AssignedUsersToTrainingCourseResource::collection($users->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourse/requestCourse",
     *     tags={"Operator - Training Course"},
     *     summary="Request a Training Course",
     *     description="Request a Training Course",
     *     operationId="requestCourse",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="title",
     *                     description="title",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="description",
     *                     description="description",
     *                     type="string",
     *                 ),
     *                 example={"title": "MDU Good Course", "description": "This course is for all of you guys. So Cheers !!!"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function requestCourse(CourseRequest $request){
        try {
            $requestTrainingCourse = new RequestTrainingCourse();
            $requestTrainingCourse->title = $request->title;
            $requestTrainingCourse->description = $request->description;
            $requestTrainingCourse->master_user_id = auth()->guard('operator')->id();
            $requestTrainingCourse->status = 'Pending';
            $requestTrainingCourse->save();
            return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.requestSuccess')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }


    /**
     * @OA\Post(
     *     path="/operator/trainingCourse/unassignUser",
     *     tags={"Operator - Training Course"},
     *     summary="Unassign users from Training Course",
     *     description="Unassign users from Training Course",
     *     operationId="unassignUser",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="user_id",
     *                     description="User Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="integer"
     *                 ),
     *                 example={"user_id": 1, "training_course_id": 1}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function unassignUser(UnAssignedUsersRequest $request) {
        try {
            $userData = User::find($request->user_id);
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;

            if ($request->user_group_id) {
                $groupCourses    = AssignTrainingCourses::whereUserGroupId($request->user_group_id)->get()->pluck('training_course_id')->toArray();
                $groupCourseList = '';
                if (count($groupCourses) > 0) {
                    $groupCourseList = implode(",", $groupCourses);
                }

                if (!empty($groupCourseList)) {
                    $data = UserAssignTrainingCourses::
                        select(\DB::raw("IF(FIND_IN_SET('".$request->training_course_id."','".$groupCourseList."'), '1','0')  as is_group_course"))
                        ->first();
                        if($data['is_group_course']==1){
                            return response()->json(setErrorResponse(__('operator.TrainingCourse.notUnassign')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                        }else{
                            $data =  UserAssignTrainingCourses::whereUserId($request->user_id)
                                                        ->whereMasterUserId($operatorId)
                                                        ->whereTrainingCourseId($request->training_course_id)
                                                        ->first();
                            if($data){
                                $data->delete();
                                return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.unassignSuccess')]))->setStatusCode(Response::HTTP_OK);
                            }
                        }
                    }else{
                        $data =  UserAssignTrainingCourses::whereUserId($request->user_id)
                                                    ->whereMasterUserId($operatorId)
                                                    ->whereTrainingCourseId($request->training_course_id)
                                                    ->first();
                        if($data){
                            $data->delete();
                            return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.unassignSuccess')]))->setStatusCode(Response::HTTP_OK);
                        }
                    }
            }else{
                $data =  UserAssignTrainingCourses::whereUserId($request->user_id)
                    ->whereMasterUserId($operatorId)
                    ->whereTrainingCourseId($request->training_course_id)
                    ->first();

                if($data){
                $data->delete();
                return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.unassignSuccess')]))->setStatusCode(Response::HTTP_OK);
                }
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/trainingCourse/resetProgress",
     *     tags={"Operator - Training Course, Module, Submodule Reset Progress"},
     *     summary="Reset Progress of Training Course, Module, Submodule",
     *     description="Reset Progress of Training Course, Module, Submodule",
     *     operationId="resetProgress",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="user_id",
     *                     description="User Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="id",
     *                     description="Id of training course/module/submodule",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="type",
     *                     description="course/module/submodule",
     *                     type="string"
     *                 ),
     *                 example={"user_id": 1, "training_course_id": 1, "type":"course"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */
    public function resetProgress(ResetProgressRequest $request){
        try{
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            if($request->type == 'course'){
                DB::transaction(function () use($operatorId,$request){
                    // Remove Certificate
                    UserCourseCertificate::where('training_course_id',$request->id)->where('user_id',$request->user_id)->delete();
                    $course = TrainingCourseProgress::whereTrainingCourseId($request->id)->whereUserId($request->user_id)->whereMasterUserId($operatorId)->where('course_progress','!=',0)->first();
                    if(!empty($course)){
                        // Reset Course Progress
                        $course->update(['course_progress' => 0, 'date_time' => date('Y-m-d H:i:s'), 'is_reset' => 1,'is_new' => 0,'is_certificate_generated' => 0,'is_result_override' => 'Default']);

                        $moduleList = TrainingCourseModuleProgress::whereTrainingCourseId($request->id)->whereUserId($request->user_id)->where('module_progress','!=',0)->get();

                        // Reset Module Progress
                        (new TrainingCourseModuleProgress)->resetProgress($moduleList);
                        
                    }
                });
            }

            if($request->type == 'module'){
                DB::transaction(function () use($operatorId,$request){
                    $moduleId = $request->id;
                    $trainingCourseId = TrainingCourseModules::whereId($moduleId)->pluck('training_course_id')->first();
                    // Remove Certificate
                    UserCourseCertificate::where('training_course_id',$trainingCourseId)->where('user_id',$request->user_id)->delete();
                    $moduleList = TrainingCourseModuleProgress::whereTrainingCourseId($trainingCourseId)->whereUserId($request->user_id)->whereMasterUserId($operatorId)->where('module_id','>=',$moduleId)->where('module_progress','!=',0)->get();

                    // Update Reset flag
                    TrainingCourseProgress::whereTrainingCourseId($trainingCourseId)->whereUserId($request->user_id)->whereMasterUserId($operatorId)->update(['is_reset' => 1,'is_new' => 0,'is_certificate_generated' => 0,'is_result_override' => 'Default']);

                    // Reset Module Progress
                    (new TrainingCourseModuleProgress)->resetProgress($moduleList);

                    // Module progress
                    $data = ['training_course_id' => $trainingCourseId,'user_id' => $request->user_id, 'module_id' => $moduleId];
                    (new TrainingCourseModuleProgress)->calculateModuleProgress($data);

                    // Training course progress
                    (new TrainingCourseProgress)->calculateTrainingCourseProgress($data);
                });
            }

            if($request->type == 'submodule'){
                DB::transaction(function () use($operatorId,$request){
                    $subModuleId = $request->id;
                    $trainingCourseId = TrainingCourseSubmoduleDetails::whereId($subModuleId)->pluck('training_course_id')->first();
                    $subModules = TrainingCourseSubmoduleProgress::whereTrainingCourseId($trainingCourseId)->whereUserId($request->user_id)->whereMasterUserId($operatorId)->where('submodule_id','>=',$subModuleId)->where('submodule_type_id','!=',16);
                    $subModuleList = $subModules->get();
                    $moduleIdArr = $subModules->pluck('module_id')->toArray();
                    $moduleIds = array_unique($moduleIdArr);

                    // Remove Certificate
                    UserCourseCertificate::where('training_course_id',$trainingCourseId)->where('user_id',$request->user_id)->delete();

                    // Update Reset flag
                    TrainingCourseProgress::whereTrainingCourseId($trainingCourseId)->whereUserId($request->user_id)->whereMasterUserId($operatorId)->update(['is_reset' => 1,'is_new' => 0,'is_certificate_generated' => 0,'is_result_override' => 'Default']);

                    (new TrainingCourseSubmoduleProgress)->resetProgress($subModuleList);
                    $remove_progress_calculation = TrainingCourseSubmoduleDetails::whereId($subModuleId)->pluck('remove_progress_calculation')->first();
                    if($remove_progress_calculation == 0){
                    // Module progress
                    foreach($moduleIds as $module){
                        $data = ['training_course_id' => $trainingCourseId,'user_id' => $request->user_id, 'module_id' => $module];
                        (new TrainingCourseModuleProgress)->calculateModuleProgress($data);
                    }
                    // Training course progress
                    $tData = ['training_course_id' => $trainingCourseId,'user_id' => $request->user_id];
                    (new TrainingCourseProgress)->calculateTrainingCourseProgress($tData);
                    }
                });
            }

            return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.resetProgress')]))->setStatusCode(Response::HTTP_OK);
        } catch(\Exception $e){
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

     /**
     * @OA\Post(
     *     path="/operator/trainingCourse/storeAssignedLink",
     *     tags={"Operator - Training Course Invite Link"},
     *     summary="Training Course Invite Link",
     *     description="Training Course Invite Link",
     *     operationId="storeAssignedLink",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="course_id",
     *                     description="Course Id",
     *                     type="integer"
     *                 ),
     *                 example={"course_id": 1}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */

    public function storeAssignedLink(StoreLinkRequest $request ) {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $request->merge(['shortLink' => $request->dynamicLinkInfo['link']]);
            if(isset($request->shortLink)){
                    // Generate QR Code of Training Course
                    $course=TrainingCourse::find($request->course_id);
                    if(!empty($course)){
                    $actualURL = url(route('trainingCourse.show', ['id' => $request->course_id])). '?type=trainingCourse&id=' . $request->course_id. '&unique_id=' . $request->uniqueId;
                    // $dynamicUrl = generateFirebaseDeepLink($actualURL, $course->master_user_id) . '?type=trainingCourse&id=' . $request->course_id;
                    $deeplinkUrl =$this->deeplinkService->generateDeeplink([
                                    'target_url' => $actualURL,
                                    'operator_id' => $course->master_user_id,
                                    'type' => 'training_course',
                                    'entity_id' => $request->course_id,
                                    'entity_type' => 'training_course',
                                ]);
                    $dynamicUrl=$deeplinkUrl;
                    $linkDataAdd = TrainingCourseInvite::create([
                        'master_user_id' => $operatorId,
                        'course_id' => $request->course_id,
                        'invite_link' => $dynamicUrl,
                        'unique_id' => $request->uniqueId
                    ]);
                    $qrCode = generateQRCode($dynamicUrl);
                    $file = storage_path('/qrcodes/') . $qrCode;
                    \Storage::disk('s3')->put(getTrainingCoursePath($request->course_id). '/' . $qrCode, file_get_contents($file));
                    $course->qr_code = $qrCode;
                    $packageName=whiteLabelSetting::where('operator_id',$operatorId)
                                            ->pluck('ios_package_name');
                    $actualURL = url(route('trainingCourse.show', ['id' => $request->course_id])). '?type=trainingCourse&id=' . $request->course_id.'&appId='.$packageName;
                    $deeplinkUrl =$this->deeplinkService->generateDeeplink([
                                    'target_url' => $actualURL,
                                    'operator_id' => $operatorId,
                                    'type' => 'training_course',
                                    'entity_id' => $request->course_id,
                                    'entity_type' => 'training_course',
                                ]);
                    $dynamicUrl=$deeplinkUrl;
                    $course->share_url = $dynamicUrl;
                    // $course->share_url = generateFirebaseDeepLink(url(route('trainingCourse.show', ['id' => $request->course_id])),$operatorId) . '?type=trainingCourse&id=' . $request->course_id.'&appId='.$packageName;
                    $course->save();
                    }
                return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.inviteLinkAdd')]))
                    ->setStatusCode(Response::HTTP_OK);
            }
        }
        catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

     /**
     * @OA\Get(
     *     path="/operator/trainingCourse/getAssignedLink/{id}",
     *     tags={"Operator - Training Course get Invite Link"},
     *     summary="Training Course get Invite Link",
     *     description="Training Course get Invite Link",
     *     operationId="getAssignedLink",
     *     @OA\Parameter(
     *         description="Id of training course to get Invite Link",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */

    public function getAssignedLink($id) {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $linkData = TrainingCourseInvite::select('master_user_id','course_id', 'invite_link')
            ->whereMasterUserId($operatorId)
            ->whereCourseId($id)
            ->latest()
            ->first();
            return response()->json(setResponse($linkData, ['message' => __('operator.TrainingCourse.inviteLinkFound')]))->setStatusCode(Response::HTTP_OK);
        }
        catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }

    }

 /**
     * @OA\Get(
     *     path="/operator/trainingCourse/getAllAssignedLink",
     *     tags={"Operator - Training Course get Invite Link"},
     *     summary="Training Course get Invite Link",
     *     description="Training Course get Invite Link",
     *     operationId="getAllAssignedLink",
       *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *
     *
     *                 example={"training_course_id": 66, "per_page": 10, "page": 1, "search_key": "", "sort_by": "name", "order_by": "asc", "filters": {"created_at": {"startDate": "2022-11-29","endDate": "2022-12-01"}}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */

    public function getAllAssignedLink(GetAllLinkListingRequest $request) {
        try {
            $linkData = $this->repository->getAllLinkListing($request->all());
            return GetAllLinkListingResource::collection($linkData->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourse/viewAssignedUsersThroughLink",
     *     tags={"Operator - Training Course"},
     *     summary="View assigned users to Training Course",
     *     description="View assigned users to Training Course",
     *     operationId="viewAssignedUsersThroughLink",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="integer"
     *                 ),
     *                  @OA\Property(
     *                     property="invite_link_id",
     *                     description="Invite Link Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="per_page",
     *                     description="Per page records",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     description="Page Number",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="search_key",
     *                     description="Search Term",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort by column name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="order_by",
     *                     description="Order By Term asc/desc",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="filters[]",
     *                     description="for advance filters[]",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                  @OA\Property(
     *                     property="isExport",
     *                     description="for export csv",
     *                     type="integer"
     *                 ),
     *
     *                 example={"invite_link_id": 16, "per_page": 10, "page": 1, "search_key": "", "sort_by": "name", "order_by": "asc", "filters": {"uname": "", "uemail": "", "gname": "", "manager_email": "", "unique_id": "",  "isExport": 0}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function viewAssignedUsersThroughLink(ViewAssignedUsersLinkListingRequest $request) {
        try {
            $users = $this->repository->getLinkAssignedUsersListing($request->all());
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $linkList =  TrainingCourseInvite::where('master_user_id', $operatorId)->pluck('id')->toArray();
            if(in_array($request->invite_link_id, $linkList)){
                if ($request->isExport) {
                    return $this->repository->exportAssignedUsers($users->get());
                }
                return AssignedUsersToTrainingCourseResource::collection($users->paginate($request->per_page));
            }else{
                return response()->json(setErrorResponse(__('You cannot access this record')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourse/deleteAssignedLinks",
     *     tags={"Operator - Training Course"},
     *     summary="Delete invite links",
     *     description="Delete invite links",
     *     operationId="deleteAssignedLinks",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="ids",
     *                     description="Ids of invite links",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 example={"ids": {4,6}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function deleteAssignedLinks(Request $request) {
        try {
            $ids = is_array($request->ids)? $request->ids: [];
            
            foreach($ids as $id){

                $check_user_count = UserAssignTrainingCourses::select(\DB::raw('count(*) as total_users'))
                ->where('invite_link_id', $id)->first();
                if($check_user_count->total_users > 0){
                    return response()->json(setErrorResponse(__('operator.TrainingCourse.notDeleteLink')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }

            }
            TrainingCourseInvite::destroy($ids);
            return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.deleteLinks')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourse/unassignUsersLink",
     *     tags={"Operator - Training Course"},
     *     summary="Unassign users from Training Course",
     *     description="Unassign users from Training Course",
     *     operationId="unassignUsersLink",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="user_id",
     *                     description="User Id",
     *                     type="integer"
     *                 ),
     *                  @OA\Property(
     *                     property="invite_link_id",
     *                     description="Link Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="integer"
     *                 ),
     *                 example={"user_id": 1, "training_course_id": 1, "invite_link_id" :1}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function unassignUsersLink(UnAssignedUsersLinkRequest $request) {
        try {
            $userData = User::find($request->user_id);
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            if ($request->user_group_id) {
                $groupCourses    = AssignTrainingCourses::whereUserGroupId($request->user_group_id)->get()->pluck('training_course_id')->toArray();
                $groupCourseList = '';
                if (count($groupCourses) > 0) {
                    $groupCourseList = implode(",", $groupCourses);
                }
                if (!empty($groupCourseList)) {
                    $data = UserAssignTrainingCourses::
                        select(\DB::raw("IF(FIND_IN_SET('".$request->training_course_id."','".$groupCourseList."'), '1','0')  as is_group_course"))
                        ->first();
                        if($data['is_group_course']==1){
                            return response()->json(setErrorResponse(__('operator.TrainingCourse.notUnassign')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                        }
                        else{
                            $data = UserAssignTrainingCourses::whereUserId($request->user_id)
                            ->whereMasterUserId($operatorId)
                            ->whereTrainingCourseId($request->training_course_id)
                            ->where('invite_link_id','=',$request->invite_link_id)
                            ->first();
                            if($data){
                                $data->delete();
                                return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.unassignSuccess')]))->setStatusCode(Response::HTTP_OK);
                            }
                        }
                }else{
                    $data = UserAssignTrainingCourses::whereUserId($request->user_id)
                    ->whereMasterUserId($operatorId)
                    ->whereTrainingCourseId($request->training_course_id)
                    ->where('invite_link_id','=',$request->invite_link_id)
                    ->first();
                    if($data){
                        $data->delete();
                        return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.unassignSuccess')]))->setStatusCode(Response::HTTP_OK);
                    }
                }
            }
            else{
                $data = UserAssignTrainingCourses::whereUserId($request->user_id)
                ->whereMasterUserId($operatorId)
                ->whereTrainingCourseId($request->training_course_id)
                ->where('invite_link_id','=',$request->invite_link_id)
                ->first();
                if($data){
                    $data->delete();
                    return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.unassignSuccess')]))->setStatusCode(Response::HTTP_OK);
                }
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

     /**
     * @OA\Post(
     *     path="/operator/trainingCourse/changeAssignedLinkStatus",
     *     tags={"Operator - Training Course"},
     *     summary="Training Course Assigned Link Change Status",
     *     description="Training Course Assigned Link Change Status",
     *     operationId="changeAssignedLinkStatus",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="ids",
     *                     description="ids",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={"ids": {1,2}, "status": "Active"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function changeAssignedLinkStatus(ChangeAssignedLinkStatusRequest $request) {
        try {
            $ids = is_array($request->ids)? $request->ids: [];
            TrainingCourseInvite::whereIn('id',$ids)->update(['status' => $request->status]);
            return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.linkStatus')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourse/changeSingleModuleCourseStatus",
     *     tags={"Operator - Training Course - Single Module Course"},
     *     summary="Training Course - Single Module Course",
     *     description="Training Course - Single Module Course",
     *     operationId="changeSingleModuleCourseStatus",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="id",
     *                     description="id",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="single_module_course",
     *                     description="Single Module Course",
     *                     type="boolean",
     *                     enum={true, false}
     *                 ),
     *                 @OA\Property(
     *                     property="single_submodule",
     *                     description="Single Submodule Quiz/PA",
     *                     type="boolean",
     *                     enum={true, false}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function changeSingleModuleCourseStatus(TrainingCourseSingleModuleCourseStatusRequest $request) {
        try {
            $course = $this->model->find($request->id);

            $singleModuleCourseStatus = $request->single_module_course;
            $singleSubmoduleStatus = $request->single_submodule;

            $moduleCourseStatus = 0;
            $submoduleStatus = 0;
            if(isset($singleModuleCourseStatus)){
                $moduleCourseStatus = $singleModuleCourseStatus;
                if(isset($singleSubmoduleStatus)){
                    $submoduleStatus = $singleSubmoduleStatus;
                    if($singleModuleCourseStatus == 0){
                        $submoduleStatus = 0;
                    }
                }
            }

            $course->update(['single_module_course' => $moduleCourseStatus,'single_submodule' => $submoduleStatus]);
            return response()->json(setResponse([], ['message' => __('operator.TrainingCourse.status')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage())))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
