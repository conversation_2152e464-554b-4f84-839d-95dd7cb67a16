<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\MasterUser;
use App\Models\User;
use App\Models\TrainingCourse;
use App\Http\Resources\Admin\v1\DashboardOperatorListingResource;
use App\Repositories\Admin\v1\DashboardRepository;
use App\Http\Requests\Admin\v1\CommonListingRequest;
use App\Models\Product;
use App\Models\Resources;
use App\Models\TrainingCourseModules;


class DashboardController extends Controller
{
    
    protected $model;
    
    protected $user_repository; 
    
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->model = new MasterUser();
        $this->repository = new DashboardRepository($this->model);
    }

    /**
     * Get Summary of Admin Dashboard
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {

        $response = [];

        // Users Analytics
        $response['users'] = User::adminStatistics();

        // Resource Analytics
        $response['resources'] = Resources::adminStatistics();

        // Product Analytics
        $response['products'] = Product::adminStatistics();

        // Training Course Analytics
        $response['courses'] = TrainingCourse::adminStatistics();

        // News Analytics
        $response['operators'] = MasterUser::adminStatistics();

        return $response;
    }

    /**
     * Get List of Recent Operators
     *
     * @return \Illuminate\Http\Response
     */
    public function recentOperatorList(CommonListingRequest $request) 
    {
        try {
            $operators = $this->repository->getRecentOperatorList($request->all());
            return DashboardOperatorListingResource::collection($operators->take(5)->get());
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
