<?php

namespace App\Http\Requests\Operator\v1\SmartAward;
use App\Models\User;
use App\Http\Requests\CustomFormRequest;
use App\Models\UserRelation;

class StoreQuizResultSmartAwardRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {

        return [
            "email_address" => 'required|email',
            "training_course_id" => 'required|int',
            "pass" => 'required|int',
            "result" => 'required|int|lte:total_questions',
            "total_questions" => 'required|int|gte:result',
        ];
    }

    public function messages() : array
    {
        return [
            'email_address.required' => __('Email address is required.'),
            'email_address.email' => __('Invalid email.'),
            'training_course_id.required' => __('Training course id is required.'),
            'pass.required' => __('Pass is required.'),
            'result.required' => __('Result is required.'),
            'total_questions.required' => __('Total questions required.'),
        ];
    }
}
