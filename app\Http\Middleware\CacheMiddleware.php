<?php

namespace App\Http\Middleware;
use Illuminate\Contracts\Auth\Factory as Auth;
use Illuminate\Support\Facades\Auth as Auth1;
use Closure;
use Illuminate\Support\Facades\Cache;

class CacheMiddleware {

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next,$guard='api') {
	//both line for testing purpose with redis caching

	if(env('CACHE_DRIVER')=='file'){

		$response = $next($request);
		return $response;
	}

	$cacheConfig = $this->getCacheConfig($request,$guard);
	$keyName = $cacheConfig['keyName'];
	$cacheTime = $cacheConfig['cacheTime'];
	$tagName = $cacheConfig['tagName'];
	if ($tagName != '') {
	    if (Cache::tags([$tagName])->has($keyName)) {
		$response = Cache::tags([$tagName])->get($keyName);
	    } else {
		$response = $next($request);
		Cache::tags([$tagName])->put($keyName, $response, $cacheTime);
	    }
	} else {
	    if ($keyName != '' && Cache::has($keyName)) {
		$response = Cache::get($keyName);
	    } else {
		$response = $next($request);
		Cache::put($keyName, $response, $cacheTime);
	    }
	}

	return $response;
    }

    private function getCacheConfig($request,$guard) {
	$authUser = Auth1::user();
	$user_id = isset($authUser) ? $authUser->id:NULL;
	$route = $request->route();
	$routeName = $route[1]['as'];
	$tagName = '';

	switch ($routeName) {
	    case in_array($routeName, ["users.profile", "users.getProfileDetails"]):
		///api/users/738
		$cacheTime = 60; //seconds
		$tagName = \cacheTag::APP_USER_PROFILE . ':' . $authUser->id;
		$keyName = \cacheTag::APP_USER_PROFILE . ':' . $authUser->id;
		if (!Cache::tags([$tagName])->has($keyName)) {
		    Cache::tags($tagName)->flush();
		}
		break;
	    // case "news.list":
		// ///api/news
		// $cacheTime = 60; //seconds
		// $tagName = \cacheTag::APP_NEWS_LIST . ':' . $authUser->user_relation->master_user_id;
		// $keyName = \cacheTag::APP_NEWS_LIST . ":" . $authUser->user_relation->master_user_id . '&' . $this->implodePostParam($request);
		// if (!Cache::tags([$tagName])->has($keyName)) {
		//     Cache::tags($tagName)->flush();
		// }
		// break;
	    case "trainingCourseSubmodule.getThreeSixtyUrl":
		///api/trainingCourseSubmodule/getThreeSixtyUrl/2
		$cacheTime = 60; //seconds
		$submoduleId = $route[2]['id'];
		$tagName = \cacheTag::APP_THREE_SIXTY_URL . ":" . $submoduleId;
		$keyName = \cacheTag::APP_THREE_SIXTY_URL . ":" . $submoduleId;
		if (!Cache::tags([$tagName])->has($keyName)) {
		    Cache::tags($tagName)->flush();
		}
		break;
	    case "trainingCourseSubmodule.getTitleSlide":
		///api/trainingCourseSubmodule/getTitleSlide/1
		$cacheTime = 60; //seconds
		$submoduleId = $route[2]['id'];
		$tagName = \cacheTag::APP_TITLE_SLIDE . ':' . $submoduleId;
		$keyName = \cacheTag::APP_TITLE_SLIDE . ":" . $submoduleId . '&' . $this->implodePostParam($request);
		if (!Cache::tags([$tagName])->has($keyName)) {
		    Cache::tags($tagName)->flush();
		}
		break;
	    // case "products.list":
		// ///api/products?page=1
		// $cacheTime = 60; //seconds
		// $user_relation = $authUser->user_relation;
		// $tagName = \cacheTag::APP_PRODUCTS_LIST . ":" . $user_relation->master_user_id;
		// $keyName = \cacheTag::APP_PRODUCTS_LIST . ":" . $user_relation->master_user_id . '&' . $this->implodePostParam($request);
		// if (!Cache::tags([$tagName])->has($keyName)) {
		//     Cache::tags($tagName)->flush();
		// }
		break;
	    case "trainingCourseSubmodule.getUploadVideo":
		///api/trainingCourseSubmodule/getUploadVideo/3
		$cacheTime = 60; //seconds
		$submoduleId = $route[2]['id'];
		$tagName = \cacheTag::APP_UPLOAD_VIDEO . ":" . $submoduleId;
		$keyName = \cacheTag::APP_UPLOAD_VIDEO . ":" . $submoduleId . '&' . $this->implodePostParam($request);
		if (!Cache::tags([$tagName])->has($keyName)) {
		    Cache::tags($tagName)->flush();
		}
		break;
	    case "trainingCourseSubmodule.getDocumentViewer":
		///api/trainingCourseSubmodule/getDocumentViewer/1730
		$cacheTime = 60; //seconds
		$submoduleId = $route[2]['id'];
		$tagName = \cacheTag::APP_DOCUMENT_VIEWER . ":" . $submoduleId;
		$keyName = \cacheTag::APP_DOCUMENT_VIEWER . ":" . $submoduleId . '&' . $this->implodePostParam($request);
		if (!Cache::tags([$tagName])->has($keyName)) {
		    Cache::tags($tagName)->flush();
		}
		break;
	    // case "trainingCourseSubmodule.imageGalleryList":
		// ///api/trainingCourseSubmodule/imageGalleryList/4
		// $cacheTime = 60; //seconds
		// $submoduleId = $route[2]['id'];
		// $tagName = \cacheTag::APP_IMAGE_GALLEY_LIST . ":" . $submoduleId;
		// $keyName = \cacheTag::APP_IMAGE_GALLEY_LIST . ":" . $submoduleId . '&' . $this->implodePostParam($request);
		// if (!Cache::tags([$tagName])->has($keyName)) {
		//     Cache::tags($tagName)->flush();
		// }
		// break;
	    // case "trainingCourseSubmodule.happyUnhappyList":
		// ///api/trainingCourseSubmodule/happyUnhappyList/6
		// $cacheTime = 60; //seconds
		// $submoduleId = $route[2]['id'];
		// $tagName = \cacheTag::APP_HAPPY_UNHAPPY_LIST . ":" . $submoduleId;
		// $keyName = \cacheTag::APP_HAPPY_UNHAPPY_LIST . ":" . $submoduleId . '&' . $this->implodePostParam($request);
		// if (!Cache::tags([$tagName])->has($keyName)) {
		//     Cache::tags($tagName)->flush();
		// }
		// break;
	    // case "trainingCourseSubmodule.videoGuideList":
		// ///api/trainingCourseSubmodule/videoGuideList/7
		// $cacheTime = 60; //seconds
		// $submoduleId = $route[2]['id'];
		// $tagName = \cacheTag::APP_VIDEO_GUIDE_LIST . ":" . $submoduleId;
		// $keyName = \cacheTag::APP_VIDEO_GUIDE_LIST . ":" . $submoduleId . '&' . $this->implodePostParam($request);
		// if (!Cache::tags([$tagName])->has($keyName)) {
		//     Cache::tags($tagName)->flush();
		// }
		// break;
	    // case "trainingCourseSubmodule.imageHotspotList":
		// ///api/trainingCourseSubmodule/imageHotspotList/700
		// $cacheTime = 60; //seconds
		// $submoduleId = $route[2]['id'];
		// $tagName = \cacheTag::APP_IMAGE_HOTSPOT_LIST . ":" . $submoduleId;
		// $keyName = \cacheTag::APP_IMAGE_HOTSPOT_LIST . ":" . $submoduleId . '&' . $this->implodePostParam($request);
		// if (!Cache::tags([$tagName])->has($keyName)) {
		//     Cache::tags($tagName)->flush();
		// }
		// break;
	    // case "trainingCourseSubmodule.confirmationBoxesList":
		// ///api/trainingCourseSubmodule/confirmationBoxesList/13
		// $cacheTime = 60; //seconds
		// $submoduleId = $route[2]['id'];
		// $tagName = \cacheTag::APP_CONFIRMATION_BOXES_LIST . ":" . $submoduleId;
		// $keyName = \cacheTag::APP_CONFIRMATION_BOXES_LIST . ":" . $submoduleId . '&' . $this->implodePostParam($request);
		// if (!Cache::tags([$tagName])->has($keyName)) {
		//     Cache::tags($tagName)->flush();
		// }
		// break;
	    // case "trainingCourseSubmodule.productList":/* Item List */
		// ///api/trainingCourseSubmodule/productList/5
		// $cacheTime = 60; //seconds
		// $submoduleId = $route[2]['id'];
		// $tagName = \cacheTag::APP_PRODUCTS_ITEMED_LIST . ":" . $submoduleId;
		// $keyName = \cacheTag::APP_PRODUCTS_ITEMED_LIST . ":" . $submoduleId . '&' . $this->implodePostParam($request);
		// if (!Cache::tags([$tagName])->has($keyName)) {
		//     Cache::tags($tagName)->flush();
		// }
		// break;
	    case "trainingCourseSubmodule.jobList":
		///api/trainingCourseSubmodule/jobList/12
		$cacheTime = 60; //seconds
		$submoduleId = $route[2]['id'];
		$tagName = \cacheTag::APP_JOB_LIST . ":" . $submoduleId;
		$keyName = \cacheTag::APP_JOB_LIST . ":" . $submoduleId . '&' . $this->implodePostParam($request);
		if (!Cache::tags([$tagName])->has($keyName)) {
		    Cache::tags($tagName)->flush();
		}
		break;
	    // case "trainingCourseSubmodule.getPracticalAssessment":
		// ///api/trainingCourseSubmodule/getPracticalAssessment/1817
		// $cacheTime = 60; //seconds
		// $submoduleId = $route[2]['id'];
		// $tagName = \cacheTag::APP_PRACTICAL_ASS . ":" . $submoduleId;
		// $keyName = \cacheTag::APP_PRACTICAL_ASS . ":" . $submoduleId . '&' . $this->implodePostParam($request);
		// if (!Cache::tags([$tagName])->has($keyName)) {
		//     Cache::tags($tagName)->flush();
		// }
		// break;
	    case "trainingCourseSubmodule.getConfirmation":
		///api/trainingCourseSubmodule/getConfirmation/9
		$cacheTime = 60; //seconds
		$submoduleId = $route[2]['id'];
		$tagName = \cacheTag::APP_GET_CONFIRMATION . ":" . $submoduleId;
		$keyName = \cacheTag::APP_GET_CONFIRMATION . ":" . $submoduleId . '&' . $this->implodePostParam($request);
		if (!Cache::tags([$tagName])->has($keyName)) {
		    Cache::tags($tagName)->flush();
		}
		break;
	    // case "trainingCourseSubmodule.pocList":
		// ///api/trainingCourseSubmodule/pocList/11
		// $cacheTime = 60; //seconds
		// $submoduleId = $route[2]['id'];
		// $tagName = \cacheTag::APP_POC_LIST . ":" . $submoduleId . ':' . $authUser->id;
		// $keyName = \cacheTag::APP_POC_LIST . ":" . $submoduleId . ':' . $authUser->id . '&' . $this->implodePostParam($request);
		// if (!Cache::tags([$tagName])->has($keyName)) {
		//     Cache::tags($tagName)->flush();
		// }
		// break;
	    // case "trainingCourseSubmodule.happyUnhappyDetail":
		// ///api/trainingCourseSubmodule/happyUnhappyDetail/1
		// $cacheTime = 60; //seconds
		// $id = $route[2]['id'];
		// $tagName = \cacheTag::APP_HAPPY_UNHPPY_DETAIL . ":" . $id;
		// $keyName = \cacheTag::APP_HAPPY_UNHPPY_DETAIL . ":" . $id . '&' . $this->implodePostParam($request);
		// if (!Cache::tags([$tagName])->has($keyName)) {
		//     Cache::tags($tagName)->flush();
		// }
		// break;
	    case "trainingCourseSubmodule.uploadJobDetail":
		///api/trainingCourseSubmodule/uploadJobDetail/188
		$cacheTime = 60; //seconds
		$id = $route[2]['id'];
		$tagName = \cacheTag::APP_UPLOAD_JOB_DETAIL . ":" . $id . ":" . $authUser->id;
		$keyName = \cacheTag::APP_UPLOAD_JOB_DETAIL . ":" . $id . ":" . $authUser->id . '&' . $this->implodePostParam($request);
		if (!Cache::tags([$tagName])->has($keyName)) {
		    Cache::tags($tagName)->flush();
		}
		break;
	    // case "trainingCourseSubmodule.imageGalleryDetail":
		// ///api/trainingCourseSubmodule/imageGalleryDetail/1
		// $cacheTime = 60; //seconds
		// $id = $route[2]['id'];
		// $tagName = \cacheTag::APP_IMAGE_GALLEY_DETAIL . ":" . $id;
		// $keyName = \cacheTag::APP_IMAGE_GALLEY_DETAIL . ":" . $id . '&' . $this->implodePostParam($request);
		// if (!Cache::tags([$tagName])->has($keyName)) {
		//     Cache::tags($tagName)->flush();
		// }
		// break;
	    // case "trainingCourseSubmodule.videoGuideStepsList":
		// ///api/trainingCourseSubmodule/videoGuideStepsList/1 *
		// $cacheTime = 60; //seconds
		// $id = $route[2]['id'];
		// $tagName = \cacheTag::APP_VIDEO_GUIDE_STEPS_LIST . ":" . $id;
		// $keyName = \cacheTag::APP_VIDEO_GUIDE_STEPS_LIST . ":" . $id . '&' . $this->implodePostParam($request);
		// if (!Cache::tags([$tagName])->has($keyName)) {
		//     Cache::tags($tagName)->flush();
		// }
		// break;
	    // case "trainingCourseSubmodule.videoGuideStepDetail":
		// ///api/trainingCourseSubmodule/videoGuideStepDetail/1 *
		// $cacheTime = 60; //seconds
		// $id = $route[2]['id'];
		// $tagName = \cacheTag::APP_VIDEO_GUIDE_STEP_DETAIL . ":" . $id;
		// $keyName = \cacheTag::APP_VIDEO_GUIDE_STEP_DETAIL . ":" . $id . '&' . $this->implodePostParam($request);
		// if (!Cache::tags([$tagName])->has($keyName)) {
		//     Cache::tags($tagName)->flush();
		// }
		// break;
	    // case "trainingCourseSubmodule.productDetail":
		// ///api/trainingCourseSubmodule/productDetail/1
		// $cacheTime = 60; //seconds
		// $id = $route[2]['id'];
		// $tagName = \cacheTag::APP_PRODUCT_DETAIL . ":" . $id;
		// $keyName = \cacheTag::APP_PRODUCT_DETAIL . ":" . $id . '&' . $this->implodePostParam($request);
		// if (!Cache::tags([$tagName])->has($keyName)) {
		//     Cache::tags($tagName)->flush();
		// }
		// break;
	    case "resources.show":
		///api/resources/3
		$cacheTime = 60; //seconds
		$id = $route[2]['id'];
		$tagName = \cacheTag::APP_RESOURCES_SHOW . ":" . $id;
		$keyName = \cacheTag::APP_RESOURCES_SHOW . ":" . $id . '&' . $this->implodePostParam($request);
		if (!Cache::tags([$tagName])->has($keyName)) {
		    Cache::tags($tagName)->flush();
		}
		break;
	    case "products.show":
		///api/products/47
		$cacheTime = 60; //seconds
		$id = $route[2]['id'];
		$tagName = \cacheTag::APP_PRODUCTS_SHOW . ":" . $id;
		$keyName = \cacheTag::APP_PRODUCTS_SHOW . ":" . $id . '&' . $this->implodePostParam($request);
		if (!Cache::tags([$tagName])->has($keyName)) {
		    Cache::tags($tagName)->flush();
		}
		break;
	    case "products.getFilters":
		///api/products/getFilters
		$cacheTime = 60; //seconds
		$tagName = \cacheTag::APP_PRODUCTS_GET_FILTERS;
		$keyName = \cacheTag::APP_PRODUCTS_GET_FILTERS . '&' . $this->implodePostParam($request);
		if (!Cache::tags([$tagName])->has($keyName)) {
		    Cache::tags($tagName)->flush();
		}
		break;
	    case "news.show":
		///api/news/49 *
		$cacheTime = 60; //seconds
		$id = $route[2]['id'];
		$tagName = \cacheTag::APP_NEWS_SHOW . ':' . $id;
		$keyName = \cacheTag::APP_NEWS_SHOW . ':' . $id . '&' . $this->implodePostParam($request);
		if (!Cache::tags([$tagName])->has($keyName)) {
		    Cache::tags($tagName)->flush();
		}
		break;
	    case "users.contactUs":
		///api/news/49
		$cacheTime = 60; //seconds
		$id = $authUser->id;
		$tagName = \cacheTag::APP_CONTACT_US;
		$keyName = \cacheTag::APP_CONTACT_US . '&' . $this->implodePostParam($request);
		if (!Cache::tags([$tagName])->has($keyName)) {
		    Cache::tags($tagName)->flush();
		}
		break;
	    case "cmsPages":
		///api/cmsPages/about_us
		$cacheTime = 60; //seconds
		$slug = $route[2]['slug'];
		$tagName = \cacheTag::APP_CMS_PAGES . ':' . $slug;
		$keyName = \cacheTag::APP_CMS_PAGES . ':' . $slug . '&' . $this->implodePostParam($request);
		if (!Cache::tags([$tagName])->has($keyName)) {
		    Cache::tags($tagName)->flush();
		}
		break;
	    case "getDomainSuggestions":
		///api/getDomainSuggestions
		$cacheTime = 60; //seconds
		$tagName = \cacheTag::APP_DOMAIN_SUGGESTIONS;
		$keyName = \cacheTag::APP_DOMAIN_SUGGESTIONS . '&' . $this->implodePostParam($request);
		if (!Cache::tags([$tagName])->has($keyName)) {
		    Cache::tags($tagName)->flush();
		}
		break;
	    case "operator.getPermissions":
		///operator/getPermissions
		$cacheTime = 120; //seconds
		$tagName = \cacheTag::GET_PERMISSIONS . ':' . $authUser->role_id;
		$keyName = \cacheTag::GET_PERMISSIONS . ':' . $authUser->role_id . '&' . $this->implodePostParam($request);
		if (!Cache::tags([$tagName])->has($keyName)) {
		    Cache::tags($tagName)->flush();
		}
		break;
	    // case "admin.getPermissions":
		// ///admin/getPermissions
		// $cacheTime = 120; //seconds
		// $tagName = \cacheTag::GET_PERMISSIONS . ':' . $authUser->role_id;
		// $keyName = \cacheTag::GET_PERMISSIONS . ':' . $authUser->role_id . '&' . $this->implodePostParam($request);
		// if (!Cache::tags([$tagName])->has($keyName)) {
		//     Cache::tags($tagName)->flush();
		// }
		// break;
	    default:
		$cacheTime = 0; // seconds
		$keyName = '';
	}
	return [
	    'keyName' => $keyName,
	    'cacheTime' => $cacheTime,
	    'tagName' => $tagName
	];
	//pending
	///api/users/operatorList
	///api/loginWithEmailLink
    }

    private function implodePostParam($request) {
	if (!empty($request->all())) {
	    $queryParam = http_build_query(array_map(function ($v) {
			if (is_array($v)) {
			    return implode(',', $v);
			} else {
			    return $v;
			}
		    }, $request->all()));
	    return $queryParam;
	}
	return '';
    }

}
