<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCompletedDateInTrainingCourseProgressTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $tableName = 'training_course_progress';
        if (Schema::hasColumn($tableName, 'complete_date')) {
            // Column already exists
            return;
        }
        Schema::table($tableName, function (Blueprint $table) {
            $table->timestamp('complete_date')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        $tableName = 'training_course_progress'; 
        if (Schema::hasColumn($tableName, 'complete_date')) {
            Schema::table($tableName, function (Blueprint $table) {
                $table->dropColumn('complete_date');
            });
        }
    }
}
