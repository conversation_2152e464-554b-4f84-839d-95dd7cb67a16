<?php

namespace App\Http\Controllers\Admin\v1;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Repositories\Admin\v1\EmailTemplatesRepository;
use App\Http\Resources\Admin\v1\EmailTemplatesResource;
use App\Http\Requests\Admin\v1\EmailTemplatesRequest;
use App\Models\EmailTemplates;
use Illuminate\Http\Response;
use App\Http\Resources\CustomCollection;

class EmailTemplatesController extends Controller
{

    private $model;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->model = new EmailTemplates();
        $this->repository = new EmailTemplatesRepository($this->model);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        //
    }

    /**
     * Get Email Templates List
     *
     * @return \Illuminate\Http\Response
     */
    public function getEmailTemplateList(Request $request)
    {
        try {
            $perPage = isset($request->per_page) ? $request->per_page : 10;
            $emailTemp = EmailTemplates::select('id','template_title','email_subject','email_content','updated_at');

            //Sorting
            if($request->has('sort_by')){
                $sortBy = json_decode($request->input('sort_by'),true);
                                
                $emailTemp = (
                    (isset($sortBy['template_title']) && $sortBy['template_title'] == 1) ? 
                        $emailTemp->orderBy('template_title') : (
                            (isset($sortBy['template_title']) && $sortBy['template_title'] == -1) ? 
                                $emailTemp->orderByDesc('template_title') : (
                                    (isset($sortBy['updated_at']) && $sortBy['updated_at'] == 1) ?
                                        $emailTemp->orderBy('updated_at') : (
                                            (isset($sortBy['updated_at']) && $sortBy['updated_at'] == -1) ?
                                                $emailTemp->orderByDesc('updated_at') : 
                                                $emailTemp->orderByDesc('id')
                                        )
                                )
                            ) 
                    );
            }
            else{
                $emailTemp =  $emailTemp->orderByDesc('id');
            }
            $emailTemp = $emailTemp->paginate($perPage);
            return new CustomCollection($emailTemp, 'App\Http\Resources\Admin\v1\EmailTemplatesResource');
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Add Email Templates
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(EmailTemplatesRequest $request)
    {
        try {
            $result = DB::transaction(function () use ($request) {
                $moduleData = $request;
                $this->repository->create($moduleData);
            });
            return response()->json(setResponse([], ['message' => __('admin.emailTemplates.add')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get  Email Templates Details
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $emailTemplateDetail = new EmailTemplatesResource(EmailTemplates::findOrFail($id));
            $message = !empty($emailTemplateDetail) ? __('admin.emailTemplates.found') : __('admin.emailTemplates.notFound');
            return response()->json(setResponse($emailTemplateDetail, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update  Email Templates Details
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(EmailTemplatesRequest $request, $id)
    {
        try {
            $result = DB::transaction(function () use ($request, $id) {
                $moduleData = $request->all();
                $emailTemplateDetail = EmailTemplates::find($id);
                $emailTemplateDetail->update($moduleData);
            });
            return response()->json(setResponse([], ['message' => __('admin.emailTemplates.update')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Delete  Email Templates
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            EmailTemplates::destroy($id);
            return response()->json(setResponse([], ['message' => __('admin.emailTemplates.delete')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
