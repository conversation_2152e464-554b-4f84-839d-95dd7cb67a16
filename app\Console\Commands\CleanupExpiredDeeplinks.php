<?php

namespace App\Console\Commands;

use App\Models\CustomDeeplink;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CleanupExpiredDeeplinks extends Command
{
    protected $signature = 'deeplinks:cleanup {--dry-run} {--batch-size=1000} {--days=365}';
    protected $description = 'Clean up expired and old unused deeplinks';

    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        $batchSize = (int) $this->option('batch-size');
        $daysOld = (int) $this->option('days');

        $this->info('Starting deeplink cleanup...');
        
        if ($isDryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        // Clean up expired deeplinks
        $expiredCount = $this->cleanupExpiredDeeplinks($isDryRun, $batchSize);
        
        // Clean up old unused deeplinks
        $oldUnusedCount = $this->cleanupOldUnusedDeeplinks($isDryRun, $batchSize, $daysOld);
        
        // Clean up inactive deeplinks
        $inactiveCount = $this->cleanupInactiveDeeplinks($isDryRun, $batchSize);

        $this->info('Cleanup completed!');
        $this->info("Expired deeplinks: {$expiredCount}");
        $this->info("Old unused deeplinks: {$oldUnusedCount}");
        $this->info("Inactive deeplinks: {$inactiveCount}");
        
        return 0;
    }

    private function cleanupExpiredDeeplinks(bool $isDryRun, int $batchSize): int
    {
        $this->info('Cleaning up expired deeplinks...');
        
        $query = CustomDeeplink::where('expires_at', '<', now());
        $count = $query->count();
        
        $this->info("Found {$count} expired deeplinks");
        
        if (!$isDryRun && $count > 0) {
            $deleted = 0;
            $query->chunk($batchSize, function ($deeplinks) use (&$deleted) {
                foreach ($deeplinks as $deeplink) {
                    try {
                        $deeplink->delete();
                        $deleted++;
                    } catch (\Exception $e) {
                        Log::error('Failed to delete expired deeplink', [
                            'deeplink_id' => $deeplink->id,
                            'short_code' => $deeplink->short_code,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            });
            
            $this->info("Deleted {$deleted} expired deeplinks");
            return $deleted;
        }
        
        return $count;
    }

    private function cleanupOldUnusedDeeplinks(bool $isDryRun, int $batchSize, int $daysOld): int
    {
        $this->info("Cleaning up deeplinks older than {$daysOld} days with no clicks...");
        
        $cutoffDate = now()->subDays($daysOld);
        
        $query = CustomDeeplink::where('created_at', '<', $cutoffDate)
            ->where('click_count', 0);
            
        $count = $query->count();
        
        $this->info("Found {$count} old unused deeplinks");
        
        if (!$isDryRun && $count > 0) {
            $deleted = 0;
            $query->chunk($batchSize, function ($deeplinks) use (&$deleted) {
                foreach ($deeplinks as $deeplink) {
                    try {
                        $deeplink->delete();
                        $deleted++;
                    } catch (\Exception $e) {
                        Log::error('Failed to delete old unused deeplink', [
                            'deeplink_id' => $deeplink->id,
                            'short_code' => $deeplink->short_code,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            });
            
            $this->info("Deleted {$deleted} old unused deeplinks");
            return $deleted;
        }
        
        return $count;
    }

    private function cleanupInactiveDeeplinks(bool $isDryRun, int $batchSize): int
    {
        $this->info('Cleaning up inactive deeplinks...');
        
        $query = CustomDeeplink::where('is_active', false);
        $count = $query->count();
        
        $this->info("Found {$count} inactive deeplinks");
        
        if (!$isDryRun && $count > 0) {
            $deleted = 0;
            $query->chunk($batchSize, function ($deeplinks) use (&$deleted) {
                foreach ($deeplinks as $deeplink) {
                    try {
                        $deeplink->delete();
                        $deleted++;
                    } catch (\Exception $e) {
                        Log::error('Failed to delete inactive deeplink', [
                            'deeplink_id' => $deeplink->id,
                            'short_code' => $deeplink->short_code,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            });
            
            $this->info("Deleted {$deleted} inactive deeplinks");
            return $deleted;
        }
        
        return $count;
    }
}
