<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\TrainingCourse;
use App\Models\TrainingCourseSubmoduleDetails;
use Carbon\Carbon;


class UnlockSubModuleCron extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'unlock:submodule';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command is for send notification to user on unlock submodule';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        
        $submodules = TrainingCourseSubmoduleDetails::where('submodule_lock',1)
                                        ->where('status','Active')
                                        ->where('notify_users',1)
                                        ->get();
        if($submodules->count() > 0){
            foreach ($submodules as $key => $submod) {   
                
                /*When the job runs, they will see the following data in the job header*/
                $headerData = [
                    'guard' => 'operator',
                    'user_id' => $submod->trainingCourse->master_user_id??null,
                    'extra' => '',
                ];
                app('queue')->createPayloadUsing(function () use ($headerData) {
                    return ['headerData' => $headerData];
                });
                $moduleNotificationJob = (new \App\Jobs\SubModuleUnlockUserNotificationJob($submod));
                dispatch($moduleNotificationJob);
            }
        }
    }
}
