<?php
/**
     * @OA\Get(
     *     path="/api/news",
     *     tags={"Mobile - News"},
     *     summary="News Listing",
     *     description="News Listing",
     *     operationId="index",
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */

/**
     * @OA\Get(
     *     path="/api/news/{id}",
     *     tags={"Mobile - News"},
     *     summary="News Details",
     *     description="News Details",
     *     operationId="show",
     *     @OA\Parameter(
     *         description="Id of news to fetch",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */