<?php
/**
     * @OA\Post(
     *     path="/api/trainingCourseSubmodule/submitCourseFeedback/",
     *     tags={"Mobile - Training Course Submodule"},
     *     summary="Submit Training Course Feedback",
     *     description="Submit Training Course Feedback",
     *     operationId="submitCourseFeedback",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="module_id",
     *                     description="Module Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="submodule_id",
     *                     description="Submodule Id",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="answer_json",
     *                     description="Answer JSON",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="time_spent",
     *                     description="Time Spent",
     *                     type="string"
     *                 ), 
     *                 example={"training_course_id": 7, "module_id": 7, "submodule_id": 861, "answer_json": "[{'question_id':12,'answer':""},{'question_id':13,'answer':'2.5'},{'question_id':14,'answer':'12'},{'question_id':15,'answer':'16,17'}]","time_spent":5}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */