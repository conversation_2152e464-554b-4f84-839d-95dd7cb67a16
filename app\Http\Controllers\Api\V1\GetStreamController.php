<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\User;
use App\Helpers\Helper;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use GetStream\StreamChat\Client;
use App\Http\Controllers\Controller;

class GetStreamController extends Controller
{

    /**
     * Display a listing of the resources.
     *
     * @return \Illuminate\Http\Response
     */
    public function GetStreamToken(Request $request)
    {
        if(env('GETSTREAM_SERVER')=='development')
        {
            $prefix='dev_user_';
        }else if(env('GETSTREAM_SERVER')=='staging'){
            $prefix='staging_user_';
        }else{
            $prefix='user_';
        }
        $client = new Client(env('GETSTREAM_API_KEY'), env('GETSTREAM_API_SECRET_KEY'));
            $data = [
                'id' => $prefix.$request->id,
                'role' => $request->role,
                'name' => $request->name,
                'email' => $request->email,
            ];
            $data = $client->upsertUser($data);
            $token = $client->createToken($prefix.$request->id);
            print_r($token);
    }

    public function DeleteGetStreamUSer(Request $request)
    {
        $client = new Client(env('GETSTREAM_API_KEY'), env('GETSTREAM_API_SECRET_KEY'));
        // $record= User::select('id','get_stream_user_id')->where('get_stream_token','!=',"")->get()->toArray();
        $record= User::select('id','get_stream_user_id')->where('get_stream_user_id','!=',"1")->get()->toArray();
        foreach($record as $value){
           $data= $client->deactivateUser($value['get_stream_user_id'], [
            "mark_messages_deleted" => true,
          ]);die;
           User::where('id', $value['id'])->update(['get_stream_user_id'=>'1','get_stream_token'=>'']);
        }
    }
}
