<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ProductType;
use App\Models\MasterUser;

class UpdateResourceTypeDisplayOrder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:resource-type-display-order';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update recource type display order first time.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $operatorList=MasterUser::select('id')->whereNull('parent_id')->where('user_type','Operator')->get()->toArray();
        foreach($operatorList as $operatorId){
            $allNextProductTypes = ProductType::whereMasterUserId($operatorId)->orderBy('id','asc')->get();
            if (!$allNextProductTypes->isEmpty()) {
                foreach ($allNextProductTypes as $key => $productTypeData) {
                    $productTypeData->display_order = $key+1;
                    $productTypeData->save();
                }
            }
        }
    }
}
