<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\App;

class RecaptchaVerification
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @param string $guard
     * @return mixed
     */
    public function handle($request, Closure $next, $guard = null)
    {
        if(($request->getPathInfo() == '/v1/operator/login' || $request->getPathInfo() == '/v1/api/oauth/web/login') && $request->has('server_secret')){
            if($request->server_secret == env('CAPTCHA_BYPASS_SECRET')){
                $request->attributes->set('captcha_bypassed', true); // Set bypass flag
                return $next($request);
            } else {
                return response()->json(setErrorResponse('Invalid Secret Key'))
                            ->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        }
        if(str_contains(App::make('url')->to('/'),'8000') || str_contains(App::make('url')->to('/'),'localhost')){
            return $next($request);
        }
        $secret = env("CAPTCHA_SECRET");
        $captchaResponse = request()->get("captcha_response");
        $url = "https://www.google.com/recaptcha/api/siteverify";
        $client = new \GuzzleHttp\Client();
        $response = $client->request('POST', $url, ['query' => [
            'secret' => $secret,
            'response' => $captchaResponse,
        ]]);
        $captchaBody = $response->getBody()->getContents();
        $captchaBodyArray = json_decode($captchaBody,true);
        if($captchaBodyArray["success"]){
            return $next($request);
        }else{
            return response()->json($captchaBodyArray)
                            ->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                            // return response()->json(setErrorResponse("Captcha verification failed."))
                            // ->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }

    }
}
