<?php

namespace App\Console\Commands;

use App\Models\News;
use App\Models\Product;
use App\Models\Resources;
use App\Models\UserGroup;
use App\Models\MasterUser;
use App\Models\ProductType;
use App\Models\UserRelation;
use App\Models\TrainingCourse;
use Illuminate\Console\Command;
use App\Models\DashBoardStatics;
use App\Models\TrainingCourseModules;

class GenerateDashBoardStatics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dashboard:statics';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate DashBoard Statics';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $operatorList=MasterUser::select('id')->whereNull('parent_id')->where('user_type','Operator')->get()->toArray();
        
        foreach($operatorList as $operatorId){
            $newUser = DashBoardStatics::updateOrCreate([
                'operator_id'   => $operatorId['id'] ?? null,
            ],[
                'resources'     => Resources::statistics($operatorId['id']) ?? null,
            ]);
        }
        
        foreach($operatorList as $operatorId){
            $newUser = DashBoardStatics::updateOrCreate([
                'operator_id'   => $operatorId['id'] ?? null,
            ],[
                'products'     => Product::statistics($operatorId['id']) ?? null,
                'news'     => News::statistics($operatorId['id']) ?? null,
                'groups'     => UserGroup::statistics($operatorId['id']) ?? null,
                'modules'     => TrainingCourseModules::statistics($operatorId['id']) ?? null,
                'courses'     => TrainingCourse::statistics($operatorId['id']) ?? null,
                'users'     => UserRelation::statistics($operatorId['id']) ?? null
            ]);
        }

    }
}
