<?php

namespace App\Repositories\Operator\v1;

use DB;
use DBTableNames;
use App\Models\User;
use App\Models\UserGroup;
use App\Models\MasterUser;
use App\Models\UserRelation;
use App\Models\TrainingCourse;
use App\Repositories\Repository;
use App\Models\AssessorAssignUser;
use App\Services\GetStreamService;
use App\Models\AssignTrainingCourses;
use App\Models\AssignUserInviteLinks;
use Illuminate\Support\Facades\Password;
use App\Models\UserAssignTrainingCourses;
use App\Notifications\TeamMemberResendPasswordNotification;
use App\Services\DeeplinkService;

class FrontUserRepository extends Repository
{

    protected $model;
    public function __construct(User $model)
    {
        $this->deeplinkService =new DeeplinkService();
        $this->model = $model;
        $this->table = $this->model->getTable();
    }

    public function getUserlist($data)
    {
        $term = $data->term ?? "";
        $sort = $data->sortBy ?? 'id';
        $order = $data->orderBy ?? 'desc';
        $perPage = $data->perPage ?? 10;

        $users = $this->model->select('id', 'first_name', 'last_name', 'email', 'country', 'mobile', 'status')
            ->with(['country_name', 'state_name', 'city_name']);
        if ($term) {
            $users->whereLike(['first_name', 'last_name', 'email', 'country', 'mobile'], $term);
        }
        return $users->orderBy($sort, $order)->paginate($perPage);
    }

    // create a new record in the database
    public function create($request)
    {
        $data = $request->all();
        $operator = getOperator();
        $master_user_id = $operator->id;
        $user_type = $operator->user_type;

        if ($data['step'] == 1) {
            //check if user already registered
            $userAlreadyRegistered = $this->model->whereEmail($data['email'])->first();

            $userRelationExist = '';
            if ($userAlreadyRegistered) {

                $user = $userAlreadyRegistered;
                $userRelationExist = UserRelation::whereMasterUserId($master_user_id)->whereUserId($user->id)->first();

                // Mobile Notification
                $message = auth()->guard('operator')->user()->name . " Operator registered you as a user successfully";
                $mutable = 1;
                $payload['type'] = "Added";
                $payload['title'] = $message;
                $payload['description'] = $message;
                $payload['master_user_id'] = auth()->guard('operator')->user()->id;
                $payload['image'] = auth()->guard('operator')->user()->image_url;
                $notificationData = [
                    'alert' => $message,
                    "mutable-content" => $mutable,
                    'data' => $payload,
                    'sound' => 'default',
                ];
                $extra['user_id'] = $user->id;
                $extra['master_user_id'] = auth()->guard('operator')->user()->id;
                $extra['type'] = 'Added';
                $extra['notification_status'] = 'Extra';
                // Dispatch Operator added user Notification Job
                $userAddNotificationJob = (new \App\Jobs\UserAddNotificationJob($userAlreadyRegistered, $notificationData, $extra))->delay(3);
                dispatch($userAddNotificationJob);
            } else {
                $data = $request->all();
                $data['added_user_id'] = $master_user_id;
                $data['added_by'] = $user_type;
                if (!empty($data['password'])) {
                    $password = trim($data['password']);
                } else {
                    $password = getRandomPassword();
                }
                $data['password'] = bcrypt($password);

                $data['device_token'] = "";
                $data['email_verified_at'] = date('Y-m-d H:i:s');
                $data['is_password_change'] = 1;
                $user = $this->model->create($data);
                $this->QRCodeGenerate($user);
                $token = Password::getRepository()->create($user);
                //Checking for email address if smartawards contains only digits before @
                $smartAwardsMailSendFlag = smartAwardsEmailSendResctrict($user->email);
                if($smartAwardsMailSendFlag == 1){
                    $user->sendCreatePasswordNotification($token, "Create");
                }
                // event(new \App\Events\UserRegisteredByOperatorEvent($user, $password));
                // Skillbase original
                $skillbaseDefault = MasterUser::whereUserType('Operator')->whereNotNull('managed_by')->first();
                if ($skillbaseDefault->enable_manager_email == 0 && $skillbaseDefault->enable_unique_id == 0) {
                    $exists = UserRelation::where('user_id',$user->id)->where('master_user_id',$skillbaseDefault->id)->first();
                    if(empty($exists)){
                        UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseDefault->id, 'is_current_operator' => 0, 'manager_id' => $skillbaseDefault->id, 'manager_email' => $skillbaseDefault->email, 'unique_id' => $skillbaseDefault->unique_id]);
                    }
                } elseif ($skillbaseDefault->enable_manager_email == 0) {
                    $exists = UserRelation::where('user_id',$user->id)->where('master_user_id',$skillbaseDefault->id)->first();
                    if(empty($exists)){
                        UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseDefault->id, 'is_current_operator' => 0, 'manager_id' => $skillbaseDefault->id, 'manager_email' => $skillbaseDefault->email]);
                    }
                } elseif ($skillbaseDefault->enable_unique_id == 0) {
                    $exists = UserRelation::where('user_id',$user->id)->where('master_user_id',$skillbaseDefault->id)->first();
                    if(empty($exists)){
                        UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseDefault->id, 'is_current_operator' => 0, 'unique_id' => $skillbaseDefault->unique_id]);
                    }
                } else {
                    $exists = UserRelation::where('user_id',$user->id)->where('master_user_id',$skillbaseDefault->id)->first();
                    if(empty($exists)){
                        UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseDefault->id, 'is_current_operator' => 0]);
                    }
                }
                // Skillbase original Course Assignment
                $allDefaulCourses = TrainingCourse::whereMasterUserId($skillbaseDefault->id)->get();
                if ($allDefaulCourses->count() > 0) {
                    foreach ($allDefaulCourses as $course) {
                        UserAssignTrainingCourses::create([
                            'user_id' => $user->id,
                            'master_user_id' => $skillbaseDefault->id,
                            'training_course_id' => $course->id,
                        ]);
                    }
                }
                // Create default Notification
                createDefaultUserNotifications($user);

            }
            if (isset($data['manager_email'])) {
                $relData['manager_email'] = $data['manager_email'];
                $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
                $managerData = MasterUser::whereEmail($data['manager_email'])->where(function ($query) use ($operatorId) {
                    $query->where('parent_id', '=', $operatorId)
                        ->orWhere('id', '=', $operatorId);
                })->first();
                if ($managerData) {
                    $relData['manager_id'] = $managerData->id;
                } else {
                    $relData['response_type'] = "Error";
                    $relData['message'] = __('operator.FrontUser.managerEmail');
                    return $relData;
                }
            }
            if (isset($data['unique_id'])) {
                $relData['unique_id'] = $data['unique_id'];
                $operatorUniqueIdName = $operator->unique_id_name;
                $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
                $managerData = MasterUser::whereUniqueId($data['unique_id'])->where(function ($query) use ($operatorId) {
                    $query->where('parent_id', '=', $operatorId)
                        ->orWhere('id', '=', $operatorId);
                })->first();

                if ($managerData) {
                    $relData['manager_id'] = $managerData->id;
                } else {
                    $relData['response_type'] = "Error";
                    $relData['message'] = $operatorUniqueIdName ? $operatorUniqueIdName . ' not found.' : 'Unique Id not found.';
                    return $relData;
                }
            }
            if (isset($data['user_group_id'])) {
                $relData['user_group_id'] = $data['user_group_id'];
            } else {
                $relData['user_group_id'] = null;
            }

            // check if record in user relation table exist
            if (isset($relData) && !empty($relData)) {
                if ($userRelationExist) {
                    $userRelationExist->update($relData);
                } else {
                    if (!isset($relData['unique_id'])) {
                        $relData['unique_id'] = auth()->guard('operator')->user()->unique_id;
                    }
                    if (!isset($relData['manager_email'])) {
                        $relData['manager_email'] = auth()->guard('operator')->user()->email;
                    }
                    $relData['manager_id'] = auth()->guard('operator')->user()->id;
                    $relData['user_id'] = $user['id'];
                    $relData['master_user_id'] = $master_user_id;
                    $isDefaultCheck = UserRelation::whereUserId($user['id'])->whereIsCurrentOperator(1)->first();
                    if ($isDefaultCheck) {
                        $relData['is_current_operator'] = 0;
                    } else {
                        $relData['is_current_operator'] = 1;
                    }
                    $exists = UserRelation::where('user_id',$user['id'])->where('master_user_id',$master_user_id)->first();
                    if(empty($exists)){
                        UserRelation::create($relData);
                    }else{
                        $exists->update(['is_current_operator'=>$relData['is_current_operator'],'user_group_id'=>$relData['user_group_id']]);
                    }
                }
            } else {
                if (!isset($relData['unique_id'])) {
                    $relData['unique_id'] = auth()->guard('operator')->user()->unique_id;
                }
                if (!isset($relData['manager_email'])) {
                    $relData['manager_email'] = auth()->guard('operator')->user()->email;
                }
                $relData['manager_id'] = auth()->guard('operator')->user()->id;
                $relData['user_id'] = $user['id'];
                $relData['master_user_id'] = $master_user_id;
                $isDefaultCheck = UserRelation::whereUserId($user['id'])->whereIsCurrentOperator(1)->first();
                if ($isDefaultCheck) {
                    $relData['is_current_operator'] = 0;
                } else {
                    $relData['is_current_operator'] = 1;
                }
                $exists = UserRelation::where('user_id',$user['id'])->where('master_user_id',$master_user_id)->first();
                if(empty($exists)){
                    UserRelation::create($relData);
                }else{
                    $exists->update(['is_current_operator'=>$relData['is_current_operator'],'user_group_id'=>$relData['user_group_id']]);
                }
            }
            $allDefaultCourses = TrainingCourse::whereMasterUserId($master_user_id)->whereIsDefault(1)->get();
            $allUsers = UserRelation::whereMasterUserId($master_user_id)->whereUserId($user->id)->get();

            if ($allDefaultCourses->count() > 0) {
                foreach ($allDefaultCourses as $key => $defaultCourse) {
                    if ($allUsers->count() > 0) {
                        foreach ($allUsers as $key => $defaultUser) {
                            UserAssignTrainingCourses::create([
                                'user_id' => $user->id,
                                'training_course_id' => $defaultCourse->id,
                                'master_user_id' => $master_user_id,
                                'is_default_course' => 1,
                                'is_manual_course' => 0,
                            ]);
                        }
                    }
                }
            }
            if (isset($data['user_group_id']) && !empty($data['user_group_id'])) {
                $groupCourse = [];
                $groupCourseIds = [];
                $getCurrentGroupCourses = AssignTrainingCourses::whereMasterUserId($master_user_id)->whereUserGroupId($data['user_group_id'])->get()->pluck('training_course_id')->toArray();
                if (count($getCurrentGroupCourses) > 0) {
                    $groupCourseIds = array_values($getCurrentGroupCourses);
                }
                if (count($groupCourseIds) > 0) {
                    $userDetails = $this->model->find($user->id);
                    dispatch((new \App\Jobs\CourseNotificationJob($userDetails, $master_user_id, $groupCourseIds)));
                }
            }
            return $user;
        }

        if ($data['step'] == 2) {
            $training_course_ids = $request->trainingCourseIds;
            $user_id = $request->userId;
            if (count($training_course_ids) > 0) {
                //delete existing assign course
                UserAssignTrainingCourses::where(['user_id' => $user_id, 'master_user_id' => $master_user_id])->delete();
                $$courseIds = [];
                foreach ($training_course_ids as $key => $courseId) {
                    $newData = [];
                    $newData['master_user_id'] = $master_user_id;
                    $newData['user_id'] = $user_id;
                    $newData['training_course_id'] = $courseId;
                    UserAssignTrainingCourses::create($newData);
                    $$courseIds[] = $courseId;
                }
                $userDetails = $this->model->find($user_id);
                dispatch((new \App\Jobs\CourseNotificationJob($userDetails, $master_user_id, $courseIds)));
            }
            return $group = $this->model->find($user_id);
        }

        if ($data['step'] == 3) {

            $id = $request->userId;
            $reldtl = UserRelation::whereMasterUserId($master_user_id)->whereUserId($id)->first();
            $data = $request->except(['userId', 'step']);
            $reldtl->update($data);
            return $reldtl;
        }

    }

    public function update($request, $id)
    {
        $data = $request->all();
        $operator = getOperator();
        $master_user_id = $operator->id;
        $user = $this->model->find($id);
        $user_id = $id;
        if ($data['step'] == 1) {
            // checking for assessment portal feature setting
            $checkAssessment = checkAssessment();
            if($checkAssessment == 1){
                $user->update($request->only(['name', 'email', 'status']));
            }else{
                $user->update($request->only(['name', 'email', 'status', 'assessor_role']));
                if ($data['assessor_role'] == 'Yes') {
                    $this->QRCodeGenerate($user);
                }
            }

            if (!empty($data['password'])) {
                $password = $data['password'];
                $data['password'] = bcrypt($password);
                $user->update(['password' => $data['password']]);
		if ($user->mfa_user_id != '' && $user->mfa_user_id != null) {
		    $mfaConfig = new \App\Http\Controllers\Api\V1\MfaConfigurationController();
		    $mfaConfig->adminSetUserPassword($user->mfa_user_id, $password);
		}
            }

            if (isset($data['manager_email'])) {
                $relData['manager_email'] = $data['manager_email'];
                $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
                $managerData = MasterUser::whereEmail($data['manager_email'])->where(function ($query) use ($operatorId) {
                    $query->where('parent_id', '=', $operatorId)
                        ->orWhere('id', '=', $operatorId);
                })->first();
                if ($managerData) {
                    $relData['manager_id'] = $managerData->id;
                } else {
                    $relData['response_type'] = "Error";
                    $relData['message'] = __('operator.FrontUser.managerEmail');
                    return $relData;
                }
            }else{
                $relData['manager_email'] = null;
            }

            if (isset($data['unique_id'])) {
                $relData['unique_id'] = $data['unique_id'];
                $operatorUniqueIdName = $operator->unique_id_name;
                $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
                $managerData = MasterUser::whereUniqueId($data['unique_id'])->where(function ($query) use ($operatorId) {
                    $query->where('parent_id', '=', $operatorId)
                        ->orWhere('id', '=', $operatorId);
                })->first();
                if ($managerData) {
                    $relData['manager_id'] = $managerData->id;
                } else {
                    $relData['response_type'] = "Error";
                    $relData['message'] = $operatorUniqueIdName ? $operatorUniqueIdName . ' not found.' : 'Unique Id not found.';
                    return $relData;
                }

                $managerDataCheck = MasterUser::whereEmail($data['manager_email'])->where(function ($query) use ($operatorId,$data) {
                    $query->where('unique_id', '=', $data['unique_id']);
                })->first();

                if(empty($managerDataCheck)){
                    $relData['response_type'] = "Error";
                    $relData['message'] = $operatorUniqueIdName ? $operatorUniqueIdName . ' not found.' : 'Unique Id not found.';
                    return $relData;
                }
            }else{
                $relData['unique_id'] = null;
            }

            if (isset($data['user_group_id'])) {
                $relData['user_group_id'] = $data['user_group_id'];
                //UserAssignTrainingCourses::where(['user_id'=>$user_id,'master_user_id'=>$master_user_id])->delete();
            } else {
                $relData['user_group_id'] = null;
            }

            // check if record in user relation table exist
            $userRelationExist = UserRelation::whereMasterUserId($master_user_id)->whereUserId($user['id'])->first();

            if ($userRelationExist) {
                if (isset($data['user_group_id']) && !empty($data['user_group_id'])) {
                    $manualCourse = $getCurrentGroupCourses = [];
                    $groupCourseIds = $manualCourseIds = [];
                    $getCurrentGroupCourses = AssignTrainingCourses::whereMasterUserId($master_user_id)->whereUserGroupId($data['user_group_id'])->get()->pluck('training_course_id')->toArray();
                    if (count($getCurrentGroupCourses) > 0) {
                        $groupCourseIds = array_values($getCurrentGroupCourses);
                        $groupCourseIds = array_unique($groupCourseIds);
                    }
                    $manualCourse = UserAssignTrainingCourses::where(['user_id' => $user_id, 'master_user_id' => $master_user_id])->get()->pluck('training_course_id')->toArray();
                    if (count($manualCourse) > 0) {
                        $manualCourseIds = array_values($manualCourse);
                        $manualCourseIds = array_unique($manualCourseIds);
                    }

                    if (count($groupCourseIds) > 0) {
                        if (count($manualCourseIds) > 0) {
                            $notificationIds = array_values(array_diff($groupCourseIds, $manualCourseIds));
                            if (count($notificationIds) > 0) {
                                if ($userRelationExist && $userRelationExist->user_group_id != $data['user_group_id']) {
                                    $userDetails = $this->model->find($user_id);
                                    dispatch((new \App\Jobs\CourseNotificationJob($userDetails, $master_user_id, $notificationIds)));
                                }
                            }
                        } else {
                            if ($userRelationExist && $userRelationExist->user_group_id != $data['user_group_id']) {
                                $userDetails = $this->model->find($user_id);
                                //dispatch((new \App\Jobs\CourseNotificationJob($userDetails, $master_user_id, $training_course_ids)));
                                dispatch((new \App\Jobs\CourseNotificationJob($userDetails, $master_user_id, $groupCourseIds)));
                            }
                        }
                    }
                }
                $userRelationExist->update($relData);
                app(GetStreamService::class)->GenerateUserGetStreamToken($userRelationExist);
            } else {
                if (isset($data['user_group_id']) && !empty($data['user_group_id'])) {
                    $manualCourse = $getCurrentGroupCourses = [];
                    $groupCourseIds = $manualCourseIds = [];
                    $getCurrentGroupCourses = AssignTrainingCourses::whereMasterUserId($master_user_id)->whereUserGroupId($data['user_group_id'])->get()->pluck('training_course_id')->toArray();
                    if (count($getCurrentGroupCourses) > 0) {
                        $groupCourseIds = array_values($getCurrentGroupCourses);
                    }
                    $manualCourse = UserAssignTrainingCourses::where(['user_id' => $user_id, 'master_user_id' => $master_user_id])->get()->pluck('training_course_id')->toArray();
                    if (count($getCurrentGroupCourses) > 0) {
                        $manualCourseIds = array_values($getCurrentGroupCourses);
                    }

                    if (count($groupCourseIds) > 0) {
                        if (count($manualCourseIds) > 0) {
                            $notificationIds = array_values(array_diff($groupCourseIds, $manualCourseIds));
                            if (count($notificationIds) > 0) {
                                $userDetails = $this->model->find($user_id);
                                dispatch((new \App\Jobs\CourseNotificationJob($userDetails, $master_user_id, $notificationIds)));
                            }
                        } else {
                            $userDetails = $this->model->find($user_id);
                            dispatch((new \App\Jobs\CourseNotificationJob($userDetails, $master_user_id, $groupCourseIds)));
                        }
                    }
                }
                $relData['user_id'] = $user['id'];
                $relData['master_user_id'] = $master_user_id;
                UserRelation::create($relData);
            }

            // Send email to operator while update password
            if (!empty($data['password'])) {
                $user->notify(new TeamMemberResendPasswordNotification($password, 'operator'));
            }

        }

        if ($data['step'] == 2) {
            $training_course_ids = $request->trainingCourseIds;
            $user_id = $request->userId;
            // If group Id is not Null
            // $currentUser = UserRelation::select('user_group_id')->whereUserId($user_id)->whereMasterUserId($master_user_id)->first();
            // if(!is_null($currentUser->user_group_id)){
            //     $getCurrentGroupCourses = AssignTrainingCourses::whereMasterUserId($master_user_id)->whereUserGroupId($currentUser->user_group_id)->get()->pluck('training_course_id')->toArray();
            //     if(count($getCurrentGroupCourses) > 0){
            //         $training_course_ids = array_merge($training_course_ids,$getCurrentGroupCourses);
            //     }
            // }
            if (count($training_course_ids) > 0) {
                //delete existing assign course
                $oldCourse = UserAssignTrainingCourses::where(['user_id' => $user_id, 'master_user_id' => $master_user_id])->get();
                UserAssignTrainingCourses::where(['user_id' => $user_id, 'master_user_id' => $master_user_id]) /*->where('is_default_course', '!=', '1')*/->delete();
                foreach ($training_course_ids as $key => $courseId) {
                    $newData = [];
                    $newData['master_user_id'] = $master_user_id;
                    $newData['user_id'] = $user_id;
                    $newData['training_course_id'] = $courseId;
                    $newData['is_manual_course'] = 1;
                    if (!is_null($user_id)) {
                        $courseExist = UserAssignTrainingCourses::where(['user_id' => $user_id, 'master_user_id' => $master_user_id, 'training_course_id' => $courseId])->first();
                        if (empty($courseExist)) {
                            UserAssignTrainingCourses::create($newData);
                        }
                    }
                }
                $userDetails = $this->model->find($user_id);
                // Send Push notifications

                if (!$oldCourse->isEmpty()) {
                    $oldIds = $oldCourse->pluck('training_course_id')->toArray();
                    $newIds = $training_course_ids;
                    $notificationListIds = array_diff($newIds, array_values($oldIds));
                    if (count($notificationListIds) > 0) {
                        dispatch((new \App\Jobs\CourseNotificationJob($userDetails, $master_user_id, array_values($notificationListIds))));
                    }
                } else {

                    $defaultCourse = UserAssignTrainingCourses::where(['user_id' => $user_id, 'master_user_id' => $master_user_id])->where('is_default_course', '=', '1')->get();

                    if (!$defaultCourse->isEmpty()) {
                        $arrayDiff = array_diff($training_course_ids, array_values($defaultCourse->pluck('training_course_id')->toArray()));
                        if (!empty($arrayDiff)) {
                            dispatch((new \App\Jobs\CourseNotificationJob($userDetails, $master_user_id, array_values($arrayDiff))));
                        }
                    } else {
                        dispatch((new \App\Jobs\CourseNotificationJob($userDetails, $master_user_id, $training_course_ids)));
                    }
                }
            } else {
                UserAssignTrainingCourses::where(['user_id' => $user_id, 'master_user_id' => $master_user_id]) /*->where('is_default_course', '!=', '1')*/->delete();
            }
            return $updatedUser = $this->model->find($user_id);
        }

        if ($data['step'] == 3) {
            $id = $request->userId;
            $reldtl = UserRelation::whereMasterUserId($master_user_id)->whereUserId($id)->first();
            $data = $request->except(['step']);
            $reldtl->update($data);
        }
    }

    public function delete($request)
    {

        $result = \DB::transaction(function () use ($request) {
            $requestData = $request->ids;
            if (count($requestData) > 0) {
                $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
                app(GetStreamService::class)->DeleteUserGetStream($requestData);
                foreach ($requestData as $value) {
                    AssessorAssignUser::whereMasterUserId($operatorId)->whereUserId($value)->delete();
                    $userRelation = UserRelation::whereMasterUserId($operatorId)->whereUserId($value)->first();

                    //     print_r($operatorId);echo "<pre>";
                    // print_r($value);exit;

                    if ($userRelation) {

                        if ($userRelation->is_current_operator) { // Set default operator to Skillbase original

                            $skillbaseId = MasterUser::whereUserType('Operator')->whereNotNull('managed_by')->pluck('id')->first();

                            $defaultRelation = UserRelation::where(['user_id' => $value, 'master_user_id' => $skillbaseId])->first();

                            if ($defaultRelation) {

                                if ($operatorId == $defaultRelation->master_user_id) {
                                    $defaultRelationData = UserRelation::where('user_id', $value)->where('master_user_id', '!=', $defaultRelation->master_user_id)->orderBy('id')->first();
                                    if ($defaultRelationData) {
                                        $defaultRelationData->update([
                                            'is_current_operator' => 1,
                                        ]);
                                    }

                                } else {
                                    $defaultRelation->update([
                                        'is_current_operator' => 1,
                                    ]);
                                }

                            } else {
                                $defaultRelationData = UserRelation::where('user_id', $value)->where('master_user_id', '!=', $operatorId)->orderBy('id')->first();
                                if ($defaultRelationData) {
                                    $defaultRelationData->update([
                                        'is_current_operator' => 1,
                                    ]);
                                }
                            }
                        }
                        UserAssignTrainingCourses::whereMasterUserId($operatorId)->whereUserId($value)->delete();
                        AssignUserInviteLinks::whereMasterUserId($operatorId)->whereUserId($value)->delete();
                        AssessorAssignUser::whereMasterUserId($operatorId)->whereUserId($value)->delete();
                        $userRelation->delete();
                    }
                    User::where('id', $value)->update(['is_logout' => 1]);
                }
            }
        });
        return $result;
    }

    public function change_status($request)
    {
        $this->model->whereIn('id', $request->ids)->update(['status' => $request->status]);
        app(GetStreamService::class)->TeamMemberChangeStatus($request->ids,$request->status);
    }

    // function for operator users
    public function getUsersListing($data)
    {
        $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
        //$master_user_id = auth()->guard('operator')->id();
        $selectColumns = [
            $this->table . '.id',
            $this->table . '.name',
            $this->table . '.email',
            $this->table . '.created_at',
            $this->table . '.assessor_id',
            $this->table . '.assessor_role',
            $this->table . '.get_stream_user_id',
            $this->table . '.get_stream_token',
            $this->table . '.photo',
            $this->table . '.last_logged_in_at',
            $this->table . '.status',
            $this->table . '.email_verified_at',
            DB::raw("IF(`user_relations`.`user_group_id` IS NOT NULL,
            (select `groups`.`name` from `groups` where `groups`.`id`=`user_relations`.`user_group_id`),null) as group_name"),
            // 'user_relations.assign_courses',
            'user_relations.manager_email',
            'user_relations.unique_id',
            'user_relations.user_group_id',
        ];

        //  DB::raw("IF(`user_relations`.`user_group_id` IS NULL,
        // (select count(user_assigned_training_courses.training_course_id) from user_assigned_training_courses where user_assigned_training_courses.user_id = `users`.`id` and user_assigned_training_courses.master_user_id = $operatorId),
        // (select count(assign_training_courses.training_course_id) from assign_training_courses where assign_training_courses.user_group_id = `user_relations`.`user_group_id`)) as assigned_course"),

        $query = User::select($selectColumns)
            ->join(DBTableNames::USER_RELATIONS . ' as user_relations', $this->table . '.id', '=', 'user_relations.user_id')
            ->leftJoin(DBTableNames::USER_GROUPS . ' as groups', 'groups.id', '=', 'user_relations.user_group_id')
            ->where('user_relations.master_user_id', $operatorId);

        // Show only assigned users if sub-operator has not permission to view all users data
        if (!canViewAllUsersData(auth()->guard('operator')->user()->role_id)) {
            $query = $query->whereIn($this->table . '.id', getAssignedUsersId());
        }

        // Searching
        $search = isset($data['search_key']) ? $data['search_key'] : "";
        $searchFields = ['users.name', 'users.email', 'users.created_at', 'users.status', 'users.assessor_id', 'users.assessor_role', 'groups.name', 'user_relations.manager_email', 'user_relations.unique_id', 'last_logged_in_at'];
        if (!empty($search)) {
            $query = $query->where(function ($query) use ($searchFields, $search) {
                foreach ($searchFields as $key => $field) {
                    $query->orWhere($field, 'LIKE', '%' . $search . '%');
                }
            });
        }

        /* Advance Filters */
        if (isset($data['filters']) && count($data['filters']) > 0) {
            if (isset($data['filters']['name']) && $data['filters']['name'] != "") {
                $query = $query->where($this->table . '.name', 'LIKE', '%' . $data['filters']['name'] . '%');
            }
            if (isset($data['filters']['assessor_id']) && $data['filters']['assessor_id'] != "") {
                $query = $query->where($this->table . '.assessor_id', 'LIKE', '%' . $data['filters']['assessor_id'] . '%');
            }
            if (isset($data['filters']['assessor_role']) && $data['filters']['assessor_role'] != "") {
                $query = $query->where($this->table . '.assessor_role', 'LIKE', '%' . $data['filters']['assessor_role'] . '%');
            }
            if (isset($data['filters']['email']) && $data['filters']['email'] != "") {
                $query = $query->where($this->table . '.email', 'LIKE', '%' . $data['filters']['email'] . '%');
            }
            if (isset($data['filters']['group_name']) && $data['filters']['group_name'] != "") {
                $query = $query->where('groups.name', 'LIKE', '%' . $data['filters']['group_name'] . '%');
            }
            if (isset($data['filters']['manager_email']) && $data['filters']['manager_email'] != "") {
                $query = $query->where('user_relations.manager_email', 'LIKE', '%' . $data['filters']['manager_email'] . '%');
            }
            if (isset($data['filters']['unique_id']) && $data['filters']['unique_id'] != "") {
                $query = $query->where('user_relations.unique_id', 'LIKE', '%' . $data['filters']['unique_id'] . '%');
            }
            if (isset($data['filters']['last_logged_in_at']) && $data['filters']['last_logged_in_at'] != "") {
                $query = $query->whereDate($this->table . '.last_logged_in_at', '=', $data['filters']['last_logged_in_at']);
            }
            if (isset($data['filters']['created_at']) && $data['filters']['created_at'] != "") {
                $query = $query->whereDate($this->table . '.created_at', '=', $data['filters']['created_at']);
            }
            if (isset($data['filters']['status']) && $data['filters']['status'] != "") {
                $query = $query->where($this->table . '.status', $data['filters']['status']);
            }
        }

        // Sorting
        $sort = 'last_logged_in_at'; // Default sort by ID
        $sortingKeys = [
            'id' => 'id',
            'name' => 'name',
            'email' => 'email',
            'group_name' => 'group_name',
            'manager_email' => 'manager_email',
            'unique_id' => 'unique_id',
            'assessor_id' => 'assessor_id',
            'assessor_role' => 'assessor_role',
            'last_logged_in_at' => 'last_logged_in_at',
            'created_at' => 'created_at',
            'status' => "status",
        ];
        if (isset($data['sort_by']) && $data['sort_by'] != "") {
            if (array_key_exists($data['sort_by'], $sortingKeys)) {
                $sort = $sortingKeys[$data['sort_by']];
            }
        }
        // Ordering (Default DESC)
        $order = isset($data['order_by']) ? $data['order_by'] : 'DESC';

        // Apply Sorting and Ordering
        if (in_array($sort, $sortingKeys)) {
            $query = $query->orderBy($sort, $order);
        }

        // if (isset($data['filters']['assigned_course']) && $data['filters']['assigned_course'] != "") {
        //     $query = $query->having('assigned_course','=',$data['filters']['assigned_course']);
        // }
        if (isset($data['group_id']) && $data['group_id'] != "") {
            $query = $query->where('user_relations.user_group_id', '=', $data['group_id']);
        }
        return $query;
    }

    public function exportCsv($users, $fields = null)
    {
        //$columns = ['name' => 'NAME', 'email' => 'EMAIL', 'manager_email' => 'MANAGER EMAIL', 'unique_id' => 'UNIQUE ID', 'group_name' => 'GROUP', 'assigned_course' => 'ASSIGN COURSES', 'last_logged_in_at' => 'LAST LOGIN DATE','created_at' => 'CREATED AT','status' => 'STATUS'];
        $columns = ['name' => 'NAME', 'email' => 'EMAIL', 'manager_email' => 'MANAGER EMAIL', 'group_name' => 'GROUP NAME', 'unique_id' => 'UNIQUE ID', 'assessor_id' => 'AssessorID', 'assessor_role' => 'Assessor Role', 'last_logged_in_at' => 'LAST LOGIN DATE', 'created_at' => 'CREATED AT', 'status' => 'STATUS'];
        $exportFields = array_flip((empty($fields)) ? array_keys($columns) : $fields);
        $fileName = '../storage/' . time() . '-' . randomString() . '.csv';
        $fp = fopen($fileName, 'w');
        fputcsv($fp, array_intersect_key($columns, $exportFields));

        foreach ($users as $raw) {
            $output = [
                "name" => $raw->name,
                "email" => $raw->email,
                "manager_email" => $raw->manager_email ?? '--',
                "group_name" => $raw->group_name ?? '--',
                "unique_id" => $raw->unique_id ?? '--',
                "assessor_id" => $raw->assessor_id,
                "assessor_role" => $raw->assessor_role,
                "last_logged_in_at" => $raw->last_logged_in_at ?? '--',
                'created_at' => date_format($raw->created_at, 'Y-m-d'),
                'status' => $raw->status,
            ];

            fputcsv($fp, array_intersect_key($output, $exportFields));
        }

        return response()->download($fileName, time() . '-operatorUsers.csv', csvHeaders())->deleteFileAfterSend(true);
    }

    public function importFrontUsers($request)
    {
        $responseData = [];
        try {
            $file = fopen($request->file('file'), "r");
            $operator = auth()->guard('operator')->user();
            $mainOperator = getOperator($operator);
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            //$managerEmails = MasterUser::where('id', $operator->id)->orWhere('parent_id', $operator->id)->pluck('email')->toArray();
            $header = fgetcsv($file);
            $rowData = [];
            while (!feof($file)) {
                $rowData[] = fgetcsv($file);
            }

            if (count($rowData) === 0) {
                return false;
            }
            $cnt = $grpCnt = 0;
            $newlyCreatedUserData = $existingUsersAdded = [];
            foreach ($rowData as $rowId => $value) {

                if (is_array($value) && !empty($value[0])) {
                    $groupName = (isset($value[3]) && trim($value[3]) != '--') ? trim($value[3]) : null;
                    $groupId = null;
                    if ($groupName) {
                        $groupDetail = UserGroup::whereName($groupName)->first();
                        if ($groupDetail) {
                            $groupId = $groupDetail->id;
                        } else {
                            $responseData['group_not_exist_message'] = 'Group does not exists';
                            $responseData['group_not_exist'][$grpCnt]['name'] = $value[0];
                            $responseData['group_not_exist'][$grpCnt]['email'] = $value[1];
                            $responseData['group_not_exist'][$grpCnt]['group_name'] = $value[3];
                            $responseData['group_not_exist'][$grpCnt]['row'] = $rowId + 1;
                            $grpCnt++;
                        }
                    }

                    $value = array_map('trim', $value);
                    if ((!empty($value[0]) && !empty($value[1]) && !empty($value[2])) && // Check if name, email and manager email, group name are not blank
                        (filter_var($value[1], FILTER_VALIDATE_EMAIL) && filter_var($value[2], FILTER_VALIDATE_EMAIL)) // Check if user and manager email are valid // Check if manager email is exists then proceed  in_array($value[2], $managerEmails)

                    ) {
                        $manager = MasterUser::whereEmail($value[2])->first();
                        $user = $this->model->whereEmail($value[1])->first();
                        $user_exist = $this->model->whereEmail($value[1])
                            ->join(DBTableNames::USER_RELATIONS . ' as user_relations', $this->table . '.id', '=', 'user_relations.user_id')
                            ->where('user_relations.master_user_id', $operatorId)->get()->toArray();
                        if ($user) {
                            $user->update(['name' => $value[0]]);
                            if ($user_exist) {
                                $responseData['email_exist_message'] = 'Email already exists';
                                $responseData['email_exist'][$cnt]['name'] = $value[0];
                                $responseData['email_exist'][$cnt]['email'] = $value[1];
                                $responseData['email_exist'][$cnt]['row'] = $rowId + 1;
                            }
                            $cnt++;
                        } else {
                            $password = getRandomPassword();
                            $user = $this->model->create([
                                'name' => $value[0],
                                'email' => $value[1],
                                'password' => bcrypt($password),
                                'added_user_id' => $operator->id,
                                'added_by' => $operator->user_type,
                                'status' => 'Active',
                                'device_token' => '',
                                'is_password_change' => 1,
                                'email_verified_at' => \Carbon\Carbon::now(),
                            ]);
                            app(GetStreamService::class)->GenerateGetStreamToken($user);
                            $token = Password::getRepository()->create($user);
                            //Checking for email address if smartawards contains only digits before @
                            $smartAwardsMailSendFlag = smartAwardsEmailSendResctrict($user->email);
                            if($smartAwardsMailSendFlag == 1){
                                $user->sendCreatePasswordNotification($token, "Create");
                            }
                            $newlyCreatedUserData[] = ['id' => $user->id, 'password' => $password];
                            // Skillbase original
                            $skillbaseDefault = MasterUser::whereUserType('Operator')->whereNotNull('managed_by')->first();
                            if ($skillbaseDefault->enable_manager_email == 0 && $skillbaseDefault->enable_unique_id == 0) {
                                UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseDefault->id, 'is_current_operator' => 0, 'manager_id' => $skillbaseDefault->id, 'manager_email' => $skillbaseDefault->email, 'unique_id' => $skillbaseDefault->unique_id, 'user_group_id' => $groupId]);
                            } elseif ($skillbaseDefault->enable_manager_email == 0) {
                                UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseDefault->id, 'is_current_operator' => 0, 'manager_id' => $skillbaseDefault->id, 'manager_email' => $skillbaseDefault->email, 'user_group_id' => $groupId]);
                            } elseif ($skillbaseDefault->enable_unique_id == 0) {
                                UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseDefault->id, 'is_current_operator' => 0, 'unique_id' => $skillbaseDefault->unique_id, 'user_group_id' => $groupId]);
                            } else {
                                UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseDefault->id, 'is_current_operator' => 0, 'user_group_id' => $groupId]);
                            }
                            /*$skillbaseId = MasterUser::whereUserType('Operator')->whereNotNull('managed_by')->pluck('id')->first();
                            UserRelation::create(['user_id' => $user->id,'master_user_id' => $skillbaseId,'is_current_operator' => 1]);*/

                            // Create default Notification
                            // createDefaultUserNotifications($user);

                        }
                        $userRelation = UserRelation::whereMasterUserId($mainOperator->id)->whereUserId($user->id)->first();
                        if (!empty($manager)) {
                            $relationData = [
                                'manager_email' => $manager->email,
                                'unique_id' => $manager->unique_id,
                                'user_group_id' => $groupId,
                            ];
                        } else {
                            $relationData = ['manager_email' => $value[2], 'user_group_id' => $groupId];
                        }
                        if ($userRelation) {
                            $userRelation->update($relationData);
                        } else {
                            $relationData['user_id'] = $user->id;
                            $relationData['master_user_id'] = $mainOperator->id;
                            $isExist = UserRelation::whereUserId($user->id)->whereIsCurrentOperator(1)->first();
                            $relationData['is_current_operator'] = !empty($isExist) ? 0 : 1;
                            //$relationData['is_current_operator'] = 1;
                            $relationData['user_group_id'] = $groupId;
                            if (!empty($manager)) {
                                $relationData['manager_id'] = $manager->id;
                                $relationData['manager_email'] = $manager->email;
                                $relationData['unique_id'] = $manager->unique_id;
                            } else {
                                $relationData['manager_email'] = $mainOperator->email;
                                $relationData['unique_id'] = $mainOperator->unique_id;
                            }
                            $userRelation = UserRelation::create($relationData);
                            if (!$user->wasRecentlyCreated) { // User was already exists, only operator added in their list
                                $existingUsersAdded[] = ['userId' => $user->id, 'operator_name' => $operator->name, 'operator_email' => $operator->email];
                            }
                        }
                    }
                }
            }
            if (count($newlyCreatedUserData) > 0) {
                // Dispatch Job for sending login credentials to new users in Email
                //dispatch((new \App\Jobs\ImportFrontUserSendMailJob($newlyCreatedUserData)));
            }
            if (count($existingUsersAdded) > 0) { // Dispatch Job to notify existing users in Email
                dispatch((new \App\Jobs\ExistingFrontUserSendMailJob($existingUsersAdded)));
            }
            $response[0] = true;
            $response[1] = $responseData;
            return $response;
        } catch (\Exception $e) {
            $response[0] = false;
            $response[1] = $e->getMessage();
            return $response;
        }
    }

    public function GrouptrainingCourseList($request)
    {
        $courseTemp=null;
        $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
        $perPage = isset($request->per_page) ? $request->per_page : 10;
        if ($request->user_group_id) {
            $groupCourses = AssignTrainingCourses::whereUserGroupId($request->user_group_id)->get()->pluck('training_course_id')->toArray();
            $groupCourseList = '';
            if (count($groupCourses) > 0) {
                $groupCourseList = implode(",", $groupCourses);
            }
            if (!empty($groupCourseList)) {
                $courseTemp = TrainingCourse::select('training_course.id','training_course.is_default', 'utc.is_default_course', 'utc.is_manual_course', 'training_course.title as courseName', 'master_users.name as createdBy', 'training_course.updated_at', DB::raw('(COUNT(DISTINCT(utc.user_id)))  as no_of_assignees'), 'training_course.status', 'training_course.publish_now', DB::raw("IF(FIND_IN_SET(training_course.id, '" . $groupCourseList . "'), '1','0')  as is_group_course"))
                    ->join('master_users', 'master_users.id', '=', 'training_course.master_user_id')
                    ->leftJoin('user_assigned_training_courses as utc', 'utc.training_course_id', '=', 'training_course.id')
                    ->leftJoin('users as u', 'u.id', '=', 'utc.user_id')
                    ->leftJoin('assign_training_courses as atc', 'atc.training_course_id', '=', 'training_course.id')
                    ->leftJoin('user_relations as ur', 'ur.user_group_id', '=', 'atc.user_group_id')
                    ->whereNull('u.deleted_at')
                    ->whereIn('training_course.id', array_unique($groupCourses))
                    ->where('training_course.master_user_id', $operatorId);
            }
        }
        // search key filter
        if ($request->has('search_key') && $request->search_key != '') {
            $search_key = $request->search_key;
            $courseTemp->where(function($q) use ($search_key) {
                $q->where('title', 'like', '%' . $search_key . '%')
                  ->orWhere('master_users.name', 'like', '%' . $search_key . '%');
            });
        }

        /* Advance Filters */
        $data = $request->all();
        if (isset($data['filters']) && count($data['filters']) > 0) {
            if (isset($data['filters']['courseName']) && $data['filters']['courseName'] != "") {
                $courseTemp->where('training_course.title', 'LIKE', '%' . $data['filters']['courseName'] . '%');
            }
            if (isset($data['filters']['createdBy']) && $data['filters']['createdBy'] != "") {
                $courseTemp->where('master_users.name', 'LIKE', '%' . $data['filters']['createdBy'] . '%');
            }
            if (isset($data['filters']['updated_at']) && $data['filters']['updated_at'] != "") {
                $courseTemp->whereDate('training_course.updated_at', '=', $data['filters']['updated_at']);
            }
            if (isset($data['filters']['no_of_assignees']) && $data['filters']['no_of_assignees'] != "") {
                $courseTemp->having('no_of_assignees', '=', $data['filters']['no_of_assignees']);
            }
            if (isset($data['filters']['status']) && $data['filters']['status'] != "") {
                $query = $query->where('training_course.status', $data['filters']['status']);
            }
        }
        //Sorting
        if ($request->has('sort_by')) {
            $sortBy = $request->input('sort_by');
            $orderBy = ($request->input('order_by') == 'asc') ? 'ASC' : 'DESC';
            if ($sortBy == 'courseName') {
                $courseTemp->orderBy('training_course.title', $orderBy);
            }
            if ($sortBy == 'createdBy') {
                $courseTemp->orderBy('createdBy', $orderBy);
            }
            if ($sortBy == 'no_of_assignees') {
                $courseTemp->orderBy('no_of_assignees', $orderBy);
            }

        } else {
            if($courseTemp){ 
                //Validate
            $courseTemp = $courseTemp->orderByDesc('id');
            }
        }
        if($courseTemp){
        $courseTemp->groupBy('id');
        }

        return $courseTemp;

    }
    public function OnlytrainingCourseList($request,$operatorId,$userDetail)
    {
        $perPage = isset($request->per_page) ? $request->per_page : 10;
        
        // User assigned training courses
        $userAssignedCoursesList = UserAssignTrainingCourses::where('user_id',$request->user_id)->where('master_user_id',$operatorId)->get();

        if (!$userAssignedCoursesList->isEmpty()) {
            foreach ($userAssignedCoursesList as $key => $course) {
                $dataId[] = $course->training_course_id;
            }
            $userDetail->assign_course = $dataId;
        }

        $userDetails=User::find($request->user_id);
        $user_relation = $userDetails->user_relation;
        if(!is_null($user_relation->user_group_id)){
            $groupTrainingCourseIds = $user_relation->group->assignCourses()->pluck('training_course_id')->toArray();
            if ($groupTrainingCourseIds != '') {
                    if (!is_null($userDetail->assign_course)) {
                        $userDetail->assign_course = array_unique(array_merge($userDetail->assign_course, $groupTrainingCourseIds));
                    } else {
                        $userDetail->assign_course = $groupTrainingCourseIds;
                    }
            }
        }
        $defaultTrainingCourse=TrainingCourse::where('is_default',1)->where('master_user_id',$operatorId)->whereNull('deleted_at')->get();
        if (!$defaultTrainingCourse->isEmpty()) {
            foreach ($defaultTrainingCourse as $key => $defaultCourse) {
                $dataId[] = $defaultCourse->id;
            }
            $userDetail->assign_course = array_unique(array_merge($userDetail->assign_course, $dataId));
        }
        $courseTemp = TrainingCourse::select('training_course.id','training_course.is_default', 'utc.is_default_course', 'utc.is_manual_course', 'training_course.title as courseName', 'master_users.name as createdBy', 'training_course.updated_at', DB::raw('(COUNT(DISTINCT(utc.user_id)))  as no_of_assignees'), 'training_course.status', 'training_course.publish_now', DB::raw("0 as is_group_course"))
            ->join('master_users', 'master_users.id', '=', 'training_course.master_user_id')
            ->leftJoin('user_assigned_training_courses as utc', 'utc.training_course_id', '=', 'training_course.id')
            ->leftJoin('users as u', 'u.id', '=', 'utc.user_id')
            ->leftJoin('assign_training_courses as atc', 'atc.training_course_id', '=', 'training_course.id')
            ->leftJoin('user_relations as ur', 'ur.user_group_id', '=', 'atc.user_group_id')
            ->whereNull('u.deleted_at')
            ->whereIn('training_course.id', $userDetail->assign_course ?? [])
            ->where('training_course.master_user_id', $operatorId);
                
        // search key filter
        if ($request->has('search_key') && $request->search_key != '') {
            $search_key = $request->search_key;
            $courseTemp->where(function($q) use ($search_key) {
                $q->where('title', 'like', '%' . $search_key . '%')
                  ->orWhere('master_users.name', 'like', '%' . $search_key . '%');
            });
        }

        /* Advance Filters */
        $data = $request->all();
        if (isset($data['filters']) && count($data['filters']) > 0) {
            if (isset($data['filters']['courseName']) && $data['filters']['courseName'] != "") {
                $courseTemp->where('training_course.title', 'LIKE', '%' . $data['filters']['courseName'] . '%');
            }
            if (isset($data['filters']['createdBy']) && $data['filters']['createdBy'] != "") {
                $courseTemp->where('master_users.name', 'LIKE', '%' . $data['filters']['createdBy'] . '%');
            }
            if (isset($data['filters']['updated_at']) && $data['filters']['updated_at'] != "") {
                $courseTemp->whereDate('training_course.updated_at', '=', $data['filters']['updated_at']);
            }
            if (isset($data['filters']['no_of_assignees']) && $data['filters']['no_of_assignees'] != "") {
                $courseTemp->having('no_of_assignees', '=', $data['filters']['no_of_assignees']);
            }
            if (isset($data['filters']['status']) && $data['filters']['status'] != "") {
                $query = $query->where('training_course.status', $data['filters']['status']);
            }
        }
        //Sorting
        if ($request->has('sort_by')) {
            $sortBy = $request->input('sort_by');
            $orderBy = ($request->input('order_by') == 'asc') ? 'ASC' : 'DESC';
            if ($sortBy == 'courseName') {
                $courseTemp->orderBy('training_course.title', $orderBy);
            }
            if ($sortBy == 'createdBy') {
                $courseTemp->orderBy('createdBy', $orderBy);
            }
            if ($sortBy == 'no_of_assignees') {
                $courseTemp->orderBy('no_of_assignees', $orderBy);
            }

        } else {
            $courseTemp = $courseTemp->orderByDesc('id');
        }
        $courseTemp->groupBy('id');

        return $courseTemp;

    }
    public function trainingCourseList($request)
    {

        $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
        $perPage = isset($request->per_page) ? $request->per_page : 10;
        if ($request->user_group_id) {
            $groupCourses = AssignTrainingCourses::whereUserGroupId($request->user_group_id)->get()->pluck('training_course_id')->toArray();
            $groupCourseList = '';
            if (count($groupCourses) > 0) {
                $groupCourseList = implode(",", $groupCourses);
            }
            if (!empty($groupCourseList)) {
                $courseTemp = TrainingCourse::select('training_course.id', 'training_course.is_default', 'utc.is_manual_course', 'training_course.title as courseName', 'master_users.name as createdBy', 'training_course.updated_at', DB::raw('(COUNT(DISTINCT(utc.user_id)))  as no_of_assignees'), 'training_course.status', 'training_course.publish_now', DB::raw("IF(FIND_IN_SET(training_course.id, '" . $groupCourseList . "'), '1','0')  as is_group_course"))
                    ->join('master_users', 'master_users.id', '=', 'training_course.master_user_id')
                    ->leftJoin('user_assigned_training_courses as utc', 'utc.training_course_id', '=', 'training_course.id')
                    ->leftJoin('users as u', 'u.id', '=', 'utc.user_id')
                    ->leftJoin('assign_training_courses as atc', 'atc.training_course_id', '=', 'training_course.id')
                    ->leftJoin('user_relations as ur', 'ur.user_group_id', '=', 'atc.user_group_id')
                    ->whereNull('u.deleted_at')
                    ->where('training_course.master_user_id', $operatorId);
            } else {
                $courseTemp = TrainingCourse::select('training_course.id', 'training_course.is_default', 'utc.is_manual_course', 'training_course.title as courseName', 'master_users.name as createdBy', 'training_course.updated_at', DB::raw('(COUNT(DISTINCT(utc.user_id)))  as no_of_assignees'), 'training_course.status', 'training_course.publish_now', DB::raw("0 as is_group_course"))
                    ->join('master_users', 'master_users.id', '=', 'training_course.master_user_id')
                    ->leftJoin('user_assigned_training_courses as utc', 'utc.training_course_id', '=', 'training_course.id')
                    ->leftJoin('users as u', 'u.id', '=', 'utc.user_id')
                    ->leftJoin('assign_training_courses as atc', 'atc.training_course_id', '=', 'training_course.id')
                    ->leftJoin('user_relations as ur', 'ur.user_group_id', '=', 'atc.user_group_id')
                    ->whereNull('u.deleted_at')
                    ->where('training_course.master_user_id', $operatorId);
            }
        } else {
            $courseTemp = TrainingCourse::select('training_course.id', 'training_course.is_default', 'utc.is_manual_course', 'training_course.title as courseName', 'master_users.name as createdBy', 'training_course.updated_at', DB::raw('(COUNT(DISTINCT(utc.user_id)))  as no_of_assignees'), 'training_course.status', 'training_course.publish_now', DB::raw("0 as is_group_course"))
                ->join('master_users', 'master_users.id', '=', 'training_course.master_user_id')
                ->leftJoin('user_assigned_training_courses as utc', 'utc.training_course_id', '=', 'training_course.id')
                ->leftJoin('users as u', 'u.id', '=', 'utc.user_id')
                ->leftJoin('assign_training_courses as atc', 'atc.training_course_id', '=', 'training_course.id')
                ->leftJoin('user_relations as ur', 'ur.user_group_id', '=', 'atc.user_group_id')
                ->whereNull('u.deleted_at')
                ->where('training_course.master_user_id', $operatorId);
        }
        // search key filter
        if ($request->has('search_key') && $request->search_key != '') {
            $search_key = $request->search_key;
            $courseTemp->where(function($q) use ($search_key) {
                $q->where('title', 'like', '%' . $search_key . '%')
                  ->orWhere('master_users.name', 'like', '%' . $search_key . '%');
            });
        }

        /* Advance Filters */
        $data = $request->all();
        if (isset($data['filters']) && count($data['filters']) > 0) {
            if (isset($data['filters']['courseName']) && $data['filters']['courseName'] != "") {
                $courseTemp->where('training_course.title', 'LIKE', '%' . $data['filters']['courseName'] . '%');
            }
            if (isset($data['filters']['createdBy']) && $data['filters']['createdBy'] != "") {
                $courseTemp->where('master_users.name', 'LIKE', '%' . $data['filters']['createdBy'] . '%');
            }
            if (isset($data['filters']['updated_at']) && $data['filters']['updated_at'] != "") {
                $courseTemp->whereDate('training_course.updated_at', '=', $data['filters']['updated_at']);
            }
            if (isset($data['filters']['no_of_assignees']) && $data['filters']['no_of_assignees'] != "") {
                $courseTemp->having('no_of_assignees', '=', $data['filters']['no_of_assignees']);
            }
            if (isset($data['filters']['status']) && $data['filters']['status'] != "") {
                $query = $query->where('training_course.status', $data['filters']['status']);
            }
        }
        //Sorting
        if ($request->has('sort_by')) {
            $sortBy = $request->input('sort_by');
            $orderBy = ($request->input('order_by') == 'asc') ? 'ASC' : 'DESC';
            if ($sortBy == 'courseName') {
                $courseTemp->orderBy('training_course.title', $orderBy);
            }
            if ($sortBy == 'createdBy') {
                $courseTemp->orderBy('createdBy', $orderBy);
            }
            if ($sortBy == 'no_of_assignees') {
                $courseTemp->orderBy('no_of_assignees', $orderBy);
            }

        } else {
            $courseTemp = $courseTemp->orderByDesc('id');
        }
        $courseTemp->groupBy('id');

        return $courseTemp;

    }

    // create a new record in the database
    public function createSmartAwardUser($request)
    {
        $data = $request->all();
        $master_user_id = 64;
        $user_type = 'Operator';

        //check if user already registered
        $userAlreadyRegistered = $this->model->whereEmail($data['email'])->first();

        $operatorDetail = MasterUser::where('id', $master_user_id)->first();

        $userRelationExist = '';
        if ($userAlreadyRegistered) {

            $user = $userAlreadyRegistered;
            $userRelationExist = UserRelation::whereMasterUserId($master_user_id)->whereUserId($user->id)->first();

            // Mobile Notification
            $message = $operatorDetail->name . " Operator registered you as a user successfully";
            $mutable = 1;
            $payload['type'] = "Added";
            $payload['title'] = $message;
            $payload['description'] = $message;
            $payload['master_user_id'] = $master_user_id;
            $payload['image'] = $operatorDetail->image_url;

            $notificationData = [
                'alert' => $message,
                "mutable-content" => $mutable,
                'data' => $payload,
                'sound' => 'default',
            ];
            $extra['user_id'] = $user->id;
            $extra['master_user_id'] = $master_user_id;
            $extra['type'] = 'Added';
            $extra['notification_status'] = 'Extra';
            // Dispatch Operator added user Notification Job
            $userAddNotificationJob = (new \App\Jobs\UserAddNotificationJob($userAlreadyRegistered, $notificationData, $extra))->delay(3);
            dispatch($userAddNotificationJob);
        } else {
            $data = $request->all();
            $data['added_user_id'] = $master_user_id;
            $data['added_by'] = $user_type;
            if (!empty($data['password'])) {
                $password = trim($data['password']);
            } else {
                $password = getRandomPassword();
            }
            $data['password'] = bcrypt($password);

            $data['device_token'] = "";
            $data['email_verified_at'] = date('Y-m-d H:i:s');
            $data['is_password_change'] = 1;
            $user = $this->model->create($data);
            app(GetStreamService::class)->GenerateGetStreamToken($user);
            $token = Password::getRepository()->create($user);
            //Checking for email address if smartawards contains only digits before @
            $smartAwardsMailSendFlag = smartAwardsEmailSendResctrict($user->email);
            if(isset($request->disableMailSend)){
                if($request->disableMailSend == 1){
                    if($smartAwardsMailSendFlag == 1){
                        $user->sendCreatePasswordNotification($token, "Create");
                    }
                }
            }else{
                if($smartAwardsMailSendFlag == 1){
                    $user->sendCreatePasswordNotification($token, "Create");
                }
            }

            // Skillbase original
            $skillbaseDefault = MasterUser::whereUserType('Operator')->whereNotNull('managed_by')->first();
            if ($skillbaseDefault->enable_manager_email == 0 && $skillbaseDefault->enable_unique_id == 0) {
                UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseDefault->id, 'is_current_operator' => 0, 'manager_id' => $skillbaseDefault->id, 'manager_email' => $skillbaseDefault->email, 'unique_id' => $skillbaseDefault->unique_id]);
            } elseif ($skillbaseDefault->enable_manager_email == 0) {
                UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseDefault->id, 'is_current_operator' => 0, 'manager_id' => $skillbaseDefault->id, 'manager_email' => $skillbaseDefault->email]);
            } elseif ($skillbaseDefault->enable_unique_id == 0) {
                UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseDefault->id, 'is_current_operator' => 0, 'unique_id' => $skillbaseDefault->unique_id]);
            } else {
                UserRelation::create(['user_id' => $user->id, 'master_user_id' => $skillbaseDefault->id, 'is_current_operator' => 0]);
            }
            // Skillbase original Course Assignment
            $allDefaulCourses = TrainingCourse::whereMasterUserId($skillbaseDefault->id)->get();
            if ($allDefaulCourses->count() > 0) {
                foreach ($allDefaulCourses as $course) {
                    UserAssignTrainingCourses::create([
                        'user_id' => $user->id,
                        'master_user_id' => $skillbaseDefault->id,
                        'training_course_id' => $course->id,
                    ]);
                }
            }
            // Create default Notification
            createDefaultUserNotifications($user);

        }
        if (isset($data['manager_email'])) {
            $relData['manager_email'] = $data['manager_email'];
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $managerData = MasterUser::whereEmail($data['manager_email'])->where(function ($query) use ($operatorId) {
                $query->where('parent_id', '=', $operatorId)
                    ->orWhere('id', '=', $operatorId);
            })->first();
            if ($managerData) {
                $relData['manager_id'] = $managerData->id;
            } else {
                $relData['response_type'] = "Error";
                $relData['message'] = __('operator.FrontUser.managerEmail');
                return $relData;
            }
        }

        if (isset($data['unique_id'])) {
            $relData['unique_id'] = $data['unique_id'];
            $operatorUniqueIdName = $operator->unique_id_name;
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $managerData = MasterUser::whereUniqueId($data['unique_id'])->where(function ($query) use ($operatorId) {
                $query->where('parent_id', '=', $operatorId)
                    ->orWhere('id', '=', $operatorId);
            })->first();

            if ($managerData) {
                $relData['manager_id'] = $managerData->id;
            } else {
                $relData['response_type'] = "Error";
                $relData['message'] = $operatorUniqueIdName ? $operatorUniqueIdName . ' not found.' : 'Unique Id not found.';
                return $relData;
            }
        }

        if (isset($data['user_group_id'])) {
            $relData['user_group_id'] = $data['user_group_id'];
        }

        // check if record in user relation table exist
        if (isset($relData) && !empty($relData)) {
            if ($userRelationExist) {
                $userRelationExist->update($relData);
            } else {
                if (!isset($relData['unique_id'])) {
                    $relData['unique_id'] = $operatorDetail->unique_id;
                }
                if (!isset($relData['manager_email'])) {
                    $relData['manager_email'] = $operatorDetail->email;
                }
                $relData['manager_id'] = $master_user_id;
                $relData['user_id'] = $user['id'];
                $relData['master_user_id'] = $master_user_id;
                $isDefaultCheck = UserRelation::whereUserId($user['id'])->whereIsCurrentOperator(1)->first();
                // if ($isDefaultCheck) {
                $relData['is_current_operator'] = 0;
                // } else {
                //     $relData['is_current_operator'] = 1;
                // }
                UserRelation::create($relData);
            }

        } else {
            if (!isset($relData['unique_id'])) {
                $relData['unique_id'] = $operatorDetail->unique_id;
            }
            if (!isset($relData['manager_email'])) {
                $relData['manager_email'] = $operatorDetail->email;
            }
            $relData['manager_id'] = $master_user_id;
            $relData['user_id'] = $user['id'];
            $relData['master_user_id'] = $master_user_id;
            $isDefaultCheck = UserRelation::whereUserId($user['id'])->whereIsCurrentOperator(1)->first();
            if ($isDefaultCheck) {
                $relData['is_current_operator'] = 0;
            } else {
                $relData['is_current_operator'] = 1;
            }
            UserRelation::create($relData);
        }
        $allDefaultCourses = TrainingCourse::whereMasterUserId($master_user_id)->whereIsDefault(1)->get();
        $allUsers = UserRelation::whereMasterUserId($master_user_id)->whereUserId($user->id)->get();

        if ($allDefaultCourses->count() > 0) {
            foreach ($allDefaultCourses as $key => $defaultCourse) {
                if ($allUsers->count() > 0) {
                    foreach ($allUsers as $key => $defaultUser) {
                        UserAssignTrainingCourses::create([
                            'user_id' => $user->id,
                            'training_course_id' => $defaultCourse->id,
                            'master_user_id' => $master_user_id,
                            'is_default_course' => 1,
                            'is_manual_course' => 0,
                        ]);
                    }
                }
            }
        }
        if (isset($data['user_group_id']) && !empty($data['user_group_id'])) {
            $groupCourse = [];
            $groupCourseIds = [];
            $getCurrentGroupCourses = AssignTrainingCourses::whereMasterUserId($master_user_id)->whereUserGroupId($data['user_group_id'])->get()->pluck('training_course_id')->toArray();
            if (count($getCurrentGroupCourses) > 0) {
                $groupCourseIds = array_values($getCurrentGroupCourses);
            }
            if (count($groupCourseIds) > 0) {
                $userDetails = $this->model->find($user->id);
                dispatch((new \App\Jobs\CourseNotificationJob($userDetails, $master_user_id, $groupCourseIds)));
            }
        }
        return $user;
    }

    public function changeSmartAwardUserEmail($request)
    {
        $data = $request->all();
        $user = $this->model->where('email', $data['current_email_address'])->first();
        $operatorId = config('constants.smart_award_operator_id');

        if (empty($user)) {
            $userUpdate = ['message' => "Email not found."];
        } else {
            if (isset($user->added_user_id) && $user->added_user_id == $operatorId) {
                $userUpdate = $user->update(['email' => $data['new_email_address']]);
            } else {
                $userUpdate = ['message' => "Email not found."];
            }
        }
        return $userUpdate;
    }

    public function setSmartAwardUserPassword($request)
    {
        $data = $request->all();
        $user = $this->model->where('email', $data['email'])->first();
        $operatorId = config('constants.smart_award_operator_id');
        if (empty($user)) {
            $userUpdate = ['message' => "User not found."];
        } else {
            if (isset($user->added_user_id) && $user->added_user_id == $operatorId) {
                $password = bcrypt($data['password']);
                $userUpdate = $user->update(['password' => $password]);
            } else {
                $userUpdate = ['message' => "User not found."];
            }
        }
        return $userUpdate;
    }

    public function getAssessorUsersList($data)
    {
        $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
        //$master_user_id = auth()->guard('operator')->id();
        $selectColumns = [
            $this->table . '.id',
            $this->table . '.name',
            $this->table . '.email',
            $this->table . '.created_at',
            $this->table . '.assessor_id',
            $this->table . '.assessor_role',
            $this->table . '.photo',
            $this->table . '.last_logged_in_at',
            $this->table . '.status',
            $this->table . '.email_verified_at',
            DB::raw("IF(`user_relations`.`user_group_id` IS NOT NULL,
            (select groups.name from `groups` where groups.id=user_relations.user_group_id),null) as group_name"),
            // 'user_relations.assign_courses',
            'user_relations.manager_email',
            'user_relations.unique_id',
            'user_relations.user_group_id',
        ];

        $query = User::select($selectColumns)
            ->join(DBTableNames::USER_RELATIONS . ' as user_relations', $this->table . '.id', '=', 'user_relations.user_id')
            ->leftJoin(DBTableNames::USER_GROUPS . ' as groups', 'groups.id', '=', 'user_relations.user_group_id')
            ->where('user_relations.master_user_id', $operatorId)
            ->where('assessor_role', 'Yes');

        // Show only assigned users if sub-operator has not permission to view all users data
        if (!canViewAllUsersData(auth()->guard('operator')->user()->role_id)) {
            $query = $query->whereIn($this->table . '.id', getAssignedUsersId());
        }

        // Searching
        $search = isset($data['search_key']) ? $data['search_key'] : "";
        $searchFields = ['users.name', 'users.email', 'users.created_at', 'users.status', 'users.assessor_id', 'users.assessor_role', 'groups.name', 'user_relations.manager_email', 'user_relations.unique_id', 'last_logged_in_at'];
        if (!empty($search)) {
            $query = $query->where(function ($query) use ($searchFields, $search) {
                foreach ($searchFields as $key => $field) {
                    $query->orWhere($field, 'LIKE', '%' . $search . '%');
                }
            });
        }

        /* Advance Filters */
        if (isset($data['filters']) && count($data['filters']) > 0) {
            if (isset($data['filters']['name']) && $data['filters']['name'] != "") {
                $query = $query->where($this->table . '.name', 'LIKE', '%' . $data['filters']['name'] . '%');
            }
            if (isset($data['filters']['assessor_id']) && $data['filters']['assessor_id'] != "") {
                $query = $query->where($this->table . '.assessor_id', 'LIKE', '%' . $data['filters']['assessor_id'] . '%');
            }
            if (isset($data['filters']['assessor_role']) && $data['filters']['assessor_role'] != "") {
                $query = $query->where($this->table . '.assessor_role', 'LIKE', '%' . $data['filters']['assessor_role'] . '%');
            }
            if (isset($data['filters']['email']) && $data['filters']['email'] != "") {
                $query = $query->where($this->table . '.email', 'LIKE', '%' . $data['filters']['email'] . '%');
            }
            if (isset($data['filters']['group_name']) && $data['filters']['group_name'] != "") {
                $query = $query->where('groups.name', 'LIKE', '%' . $data['filters']['group_name'] . '%');
            }
            if (isset($data['filters']['manager_email']) && $data['filters']['manager_email'] != "") {
                $query = $query->where('user_relations.manager_email', 'LIKE', '%' . $data['filters']['manager_email'] . '%');
            }
            if (isset($data['filters']['unique_id']) && $data['filters']['unique_id'] != "") {
                $query = $query->where('user_relations.unique_id', 'LIKE', '%' . $data['filters']['unique_id'] . '%');
            }
            if (isset($data['filters']['last_logged_in_at']) && $data['filters']['last_logged_in_at'] != "") {
                $query = $query->whereDate($this->table . '.last_logged_in_at', '=', $data['filters']['last_logged_in_at']);
            }
            if (isset($data['filters']['created_at']) && $data['filters']['created_at'] != "") {
                $query = $query->whereDate($this->table . '.created_at', '=', $data['filters']['created_at']);
            }
            if (isset($data['filters']['status']) && $data['filters']['status'] != "") {
                $query = $query->where($this->table . '.status', $data['filters']['status']);
            }
        }

        // Sorting
        $sort = 'last_logged_in_at'; // Default sort by ID
        $sortingKeys = [
            'id' => 'id',
            'name' => 'name',
            'email' => 'email',
            'group_name' => 'group_name',
            'manager_email' => 'manager_email',
            'unique_id' => 'unique_id',
            'assessor_id' => 'assessor_id',
            'assessor_role' => 'assessor_role',
            'last_logged_in_at' => 'last_logged_in_at',
            'created_at' => 'created_at',
            'status' => "status",
        ];
        if (isset($data['sort_by']) && $data['sort_by'] != "") {
            if (array_key_exists($data['sort_by'], $sortingKeys)) {
                $sort = $sortingKeys[$data['sort_by']];
            }
        }
        // Ordering (Default DESC)
        $order = isset($data['order_by']) ? $data['order_by'] : 'DESC';

        // Apply Sorting and Ordering
        if (in_array($sort, $sortingKeys)) {
            $query = $query->orderBy($sort, $order);
        }

        // if (isset($data['filters']['assigned_course']) && $data['filters']['assigned_course'] != "") {
        //     $query = $query->having('assigned_course','=',$data['filters']['assigned_course']);
        // }
        if (isset($data['group_id']) && $data['group_id'] != "") {
            $query = $query->where('user_relations.user_group_id', '=', $data['group_id']);
        }
        return $query;
    }

    public function AssessorExportCsv($users, $fields = null)
    {
        //$columns = ['name' => 'NAME', 'email' => 'EMAIL', 'manager_email' => 'MANAGER EMAIL', 'group_name' => 'GROUP NAME', 'unique_id' => 'UNIQUE ID', 'assessor_id' => 'AssessorID', 'assessor_role' => 'Assessor Role', 'last_logged_in_at' => 'LAST LOGIN DATE', 'created_at' => 'CREATED AT', 'status' => 'STATUS'];
        $columns = ['name' => 'NAME', 'email' => 'EMAIL', 'assessor_id' => 'AssessorID', 'assessor_role' => 'Assessor Role', 'last_logged_in_at' => 'LAST LOGIN DATE', 'created_at' => 'CREATED AT', 'status' => 'STATUS'];
        $exportFields = array_flip((empty($fields)) ? array_keys($columns) : $fields);
        $fileName = '../storage/' . time() . '-' . randomString() . '.csv';
        $fp = fopen($fileName, 'w');
        fputcsv($fp, array_intersect_key($columns, $exportFields));

        foreach ($users as $raw) {
            $output = [
                "name" => $raw->name,
                "email" => $raw->email,
                "assessor_id" => $raw->assessor_id,
                "assessor_role" => $raw->assessor_role,
                "last_logged_in_at" => $raw->last_logged_in_at ?? '--',
                'created_at' => date_format($raw->created_at, 'Y-m-d'),
                'status' => $raw->status,
            ];

            fputcsv($fp, array_intersect_key($output, $exportFields));
        }

        return response()->download($fileName, time() . '-operatorUsers.csv', csvHeaders())->deleteFileAfterSend(true);
    }

    public function AssignAssessor($data)
    {
        $insert = [];
        AssessorAssignUser::where('assessor_id', $data['assessorId'])->delete();
        $assessor_id = User::where('id', $data['assessorId'])->value('assessor_id');
        foreach ($data['assignedUserIds'] as $key => $value) {
            $insert[$key]['user_id'] = $value;
            $insert[$key]['assessor_id'] = $data['assessorId'];
            $insert[$key]['master_user_id'] = auth()->guard('operator')->user()->id;
            $insert[$key]['assessorid'] = $assessor_id;
            $insert[$key]['created_at'] = date('Y-m-d H:i:s');
            $insert[$key]['updated_at'] = date('Y-m-d H:i:s');
        }
        AssessorAssignUser::insert($insert);
        return true;
    }

    public function getAssessorAssignFrontUsersList($data)
    {
        $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
        //$master_user_id = auth()->guard('operator')->id();
        $selectColumns = [
            $this->table . '.id',
            $this->table . '.name',
            $this->table . '.email',
            $this->table . '.created_at',
            $this->table . '.photo',
            $this->table . '.last_logged_in_at',
            $this->table . '.status',
            $this->table . '.email_verified_at',
            DB::raw("IF(`user_relations`.`user_group_id` IS NOT NULL,
            (select groups.name from `groups` where groups.id=user_relations.user_group_id),null) as group_name"),
            // 'user_relations.assign_courses',
            'user_relations.manager_email',
            'user_relations.unique_id',
            'user_relations.user_group_id',
        ];
        $assessorIds = User::where('assessor_role', 'Yes')->pluck('id');
        $query = User::select($selectColumns)
            ->join(DBTableNames::USER_RELATIONS . ' as user_relations', $this->table . '.id', '=', 'user_relations.user_id')
            ->leftJoin(DBTableNames::USER_GROUPS . ' as groups', 'groups.id', '=', 'user_relations.user_group_id')
            ->where('user_relations.master_user_id', $operatorId)
            ->whereNotIn('users.id', $assessorIds);

        // Show only assigned users if sub-operator has not permission to view all users data
        if (!canViewAllUsersData(auth()->guard('operator')->user()->role_id)) {
            $query = $query->whereIn($this->table . '.id', getAssignedUsersId());
        }

        // Searching
        $search = isset($data['search_key']) ? $data['search_key'] : "";
        $searchFields = ['users.name', 'users.email', 'users.created_at', 'users.status', 'users.assessor_id', 'users.assessor_role', 'groups.name', 'user_relations.manager_email', 'user_relations.unique_id', 'last_logged_in_at'];
        if (!empty($search)) {
            $query = $query->where(function ($query) use ($searchFields, $search) {
                foreach ($searchFields as $key => $field) {
                    $query->orWhere($field, 'LIKE', '%' . $search . '%');
                }
            });
        }

        /* Advance Filters */
        if (isset($data['filters']) && count($data['filters']) > 0) {
            if (isset($data['filters']['name']) && $data['filters']['name'] != "") {
                $query = $query->where($this->table . '.name', 'LIKE', '%' . $data['filters']['name'] . '%');
            }
            if (isset($data['filters']['email']) && $data['filters']['email'] != "") {
                $query = $query->where($this->table . '.email', 'LIKE', '%' . $data['filters']['email'] . '%');
            }
            if (isset($data['filters']['group_name']) && $data['filters']['group_name'] != "") {
                $query = $query->where('groups.name', 'LIKE', '%' . $data['filters']['group_name'] . '%');
            }
            if (isset($data['filters']['manager_email']) && $data['filters']['manager_email'] != "") {
                $query = $query->where('user_relations.manager_email', 'LIKE', '%' . $data['filters']['manager_email'] . '%');
            }
            if (isset($data['filters']['unique_id']) && $data['filters']['unique_id'] != "") {
                $query = $query->where('user_relations.unique_id', 'LIKE', '%' . $data['filters']['unique_id'] . '%');
            }
            if (isset($data['filters']['last_logged_in_at']) && $data['filters']['last_logged_in_at'] != "") {
                $query = $query->whereDate($this->table . '.last_logged_in_at', '=', $data['filters']['last_logged_in_at']);
            }
            if (isset($data['filters']['created_at']) && $data['filters']['created_at'] != "") {
                $query = $query->whereDate($this->table . '.created_at', '=', $data['filters']['created_at']);
            }
            if (isset($data['filters']['status']) && $data['filters']['status'] != "") {
                $query = $query->where($this->table . '.status', $data['filters']['status']);
            }
        }

        // Sorting
        $sort = 'last_logged_in_at'; // Default sort by ID
        $sortingKeys = [
            'id' => 'id',
            'name' => 'name',
            'email' => 'email',
            'group_name' => 'group_name',
            'manager_email' => 'manager_email',
            'unique_id' => 'unique_id',
            'last_logged_in_at' => 'last_logged_in_at',
            'created_at' => 'created_at',
            'status' => "status",
        ];
        if (isset($data['sort_by']) && $data['sort_by'] != "") {
            if (array_key_exists($data['sort_by'], $sortingKeys)) {
                $sort = $sortingKeys[$data['sort_by']];
            }
        }
        // Ordering (Default DESC)
        $order = isset($data['order_by']) ? $data['order_by'] : 'DESC';

        // Apply Sorting and Ordering
        if (in_array($sort, $sortingKeys)) {
            $query = $query->orderBy($sort, $order);
        }
        if (isset($data['group_id']) && $data['group_id'] != "") {
            $query = $query->where('user_relations.user_group_id', '=', $data['group_id']);
        }
        return $query;
    }

    public function QRCodeGenerate($data)
    {
	$operatorId = auth()->guard('operator')->user()->id;
        $actualURL = url(route('users.AssessorAssign')). '?type=assessorAssign&operator_id=' . $operatorId . '&user_id=' . $data['id'];
        $androidactualURL = url(route('users.AssessorAssign')). '?type=assessorAssign&operator_id=' . $operatorId . '&user_id=' . $data['id'];
        // $AndroiddynamicUrl = generateFirebaseDeepLink($androidactualURL, $operatorId);
        $deeplinkUrl =$this->deeplinkService->generateDeeplink([
                                    'target_url' => $androidactualURL,
                                    'operator_id' => $operatorId,
                                    'type' => 'assessorAssign',
                                    'entity_id' => $operatorId,
                                    'entity_type' => 'assessorAssign',
                                ]);
        $AndroiddynamicUrl=$deeplinkUrl;
        // $dynamicUrl = generateFirebaseDeepLink($actualURL, $operatorId) . '?type=assessorAssign&operator_id=' . $operatorId . '&user_id=' . $data['id'];
        $deeplinkUrl =$this->deeplinkService->generateDeeplink([
                                    'target_url' => $actualURL,
                                    'operator_id' => $operatorId,
                                    'type' => 'assessorAssign',
                                    'entity_id' => $operatorId,
                                    'entity_type' => 'assessorAssign',
                                ]);
        $dynamicUrl=$deeplinkUrl;
        $qrCode = generateQRCodeAssessor($dynamicUrl);
        $file = storage_path('/qrcodes/') . $qrCode;
        \Storage::disk('s3')->put(getUsersPath() . '/qrcode/' . $data['id'] . '/' . $qrCode, file_get_contents($file));
        $AssessorId = assessorUniqueIdGenerate();
        User::where('id', $data['id'])->update(['qr_code' => $qrCode,'qr_code_link' => $AndroiddynamicUrl, 'assessor_id' => $AssessorId]);
    }

    // function for specific operator users
    public function getAnalyticsUsersListing($data)
    {
        $operatorId = $data['operatorID'];
        $selectColumns = [
            $this->table . '.id',
            $this->table . '.email',
            $this->table . '.created_at',
        ];

        $query = User::select($selectColumns);
        $query->join(DBTableNames::USER_RELATIONS . ' as user_relations', $this->table . '.id', '=', 'user_relations.user_id');
        if((isset($data['startDate']) && $data['startDate'] != "") && (isset($data['endDate']) && $data['endDate'] != "")){
            $startDate = date("Y-m-d H:i:s",strtotime($data['startDate']));
            $endDate = date("Y-m-d H:i:s",strtotime($data['endDate']));
            $query->where(function($q) use ($operatorId,$startDate, $endDate) {
                $q->where($this->table .'.created_at','>=',$startDate);
                $q->where($this->table .'.created_at','<=',$endDate);
                $q->where('user_relations.master_user_id', $operatorId);
            });
        }else{
            $query->where('user_relations.master_user_id', $operatorId);
        }

        $query = $query->orderBy($this->table.'.created_at', 'DESC');
        return $query;
    }
}
