<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;
use App\Models\Industry;

class IndustryRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array
    {
        \Validator::extend('name_exists',
            function($attribute, $value, $parameters)
            {
                $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
                if(isset($parameters[0]) && !is_null($parameters[0])){
                    $existNews = Industry::where('name',$value)->where('master_user_id',$operatorId)->where('id','!=', $parameters[0])->first();
                }else{
                    $existNews = Industry::where('name',$value)->where('master_user_id',$operatorId)->first();
                }
                return $existNews ? false : true;
            }
        );
        return [
            'name' => 'required|max:255|name_exists:'.$this->id,
            'status' => 'required|in:Active,Inactive',
        ];
    }

    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'name.required' => __('Industry name is required.'),
            'name.name_exists' => __('Industry name already exists.'),
            'name.max' => __('Industry name may not be greater than 255 characters.'),
            'status.required' => __('Status is required.'),
            'status.in' => __('Invalid status.'),
        ];
    }

}
