<?php

namespace App\Http\Requests\Operator\v1;

use App\Http\Requests\CustomFormRequest;

class UserGroupDeleteRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {
        return [
            'ids' => 'required|array',
             'ids.*' => 'required|int'
        ];
    }

    public function messages() : array
    {
        return [
            'ids.required' => __('Group id is required.'),
            'ids.*' => __('Group id must be integer.'),
        ];
    }
}
