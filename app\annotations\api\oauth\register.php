<?php 
/**
     * @OA\Post(
     *     path="/api/oauth/register",
     *     tags={"Mobile - Auth"},
     *     summary="User register",
     *     description="User register with details",
     *     operationId="register",
     *     @OA\Parameter(
     *         description="Platform",
     *         name="platform",
     *         in="header",
     *         required=false,
     *         @OA\Schema(
     *             type="string",
     *             enum={"Android", "iOS", "Web"}
     *         )
     *     ),
     *     @OA\Parameter(
     *         description="App Version",
     *         name="appVersion",
     *         in="header",
     *         required=false,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Parameter(
     *         description="Device",
     *         name="device",
     *         in="header",
     *         required=false,
     *         @OA\Schema(
     *             type="string",
     *             enum={"Android", "iOS"}
     *         )
     *     ),
     *     @OA\Parameter(
     *         description="Device Unique Id",
     *         name="deviceUniqueId",
     *         in="header",
     *         required=false,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="email",
     *                     description="Email Address",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="password",
     *                     description="Password",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="device_type",
     *                     description="device_type Iphone,Android",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="device_token",
     *                     description="Device Token",
     *                     type="string"
     *                 ),
     *                 example={"name": "John Doe", "email": "<EMAIL>", "password": "Indianic@123", "device_type": "Android", "device_token": "pfe9dgXujGVqDVypYOZb2b6TMrGWBP"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     * )
     */