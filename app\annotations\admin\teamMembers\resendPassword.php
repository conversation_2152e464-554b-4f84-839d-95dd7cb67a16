<?php
/**
     * @OA\Get(
     *     path="/admin/teamMembers/resendPassword/{id}",
     *     tags={"Admin - Team Members Management"},
     *     summary="Resend Password to team member",
     *     description="Resend Password to team member",
     *     operationId="resendPassword",
     *     @OA\Parameter(
     *         description="id",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */