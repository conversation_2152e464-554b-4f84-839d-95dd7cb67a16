<?php

namespace App\Http\Resources\Operator\v1;
use App\Models\TrainingCourseSubModulePracticalAssessmentAnswer;
use App\Models\TrainingCourseSubModulePracticalAssessmentQuestion;
use DB;
use Illuminate\Http\Resources\Json\JsonResource;

class TrainingCourseSubModuleSelfAssessmentResource extends JsonResource
{
    public function toArray($request)
    {

        //PQMS Progress Calculation Status
        $UserCount = TrainingCourseSubModulePracticalAssessmentAnswer::where('submodule_id', $this->submodule_id)->where('assessor_id',$this->assessor_user_id)->where('user_id',$this->uid)->count('id');
        if($UserCount>0){
            $result = DB::table('training_course_submodule_practical_assessment_results')->where('submodule_id', $this->submodule_id)->where('assessor_id', $this->assessor_user_id)->where('user_id', $this->uid)->value('is_pass');
            if (isset($result) && $result==0 || $result==1) {
                $status = $result;
            } else {
                $status = 2;
            }
        }else {
            $status = 3;
        }

        return [
            "uid" => $this->uid,
            "submodule_id" => $this->submodule_id,
            // "assessor_id" => $this->assessor_id,
            // "assessor_user_id" => $this->assessor_user_id,
            // "accessor_name" => $this->accessor_name,
            // "accessor_email" => $this->accessor_email,
            "uname" => $this->uname,
            "uemail" => $this->uemail,
            "manager_email" => $this->manager_email,
            "assessed_by_assessor" => $this->assessed_by_assessor,
            "assessment_result" => $status,
            "updated_at" => !empty($this->updated_at)?date_format($this->updated_at, 'Y-m-d H:i:s'):null,
        ];
    }
}
