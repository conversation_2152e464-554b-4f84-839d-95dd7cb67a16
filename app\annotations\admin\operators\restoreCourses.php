<?php
/**
     * @OA\Post(
     *     path="/admin/operators/restoreCourses",
     *     tags={"Admin - Operators Management"},
     *     summary="Restore Courses",
     *     description="Restore Courses",
     *     operationId="restoreCourses",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="ids",
     *                     description="ids",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                
     *                 example={"ids": {"3","4"}}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */