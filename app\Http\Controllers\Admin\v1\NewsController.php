<?php

namespace App\Http\Controllers\Admin\v1;

use Illuminate\Http\Response;
use App\Models\News;
use App\Repositories\Admin\v1\NewsRepository;
use App\Http\Requests\Admin\v1\CommonListingRequest;
use App\Http\Requests\Admin\v1\CommonBulkStatusChangeRequest;
use App\Http\Resources\Admin\v1\NewsDetailResource;
use App\Http\Resources\Admin\v1\NewsListingResource;

class NewsController extends Controller
{
    
    protected $model;
    protected $repository;
    
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->model = new News();
        $this->repository = new NewsRepository($this->model);
    }
    
    /**
     * List news
     *
     * @return \Illuminate\Http\Response
     */
    public function getListing(CommonListingRequest $request) {
        try {
            $news = $this->repository->getListing($request->all());
            if ($request->isExport) {
                return $this->repository->exportCsv($news->get(), $request->exportFields);
            }
            return NewsListingResource::collection($news->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * Get news details
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id) {
        try {
            $news = $this->model->find($id);
            return ($news) ?
                    (new NewsDetailResource($news)) :
                    response()->json(setErrorResponse(__('admin.news.not-found')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * Change news status
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function changeStatus(CommonBulkStatusChangeRequest $request) {
        try {
            $this->model->whereIn('id', $request->ids)->update(['status' => $request->status]);
            return response()->json(setResponse([], ['message' => __('admin.news.status-changed')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
