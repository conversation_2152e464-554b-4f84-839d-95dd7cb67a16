<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;
use DBTableNames;

class PushNotificationTemplateRequest extends CustomFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array
    {
        return [
            'title' => 'required|max:255',
            'template_key' => 'required|max:255|unique:' . DBTableNames::PUSH_NOTIFICATION_TEMPLATES . ',template_key,{$id},id,deleted_at,NULL',
            'user_type' => 'required|in:Admin,Operator,ContentProvider,EndUser',
            'message' => 'required',
        ];
    }
    
    /**
     * Get the validation messages that apply to the rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'title.required' => __('Please enter title.'),
            'template_key.required' => __('Please enter template key.'),
            'template_key.unique' => __('Please enter unique template key.'),
            'user_type.required' => __('Please select user type.'),
            'user_type.in' => __('Invalid user type.'),
            'message.required' => __('Please enter message.'),
        ];
    }
    
}
