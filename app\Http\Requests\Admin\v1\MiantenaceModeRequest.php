<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;

class MiantenaceModeRequest extends CustomFormRequest {

    /**
     * Get the validation rules that apply to the request.
     * @return array
     */
    protected function rules(): array {
	$request = $this;
	
	if ($request->push_notification && $request->email_notification) {
	    $rules = [
		'maintenance_mode' => 'required|boolean',
		'email_title' => 'required',
		'email_message' => 'required',
		'mobile_title' => 'required',
		'mobile_message' => 'required',
	    ];
	} elseif ($request->push_notification) {
	    $rules = [
		'maintenance_mode' => 'required|boolean',
		'mobile_title' => 'required',
		'mobile_message' => 'required',
	    ];
	} elseif ($request->email_notification) {
	    $rules = [
		'maintenance_mode' => 'required|boolean',
		'email_title' => 'required',
		'email_message' => 'required',
	    ];
	} else {
	    $rules = [
		'maintenance_mode' => 'required|boolean'
	    ];
	}
	return $rules;
    }

    /**
     * Get the validation messages that apply to the rules.
     * @return array
     */
    public function messages(): array {
	return [
	    'is_maintenance_mode.required' => __('Mode is required.'),
	    'email_title.required' => __('Email title is required.'),
	    'email_message.required' => __('Email message required.'),
	    'mobile_title.required' => __('Mobile title is required.'),
	    'mobile_message.required' => __('Mobile message is required.'),
	];
    }

}
