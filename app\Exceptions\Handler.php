<?php

namespace App\Exceptions;

use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Validation\ValidationException;
use <PERSON><PERSON>\Lumen\Exceptions\Handler as ExceptionHandler;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Kreait\Firebase\DynamicLink\CreateDynamicLink\FailedToCreateDynamicLink;
use Throwable;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class Handler extends ExceptionHandler {

    /**
     * A list of the exception types that should not be reported.
     *
     * @var array
     */
    protected $dontReport = [
	AuthorizationException::class,
	HttpException::class,
	ModelNotFoundException::class,
	ValidationException::class,
    ];

    /**
     * Report or log an exception.
     *
     * This is a great spot to send exceptions to Sen<PERSON>, <PERSON>nag, etc.
     *
     * @param  \Throwable  $exception
     * @return void
     *
     * @throws \Exception
     */
    public function report(Throwable $exception) {
        // Report to New Relic with enhanced context
        \App\Exceptions\NewRelicErrorHandler::report($exception);
        
        // Also call parent for standard reporting
	    parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Throwable  $exception
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     *
     * @throws \Throwable
     */
    public function render($request, Throwable $exception) {
       
	if ($exception instanceof ModelNotFoundException) {
	    \Log::info("Error Handler : ModelNotFoundException = " . json_encode($exception->getMessage()));
	    return response()->json(setErrorResponse('No record found'))
			    ->setStatusCode(JsonResponse::HTTP_NOT_FOUND);
	} else if ($exception instanceof \Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException) {
	    \Log::info("Error Handler : MethodNotAllowedHttpException = " . json_encode($exception->getMessage()));
	    return response()->json(setErrorResponse(__('Method not found')))
			    ->setStatusCode(Response::HTTP_METHOD_NOT_ALLOWED);
	} else if ($exception instanceof \Error) {
	    \Log::info("Error Handler : Error = " . json_encode($exception->getMessage()));
	    return response()->json(setErrorResponse(__('validation.something_wrong')))
			    ->setStatusCode(Response::HTTP_GATEWAY_TIMEOUT);
	} else if ($exception instanceof \Illuminate\Auth\AuthenticationException) {
	    \Log::info("Error Handler : AuthenticationException = " . json_encode($exception->getMessage()));
	    return response()->json(setErrorResponse(__('Unauthorized Access')))
			    ->setStatusCode(Response::HTTP_UNAUTHORIZED);
	} else if ($exception instanceof NotFoundHttpException) {
	    \Log::info("Error Handler : NotFoundHttpException = " . json_encode($exception->getMessage()));
	    return response()->json(setErrorResponse(__('Method not found')))
			    ->setStatusCode(Response::HTTP_METHOD_NOT_ALLOWED);
	} else if ($exception instanceof \Illuminate\Database\QueryException) {
	    \Log::info("Error Handler : QueryException = " . json_encode($exception->getMessage()));
	    return response()->json(setErrorResponse(__('validation.something_wrong')))
			    ->setStatusCode(Response::HTTP_INTERNAL_SERVER_ERROR);
	} else if ($exception instanceof FailedToCreateDynamicLink) {
	    \Log::info("Error Handler : FailedToCreateDynamicLink = " . json_encode($exception->getMessage()));
	    return response()->json(setErrorResponse(__('validation.something_wrong')))
			    ->setStatusCode(Response::HTTP_INTERNAL_SERVER_ERROR);
	} else if ($exception instanceof \Swift_TransportException) {
	    \Log::info("Error Handler : Swift_TransportException = " . json_encode($exception->getMessage()));
	    return response()->json(setErrorResponse(__('Failed to authenticate on SMTP server')))
			    ->setStatusCode($exception->getCode());
	} else if ($exception instanceof \RuntimeException) {
	    \Log::info("Error Handler : RuntimeException = " . json_encode($exception->getMessage()));
	    return response()->json(setErrorResponse(__('validation.something_wrong')))
			    ->setStatusCode(JsonResponse::HTTP_UNAUTHORIZED);
	} else if ($exception instanceof \Illuminate\Contracts\Container\BindingResolutionException) {
	    \Log::info("Error Handler : BindingResolutionException = " . json_encode($exception->getMessage()));
	    return response()->json(setErrorResponse(__('validation.something_wrong')))
			    ->setStatusCode(JsonResponse::HTTP_UNAUTHORIZED);
	} else {
	    return parent::render($request, $exception);
	}
    }

}
