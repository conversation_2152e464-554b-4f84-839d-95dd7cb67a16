<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;
use Illuminate\Validation\Rule;

class EmailSettingRequest extends CustomFormRequest {

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules(): array {
		$request = $this;
		// print_r($this->toArray());exit;
		$rules = [
			'operator_id' => 'required|int',
			'is_email_settings_on' => 'required|boolean'
		];
		if (isset($request->is_email_settings_on) && $request->is_email_settings_on) {
			$rules = array_merge($rules, [
			'mail_username' => 'required',
			'mail_password' => 'required',
			'mail_from_name' => 'required',
			'mail_from_address' => 'required',
			'app_name' => 'required',
			'logo' => [
				Rule::requiredIf(function ()use ($request) {
				// Get the value of 'logo_name' from the database
				$logoName = \DB::table('white_label_settings')->where('operator_id', $request->operator_id)->value('logo_name');
				return $logoName === null;
				}),
				'file',
				'mimes:png',
			],
			'view_background_image' => [
				Rule::requiredIf(function ()use ($request) {
				// Get the value of 'view_background_image' from the database
				$backgroundImageName = \DB::table('white_label_settings')->where('operator_id', $request->operator_id)->value('view_background_image');
				return $backgroundImageName === null;
				}),
				'file',
				'mimes:png,jpg,jpeg',
			],
			'fallback_link' => 'required'
			]);
		}
		return $rules;
    }

    public function messages(): array {
		return [
			'operator_id.required' => __('Operator Id is required.'),
			'is_email_settings_on.required' => __('Email Feature setting is required.'),
			'mail_username.required' => __('Mail User Name is required.'),
			'mail_password.required' => __('Mail Password is required.'),
			'mail_from_name.required' => __('Mail From Name is required.'),
			'mail_from_address.required' => __('Mail From Address is required.'),
			'app_name.required' => __('App name is required.'),
			'logo_name.required' => __('Email logo is required.'),
			'view_background_image.required' => __('Email background image is required.'),
			'fallback_link.required' => __('Fallback link is required.'),
		];
    }
}
