<?php

namespace App\Http\Requests\Operator\v1;

use App\Http\Requests\CustomFormRequest;

class MediaRequest extends CustomFormRequest {

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        $rule = [
            'type' => 'required|in:submodules,resources',
            'media_type' => 'required|in:video,videowiththumb,pdf,pdfwiththumb,document,zip'
        ];

        // Add cable matrix specific validation
        if ($this->has('question_type') && $this->question_type === 'cableMatrix') {
            $rule = array_merge($rule, [
                'question_type' => 'required|in:cableMatrix'
            ]);
        }

        // Add location matrix specific validation
        if ($this->has('question_type') && $this->question_type === 'locationMatrix') {
            $rule = array_merge($rule, [
                'question_type' => 'required|in:locationMatrix'
            ]);
        }

        // dd($this->media_type);
        if($this->media_type == 'document'){
            $rule = array_merge($rule,['file' => 'required|mimes:pdf,ppt,docx,txt,pptx,doc,csv,excel,xlsx,xls']);
        }else if($this->media_type == 'video'){
            $rule = array_merge($rule,['file' => 'required|mimes:mp4,mov']);
        }else if($this->media_type == 'pdf'){
            $rule = array_merge($rule,['file' => 'required|mimes:pdf']);
        }else if($this->media_type == 'zip'){
            $rule = array_merge($rule,['file' => 'required|mimes:zip']);
        }else{
            array_merge($rule,['file' => 'required|mimes:pdf,ppt,docx,txt,pptx,doc,mp4,mov,zip,csv,excel,xlsx,xls']);
        }
        return $rule;
    }
    
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'type.in' => __('Type must be submodules'),
            'file.required' => __('File is required'),
            'media_type.required' => __('Media type must be video'),
        ];
    }

}
