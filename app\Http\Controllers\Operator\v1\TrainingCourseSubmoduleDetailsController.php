<?php

namespace App\Http\Controllers\Operator\v1;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use DBTableNames;
use App\Repositories\Operator\v1\TrainingCourseSubmoduleDetailsRepository;
use App\Repositories\Operator\v1\TrainingCourseSubModuleCourseFeedbackRepository;
use App\Repositories\Operator\v1\TrainingCourseSubModuleQuizResultRepository;
use App\Repositories\Operator\v1\TrainingCourseRenameSubmoduleTypesRepository;
use App\Http\Resources\Operator\v1\TrainingCourseSubmoduleDetailsResource;
use App\Http\Resources\Operator\v1\TrainingCourseSubModuleQuizPdfResultResource;
use App\Http\Resources\Operator\v1\TrainingCourseSubModuleMiniQuizPdfResultResource;
use App\Http\Requests\Operator\v1\TrainingCourseSubmoduleDetailsRequest;
use App\Http\Requests\Operator\v1\SortSubmodulesRequest;
use App\Http\Requests\Operator\v1\TrainingCourseRenameSubModuleTypeRequest;
use App\Models\TrainingCourseSubmoduleDetails;
use App\Models\TrainingCourse;
use App\Models\TrainingCourseModules;
use Illuminate\Http\Response;
use App\Models\TrainingCourseSubModuleTypes;
use App\Http\Resources\Operator\v1\TrainingCourseSubModuleTypesResource;
use App\Http\Resources\Operator\v1\TrainingCourseRenameSubModuleTypesResource;
use App\Http\Requests\Operator\v1\TrainingCourseSubModuleCopyPasteRequest;
use App\Http\Requests\Operator\v1\TrainingCourseSubmoduleUpdateDetailsRequest;
use App\Http\Requests\Operator\v1\TrainingCourseSubmoduleStatusRequest;
use App\Models\TrainingCourseSubModuleTitleSlide;
use App\Models\TrainingCourseSubModuleUploadVideo;
use App\Models\TrainingCourseSubModuleImageGallery;
use App\Models\TrainingCourseSubModuleProductList;
use App\Models\TrainingCourseSubModuleFeedbackQuestion;
use App\Models\TrainingCourseSubModuleFeedbackQuestionOptions;
use App\Models\TrainingCourseSubModuleHappyUnhappy;
use App\Models\TrainingCourseSubModuleVideoGuide;
use App\Models\TrainingCourseSubModuleVideoGuideSteps;
use App\Models\TrainingCourseSubModuleConfirmation;
use App\Models\TrainingCourseSubModuleJob;
use App\Models\TrainingCourseSubModuleConfirmationBox;
use App\Models\PrerequisiteSubModules;
use App\Models\ScormProgress;
use App\Models\TrainingCourseSubModuleQuizCategory;
use App\Models\TrainingCourseSubModuleFeedbackQuestionAnswer;
use App\Models\TrainingCourseSubModuleQuizResults;
use App\Models\TrainingCourseSubModuleFeedbackPDFResults;
use App\Models\TrainingCourseRenameSubModuleTypes;
use App\Models\TrainingCourseSubModulePracticalAssessmentQuestion;
use App\Models\TrainingCourseSubModulePracticalAssessmentOption;
use App\Http\Requests\Operator\v1\TrainingCourseSubmoduleCreateMicroLearningRequest;
use App\Http\Requests\Operator\v1\TrainingCourseSubmoduleUpdateMicroLearningRequest;
use App\Http\Requests\Operator\v1\MicroLearningProgressRequest;
use App\Models\TrainingCourseModuleProgress;
use App\Models\TrainingCourseProgress;
use App\Models\MasterUser;
use App\Http\Requests\Operator\v1\sendMicroLearningEmail;
use App\Notifications\MicroLearningOperatorSendEmail;
use Illuminate\Support\Facades\Notification;
use App\Models\WebNotifications;
use App\Http\Requests\Operator\v1\RestoreAssessmentQuestionOptions;
class TrainingCourseSubmoduleDetailsController extends Controller
{

    private $model;

    public function __construct() {
        $this->model = new TrainingCourseSubmoduleDetails();
        $this->repository = new TrainingCourseSubmoduleDetailsRepository($this->model);
        $this->quizResultModel = new TrainingCourseSubModuleQuizResults();
        $this->quizResultRepository = new TrainingCourseSubModuleQuizResultRepository($this->quizResultModel);
        $this->renameSubmoduleTypeModel = new TrainingCourseRenameSubModuleTypes();
        $this->renameSubmoduleTypeRepository = new TrainingCourseRenameSubmoduleTypesRepository($this->renameSubmoduleTypeModel);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
      /**
     * @OA\Post(
     *     path="/operator/trainingCourseSubmodule",
     *     tags={"Operator - Training Course Submodule"},
     *     summary="Store Training Course Submodule Web View",
     *     description="Store Training Course Submodule Web View",
     *     operationId="store",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="module_id",
     *                     description="Module Id",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="submodule_type_id",
     *                     description="Submodule Type Id",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="submodule_name",
     *                     description="Submodule Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="thumbnail",
     *                     description="Thumbnail",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="description",
     *                     description="Description",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="url_360",
     *                     description="360 URL",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="submodule_lock",
     *                     description="Submodule Lock",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="submodule_complete",
     *                     description="Submodule Complete",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="duration",
     *                     description="Duration (Month)",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="duration_type",
     *                     description="Duration Type",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="unlock_datetime",
     *                     description="Unlock Datetime",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="enable_time_spend",
     *                     description="Enable Time Spend",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="completion_percentage",
     *                     description="Completion Percentage",
     *                     type="string"
     *                 ),
     *                  @OA\Property(
     *                     property="condition",
     *                     description="Condition",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="time_spent",
     *                     description="Time Spent (Seconds)",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="time_spent_type",
     *                     description="Time Spent Type",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="touch_count",
     *                     description="Tourch Count (only for 360)",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 @OA\Property(
     *                     property="notify_users",
     *                     description="notify_users",
     *                     type="string",
     *                 ),
     *                  @OA\Property(
     *                     property="auto_approve",
     *                     description="auto_approve",
     *                     type="string",
     *                 ),
     *
     *                 @OA\Property(
     *                     property="prerequisite_submodules",
     *                     description="prerequisite_submodules",
     *                     type="string"
     *                 ),
     *                 example={"training_course_id": 1,"module_id": 1,"submodule_type_id": 2,"title": "360 image","thumbnail": "demo1.jpg","description": "Description Template","url_360": "This is Send Push Notification email template update","submodule_lock": 1,"submodule_complete": 0,"duration": "","duration_type": "","unlock_datetime": "2014-08-12 11:14:54","enable_time_spend": 1,"completion_percentage": 90,"condition": 0,"time_spent": 15,"time_spent_type": "Seconds","touch_count": 0,"status": "Active", "notify_users": 0, "auto_approve": 0, "prerequisite_submodules": "[{id: 2,percentage: 100}]"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function store(TrainingCourseSubmoduleDetailsRequest $request)
    {
        try {
            $this->repository->create($request);
            $data = [];
            if(request()->submodule_type_id == 17){
                $submoduleLastData = TrainingCourseSubmoduleDetails::where('training_course_id',request()->training_course_id)
                                                ->where('module_id',request()->module_id)
                                                ->where('submodule_type_id',request()->submodule_type_id)
                                                ->orderBy('id','DESC')
                                                ->first();
                $url = $submoduleLastData->scorm_zip_name_url;
                $data = [
                    'scorm_zip_name_url' => $url,
                    'scorm_course_id' => $submoduleLastData->scorm_course_id
                ];
            }
            return response()->json(setResponse($data, ['message' => __('operator.TrainingCourseSubmoduleDetails.add')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/trainingCourseSubmodule/{id}",
     *     tags={"Operator - Training Course Submodule"},
     *     summary="Get Training Course Submodule Details",
     *     description="Get Training Course Submodule Details",
     *     operationId="show",
     *     @OA\Parameter(
     *         description="Id of training course submodule to fetch",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function show($id) {
        try {
            $subModule = $this->model->find($id);
            $modRepo = (new TrainingCourseSubmoduleDetailsResource($subModule));
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $courseList = TrainingCourse::where('master_user_id', $operatorId)->pluck('id')->toArray();
            // checking for assessment portal feature setting
            $checkAssessment = checkAssessmentWithId($subModule->submodule_type_id);
            if($checkAssessment == 1) {
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
                return ($subModule) ? $modRepo :
                response()->json(setErrorResponse(__('operator.TrainingCourseSubmoduleDetails.notExist')))->setStatusCode(Response::HTTP_NOT_FOUND);

        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
      /**
     * @OA\Put(
     *     path="/operator/trainingCourseSubmodule/{id}",
     *     tags={"Operator - Training Course Submodule"},
     *     summary="Update Training Course Submodule Web View",
     *     description="Update Training Course Submodule Web View",
     *     operationId="update",
     *     @OA\Parameter(
     *         description="Id of training course sub module to update",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="module_id",
     *                     description="Module Id",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="submodule_type_id",
     *                     description="Submodule Type Id",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="submodule_name",
     *                     description="Submodule Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="thumbnail",
     *                     description="Thumbnail",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="description",
     *                     description="Description",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="url_360",
     *                     description="360 URL",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="submodule_lock",
     *                     description="Submodule Lock",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="submodule_complete",
     *                     description="Submodule Complete",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="duration",
     *                     description="Duration (Month)",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="duration_type",
     *                     description="Duration Type",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="unlock_datetime",
     *                     description="Unlock Datetime",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="enable_time_spend",
     *                     description="Enable Time Spend",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="completion_percentage",
     *                     description="Completion Percentage",
     *                     type="string"
     *                 ),
     *                  @OA\Property(
     *                     property="condition",
     *                     description="Condition",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="time_spent",
     *                     description="Time Spent (Seconds)",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="time_spent_type",
     *                     description="Time Spent Type",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="touch_count",
     *                     description="Tourch Count (only for 360)",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="notify_users",
     *                     description="notify_users",
     *                     type="string",
     *                 ),
     *                 @OA\Property(
     *                     property="auto_approve",
     *                     description="auto_approve",
     *                     type="string",
     *                 ),
     *                 @OA\Property(
     *                     property="prerequisite_submodules",
     *                     description="prerequisite_submodules",
     *                     type="string"
     *                 ),
     *                 example={"training_course_id": 1,"module_id": 1,"submodule_type_id": 2,"title": "360 image","thumbnail": "demo1.jpg","description": "Description Template","url_360": "This is Send Push Notification email template update","submodule_lock": 1,"submodule_complete": 0,"duration": "","duration_type": "","unlock_datetime": "2014-08-12 11:14:54","enable_time_spend": 1,"completion_percentage": 90,"condition": 0,"time_spent": 15,"time_spent_type": "Seconds","touch_count": 0,"status": "Active", "notify_users": 0, "auto_approve": 1, "prerequisite_submodules": "[{id: 2,percentage: 100}]"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function update(TrainingCourseSubmoduleUpdateDetailsRequest $request, $id)
    {
        try {
            $submodules = $this->model->find($id);
            $checkAssessment = checkAssessmentWithId($submodules->submodule_type_id);
            if($checkAssessment == 1){
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
            // main
            $this->repository->update($request, $id);
            $data = [];
            if($submodules->submodule_type_id == 17){
                $submoduleLastData = TrainingCourseSubmoduleDetails::where('id',$id)
                                                ->first();
                $url = $submoduleLastData->scorm_zip_name_url;
                $data = [
                    'scorm_zip_name_url' => $url,
                    'scorm_course_id' => $submoduleLastData->scorm_course_id
                ];
            }
            return response()->json(setResponse($data, ['message' => __('operator.TrainingCourseSubmoduleDetails.update')]))->setStatusCode(Response::HTTP_OK);

        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Delete(
     *     path="/operator/trainingCourseSubmodule/{id}",
     *     tags={"Operator - Training Course Submodule"},
     *     summary="Delete Training Course Submodule",
     *     description="Delete Training Course Submodule",
     *     operationId="delete",
     *     @OA\Parameter(
     *         description="id",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Training Course Submodule Deleted successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function destroy($id) {
        try{
            if(isset($id) && !is_null($id)){
                $subModule = $this->model->find($id);
                if(!empty($subModule)){
                    $checkAssessment = checkAssessmentWithId($subModule->submodule_type_id);
                    if($checkAssessment == 1){
                        return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
                    }else{
                        \DB::transaction(function () use ($id) {
                            $this->repository->delete($id);
                        });
                        return response()->json(setResponse([], ['message' => __('operator.TrainingCourseSubmoduleDetails.delete')]))->setStatusCode(Response::HTTP_OK);
                    }
                }else{
                    return response()->json(setErrorResponse(__('operator.TrainingCourseSubmoduleDetails.notExist')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
            }
        } catch(\Exception $e)   {
            dd($e);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/trainingCourseSubmodule/{courseId}/{moduleId}",
     *     tags={"Operator - Training Course Submodule"},
     *     summary="Get List Of Sub Modules by Course and Module id",
     *     description="Get List Of Sub Modules by Course and Module id",
     *     operationId="getCourseSubmodulesList",
     *     @OA\Parameter(
     *         description="Training Course Id",
     *         name="courseId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Parameter(
     *         description="Training Course Submodule Id",
     *         name="moduleId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getCourseSubmodulesList($courseId, $moduleId){
        try {
            // checking for assessment portal feature setting
            $checkAssessment = checkAssessment();
            $allSubModules = TrainingCourseSubmoduleDetails::query();
                            $allSubModules->select('id', 'submodule_name', 'submodule_type_id', 'description', 'status');
                            $allSubModules->whereTrainingCourseId($courseId);
                            $allSubModules->whereModuleId($moduleId);
                            if($checkAssessment == 1){
                                $allSubModules->where('submodule_type_id','!=',16);
                            }
                            $allSubModules->orderBy('display_order');
            $allCourseSubModules = $allSubModules->get();
            $message = count($allCourseSubModules) > 0 ? __('operator.TrainingCourseSubmoduleDetails.listFound') : __('operator.TrainingCourseSubmoduleDetails.listNotFound');
            return response()->json(setResponse($allCourseSubModules, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

      /**
     * @OA\Get(
     *     path="/operator/trainingCourseSubmodule/quizMiniquizExport/{courseId}/{moduleId}",
     *     tags={"Operator - Training Course Submodule"},
     *     summary="Get List Of CSVs by Course and Module id",
     *     description="Get List Of CSVs by Course and Module id",
     *     operationId="getCourseSubmodulesList",
     *     @OA\Parameter(
     *         description="Training Course Id",
     *         name="courseId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Parameter(
     *         description="Training Course Submodule Id",
     *         name="moduleId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getAllQuizMiniquizExport($courseId, $moduleId){
        try {
            $manager = getOperator();
            $operatorEmail = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->email;
            $operatorUniqueId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->unique_id;

            $allQuizMiniQuizSubModules = TrainingCourseSubmoduleDetails::select('training_course_submodule_details.id as submodule_id','submodule_type_id','training_course_submodule_quiz_results.attempts','training_course_submodule_details.training_course_id','training_course_submodule_quiz_results.pdf','training_course_submodule_feedback_pdf_results.pdf as m_pdf','u1.name','u1.email','u2.name as u2_name','u2.email as u2_email', 'training_course_submodule_quiz_results.updated_at','training_course_submodule_feedback_pdf_results.updated_at as m_updated_at','group.name as group_name', 'training_course_submodule_details.submodule_type_id','training_course_submodule_details.submodule_name' )
            ->leftjoin('training_course_submodule_quiz_results','training_course_submodule_quiz_results.submodule_id','training_course_submodule_details.id')
            ->leftjoin('training_course_submodule_feedback_pdf_results','training_course_submodule_feedback_pdf_results.submodule_id','training_course_submodule_details.id')
            ->leftjoin('users as u1','u1.id','training_course_submodule_quiz_results.user_id')
            ->leftjoin('users as u2','u2.id','training_course_submodule_feedback_pdf_results.user_id')
            ->leftJoin(DBTableNames::USER_RELATIONS . ' as relation', function($join) use($manager) {
                $join->on('relation.user_id', '=', 'u1.id');
                $join->where('relation.master_user_id', '=', $manager->id);
            })
            ->leftJoin(DBTableNames::USER_GROUPS . ' as group', 'group.id', '=', 'relation.user_group_id')
            ->where('training_course_submodule_details.training_course_id', $courseId)
            ->where('training_course_submodule_details.module_id', $moduleId)->whereIn('submodule_type_id', [7, 12])->orderBy('display_order')
            ->get();
            $AllExportLinkList = [];
            $LinkArray = [];


            foreach($allQuizMiniQuizSubModules as $k=>$quizminiquiz){

                if(!empty($quizminiquiz->pdf.$quizminiquiz->m_pdf)) {

                    $quiz_pdf = $quizminiquiz->pdf ? env('CDN_URL') . getTrainingCourseSubmodulePath($quizminiquiz->training_course_id) . '/quiz/' . $quizminiquiz->pdf : env('CDN_URL') . getTrainingCourseSubmoduleFeedbackPath($quizminiquiz->training_course_id) . $quizminiquiz->m_pdf;

                    $AllExportLinkList[$k]['submodule_id'] = $quizminiquiz->submodule_id;
                    $AllExportLinkList[$k]['submodule_name'] = $quizminiquiz->submodule_name;
                    $AllExportLinkList[$k]['submodule_type_id'] = $quizminiquiz->submodule_type_id;
                    $AllExportLinkList[$k]['uname'] = $quizminiquiz->name ? $quizminiquiz->name : $quizminiquiz->u2_name;
                    $AllExportLinkList[$k]['uemail'] = $quizminiquiz->email ? $quizminiquiz->email : $quizminiquiz->u2_email;
                    $AllExportLinkList[$k]['manager_email'] = $operatorEmail;
                    $AllExportLinkList[$k]['unique_id'] = $operatorUniqueId;
                    $AllExportLinkList[$k]['gname'] = $quizminiquiz->group_name;
                    $AllExportLinkList[$k]['attempts'] = $quizminiquiz->attempts;
                    $AllExportLinkList[$k]['pdf'] = $quiz_pdf;
                    $AllExportLinkList[$k]['updated_at'] = $quizminiquiz->updated_at ? Carbon::parse($quizminiquiz->updated_at)->format('Y-m-d') : Carbon::parse($quizminiquiz->m_updated_at)->format('Y-m-d');
                }
            }
            $groupBySubmodule = array();

                foreach ( $AllExportLinkList as $value ) {
                    $groupBySubmodule[$value['submodule_id']][] = $value;

                }
                foreach($groupBySubmodule as $g){
                    $LinkArray[]  = $this->quizResultRepository->exportModuleAllQuizCsv($g, null);

                }

            $message = count($LinkArray) > 0 ? __('operator.TrainingCourseSubmoduleDetails.csvFound') : __('operator.TrainingCourseSubmoduleDetails.csvNotFound');
            if(count($LinkArray) > 0){
                return response()->json(setResponse($LinkArray, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
            }else{
                return response()->json(setErrorResponse($message))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }


    /**
     * @OA\Get(
     *     path="/operator/trainingCourseSubmodule/getAllCourseSubModules/{courseId}",
     *     tags={"Operator - Training Course Submodule"},
     *     summary="Get All Prerequisite Submodules",
     *     description="Get All Prerequisite Submodules",
     *     operationId="getAllCourseSubModules",
     *     @OA\Parameter(
     *         description="Training Course Id",
     *         name="courseId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getAllCourseSubModules($courseId){
        try {
            $allCourseModules = TrainingCourseSubmoduleDetails::select('training_course_submodule_details.id', 'training_course_submodule_details.training_course_id', 'training_course_submodule_details.module_id', 'training_course_modules.name', 'training_course_submodule_details.submodule_name', 'training_course_submodule_details.status')
                ->leftJoin('training_course_modules', 'training_course_submodule_details.module_id', '=', 'training_course_modules.id')
                ->where('training_course_submodule_details.training_course_id', '=', $courseId)
                ->get()->toArray();
            $message = count($allCourseModules) > 0 ? __('operator.TrainingCourseSubmoduleDetails.prerequisiteListFound') : __('operator.TrainingCourseSubmoduleDetails.prerequisiteListNotFound');
            return response()->json(setResponse($allCourseModules, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/trainingCourseSubmodule/getQuizCategories/{courseId}",
     *     tags={"Operator - Training Course Submodule"},
     *     summary="Get All Quiz Categories",
     *     description="Get All Quiz Categories",
     *     operationId="getQuizCategories",
     *     @OA\Parameter(
     *         description="Training Course Id",
     *         name="courseId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="List fetched successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getQuizCategories($courseId)
    {
        try {
            $categories = TrainingCourseSubModuleQuizCategory::select('id', 'name')->withCount('questions as totalQuestions')->where(['training_course_id' => $courseId, 'status' => 'Active'])->get()->toArray();
            return response()->json(setResponse($categories))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourseSubmodule/changeStatus",
     *     tags={"Operator - Training Course Submodule"},
     *     summary="Update Submodules status",
     *     description="Update Submodules status",
     *     operationId="changeStatus",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="id",
     *                     description="id",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="Status",
     *                     type="string",
     *                     enum={"Active", "Inactive"}
     *                 ),
     *                 example={"status":"Inactive","id":9}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Status updated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function changeStatus(TrainingCourseSubmoduleStatusRequest $request)
    {
        try {
            if ($request->status === 'Inactive') {
                $prerequisiteSubmodule = PrerequisiteSubModules::where(['prerequisite_submodule_id' => $request->id])->first();
                if ($prerequisiteSubmodule) {
                    return response()->json(setErrorResponse(__('operator.TrainingCourseSubmoduleDetails.status-not-changed')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            }
            $checkStatus = $this->repository->change_status($request);
            if($checkStatus == 0){
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }else{
                return response()->json(setResponse([], ['message' => __('operator.TrainingCourseSubmoduleDetails.status')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/trainingCourseSubmodule/subModulesDuplicate/{id}",
     *     tags={"Operator - Training Course Submodule"},
     *     summary="Duplicate Submodules",
     *     description="Duplicate Submodules",
     *     operationId="duplicateSubModule",
     *     @OA\Parameter(
     *         description="Training Course submodule Id",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Submodule duplicated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function duplicateSubModule($id)
    {
        try {
            $courseSubModules = TrainingCourseSubmoduleDetails::find($id);
            // checking for assessment portal feature setting
            $checkAssessment = checkAssessmentWithId($courseSubModules->submodule_type_id);
            if($checkAssessment == 1) {
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }

            if($courseSubModules->submodule_type_id == 17){
                return response()->json(setErrorResponse(__('operator.TrainingCourseSubmoduleDetails.scormTypeSubmodule')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            $result = \DB::transaction(function () use ($id,$courseSubModules) {
                $imageName = '';
                $hotspotImage = $hotspotPhoto = null;
                if(!empty($courseSubModules->thumbnail)){
                    $image = explode('.',$courseSubModules->thumbnail);
                    $imageName = $image[0].rand().'.'.$image[1];
                }
                if(!empty($courseSubModules->hotspot_image)){
                    $image = explode('.',$courseSubModules->hotspot_image);
                    $hotspotImage = $image[0].rand().'.'.$image[1];
                    \Storage::disk('s3')->copy(getTrainingCourseSubmodulePath($courseSubModules->training_course_id)."/".$courseSubModules->hotspot_image, getTrainingCourseSubmodulePath($courseSubModules->training_course_id)."/".$hotspotImage);
                }
                if(!empty($courseSubModules->hotspot_photo)){
                    $image = explode('.',$courseSubModules->hotspot_photo);
                    $hotspotPhoto = $image[0].rand().'.'.$image[1];
                    \Storage::disk('s3')->copy(getTrainingCourseSubmodulePath($courseSubModules->training_course_id)."/".$courseSubModules->hotspot_photo, getTrainingCourseSubmodulePath($courseSubModules->training_course_id)."/".$hotspotPhoto);
                }
                $latestDisplayOrders = TrainingCourseSubmoduleDetails::whereTrainingCourseId($courseSubModules->training_course_id)->whereModuleId($courseSubModules->module_id)->orderby('display_order', 'DESC')->first();
                if($latestDisplayOrders){
                    $displayOrder = $latestDisplayOrders->display_order + 1;
                }else{
                    $displayOrder = 1;
                }
                $newCourseSubModules = $courseSubModules->replicate()->fill([
                    'submodule_name' => $courseSubModules->submodule_name .' - copy',
                    'thumbnail' => $imageName,
                    'status' => 'Inactive',
                    'hotspot_image' => $hotspotImage,
                    'hotspot_photo' => $hotspotPhoto,
                    'copy_from' => $id,
                    'total_subdata' => 0,
                    'display_order' => $displayOrder
                ]);
                $newCourseSubModules->save();
                if(!empty($courseSubModules->thumbnail)){
                    \Storage::disk('s3')->copy(getTrainingCourseSubmodulePath($courseSubModules->training_course_id)."/".$courseSubModules->thumbnail, getTrainingCourseSubmodulePath($newCourseSubModules->training_course_id)."/".$imageName);
                }
                // Duplicate Submodule child Data
                TrainingCourseSubmoduleDetails::duplicateSubModulesChild($newCourseSubModules, $courseSubModules->training_course_id, true, auth()->guard('operator')->user());
            });
            return response()->json(setResponse([], ['message' => __('operator.TrainingCourseSubmoduleDetails.duplicate')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }


    /**
     * @OA\Get(
     *     path="/operator/trainingCourseSubmodule/getSubModuleTypes",
     *     tags={"Operator - Training Course Submodule"},
     *     summary="Get SubModule Types",
     *     description="Get SubModule Types",
     *     operationId="getSubModuleTypes",
     *     @OA\Parameter(
     *         description="",
     *         name="search_key",
     *         in="query",
     *         required=false,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getSubModuleTypes(Request $request) {
        try {
            $checkAssessment = checkAssessment();
            $search = $request->search_key;
            if (!empty($search)) {
                $submoduletypes = TrainingCourseSubModuleTypes::query();
                $submoduletypes->with('renameSubmoduleType');
                $submoduletypes->orderBy('display_order');
                $submoduletypes->where(function($q) use($search){
                        $q->Where('name', 'LIKE', '%'.$search.'%');
                        $q->orWhereHas('renameSubmoduleType', function($q) use($search){
                            $q->Where('name', 'LIKE', '%'.$search.'%');
                        });
                    });
                if($checkAssessment == 1){
                    $submoduletypes->where('id','!=',16);
                }
                $submoduletypes->where('status','Active');
                $types = $submoduletypes->get();
            } else {
                $submoduletypes = TrainingCourseSubModuleTypes::query();
                                $submoduletypes->orderBy('display_order');
                                if($checkAssessment == 1){
                                    $submoduletypes->where('id','!=',16);
                                }
                                $submoduletypes->where('status','Active');
                $types = $submoduletypes->get();
            }
            return response()->json(setResponse([
                'types' => (TrainingCourseSubModuleTypesResource::collection($types))
            ], []))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/trainingCourseSubmodule/copyPasteSubModule",
     *     tags={"Operator - Training Course Submodule"},
     *     summary="Copy Past selected sub module into target module",
     *     description="Copy Past selected sub module into target module",
     *     operationId="copyPasteSubModule",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 example={"moduleId":1, "subModuleId":1}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Module added in to selected course Successfully"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function copyPasteSubModule(TrainingCourseSubModuleCopyPasteRequest $request)
    {
        try {
            $subModule = $this->model->find($request->subModuleId);
            // checking for assessment portal feature setting
            $checkAssessment = checkAssessmentWithId($subModule->submodule_type_id);
            if($checkAssessment == 1) {
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }

            if($subModule->submodule_type_id == 17){
                return response()->json(setErrorResponse(__('operator.TrainingCourseSubmoduleDetails.scormTypeSubmodule')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            // including single submodule in single module course
            $moduleDetails = TrainingCourseModules::where('id',$request->moduleId)->first();
            $trainingCourse = $moduleDetails->course;
            if($trainingCourse->single_submodule == 1){
                $trainingCourseSubModuleCount = TrainingCourseSubmoduleDetails::where('module_id',$request->moduleId)->whereNull('deleted_at')->count();
                if($trainingCourseSubModuleCount == 0){
                    $submoduleDetails = TrainingCourseSubmoduleDetails::where('id',$request->subModuleId)
                                                ->where(function($query) {
                                                    $query->orWhere('submodule_type_id', config('constants.submodule_types.question_bank_quiz'))
                                                        ->orWhere('submodule_type_id', config('constants.submodule_types.practical_assessment'));
                                                })
                                                ->whereNull('deleted_at')->first();

                    if(!empty($submoduleDetails)){
                        TrainingCourseSubmoduleDetails::copyPasteSubModule($request->moduleId, $request->subModuleId, auth()->guard('operator')->user());
                        return response()->json(setResponse([], ['message' => __('operator.TrainingCourseSubmoduleDetails.copyPaste')]))->setStatusCode(Response::HTTP_OK);
                    }else{
                        return response()->json(setErrorResponse(__('operator.TrainingCourseSubmoduleDetails.addOnlyQuizPASubModule')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                    }
                } else{
                    return response()->json(setErrorResponse(__('operator.TrainingCourseSubmoduleDetails.singleModuleCourseError')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            }else{
                TrainingCourseSubmoduleDetails::copyPasteSubModule($request->moduleId, $request->subModuleId, auth()->guard('operator')->user());
                return response()->json(setResponse([], ['message' => __('operator.TrainingCourseSubmoduleDetails.copyPaste')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }


    /**
     * @OA\Post(
     *     path="/operator/trainingCourseSubmodule/sortSubModules",
     *     tags={"Operator - Training Course Submodule"},
     *     summary="Copy Past selected sub module into target module",
     *     description="Copy Past selected sub module into target module",
     *     operationId="sortSubModules",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 example={"{'sort_submodules' : [{'submodule_id':1,'order':1},{'submodule_id':2,'order':2},{'submodule_id':3,'order':3}]}"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Module added in to selected course Successfully"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function sortSubModules(SortSubmodulesRequest $request)
    {
        try {
            $submoduleList = $request->sort_submodules;
            foreach($submoduleList as $key => $submodule){
                TrainingCourseSubmoduleDetails::whereId($submodule['submodule_id'])->update(['display_order' => $submodule['order']]);
            }
            return response()->json(setResponse([], ['message' => __('operator.TrainingCourseSubmoduleDetails.sortSuccess')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/trainingCourseSubmodule/getRenamedSubModuleTypesList",
     *     tags={"Operator - Training Course Submodule"},
     *     summary="Get Renamed SubModule Types List",
     *     description="Get Renamed SubModule Types",
     *     operationId="getRenamedSubModuleTypesList List",
     *     @OA\Parameter(
     *         description="",
     *         name="search_key",
     *         in="query",
     *         required=false,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getAllSubModuleTypes(Request $request) {
        try {
            $checkAssessment = checkAssessment();
            $search = $request->search_key;
            if (!empty($search)) {
                $query = TrainingCourseSubModuleTypes::with('renameSubmoduleType');
                $order = 'asc';
                $sort = 'display_order';
                if (isset($request->sort_by) && $request->sort_by != "") {
                    $sort = $request->sort_by;
                }
                if (isset($request->order_by) && $request->order_by != "") {
                    $order = $request->order_by;
                }
                $query->orderBy($sort,$order);
                $query->where(function($q) use($search){
                    $q->Where('name', 'LIKE', '%'.$search.'%');
                    $q->orWhereHas('renameSubmoduleType', function($q) use($search){
                        $q->Where('name', 'LIKE', '%'.$search.'%');
                    });
                });
                if($checkAssessment == 1){
                    $query->where('id','!=',16);
                }
                $query->where('status','Active');
                $types = $query;
            } else {
                $query = TrainingCourseSubModuleTypes::with('renameSubmoduleType');
                $order = 'asc';
                $sort = 'display_order';
                if (isset($request->sort_by) && $request->sort_by != "") {
                    $sort = $request->sort_by;
                }
                if (isset($request->order_by) && $request->order_by != "") {
                    $order = $request->order_by;
                }
                if($checkAssessment == 1){
                    $query->where('id','!=',16);
                }
                $query->where('status','Active');
                $query->orderBy($sort,$order);
                $types = $query;
            }
            return TrainingCourseRenameSubModuleTypesResource::collection($types->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Get(
     *     path="/operator/getSubmoduleType/{id}",
     *     tags={"Operator - Edit Submodule Type"},
     *     summary="Edit Submodule Type",
     *     description="Edit Submodule Type",
     *     operationId="getSubmoduleType",
     *     @OA\Parameter(
     *         description="id",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function getSubmoduleType($id) {
        try {
            $checkAssessment = checkAssessmentWithId($id);
            if($checkAssessment == 1){
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
            $submoduleType = TrainingCourseSubModuleTypes::with('renameSubmoduleType')->where('id',$id)->where('status','Active')->first();
            return ($submoduleType) ?
                    (new TrainingCourseRenameSubModuleTypesResource($submoduleType)) :
                    response()->json(setErrorResponse(__('operator.products.not-found')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/updateSubmoduleType",
     *     tags={"Operator - Update submodule type"},
     *     summary="Update submodule type",
     *     description="Update submodule type",
     *     operationId="updateSubmoduleType",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="submodule_type_id",
     *                     description="submodule_type_id",
     *                     format="int64",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="name",
     *                     description="name",
     *                     type="string",
     *                 ),
     *                 @OA\Property(
     *                     property="description",
     *                     description="description",
     *                     type="string",
     *                 ),
     *                 example={"submodule_type_id" : 2,"name" : "Web View OP4","description" : "Show an embedded web page OP4"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Status updated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function updateSubmoduleType(TrainingCourseRenameSubModuleTypeRequest $request)
    {
        try {
            $checkAssessment = checkAssessmentWithId($request->submodule_type_id);
            if($checkAssessment == 1) {
                return response()->json(setErrorResponse(__('operator.assessment.unauthorized')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
            // main
            $this->renameSubmoduleTypeRepository->renameSubmoduleType($request, $request->submodule_type_id);
            return response()->json(setResponse([], ['message' => __('operator.TrainingCourseSubmoduleDetails.updateSubmoduleType')]))->setStatusCode(Response::HTTP_OK);

        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
      /**
     * @OA\Post(
     *     path="/operator/micro-learning/trainingCourseSubmodule/create",
     *     tags={"Operator -Micro Learning"},
     *     summary="Store Training Course Submodule Micro Learning",
     *     description="Store Training Course Submodule Micro Learning",
     *     operationId="storeMicroLearningSubmodule",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="submodule_name",
     *                     description="Submodule Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="thumbnail",
     *                     description="Thumbnail",
     *                     type="file"
     *                 ),
     *                  @OA\Property(
     *                      description="file",
     *                      property="file",
     *                      type="file",
     *                 ),
     *                 @OA\Property(
     *                     property="description",
     *                     description="Description",
     *                     type="string"
     *                 ),
     *                 example={"training_course_id": 1,"title": "micro learning submodule ","thumbnail": "demo1.jpg","file": "demo1.html","description": "Description Template"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function storeMicroLearningSubmodule(TrainingCourseSubmoduleCreateMicroLearningRequest $request)
    {
        try {
            $result=$this->repository->createMicroLearningSubmodule($request);
            return response()->json(setResponse(['submodule_id'=>$result], ['message' => __('operator.TrainingCourseSubmoduleDetails.add')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="/operator/micro-learning/trainingCourseSubmodule/update",
     *     tags={"Operator -Micro Learning"},
     *     summary="Store Training Course Submodule Micro Learning",
     *     description="Store Training Course Submodule Micro Learning",
     *     operationId="storeMicroLearningSubmodule",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                  @OA\Property(
     *                     property="id",
     *                     description="Id",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course Id",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="submodule_name",
     *                     description="Submodule Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="thumbnail",
     *                     description="Thumbnail",
     *                     type="file"
     *                 ),
     *                  @OA\Property(
     *                      description="file",
     *                      property="file",
     *                      type="file",
     *                 ),
     *                 @OA\Property(
     *                     property="description",
     *                     description="Description",
     *                     type="string"
     *                 ),
     *                 example={"id": 3113,"title": "micro learning submodule ","thumbnail": "demo1.jpg","file": "demo1.html","description": "Description Template"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function updateMicroLearningSubmodule(TrainingCourseSubmoduleUpdateMicroLearningRequest $request)
    {
        try {
            $this->repository->updateMicroLearningSubmodule($request);
            return response()->json(setResponse([], ['message' => __('operator.TrainingCourseSubmoduleDetails.update')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="operator/micro-learning/MicroLearningProgress",
     *     tags={"Operator - Micro Learning"},
     *     summary="Store Progress For Training Course Module for Micro Learning",
     *     description="Store Progress For Training Course Module for Micro Learning",
     *     operationId="progressMicroLearningModule",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="submodule_id",
     *                     description="Submodule Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="operator_id",
     *                     description="Operator ID",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="user_id",
     *                     description="User ID",
     *                     type="integer"
     *                 ),
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function MicroLearningProgress(MicroLearningProgressRequest $request) {
        try {
            $result = DB::transaction(function () use ($request) {
                $data = $request->all();
                $submoduleDetails = TrainingCourseSubmoduleDetails::find($data['submodule_id']);
                $data['module_id'] = $submoduleDetails->module_id;
                $data['training_course_id'] = $submoduleDetails->training_course_id;

                $subModuleCount = TrainingCourseSubmoduleDetails::where('training_course_id',$data['training_course_id'])->where('module_id',$data['module_id'])->count();
                $moduleCount = TrainingCourseModules::where('training_course_id',$data['training_course_id'])->count();

                TrainingCourseModules::where('id',$data['module_id'])->update(['total_submodules'=>$subModuleCount]);
                TrainingCourse::where('id',$data['training_course_id'])->update(['total_modules'=>$moduleCount]);

                // Submodule progress
                $submoduleAdd = (new TrainingCourseSubmoduleDetails)->calculateMicroLearningSubmoduleProgress($data);
                // Module progress
                (new TrainingCourseModuleProgress)->calculateModuleProgress($data);
                // Training course progress
                (new TrainingCourseProgress)->calculateTrainingCourseProgress($data);
            });
            return response()->json(setResponse([], ['message' => __('Progress Added Successfully!')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="operator/micro-learning/trainingCourseSubmodule/sendEmail",
     *     tags={"Operator - Micro Learning"},
     *     summary="Send email and notification to operator and training course assigned users on click Generate Micro Learning Module",
     *     description="Send email and notification to operator and training course assigned users on click Generate Micro Learning Module",
     *     operationId="sendEmail",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="training_course_id",
     *                     description="Training Course ID",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="operator_id",
     *                     description="Operator ID",
     *                     type="integer"
     *                 ),
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function sendEmail(sendMicroLearningEmail $request) {
        try {
            $result = DB::transaction(function () use ($request) {
                $data = $request->all();
                $module = TrainingCourseModules::where('training_course_id',$data['training_course_id'])->where('is_micro_learning_module',1)->first();
                $masterUser = MasterUser::find($data['operator_user_id']);
                //Checking for email address if smartawards contains only digits before @
                $smartAwardsMailSendFlag = smartAwardsEmailSendResctrict($masterUser->email);
                if($smartAwardsMailSendFlag == 1){
                    Notification::send($masterUser, new MicroLearningOperatorSendEmail($data['training_course_id']));
                }

                $webNotification['type'] = 'MicroLearning';
                $webNotification['master_user_id'] = $data['operator_user_id'] ?? null;
                $webNotification['manager_id'] = $data['operator_user_id'] ?? null;
                $webNotification['training_course_id'] = $data['training_course_id'] ?? null;
                $webNotification['module_id'] = $module->id ?? null;
                WebNotifications::storeWebNotification($webNotification);
            });
            return response()->json(setResponse([], ['message' => __('Micro-learning module has been successfully generated.')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()), null, true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @OA\Post(
     *     path="operator/operator/trainingCourseSubmodule/questionOptions/restore",
     *     tags={"Operator - Training Course Submodule"},
     *     summary="Restore practical assessment deleted question options",
     *     description="Restore practical assessment deleted question options",
     *     operationId="questionOptionsRestore",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="question_id",
     *                     description="Question ID",
     *                     type="integer"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Operator": {}}
     *     },
     * )
     */
    public function questionOptionsRestore(RestoreAssessmentQuestionOptions $request) {

        try {
            $questionOptions = TrainingCourseSubModulePracticalAssessmentOption::where('question_id',request()->question_id)->withTrashed()->get();
            if(count($questionOptions) > 0){
                foreach ($questionOptions as $option) {
                    $option->deleted_at = null;
                    $option->save();
                }
                return response()->json(setResponse([], ['message' => __('operator.TrainingCourseSubmoduleDetails.questionOptionRestore')]))->setStatusCode(Response::HTTP_OK);
            }else{
                return response()->json(setResponse([], ['message' => __('operator.TrainingCourseSubmoduleDetails.optionsNotFound')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
