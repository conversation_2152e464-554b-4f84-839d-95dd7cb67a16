<?php

/**
     * @OA\Post(
     *     path="/api/oauth/resetPassword",
     *     tags={"Mobile - Auth"},
     *     summary="User reset password process",
     *     description="User reset password process",
     *     operationId="resetPassword",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="token",
     *                     description="Token",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="email",
     *                     description="Email",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="password",
     *                     description="Password",
     *                     type="string"
     *                 ),
     *                  @OA\Property(
     *                     property="password_confirmation",
     *                     description="Confirm Password",
     *                     type="string"
     *                 ),
     *                 example={"email": "<EMAIL>", "token": "c4b627de2282efa37d29c6a6356c6a7e512b7ac51917574b1493ca04effb37e3", "password": "Indianic@123", "password_confirmation": "Indianic@123"}
     *              )
     *          )
     *      ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     * )
     */