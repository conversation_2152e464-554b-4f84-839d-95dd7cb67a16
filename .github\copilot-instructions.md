# Copilot Instructions for skillbase-api-v2

## Project Overview
- This is a Laravel Lumen API project, structured for modularity and scalability.
- Major directories:
  - `app/`: Core logic, including `Http/Controllers`, `Models`, `Repositories`, `Services`, and `Helpers`.
  - `routes/`: API route definitions (see `api.php`, `admin.php`, `operator.php`).
  - `config/`: Environment and service configuration.
  - `database/`: Migrations, seeds, and factories.
  - `public/`: Entry point (`index.php`) and static assets.
  - `storage/`: Logs, cache, and file uploads.

## Key Workflows
- **Setup:**
  - Copy `.env.example` to `.env` and configure environment variables (DB, mail, AWS, etc.).
  - Run `composer install` to install dependencies.
  - Run migrations: `php artisan migrate` and optionally `php artisan db:seed`.
  - Generate API docs: `php artisan swagger-lume:generate` (not required in production).
  - For Windows, create storage symlink: `mklink /D <public/storage> <storage/app/public>`.
- **Development Branching:**
  - Use `develop` for active development, `staging` for pre-production, `master` for production.
  - Feature branches are created from `develop` and merged back via merge requests.
- **Testing:**
  - Use `phpunit` for tests (see `tests/` directory).
- **Queue/Jobs:**
  - Start queue worker for async jobs: `php artisan queue:work`.
- **Deployment:**
  - CI/CD is triggered by merges to `develop` or `staging`.

## Project-Specific Conventions
- **Controllers** are organized under `app/Http/Controllers/Api/` for API endpoints.
- **Repositories** and **Services** abstract business logic and data access.
- **Helpers** in `app/Helpers/` provide utility functions.
- **Swagger** is used for API documentation (see `php artisan swagger-lume:generate`).
- **AWS S3** integration via `league/flysystem-aws-s3-v3` (check `composer.json`).
- **SonarQube** is used for code quality (see README for setup).

## Integration Points
- **External services:** AWS S3, mail providers, and Firebase (see `config/` and `.env`).
- **API documentation:** Generated with Swagger Lume.
- **Queue system:** Used for email and other async tasks.

## Examples
- To add a new API endpoint:
  1. Define the route in `routes/api.php`.
  2. Implement the controller in `app/Http/Controllers/Api/`.
  3. Add business logic in a Service or Repository if needed.
  4. Update Swagger annotations for documentation.

- To run tests:
  ```sh
  vendor\bin\phpunit
  ```

## References
- See `README.md` for full setup, deployment, and SonarQube instructions.
- Key configs: `.env`, `config/`, `composer.json`.
- For storage and symlink issues, see the relevant section in the README.

---
Edit this file to keep AI agents up to date with project-specific knowledge and workflows.
