<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\PushNoticationTemplate;
use App\Http\Resources\Admin\v1\PushNotificationTemplateListingResource;
use App\Repositories\Admin\v1\PushNotificationTemplateRepository;
use App\Http\Requests\Admin\v1\CommonListingRequest;
use App\Http\Requests\Admin\v1\PushNotificationTemplateRequest;
use App\Http\Requests\Admin\v1\PushNotificationTemplateUpdateRequest;
use DB;

class PushNotificationTemplateController extends Controller
{
    protected $model;   
    protected $user_repository; 
    
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->model = new PushNoticationTemplate();
        $this->repository = new PushNotificationTemplateRepository($this->model);
    }

    /**
     * List All Push Notification templates
     *
     * @return \Illuminate\Http\Response
     */
    public function getPushNotificationtemplateList(CommonListingRequest $request)
    {
        try {
            $users = $this->repository->getPushNotificationListing($request->all());
            return PushNotificationTemplateListingResource::collection($users->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Add Push Notification Template
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(PushNotificationTemplateRequest $request)
    {
        // return $request->all();
        try {
            $result = DB::transaction(function () use ($request) {
                $moduleData = $request;
                $this->repository->create($moduleData);
            });
            return response()->json(setResponse([], ['message' => __('admin.PushNotification.add')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Update Push Notification Template
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(PushNotificationTemplateUpdateRequest $request, $id)
    {
        try {
            $this->repository->update($request, $id);
            return response()->json(setResponse([], ['message' => __('admin.PushNotification.update')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
    
    /**
     * Show Push Notification Template Details
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {   
        try {
            $pushNotificationDetail=$this->model->select('id','title','message')->whereId($id)->first();

            $message = !empty($pushNotificationDetail) ? __('admin.PushNotification.found') : __('admin.PushNotification.notFound');
            return response()->json(setResponse($pushNotificationDetail, ['message' => $message]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
