# Cable Matrix API Documentation

## Overview

The Cable Matrix feature allows users to select cables from a large dataset using hierarchical filtering and search functionality. This document outlines all API endpoints and payloads for frontend and mobile teams.

## Table of Contents

1. [Execution Flow](#execution-flow)
2. [CSV Upload API](#csv-upload-api)
3. [Mobile API Endpoints](#mobile-api-endpoints)
4. [Data Structures](#data-structures)
5. [<PERSON>rro<PERSON> Handling](#error-handling)
6. [Implementation Examples](#implementation-examples)

---

## Execution Flow

The Cable Matrix feature follows a specific execution flow that integrates with the existing submodule creation process:

### Frontend Execution Flow

1. **Create Training Course**
   - Standard training course creation

2. **Upload CSV File** (During Submodule Creation)
   - Upload CSV file using `POST /operator/uploadQuestionMedia`
   - Include `question_type: "cableMatrix"` and `id: {training_course_id}`
   - Receive file path and validation results
   - CSV is validated but not processed yet

3. **Create Submodule with Questions**
   - Submit submodule creation with question list
   - Include `file_name` and `file_path` from step 2 in question data
   - CSV data is automatically processed when question is created

4. **View/Update Submodule**
   - Use standard submodule detail and update APIs
   - CSV data is available for mobile consumption

### Backend Processing Flow

1. **CSV Upload** (`uploadQuestionMedia`)
   - Validate CSV structure and headers
   - Check for required columns and data integrity
   - Store file in S3 and return validation results

2. **Question Creation** (`TrainingCourseSubModulePracticalAssessmentQuestion::createData`)
   - Process uploaded CSV file
   - Parse and store data in `cable_matrix_data` table
   - Create hierarchical filter structure

3. **Mobile API Access**
   - Provide filtered data through API endpoints
   - Support search and pagination

---

## CSV Upload API

### Upload Cable Matrix CSV

**Endpoint:** `POST /operator/uploadQuestionMedia`

**Description:** Upload CSV file for cable matrix question type using existing upload infrastructure. This is done during submodule creation, before questions are created.

**Headers:**
```
Content-Type: multipart/form-data
Authorization: Bearer {operator_token}
```

**Request Payload:**
```javascript
{
  "type": "submodules",                    // Required: string
  "media_type": "document",                // Required: string
  "file": File,                           // Required: CSV file
  "question_type": "cableMatrix",         // Required: string
  "id": 344                               // Required: integer (training course ID)
}
```

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "file": "1690876543_cable_matrix.csv",
    "url": "https://s3-region.amazonaws.com/bucket/path/file.csv",
    "cable_matrix_validation": {
      "total_rows": 1500,
      "valid_rows": 1485,
      "headers": ["Index", "Cable Description", "Cable Type", "Kg/m", "Type", "Cable Diameter (mm)"],
      "sample_data": [
        {
          "Index": "1",
          "Cable Description": "Cable Description 1",
          "Cable Type": "Heavy",
          "Kg/m": "2.5",
          "Type": "Standard",
          "Cable Diameter (mm)": "16"
        }
      ]
    }
  },
  "message": "File uploaded successfully"
}
```

**Error Response (422):**
```json
{
  "success": false,
  "message": "Invalid CSV structure: Missing required headers: Cable Description",
  "errors": null
}
```

**CSV File Requirements:**
- Required headers: `Cable Diameter (mm)`, `Cable Type`, `Cable Description`
- Optional headers: `Index`, `Kg/m`, `Type` (stored as additional_data)
- Cable Description must not be blank or duplicate
- File format: CSV with comma separation
- Maximum file size: Follow default upload limits

---

## Mobile API Endpoints

### Unified Cable Matrix API

**Endpoint:** `GET /api/trainingCourseSubmodule/cableMatrix/{questionId}`

**Description:** Single, flexible API endpoint that supports multiple result types with filtering, search, and pagination capabilities.

**Headers:**
```
Authorization: Bearer {user_token}
```

**Query Parameters:**
```javascript
{
  "resultType": "diameter",      // Required: diameter|type|description (default: diameter)
  "diameter": "25",              // Optional: string - Filter by cable diameter
  "type": "Heavy",               // Optional: string - Filter by cable type
  "search": "heavy duty",        // Optional: string - Free text search (min: 2, max: 255)
  "page": 1,                     // Optional: integer - Page number (default: 1)
  "pageSize": 20,                // Optional: integer - Items per page (default: 20, max: 100)
  "distinct": true               // Optional: boolean - Return unique values only (default: true)
}
```

### Usage Examples

#### 1. Get All Diameters
```
GET /api/trainingCourseSubmodule/cableMatrix/123?resultType=diameter
```

**Response:**
```json
{
  "success": true,
  "data": {
    "resultType": "diameter",
    "filters": {
      "diameter": null,
      "type": null,
      "search": null
    },
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "distinct": true
    },
    "data": {
      "diameters": ["16", "25", "35", "50", "70", "95"],
      "count": 6
    }
  }
}
```

#### 2. Get Types for Specific Diameter
```
GET /api/trainingCourseSubmodule/cableMatrix/123?resultType=type&diameter=25
```

**Response:**
```json
{
  "success": true,
  "data": {
    "resultType": "type",
    "filters": {
      "diameter": "25",
      "type": null,
      "search": null
    },
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "distinct": true
    },
    "data": {
      "types": ["Heavy", "Light", "Medium", "Standard"],
      "count": 4,
      "filters": {
        "diameter": "25"
      }
    }
  }
}
```

#### 3. Get Descriptions with Filters and Pagination
```
GET /api/trainingCourseSubmodule/cableMatrix/123?resultType=description&diameter=25&type=Heavy&page=1&pageSize=10
```

**Response:**
```json
{
  "success": true,
  "data": {
    "resultType": "description",
    "filters": {
      "diameter": "25",
      "type": "Heavy",
      "search": null
    },
    "pagination": {
      "page": 1,
      "pageSize": 10,
      "distinct": true
    },
    "data": {
      "descriptions": [
        {
          "cable_description": "25mm Heavy Duty Cable Type A",
          "cable_diameter": "25",
          "cable_type": "Heavy",
          "additional_data": {
            "Index": "1",
            "Kg/m": "2.5",
            "Type": "Standard"
          }
        },
        {
          "cable_description": "25mm Heavy Duty Cable Type B",
          "cable_diameter": "25",
          "cable_type": "Heavy",
          "additional_data": {
            "Index": "2",
            "Kg/m": "2.8",
            "Type": "Premium"
          }
        }
      ],
      "pagination": {
        "current_page": 1,
        "per_page": 10,
        "total": 45,
        "last_page": 5,
        "from": 1,
        "to": 10
      },
      "filters": {
        "diameter": "25",
        "type": "Heavy",
        "search": null
      }
    }
  }
}
```

#### 4. Search Across All Fields
```
GET /api/trainingCourseSubmodule/cableMatrix/123?resultType=description&search=heavy%20duty&page=1&pageSize=20
```

**Response:**
```json
{
  "success": true,
  "data": {
    "resultType": "description",
    "filters": {
      "diameter": null,
      "type": null,
      "search": "heavy duty"
    },
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "distinct": true
    },
    "data": {
      "descriptions": [
        {
          "cable_description": "25mm Heavy Duty Cable Type A",
          "cable_diameter": "25",
          "cable_type": "Heavy",
          "additional_data": {
            "Index": "1",
            "Kg/m": "2.5",
            "Type": "Standard"
          }
        }
      ],
      "pagination": {
        "current_page": 1,
        "per_page": 20,
        "total": 12,
        "last_page": 1,
        "from": 1,
        "to": 12
      },
      "filters": {
        "diameter": null,
        "type": null,
        "search": "heavy duty"
      }
    }
  }
}
```

---

## Data Structures

### Cable Matrix Answer Submission

When submitting self-assessment answers, include cable matrix data in the answer object:

**Answer Object for Cable Matrix:**
```json
{
  "question_type": "cableMatrix",
  "cable_diameter": "25",
  "cable_type": "Heavy", 
  "cable_description": "25mm Heavy Duty Cable Type A",
  "additional_data": {
    "Index": "1",
    "Kg/m": "2.5", 
    "Type": "Standard"
  }
}
```

**Complete Self Assessment Submission Example:**
```json
{
  "training_course_id": 123,
  "module_id": 456,
  "submodule_id": 789,
  "attempt": 1,
  "assessed_by_assessor": 0,
  "is_progress": 0,
  "result": [
    {
      "question_id": 101,
      "answer_json": [
        {
          "user_id": "user123",
          "question_type": "cableMatrix",
          "cable_diameter": "25",
          "cable_type": "Heavy",
          "cable_description": "25mm Heavy Duty Cable Type A",
          "additional_data": {
            "Index": "1",
            "Kg/m": "2.5",
            "Type": "Standard"
          }
        }
      ]
    }
  ]
}
```

---

## Error Handling

### Common Error Responses

**Validation Error (422):**
```json
{
  "success": false,
  "message": "The given data was invalid.",
  "errors": {
    "search_term": ["The search term field is required."]
  }
}
```

**Not Found Error (404):**
```json
{
  "success": false,
  "message": "Question not found"
}
```

**Server Error (500):**
```json
{
  "success": false,
  "message": "Internal server error"
}
```

**CSV Upload Errors:**
- Missing required headers
- Duplicate cable descriptions
- Empty cable descriptions
- Invalid file format
- File size exceeded

---

## Implementation Examples

### Frontend (Operator Panel) JavaScript

```javascript
// Upload CSV for cable matrix question (during submodule creation)
async function uploadCableMatrixCsv(trainingCourseId, csvFile) {
    const formData = new FormData();
    formData.append('file', csvFile);
    formData.append('type', 'submodules');
    formData.append('media_type', 'document');
    formData.append('question_type', 'cableMatrix');
    formData.append('id', trainingCourseId);

    try {
        const response = await fetch('/operator/uploadQuestionMedia', {
            method: 'POST',
            body: formData,
            headers: {
                'Authorization': `Bearer ${operatorToken}`
            }
        });

        const result = await response.json();

        if (result.success) {
            console.log('CSV uploaded successfully:', result.data.cable_matrix_validation);
            // Show success message with validation info
            showUploadSuccess(result.data.cable_matrix_validation);

            // Return file info for use in question creation
            return {
                file_name: result.data.file,
                file_path: result.data.url
            };
        } else {
            console.error('Upload failed:', result.message);
            showUploadError(result.message);
            return null;
        }
    } catch (error) {
        console.error('Upload error:', error);
        showUploadError('Upload failed. Please try again.');
        return null;
    }
}

// Create submodule with cable matrix question
async function createSubmoduleWithCableMatrix(submoduleData, csvFileInfo) {
    const payload = {
        ...submoduleData,
        question_list: [
            {
                question_type: "cableMatrix",
                type: "general",
                is_required: "1",
                question: "Cable Matrix Question",
                file_name: csvFileInfo.file_name,
                file_path: csvFileInfo.file_path,
                question_list_order: 1,
                question_order: 1
            }
        ]
    };

    try {
        const response = await fetch('/operator/trainingCourseSubmodule', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${operatorToken}`
            },
            body: JSON.stringify(payload)
        });

        const result = await response.json();

        if (result.success) {
            console.log('Submodule created successfully');
            // CSV will be processed automatically during question creation
        } else {
            console.error('Submodule creation failed:', result.message);
        }
    } catch (error) {
        console.error('Submodule creation error:', error);
    }
}

// Validate offline submission compatibility
function validateOfflineSubmission(questionType, allowOffline) {
    if (questionType === 'cableMatrix' && allowOffline) {
        alert('Cable Matrix questions cannot be used with offline submission');
        return false;
    }
    return true;
}
```

### Mobile App JavaScript/React Native

```javascript
// Cable Matrix Question Component using Unified API
class CableMatrixQuestion {
    constructor(questionId, authToken) {
        this.questionId = questionId;
        this.authToken = authToken;
        this.selectedDiameter = null;
        this.selectedType = null;
        this.selectedDescription = null;
        this.selectedAdditionalData = null;
        this.baseUrl = `/api/trainingCourseSubmodule/cableMatrix/${this.questionId}`;
    }

    // Load initial diameter options
    async loadInitialFilters() {
        try {
            const response = await fetch(
                `${this.baseUrl}?resultType=diameter`,
                {
                    headers: {
                        'Authorization': `Bearer ${this.authToken}`
                    }
                }
            );

            const data = await response.json();

            if (data.success) {
                this.renderDiameterOptions(data.data.data.diameters);
            } else {
                console.error('Failed to load filters:', data.message);
            }
        } catch (error) {
            console.error('Error loading filters:', error);
        }
    }

    // Handle diameter selection
    async onDiameterChange(diameter) {
        this.selectedDiameter = diameter;
        this.selectedType = null;
        this.selectedDescription = null;
        this.selectedAdditionalData = null;

        try {
            const response = await fetch(
                `${this.baseUrl}?resultType=type&diameter=${encodeURIComponent(diameter)}`,
                {
                    headers: {
                        'Authorization': `Bearer ${this.authToken}`
                    }
                }
            );

            const data = await response.json();

            if (data.success) {
                this.renderTypeOptions(data.data.data.types);
            }
        } catch (error) {
            console.error('Error loading types:', error);
        }
    }

    // Handle type selection
    async onTypeChange(type) {
        this.selectedType = type;
        this.selectedDescription = null;
        this.selectedAdditionalData = null;

        try {
            const params = new URLSearchParams({
                resultType: 'description',
                diameter: this.selectedDiameter,
                type: type,
                page: 1,
                pageSize: 20
            });

            const response = await fetch(
                `${this.baseUrl}?${params}`,
                {
                    headers: {
                        'Authorization': `Bearer ${this.authToken}`
                    }
                }
            );

            const data = await response.json();

            if (data.success) {
                this.renderDescriptionOptions(data.data.data.descriptions, data.data.data.pagination);
            }
        } catch (error) {
            console.error('Error loading descriptions:', error);
        }
    }

    // Load more descriptions (pagination)
    async loadMoreDescriptions(page = 1) {
        try {
            const params = new URLSearchParams({
                resultType: 'description',
                diameter: this.selectedDiameter,
                type: this.selectedType,
                page: page,
                pageSize: 20
            });

            const response = await fetch(
                `${this.baseUrl}?${params}`,
                {
                    headers: {
                        'Authorization': `Bearer ${this.authToken}`
                    }
                }
            );

            const data = await response.json();

            if (data.success) {
                this.appendDescriptionOptions(data.data.data.descriptions, data.data.data.pagination);
            }
        } catch (error) {
            console.error('Error loading more descriptions:', error);
        }
    }

    // Handle description selection
    onDescriptionChange(descriptionItem) {
        this.selectedDescription = descriptionItem.cable_description;
        this.selectedAdditionalData = descriptionItem.additional_data;
        this.selectedDiameter = descriptionItem.cable_diameter;
        this.selectedType = descriptionItem.cable_type;
    }

    // Search functionality with debouncing
    async searchCables(searchTerm, page = 1) {
        if (searchTerm.length < 2) {
            this.clearSearchResults();
            return;
        }

        try {
            const params = new URLSearchParams({
                resultType: 'description',
                search: searchTerm,
                page: page,
                pageSize: 20
            });

            const response = await fetch(
                `${this.baseUrl}?${params}`,
                {
                    headers: {
                        'Authorization': `Bearer ${this.authToken}`
                    }
                }
            );

            const data = await response.json();

            if (data.success) {
                this.renderSearchResults(data.data.data.descriptions, data.data.data.pagination);
            }
        } catch (error) {
            console.error('Error searching cables:', error);
        }
    }

    // Advanced search with filters
    async advancedSearch(filters = {}) {
        try {
            const params = new URLSearchParams({
                resultType: 'description',
                ...filters,
                page: filters.page || 1,
                pageSize: filters.pageSize || 20
            });

            const response = await fetch(
                `${this.baseUrl}?${params}`,
                {
                    headers: {
                        'Authorization': `Bearer ${this.authToken}`
                    }
                }
            );

            const data = await response.json();

            if (data.success) {
                return data.data.data;
            }
        } catch (error) {
            console.error('Error in advanced search:', error);
            return null;
        }
    }

    // Get answer data for submission
    getAnswerData() {
        if (!this.selectedDescription) {
            throw new Error('Please select a cable description');
        }

        return {
            question_type: 'cableMatrix',
            cable_diameter: this.selectedDiameter,
            cable_type: this.selectedType,
            cable_description: this.selectedDescription,
            additional_data: this.selectedAdditionalData
        };
    }

    // Validate selection
    isValid() {
        return this.selectedDescription !== null;
    }

    // Clear all selections
    clearSelections() {
        this.selectedDiameter = null;
        this.selectedType = null;
        this.selectedDescription = null;
        this.selectedAdditionalData = null;
    }
}

// Usage examples
const cableMatrix = new CableMatrixQuestion(123, userToken);

// Load initial diameters
await cableMatrix.loadInitialFilters();

// Search with filters
const searchResults = await cableMatrix.advancedSearch({
    diameter: '25',
    search: 'heavy duty',
    page: 1,
    pageSize: 10
});

// For self-assessment submission
const answerData = cableMatrix.getAnswerData();
```

---

## Offline Submission Compatibility

**Important:** Cable Matrix questions are **NOT compatible** with offline submission due to:

1. **Dynamic Data Requirements:** Hierarchical filtering requires real-time API calls
2. **Large Dataset Size:** 1800+ records cannot be efficiently cached offline
3. **Search Functionality:** Free text search across multiple fields requires server processing

**Implementation Note:** When "Allow Offline Submission" is enabled for a self-assessment, Cable Matrix question type should be disabled in the operator panel.

---

## Performance Considerations

### Pagination
- All list endpoints support pagination with `page` and `per_page` parameters
- Default page size: 20 items
- Maximum page size: 100 items
- Use pagination for descriptions and search results to handle large datasets

### Caching
- Filter options (diameters, types) are cached for performance
- Search results are not cached due to dynamic nature
- CSV data is processed once during upload and stored in database

### Search Optimization
- Minimum search term length: 2 characters
- Search across all fields: diameter, type, description, additional_data
- Results are limited to 50 items per page for performance
- Implement debouncing on frontend to reduce API calls

---

## Database Schema

### cable_matrix_data Table
```sql
- id (bigint, primary key)
- question_id (bigint, foreign key)
- master_user_id (bigint, foreign key)
- cable_diameter (string, nullable)
- cable_type (string, nullable)
- cable_description (text)
- additional_data (json, nullable)
- created_at, updated_at, deleted_at (timestamps)
```

### training_course_submodule_practical_assessment_answers Table
```sql
-- New column added:
- cable_matrix_selection (json, nullable)
```

---

## Testing Endpoints

Use the provided sample CSV file at `cablematrix_sample.csv` for testing the upload functionality.

**Sample CSV Structure:**
```csv
Index,Cable Description,Cable Type,Kg/m,Type,Cable Diameter (mm)
1,Cable Description 1,Heavy,2.5,Standard,16
2,Cable Description 2,Light,1.8,Premium,25
...
```

For any questions or issues with the API implementation, please contact the development team.
```
