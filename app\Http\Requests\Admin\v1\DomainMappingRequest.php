<?php

namespace App\Http\Requests\Admin\v1;

use App\Http\Requests\CustomFormRequest;

class DomainMappingRequest extends CustomFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    protected function rules() : array
    {
        return [
            'domain' => "array",
            'operator_id' => 'required'                
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function messages() : array
    {
        return [
            'domain.array' => __("Domain must be Array."),
            'operator_id.required' => __("Operator id is required."),
        ];
    }
}
