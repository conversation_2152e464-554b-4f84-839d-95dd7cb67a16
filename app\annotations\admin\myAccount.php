<?php
/**
     * @OA\Get(
     *     path="/admin/myAccount",
     *     tags={"Admin - User"},
     *     summary="Edit Account",
     *     description="Edit Account",
     *     operationId="myAccount",
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */

/**
     * @OA\Put(
     *     path="/admin/myAccount",
     *     tags={"Admin - User"},
     *     summary="Update Account",
     *     description="Update Account",
     *     operationId="updateMyAccount",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="company_name",
     *                     description="Company Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="contact_no",
     *                     description="Contact No",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="address",
     *                     description="Address",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="latitude",
     *                     description="Latitude",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="longitude",
     *                     description="Longitude",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="website",
     *                     description="Website",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="enable_manager_email",
     *                     description="Enable Manager Email",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="enable_unique_id",
     *                     description="Enable Unique Id",
     *                     type="integer"
     *                 ),
     *                 @OA\Property(
     *                     property="unique_id_name",
     *                     description="Unique Id Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="enable_custom_contact_info",
     *                     description="Enable Custom Contact Info",
     *                     type="integer"
     *                 ),
     *                 example={"company_name": "Rogahn PLC", "contact_no": "+44 7700 900077", "address": "St Hermistone EH16 6UF", "latitude": "44.463005", "longitude": "110.366479", "website": "https://www.skillsbase.com", "enable_manager_email": 0, "enable_unique_id": 1, "unique_id_name": "Team ID", "enable_custom_contact_info": 1}
     *              )
     *          )
     *      ),
     *     security={
     *         {"Admin": {}}
     *     },
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     * )
     */