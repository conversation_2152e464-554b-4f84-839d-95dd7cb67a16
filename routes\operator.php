<?php

/*
  |--------------------------------------------------------------------------
  | Application Operator Routes
  |--------------------------------------------------------------------------
 */

/*
 * |--------------------------------------------------------------------------
 * | Route naming convention must follow below rules for Permission
 * |--------------------------------------------------------------------------
 * | Every route name starts with :-
 * | - Type of User (Either operator or admin)
 * | - Module Name (as defined in the constants.php file under permission_modules array)
 * | - Route Action (must be one of the following :- view, add, edit, delete)
 * |
 * | Above 3 points must be separated by a dot
 * | (Used as exploding delimeter to check access in Permission Middleware)
 * |--------------------------------------------------------------------------
 * | For example :- operator.dashboard.view | operator.courses.add | operator.courses.add.addModules
 */

$router->get('/', function () use ($router) {
    return $router->app->version();
});

$router->group(["namespace" => "v1", "prefix" => "operator","middleware" => "system-logs:operator"], function () use ($router) {
    $router->group(['middleware' => ['override-mail-config:operator','captchaVerification']], function () use ($router) {
		$router->post('login', 'AuthController@login');
    });
    $router->group(['middleware' => ['captchaVerification', 'check-maintenance-mode:operator']], function () use ($router) {
		$router->post('resetPassword', 'AuthController@resetPassword');
		//$router->post('login', 'AuthController@login');
		$router->post('forgotPassword', ['middleware' => ['override-mail-config:operator,forgotPassword','set-custom-headers:operator,forgotPassword'], 'uses' => 'AuthController@forgotPassword']);
    });
    $router->group(['middleware' => ['check-maintenance-mode:operator']], function () use ($router) {
	$router->post('socialLogin', 'AuthController@socialLogin');
	$router->post('otp_verification', 'AuthController@otpVerification');
	$router->post('resend_otp', 'AuthController@resendOtp');
	$router->post('register', 'AuthController@register');
	$router->post('verifyEmailToken', 'AuthController@verifyEmailToken');
	$router->get("/test-notification", "AuthController@testNotification");
	$router->get("/create-getstream-user", "GetStreamController@CreateGetStreamUser");
	$router->get('verifyEmail/{key}', ['uses' => 'AuthController@verifyUserEmail', 'as' => 'operator.verifyEmail']);
	$router->group(['middleware' => ['auth:operator']], function () use ($router) {
	    $router->get('getPermissions', ['middleware' => ['replica.db'], 'uses' => 'AuthController@getPermissions', 'as' => 'operator.getPermissions']);
	    $router->group(['prefix' => 'dashboard'], function () use ($router) {
		$router->get('/', ['middleware' => ['replica.db'], 'uses' => 'DashboardController@index', 'as' => 'operator.dashboard.view']);
	    });
	});
	$router->group(['middleware' => ['auth:operator', 'checkPermission:operator','override-mail-config:operator','set-custom-headers:operator']], function () use ($router) {
	    $router->post('uploadImage', 'GlobalController@uploadImage');
	    $router->post('uploadMedia', 'GlobalController@uploadMedia');
		$router->post('uploadQuestionMedia', 'GlobalController@uploadQuestionMedia');
	    $router->post('getVimeoMp4Url', 'GlobalController@getVimeoMp4Url');
	    $router->post('logout', 'AuthController@logout');
	    $router->post('saveFavouriteFilter', 'GlobalController@saveFavouriteFilter');
	    $router->get('getTableSettings', 'GlobalController@getTableSettings');
	    $router->post('saveColumns', 'GlobalController@saveColumns');

	    //$router->group(['prefix' => 'operator'], function () use ($router) {
	    $router->group(['middleware' => ['captchaVerification']], function () use ($router) {
		$router->put('changePassword', 'UserController@changePassword');
	    });

	    //$router->get('/', 'UserController@userList');
	    //$router->get('/{userId}', 'UserController@details');
	    $router->get('/myAccount', 'UserController@myAccount');
	    $router->put('/myAccount', 'UserController@updateMyAccount');
	    //$router->delete('/', 'UserController@destroy');
	    //});
	    $router->get('/getInviteLink', 'UserController@getInviteLink');
	    $router->post('/generateInviteLink', 'UserController@generateInviteLink');
	    $router->post('/getAllInviteLinks', 'UserController@getAllInviteLinks');
	    $router->post('/getAllAssignedUsers', 'UserController@getAllAssignedUsers');
	    $router->post('/deleteInviteLinks', 'UserController@deleteInviteLinks');
	    $router->post('/changeLinkStatus', 'UserController@changeLinkStatus');

	    $router->group(['prefix' => 'trainingCourse'], function () use ($router) {
		$router->get('/getCourseDisplayOrders', 'TrainingCourseController@getCourseDisplayOrders');
		$router->post('/list', 'TrainingCourseController@index');
		$router->post('/changeStatus', ['uses' => 'TrainingCourseController@changeStatus', 'as' => 'operator.courses.edit.changeStatus']);
		$router->post('/addModules', ['uses' => 'TrainingCourseController@addModules', 'as' => 'operator.courses.add.modules.addModules']);
		$router->post('/viewAssignedUsers', ['uses' => 'TrainingCourseController@viewAssignedUsers']);
		$router->post('/viewAssignedGroupUsers', ['uses' => 'TrainingCourseController@viewAssignedGroupUsers']);
		$router->post('/viewAssignedUsersThroughLink', ['uses' => 'TrainingCourseController@viewAssignedUsersThroughLink']);
		$router->post('deleteAssignedLinks', ['uses' => 'TrainingCourseController@deleteAssignedLinks']);
		$router->get('/', ['uses' => 'TrainingCourseController@index', 'as' => 'operator.courses.view.index']);
		$router->post('/', ['uses' => 'TrainingCourseController@store', 'as' => 'operator.courses.add']);
		$router->put('/{id}', ['uses' => 'TrainingCourseController@update', 'as' => 'operator.courses.edit']);
		$router->get('/{id}', ['uses' => 'TrainingCourseController@show', 'as' => 'operator.courses.view']);
		$router->delete('/{id}', ['uses' => 'TrainingCourseController@destroy', 'as' => 'operator.courses.delete']);
		$router->get('/duplicateCourse/{id}', ['uses' => 'TrainingCourseController@duplicateCourse', 'as' => 'operator.courses.add']);
		$router->post('/requestCourse', 'TrainingCourseController@requestCourse');
		$router->post('/unassignUser', 'TrainingCourseController@unassignUser');
		$router->post('/unassignUsersLink', 'TrainingCourseController@unassignUsersLink');
		$router->get('/preSelect/{id}', 'TrainingCourseController@preselectCourse');
		$router->post('/storeAssignedLink', 'TrainingCourseController@storeAssignedLink');
		$router->get('/getAssignedLink/{id}', 'TrainingCourseController@getAssignedLink');
		$router->post('/getAllAssignedLink', 'TrainingCourseController@getAllAssignedLink');
		$router->post('/changeAssignedLinkStatus', 'TrainingCourseController@changeAssignedLinkStatus');
		$router->post('/changeSingleModuleCourseStatus', ['uses' => 'TrainingCourseController@changeSingleModuleCourseStatus']);
	    });

	    $router->group(['prefix' => 'trainingCourseModule'], function () use ($router) {
		$router->get('/getDefaultImages', 'TrainingCourseModulesController@getDefaultImages');
		$router->get('/getAllCourseModulesList', ['uses' => 'TrainingCourseModulesController@getAllCourseModulesList', 'as' => 'operator.courses.view.modules.getAllCourseModulesList']);
		$router->post('/addCourseModulesReplica', ['uses' => 'TrainingCourseModulesController@addCourseModulesReplica', 'as' => 'operator.courses.add.modules.addCourseModulesReplica']);
		$router->post('/copyPasteModule', ['uses' => 'TrainingCourseModulesController@copyPasteModule', 'as' => 'operator.courses.add.modules.copyPasteModule']);
		$router->post('/changeStatus', ['uses' => 'TrainingCourseModulesController@changeStatus', 'as' => 'operator.courses.edit.modules.changeStatus']);
		$router->post('/addSubModules', ['uses' => 'TrainingCourseModulesController@addSubModules', 'as' => 'operator.courses.add.submodules.addSubModules']);
		$router->post('/', ['uses' => 'TrainingCourseModulesController@store', 'as' => 'operator.courses.add.modules.add']);
		$router->get('/{id}', ['uses' => 'TrainingCourseModulesController@show', 'as' => 'operator.courses.view.modules.view']);
		$router->put('/{id}', ['uses' => 'TrainingCourseModulesController@update', 'as' => 'operator.courses.edit.modules.edit']);
		$router->get('/getAllCourseModules/{courseId}', ['uses' => 'TrainingCourseModulesController@getAllCourseModules', 'as' => 'operator.courses.view.modules.getAllCourseModules']);
		$router->delete('/{id}', ['uses' => 'TrainingCourseModulesController@destroy', 'as' => 'operator.courses.delete.modules.delete']);
		$router->get('/duplicateModule/{courseId}/{moduleId}', ['uses' => 'TrainingCourseModulesController@duplicateModule', 'as' => 'operator.courses.add.modules.duplicateModule']);
		$router->post('/sortModules', 'TrainingCourseModulesController@sortModules');
	    });

	    $router->group(['prefix' => 'trainingCourseSubmodule'], function () use ($router) {
		// Product Listing Submodule
		$router->get('/getProductList/{id}', 'TrainingCourseSubModuleProductListController@getProductList');
		$router->post('/storeProductList', 'TrainingCourseSubModuleProductListController@storeProductList');
		$router->put('/updateProductList', 'TrainingCourseSubModuleProductListController@updateProductList');

		$router->post('/changeStatus', ['uses' => 'TrainingCourseSubmoduleDetailsController@changeStatus', 'as' => 'operator.courses.edit.submodules.changeStatus']);
		$router->get('/subModulesDuplicate/{id}', ['uses' => 'TrainingCourseSubmoduleDetailsController@duplicateSubModule', 'as' => 'operator.courses.add.submodules.duplicateSubModule']);
		$router->post('/copyPasteSubModule', ['uses' => 'TrainingCourseSubmoduleDetailsController@copyPasteSubModule', 'as' => 'operator.courses.add.submodules.copyPasteSubModule']);
		$router->post('/sortSubModules', ['uses' => 'TrainingCourseSubmoduleDetailsController@sortSubModules', 'as' => 'operator.courses.add.submodules.sortSubModules']);

		// Submodule Type
		$router->get('/getSubModuleTypes', 'TrainingCourseSubmoduleDetailsController@getSubModuleTypes');
		$router->get('/getAllCourseSubModules/{courseId}', ['uses' => 'TrainingCourseSubmoduleDetailsController@getAllCourseSubModules', 'as' => 'operator.courses.view.submodules.getAllCourseSubModules']);
		$router->get('/getQuizCategories/{courseId}', ['uses' => 'TrainingCourseSubmoduleDetailsController@getQuizCategories', 'as' => 'operator.courses.view.submodules.getQuizCategories']);
		$router->get('/{courseId}/{moduleId}', ['uses' => 'TrainingCourseSubmoduleDetailsController@getCourseSubmodulesList', 'as' => 'operator.courses.view.submodules.getCourseSubmodulesList']);
		$router->get('quizMiniquizExport/{courseId}/{moduleId}', ['uses' => 'TrainingCourseSubmoduleDetailsController@getAllQuizMiniquizExport', 'as' => 'operator.courses.view.submodules.getAllQuizMiniquizExport']);

		$router->get('/', ['uses' => 'TrainingCourseSubmoduleDetailsController@index', 'as' => 'operator.courses.view.submodules.index']);
		$router->post('/', ['uses' => 'TrainingCourseSubmoduleDetailsController@store', 'as' => 'operator.courses.add.submodules.add']);
		$router->get('/{id}', ['uses' => 'TrainingCourseSubmoduleDetailsController@show', 'as' => 'operator.courses.view.submodules.view']);
		$router->put('/{id}', ['uses' => 'TrainingCourseSubmoduleDetailsController@update', 'as' => 'operator.courses.edit.submodules.edit']);
		$router->delete('/{id}', ['uses' => 'TrainingCourseSubmoduleDetailsController@destroy', 'as' => 'operator.courses.delete.submodules.delete']);
		$router->post('/questionOptions/restore', ['uses' => 'TrainingCourseSubmoduleDetailsController@questionOptionsRestore', 'as' => 'operator.courses.restore.questionOptions']);
	    });

	    //Rename Submodule Name
	    $router->post('/getAllSubModuleTypes', 'TrainingCourseSubmoduleDetailsController@getAllSubModuleTypes');
	    $router->get('/getSubmoduleType/{id}', 'TrainingCourseSubmoduleDetailsController@getSubmoduleType');
	    $router->put('/updateSubmoduleType', 'TrainingCourseSubmoduleDetailsController@updateSubmoduleType');

	    $router->group(['prefix' => 'emailTemplates'], function () use ($router) {
		$router->get('/', 'EmailTemplatesController@index');
		$router->post('/', 'EmailTemplatesController@store');
		$router->get('/{id}', 'EmailTemplatesController@show');
		$router->put('/{id}', 'EmailTemplatesController@update');
		$router->delete('/{id}', 'EmailTemplatesController@destroy');
	    });

	    $router->group(['prefix' => 'teamMembers'], function () use ($router) {
		$router->get('/getLoginOperatorDetails', 'TeamMembersController@getLoginOperatorDetails');
		$router->get('/getRoles', 'TeamMembersController@getRoles');
		$router->post('/changeStatus', 'TeamMembersController@changeStatus');
		$router->post('/getListing', 'TeamMembersController@getListing');
		$router->post('/', 'TeamMembersController@store');
		$router->get('/{id}', 'TeamMembersController@show');
		$router->put('/{id}', 'TeamMembersController@update');
		$router->delete('/{id}', 'TeamMembersController@destroy');
	    });

	    $router->group(['prefix' => 'resources'], function () use ($router) {
		$router->post('/getAssignResourceListing', 'ResourceController@getAssignResourceListing');
		$router->post('/changeStatus', ['uses' => 'ResourceController@changeStatus', 'as' => 'operator.resources.edit.changeStatus']);
		$router->post('/getListing', ['uses' => 'ResourceController@getListing', 'as' => 'operator.resources.view.getListing']);

		$router->post('/delete', ['uses' => 'ResourceController@destroy', 'as' => 'operator.resources.delete']);
		$router->post('/', ['uses' => 'ResourceController@store', 'as' => 'operator.resources.add']);
		$router->get('/{id}', ['uses' => 'ResourceController@show', 'as' => 'operator.resources.view']);
		$router->put('/{id}', ['uses' => 'ResourceController@update', 'as' => 'operator.resources.edit']);
	    });

	    $router->group(['prefix' => 'products'], function () use ($router) {
		$router->post('/getAssignProductsListing', 'ProductController@getAssignProductsListing');
		$router->get('/getProductTypes', 'ProductController@getProductTypes');
		$router->get('/getIndustries', 'ProductController@getIndustries');
		$router->get('/getDisplayOrder', 'ProductController@getDisplayOrder');

		$router->post('/changeStatus', ['uses' => 'ProductController@changeStatus', 'as' => 'operator.products.edit.changeStatus']);
		$router->post('/assignResources', 'ProductController@assignResources');
		$router->post('/featuredProduct', 'ProductController@featuredProduct');
		$router->post('/getListing', ['uses' => 'ProductController@getListing', 'as' => 'operator.products.view.getListing']);
		$router->post('/delete', ['uses' => 'ProductController@destroy', 'as' => 'operator.products.delete']);
		$router->post('/', ['uses' => 'ProductController@store', 'as' => 'operator.products.add']);
		$router->get('/{id}', ['uses' => 'ProductController@show', 'as' => 'operator.products.view']);
		$router->put('/{id}', ['uses' => 'ProductController@update', 'as' => 'operator.products.edit']);
	    });
        //  Industries
        $router->group(['prefix' => 'industries'], function () use ($router) {
            $router->post('/getListing', 'IndustryController@getListing');
            $router->post('/changeStatus', 'IndustryController@changeStatus');
            $router->post('/', 'IndustryController@store');
            $router->get('/{id}', 'IndustryController@show');
            $router->put('/{id}', 'IndustryController@update');
            $router->delete('/{id}', 'IndustryController@destroy');
        });
        $router->group(['prefix' => 'certificates'], function () use ($router) {
            $router->post('/getListing', 'CertificateController@getListing');
            $router->post('/changeStatus', 'CertificateController@changeStatus');
            $router->post('/', 'CertificateController@store');
            $router->get('/{id}', 'CertificateController@show');
            $router->post('/{id}', 'CertificateController@update');
            $router->delete('/{id}', 'CertificateController@destroy');
        });
	    $router->group(['prefix' => 'news'], function () use ($router) {
		$router->post('/getAssignNewsListing', ['uses' => 'NewsController@getAssignNewsListing', 'as' => 'operator.news.view.getAssignNewsListing']);
		$router->post('/changeStatus', ['uses' => 'NewsController@changeStatus', 'as' => 'operator.news.edit.changeStatus']);
		$router->post('/getListing', ['uses' => 'NewsController@getListing', 'as' => 'operator.news.view.getListing']);
		$router->post('/delete', ['uses' => 'NewsController@destroy', 'as' => 'operator.news.delete']);
		$router->post('/', ['uses' => 'NewsController@store', 'as' => 'operator.news.add']);
		$router->get('/{id}', ['uses' => 'NewsController@show', 'as' => 'operator.news.view']);
		$router->put('/{id}', ['uses' => 'NewsController@update', 'as' => 'operator.news.edit']);
	    });

	    $router->group(['prefix' => 'notifications'], function () use ($router) {
		$router->get('/', 'NotificationsController@index');
		$router->post('/', 'NotificationsController@store');
	    });

	    //Generate Report
	    $router->group(['prefix' => 'generate_report'], function () use ($router) {
		$router->post('/getQuizReportListing', 'GenerateReportController@getListing');
		$router->post('/getAssessmentReportListing', 'GenerateReportController@getAssessmentReportListing');
		$router->post('/getSelfAssessmentReportListing', 'GenerateReportController@getSelfAssessmentReportListing');
		$router->post('/importQuizReport', 'GenerateReportController@importQuizReport');
		$router->post('/importAssessmentReport', 'GenerateReportController@importAssessmentReport');
		$router->post('/getAllCoursesListing', 'GenerateReportController@getAllCoursesListing');
		$router->post('/allCourses/pdfExport', 'GenerateReportController@pdfExport');
		$router->post('/getScormReportListing', 'GenerateReportController@getScormReportListing');
	    });

	    $router->group(['prefix' => 'webNotifications'], function () use ($router) {
		$router->get('/todayWebNotifications', 'WebNotificationController@todayWebNotifications');
		$router->get('/previousWebNotifications', 'WebNotificationController@previousWebNotifications');
		$router->get('/notificationCount', 'WebNotificationController@notificationCount');
		$router->post('/markasread', 'WebNotificationController@markAsRead');
		$router->delete('/delete', 'WebNotificationController@deleteNotifications');
	    });

	    $router->group(['prefix' => 'domainMapping'], function () use ($router) {
		$router->get('/', 'DomainMappingController@index');
		$router->post('/', 'DomainMappingController@store');
		$router->get('/{id}', 'DomainMappingController@show');
		$router->put('/{id}', 'DomainMappingController@update');
	    });

	    $router->group(['prefix' => 'reports'], function () use ($router) {
		$router->post('/commonNeverReport', ['uses' => 'ReportsController@commonNeverReport', 'as' => 'operator.reports.view.commonNeverReport']);
		$router->post('/partialCompletion', ['uses' => 'ReportsController@partialCompletion', 'as' => 'operator.reports.view.partialCompletion']);
		$router->post('/lowCompletionRate', ['uses' => 'ReportsController@lowCompletionRate', 'as' => 'operator.reports.view.lowCompletionRate']);
		$router->post('/sendReminderNotification', ['uses' => 'ReportsController@sendReminderNotification']);
		$router->post('/costSavedForJobsReport', ['uses' => 'ReportsController@costSavedForJobsReport', 'as' => 'operator.reports.view.costSavedForJobsReport']);
	    });

	    $router->group(['prefix' => 'roles'], function () use ($router) {
		$router->get('/roleModuleList', 'RolePermissionsController@roleModuleList');
		$router->post('/getRoleList', 'RolePermissionsController@getRoleList');
		$router->get('/', 'RolePermissionsController@index');
		$router->post('/', 'RolePermissionsController@store');
		$router->get('/{id}', 'RolePermissionsController@show');
		$router->put('/{id}', 'RolePermissionsController@update');
		$router->post('/delete', 'RolePermissionsController@destroy');
		$router->post('/changeStatus', 'RolePermissionsController@changeStatus');
		$router->post('/roleFieldsList', 'RolePermissionsController@roleFieldsList');
	    });

	    $router->group(['prefix' => 'trainingCourseSubmodule/pocResponses'], function () use ($router) {
		$router->post('region/comment', 'TrainingCourseSubmodulePocController@manageComment');
		$router->delete('region/comment/{id}', 'TrainingCourseSubmodulePocController@destroyComment');
		$router->post('region/changeStatus', 'TrainingCourseSubmodulePocController@changeStatus');
		$router->post('userResponse/getListing', 'TrainingCourseSubmodulePocController@userResponsesListing');
		$router->post('userResponse', 'TrainingCourseSubmodulePocController@userResponse');
		$router->post('getListing', 'TrainingCourseSubmodulePocController@index');
	    });

	    $router->group(['prefix' => 'trainingCourseSubmodule/feedback'], function () use ($router) {
		$router->post('user/getListing', 'TrainingCourseSubmoduleCourseFeedbackController@getListing');
		$router->get('{submoduleId}/user/{userId}', 'TrainingCourseSubmoduleCourseFeedbackController@userResponse');
	    });

	    $router->group(['prefix' => 'trainingCourseSubmodule/assessment'], function () use ($router) {
			$router->post('userResponse/getListing', 'TrainingCourseSubmodulePracticalAssessmentController@getListing');
			$router->get('{submoduleId}/response/{assessorId}/{userId}', 'TrainingCourseSubmodulePracticalAssessmentController@viewAssessment');
			$router->get('{submoduleId}/self_response/{userId}', 'TrainingCourseSubmodulePracticalAssessmentController@viewSelfAssessment');
            $router->get('{submoduleId}/pa_response/{assessorId}/{userId}', 'TrainingCourseSubmodulePracticalAssessmentController@viewPaAssessment');
			$router->get('{submoduleId}/sa_response/{userId}', 'TrainingCourseSubmodulePracticalAssessmentController@viewSaAssessment');
	    });

		$router->group(['prefix' => 'trainingCourseSubmodule/assessment'], function () use ($router) {
			$router->post('userResponse/getListing', 'TrainingCourseSubmodulePracticalAssessmentController@getListing');
		});
		$router->group(['prefix' => 'trainingCourseSubmodule/selfassessment'], function () use ($router) {
			$router->post('userResponse/getListing', 'TrainingCourseSubmodulePracticalAssessmentController@getSelfAssessmentListing');
		});

		$router->group(['prefix' => 'trainingCourseSubmodule/scorm'], function () use ($router) {
			$router->post('userResponse/getListing', 'TrainingCourseSubmoduleScormController@getListing');
		});

		// Single PA module routes
		$router->group(['prefix' => 'practicalAssessment'], function () use ($router) {
			$router->post('/getListing', 'TrainingCourseSubmodulePracticalAssessmentController@getPracticalAssessmentListing');
			$router->get('/details/{id}', 'TrainingCourseSubmodulePracticalAssessmentController@editAssessmentResponse');
            $router->get('/pa_details/{id}', 'TrainingCourseSubmodulePracticalAssessmentController@PaAssessmentResponse');
			$router->post('pdfExport', 'TrainingCourseSubmodulePracticalAssessmentController@pdfExport');
			$router->post('globalTracking', 'TrainingCourseSubmodulePracticalAssessmentController@globalTracking');
			$router->post('update/{id}', 'TrainingCourseSubmodulePracticalAssessmentController@update');
		});
		$router->group(['prefix' => 'selfAssessment'], function () use ($router) {
			$router->post('/getListing', 'TrainingCourseSubmodulePracticalAssessmentController@getSelfAssessmentResultListing');
			$router->get('/details/{id}', 'TrainingCourseSubmodulePracticalAssessmentController@editSelfAssessmentResponse');
			$router->get('/sa_details/{id}', 'TrainingCourseSubmodulePracticalAssessmentController@SaAssessmentResponse');
			$router->post('update/{id}', 'TrainingCourseSubmodulePracticalAssessmentController@update');
			$router->post('pdfExport', 'TrainingCourseSubmodulePracticalAssessmentController@pdfExportSelfAssessment');
		});

	    $router->group(['prefix' => 'jobs'], function () use ($router) {
		$router->post('getListing', 'TrainingCourseSubmoduleJobController@getListing');
		$router->get('/{id}', 'TrainingCourseSubmoduleJobController@show');
		$router->put('/{id}', 'TrainingCourseSubmoduleJobController@update');
		$router->post('media/{id}', 'TrainingCourseSubmoduleJobController@media');
		$router->post('getCourseJobList', 'TrainingCourseSubmoduleJobController@getCourseJobList');
		$router->post('/delete', 'TrainingCourseSubmoduleJobController@destroy');
	    });

	    $router->group(['prefix' => 'trainingCourseSubmodule/quiz'], function () use ($router) {
		// Question Bank
		$router->post('questionBank/import', 'TrainingCourseSubmoduleQuizController@questionBankImport');
		$router->get('questionBank/export/{id}', 'TrainingCourseSubmoduleQuizController@questionBankExport');

		// Questions
		$router->post('categories/storeQuestions', 'TrainingCourseSubmoduleQuizController@storeQuestions');
		$router->get('categories/listQuestions/{id}', 'TrainingCourseSubmoduleQuizController@listQuestions');

		// Categories
		$router->post('categories/changeStatus', 'TrainingCourseSubmoduleQuizController@changeStatus');
		$router->post('categories/getListing', 'TrainingCourseSubmoduleQuizController@index');
		$router->post('categories/', 'TrainingCourseSubmoduleQuizController@store');
		$router->get('categories/{id}', 'TrainingCourseSubmoduleQuizController@show');
		$router->put('categories/{id}', 'TrainingCourseSubmoduleQuizController@update');
		$router->post('categories/delete', 'TrainingCourseSubmoduleQuizController@destroy');

		// Quiz Responses
		$router->post('result/getListing', 'TrainingCourseSubmoduleQuizController@getResultListing');
		$router->get('result/markAsPass/{id}', 'TrainingCourseSubmoduleQuizController@markAsPass');
		$router->post('result/quizResults', 'TrainingCourseSubmoduleQuizController@quizResults');
		$router->post('manualPDFGenerate', 'TrainingCourseSubmoduleQuizController@manualPDFGenerate');
	    });

	    $router->group(['prefix' => 'userGroups'], function () use ($router) {
		// $router->get('/UsergroupList', 'UserGroupController@roleModuleList');
		$router->post('/getUserGroupList', ['uses' => 'UserGroupController@getUserGroupList', 'as' => 'operator.user_groups.view.getUserGroupList']);
		$router->get('/', ['uses' => 'UserGroupController@index', 'as' => 'operator.user_groups.view.index']);
		$router->post('/', ['uses' => 'UserGroupController@store', 'as' => 'operator.user_groups.add']);
		$router->get('/{id}', ['uses' => 'UserGroupController@show', 'as' => 'operator.user_groups.view']);
		$router->put('/{id}', ['uses' => 'UserGroupController@update', 'as' => 'operator.user_groups.edit']);
		$router->post('/delete', ['uses' => 'UserGroupController@destroy', 'as' => 'operator.user_groups.delete']);
		$router->post('/changeStatus', ['uses' => 'UserGroupController@changeStatus', 'as' => 'operator.user_groups.edit.changeStatus']);
		$router->post('/getCoursesList', ['uses' => 'UserGroupController@getCoursesList', 'as' => 'operator.courses.view.getCoursesList']);
	    });

	    $router->group(['prefix' => 'users'], function () use ($router) {
		$router->get('/getAllUserGroup', ['uses' => 'FrontUserController@getAllUserGroup', 'as' => 'operator.user_groups.view.getAllUserGroup']);
		$router->get('/getUserGroupDetailById/{id}', 'FrontUserController@getUserGroupDetailById');
		$router->post('/getUsersList', ['uses' => 'FrontUserController@getUsersList', 'as' => 'operator.users.view.getUsersList']);
		$router->get('/', ['uses' => 'FrontUserController@index', 'as' => 'operator.users.view.index']);
		$router->post('/', ['uses' => 'FrontUserController@store', 'as' => 'operator.users.add']);
		$router->get('/{id}', ['uses' => 'FrontUserController@show', 'as' => 'operator.users.view']);
		$router->put('/{id}', ['uses' => 'FrontUserController@update', 'as' => 'operator.users.edit']);
		$router->post('/delete', ['uses' => 'FrontUserController@destroy', 'as' => 'operator.users.delete']);
		$router->post('/changeStatus', ['uses' => 'FrontUserController@changeStatus', 'as' => 'operator.users.edit.changeStatus']);
		$router->post('/getUserManagerDetailByField', 'FrontUserController@getUserManagerDetailByField');
		$router->post('/getFrontUsersList', ['uses' => 'FrontUserController@getFrontUsersList', 'as' => 'operator.users.view.getFrontUsersList']);
		$router->post('/importFrontUsers', ['uses' => 'FrontUserController@importFrontUsers', 'as' => 'operator.users.add.importFrontUsers']);
		$router->post('/getTrainingCourseList', ['uses' => 'FrontUserController@getTrainingCourseList', 'as' => 'operator.courses.view.getTrainingCourseList']);
		$router->post('/getGroupTrainingCourseList', ['uses' => 'FrontUserController@getGroupTrainingCourseList', 'as' => 'operator.courses.view.getGroupTrainingCourseList']);
		$router->post('/getOnlyTrainingCourseList', ['uses' => 'FrontUserController@getOnlyTrainingCourseList', 'as' => 'operator.courses.view.getOnlyTrainingCourseList']);
		$router->get('/getProfile/{id}', 'FrontUserController@getProfile');
		$router->post('/getGroupCourseList', ['uses' => 'FrontUserController@getGroupCourseList', 'as' => 'operator.courses.view.getGroupCourseList']);
		$router->post('/assignedCourses', 'FrontUserController@assignedCourses');
		$router->post('/overrideCourseProgress', 'FrontUserController@overRideCourseProgress');
		$router->post('/verifyRegisteredUser', 'FrontUserController@verifyRegisteredUser');
		$router->post('/sendUserDetailsEmail', ['uses' => 'AuthController@sendUserDetailsEmail', 'as' => 'operator.users.sendUserDetailsEmail']);
	    });
	    $router->group(['prefix' => 'assessor'], function () use ($router) {
		$router->get('/{id}', ['uses' => 'AssessorController@show', 'as' => 'operator.assessor.view']);
		$router->put('/{id}', ['uses' => 'AssessorController@update', 'as' => 'operator.assessor.edit']);
		$router->get('/getProfile/{id}', 'AssessorController@getProfile');
		$router->post('/getAssessorUsersList', ['uses' => 'AssessorController@getAssessorUsersList', 'as' => 'operator.users.view.getAssessorUsersList']);
		$router->post('/AssignAssessor', 'AssessorController@AssignAssessor');
		$router->post('/getAssessorAssignFrontUsersList', ['uses' => 'AssessorController@getAssessorAssignFrontUsersList', 'as' => 'operator.users.view.getAssessorAssignFrontUsersList']);
		$router->post('/getOnlyAssessorAssignFrontUsersList', ['uses' => 'AssessorController@getOnlyAssessorAssignFrontUsersList', 'as' => 'operator.users.view.getOnlyAssessorAssignFrontUsersList']);
	    });

		$router->group(['prefix' => 'dataMigration'], function () use ($router) {
			$router->post('/','DataMigrationController@store');
			});

	    // Product Types
	    $router->group(['prefix' => 'productTypes'], function () use ($router) {
		$router->post('/getListing', 'ProductTypeController@getListing');
		$router->post('/changeStatus', 'ProductTypeController@changeStatus');
		$router->post('/importProductTypes', 'ProductTypeController@importProductTypes');
		$router->post('/', 'ProductTypeController@store');
		$router->get('/getGridView', 'ProductTypeController@getGridView');
		$router->post('/enableGridView', 'ProductTypeController@enableGridView');
		$router->get('/getDisplayOrder', 'ProductTypeController@getDisplayOrder');
		$router->get('/{id}', 'ProductTypeController@show');
		$router->put('/{id}', 'ProductTypeController@update');
		$router->delete('/{id}', 'ProductTypeController@destroy');

	    });

	    // Reset Progress
	    $router->group(['prefix' => 'trainingCourse/resetProgress'], function () use ($router) {
		$router->post('/', 'TrainingCourseController@resetProgress');
	    });

	    $router->group(['prefix' => 'costs'], function () use ($router) {

		$router->get('/getUploadmediaSubmodules', 'CostController@getTrainingCourseUploadmediaSubmodules');
		$router->post('/saveCost', 'CostController@saveCost');
		$router->post('/costSavedAllUsers', 'CostReportingController@barChartCostSavedAllUsers');
		$router->post('/costSavedByGroups', 'CostReportingController@barChartCostSavedByGroups');
	    });
	});
    });
});

//Smart Awards Operator Routes
$router->group(['middleware' => ['check-maintenance-mode:operator', 'smartAwardOperatorCheck:operator','override-mail-config:operator,smartawards','set-custom-headers:operator,smartawards']], function () use ($router) {
    $router->group(["namespace" => "v1", "prefix" => "operatorSmartAward"], function () use ($router) {
	// create new user
	$router->post('/user', ['uses' => 'FrontUserController@storeSmartAwardUser']);
	//change user email
	$router->put('/changeUserEmail', ['uses' => 'FrontUserController@changeSmartAwardUserEmail']);
	//set user password
	$router->put('/setUserPassword', ['uses' => 'FrontUserController@setSmartAwardUserPassword']);
	//reset user password
	$router->post('/resetUserPassword', ['uses' => 'AuthController@resetSmartAwardUserPassword']);
	// assign training courses
	$router->post('/users/assignedCourses', 'FrontUserController@smartAwardsAssignedCourses');
	//store quiz results (Training Course Completed)
	$router->post('/trainingCourseSubmodule/storeQuizResult', 'FrontUserController@smartAwardsStoreQuizResult');
    });
});

//Analytics API Operator Routes
$router->group(['middleware' => ['check-maintenance-mode:operator', 'analyticsAPICheck:operator']], function () use ($router) {
    $router->group(["namespace" => "v1", "prefix" => "analytics"], function () use ($router) {
	// List of users based on operator ID
	$router->get('/newUsers', ['uses' => 'AnalyticsController@newUsers']);
	// Count of total users based on operator ID
	$router->get('/totalUsers', ['uses' => 'AnalyticsController@totalUsers']);
	// Quiz data based on operator ID
	$router->get('/quizData', ['uses' => 'AnalyticsController@quizData']);
	// Course data based on operator ID
	$router->get('/courseData', ['uses' => 'AnalyticsController@courseData']);
	// Job data based on operator ID
	$router->get('/jobData', ['uses' => 'AnalyticsController@jobData']);
    });
});

//Micro Learnign APIs - Operator Routes
$router->group(['middleware' => ['check-maintenance-mode:operator','microLearningAPICheck']], function () use ($router) {
    $router->group(["namespace" => "v1", "prefix" => "operator/micro-learning"], function () use ($router) {
        //Progress Micro Learning Course,Module,SubModule
		$router->post('/MicroLearningProgress', ['uses' => 'TrainingCourseSubmoduleDetailsController@MicroLearningProgress']);
		//Store Micro Learning Module
		$router->post('/trainingCourseModule', ['uses' => 'TrainingCourseModulesController@storeMicroLearningModule']);
		//Store Micro Learning Submodule
		$router->post('/trainingCourseSubmodule/create', ['uses' => 'TrainingCourseSubmoduleDetailsController@storeMicroLearningSubmodule']);
		//Update Micro Learning Submodule
		$router->post('/trainingCourseSubmodule/update', ['uses' => 'TrainingCourseSubmoduleDetailsController@updateMicroLearningSubmodule']);
		// Send email and notification to operator and training course assigned users
		$router->post('/trainingCourseSubmodule/sendEmail', ['uses' => 'TrainingCourseSubmoduleDetailsController@sendEmail']);
		// Send email to the requested email address
		$router->post('/trainingCourseModule/sendEmail', ['uses' => 'TrainingCourseModulesController@sendEmail']);
	});
});
