<?php

namespace App\Http\Requests\Admin\v1;
use DBTableNames;
use App\Http\Requests\CustomFormRequest;

class LoginRequest extends CustomFormRequest {

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'email' => 'required|email',
            'password' => 'required'
        ];
    }
    
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'email.required' => 'Please enter email.',
            'email.email' => 'Email must be a valid email address (E.g.: <EMAIL>).',
            'password.required' => 'Please enter password.'
        ];
    }

}
