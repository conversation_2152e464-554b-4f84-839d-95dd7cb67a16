<?php

namespace App\Http\Requests\Admin\v1;
use App\Http\Requests\CustomFormRequest;

class EmailTemplatesRequest extends CustomFormRequest
{
    
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    protected function rules() : array
    {
        return [
            "template_title" => 'required',
            "email_subject" => 'required',
            "email_content" => 'required',
        ];
    }

    public function messages() : array
    {
        return [
            'template_title.required' => __('The title field is required.'),
            'email_subject.required' => __('The subject field is required.'),
            'email_content.required' => __('The content field is required.'),
        ];
    }
}
