<?php
namespace App\Http\Controllers\Operator\v1;
use App\Models\Certificate;
use Barryvdh\DomPDF\Facade\Pdf as PDF;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Requests\Admin\v1\CommonListingRequest;
use App\Http\Requests\Operator\v1\CertificateRequest;
use App\Http\Resources\Operator\v1\CertificateResource;
use App\Repositories\Operator\v1\CertificateRepository;
use App\Http\Requests\Operator\v1\UpdateCertificateRequest;
use App\Http\Requests\Admin\v1\CommonBulkStatusChangeRequest;

class CertificateController extends Controller
{
    private $model;
    private $repository;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->model = new Certificate();
        $this->repository = new CertificateRepository($this->model);
    }

    /**
     * Get Certificate List
     *
     * @return \Illuminate\Http\Response
     */
    public function getListing(CommonListingRequest $request) {
        try {
            $industries = $this->repository->getListing($request->all());
            if ($request->isExport) {
                return $this->repository->exportCsv($industries->get());
            }
            return CertificateResource::collection($industries->paginate($request->per_page));
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage())))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Add Certificate
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(CertificateRequest $request) {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $data=array();
            $data=$request->all();
            $data['master_user_id']=$operatorId;
            $data['award_date']=date('d-M-Y');
            $data['calculated_expiry_date']=date('d-M-Y', strtotime('+'.$data['expiry_date'].' years'));
            if(isset($data['image']) && !empty($data['image']))
            {
                $path=config('constants.aws.certificate').'/'.$data['training_course_id'];
                $image=S3BucketFileUpload($data['image'],$path);
                $data['primary_background_image']=$image;
                $data['primary_background_image_url']=env('CDN_URL') .config('constants.aws.certificate').'/'.$data['training_course_id'].'/'.$data['primary_background_image'];
            }else{
                $data['primary_background_image']=null;
                $data['primary_background_image_url']=null;
            }
            if(isset($data['logo_image']) && !empty($data['logo_image']))
            {
                $path=config('constants.aws.certificate_logo').'/'.$data['training_course_id'];
                $image=S3BucketFileUpload($data['logo_image'],$path);
                $data['logo_image']=$image;
                $data['logo_image_url']=env('CDN_URL') .config('constants.aws.certificate_logo').'/'.$data['training_course_id'].'/'.$data['logo_image'];
            }else{
                $data['logo_image']=null;
                $data['logo_image_url']=null;
            }
            $quizFolder = base_path('storage/certificate');
            $pdfName = 'Certificate_'.$data['training_course_id'].'_'.date('Y-m-d').'_'.time().'.pdf';
            $pdfPath = $quizFolder . '/' . $pdfName;
            if (!is_dir($quizFolder)) {
                mkdir($quizFolder);
            }
            if($data['landscape_portrait']=='landscape') {
                $file='landscape';
            }else{
                $file='portrait';
            }
            $data['preview_pdf']=$pdfName;
            $data['title']=json_decode($data['certificate_title']);
            $data['body']=json_decode($data['certificate_body']);
            $data['certification_name']=$data['certificate_name'];
            $pdf = PDF::loadView($file,compact('data'))
                                    ->setPaper('a4', $data['landscape_portrait'])->setWarnings(false)
                                    ->setOptions(['isRemoteEnabled' => true, 'isHtml5ParserEnabled' => true, 'isPhpEnabled' => true])
                                    ->save($pdfPath);
            \Storage::disk('s3')->put(getCertificatePath($pdfName,'pathOnly'), file_get_contents($pdfPath));
            unlink($pdfPath);
            $ExitsCertificate=Certificate::where('master_user_id',$operatorId)->where('training_course_id',$data['training_course_id'])->first();
            if(empty($ExitsCertificate)){
            $this->model->create($data);
            return response()->json(setResponse([], ['message' => __('admin.certificate.created')]))->setStatusCode(Response::HTTP_OK);
            }else{
                return response()->json(setResponse([], ['message' => __('admin.certificate.exits')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage())))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Industry Details
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id) {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $certificate = Certificate::where('master_user_id',$operatorId)->where('training_course_id',$id)->first();
            return ($certificate) ?
                    (new CertificateResource($certificate)) :
                    response()->json(setResponse([], ['message' => __('admin.certificate.not-found')]))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage())))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Update Industries Details
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateCertificateRequest $request, $id) {
        try {
            $operatorId = auth()->guard('operator')->user()->parent_id ?? auth()->guard('operator')->user()->id;
            $certificateId=Certificate::where('master_user_id',$operatorId)->where('training_course_id',$id)->value('id');
            $certificate = $this->model->find($certificateId);
            $data=$request->all();
            $data['award_date']=date('d-M-Y');
            $data['calculated_expiry_date']=date('d-M-Y', strtotime('+'.$data['expiry_date'].' years'));
            if ($certificate) {
                if(isset($data['image']) && !empty($data['image']))
                {
                    $path=config('constants.aws.certificate').'/'.$data['training_course_id'];
                    $image=S3BucketFileUpload($data['image'],$path);
                    $data['primary_background_image']=$image;
                    $data['primary_background_image_url']=env('CDN_URL') .config('constants.aws.certificate').'/'.$request->training_course_id.'/'.$data['primary_background_image'];
                }else{
                    if(isset($data['primary_background_image']) && !empty($data['primary_background_image'])){
                    $data['primary_background_image']=$certificate->primary_background_image;
                    }else{
                        $data['primary_background_image']=null;
                    }
                }
                if(isset($data['logo_image']) && !empty($data['logo_image']))
                {
                    $path=config('constants.aws.certificate_logo').'/'.$data['training_course_id'];
                    $image=S3BucketFileUpload($data['logo_image'],$path);
                    $data['logo_image']=$image;
                }else{
                    if(isset($data['logo_image_url']) && !empty($data['logo_image_url'])){
                        $data['logo_image']=$certificate->logo_image;
                    }else{
                    $data['logo_image']=null;
                    }
                }
                $quizFolder = base_path('storage/certificate');
                $pdfName = 'Certificate_'.$data['training_course_id'].'_'.date('Y-m-d').'_'.time().'.pdf';
                $pdfPath = $quizFolder . '/' . $pdfName;
                if (!is_dir($quizFolder)) {
                    mkdir($quizFolder);
                }
                if($data['landscape_portrait']=='landscape') {
                    $file='landscape';
                }else{
                    $file='portrait';
                }
                $data['preview_pdf']=$pdfName;
                $data['certification_name']=$data['certificate_name'];
                $data['title']=json_decode($data['certificate_title']);
                $data['body']=json_decode($data['certificate_body']);
                $data['primary_background_image_url']=($data['primary_background_image']) ? env('CDN_URL') .config('constants.aws.certificate').'/'.$request->training_course_id.'/'.$data['primary_background_image']:null;
                $data['logo_image_url']=($data['logo_image']) ? env('CDN_URL') .config('constants.aws.certificate_logo').'/'.$request->training_course_id.'/'.$data['logo_image']:null;
                $data['certificate_footer']=($request->certificate_footer != 'null') ? $request->certificate_footer : null;
                $pdf = PDF::loadView($file,compact('data'))
                                        ->setPaper('a4', $data['landscape_portrait'])->setWarnings(false)
                                        ->setOptions(['isRemoteEnabled' => true, 'isHtml5ParserEnabled' => true, 'isPhpEnabled' => true])
                                        ->save($pdfPath);
                \Storage::disk('s3')->put(getCertificatePath($pdfName,'pathOnly'), file_get_contents($pdfPath));
                unlink($pdfPath);
                $certificate->update(['training_course_id' => $request->training_course_id, 'certificate_title' => $request->certificate_title
                    , 'certificate_name' => $request->certificate_name, 'certificate_body' => $request->certificate_body
                    , 'certificate_footer' => $request->certificate_footer, 'landscape_portrait' => $request->landscape_portrait
                    , 'email_engineers' => $request->email_engineers, 'email_managers' => $request->email_managers
                    , 'calculated_expiry_date' => $data['calculated_expiry_date'], 'other_email' => $request->other_email, 'preview_pdf' => $data['preview_pdf']
                    , 'certification_name' => $data['certification_name'], 'can_add_custom_name' => $request->can_add_custom_name
                    , 'show_issue_date' => $request->show_issue_date, 'show_expiry_date' => $request->show_expiry_date
                    , 'logo_image' => $data['logo_image'], 'cert_never_expires' => $request->cert_never_expires, 'custom_background' => $request->custom_background
                    , 'expiry_date' => $request->expiry_date, 'primary_background_image' => $data['primary_background_image']
                ]);
                return response()->json(setResponse([], ['message' => __('admin.certificate.updated')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setResponse([], ['message' => __('admin.certificate.not-found')]))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage())))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Delete Industries
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id) {
        try {
            $industry = $this->model->find($id);
            if ($industry) {
                $industry->delete();
                return response()->json(setResponse([], ['message' => __('admin.industry.deleted')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setResponse([], ['message' => __('admin.industry.not-found')]))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage())))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Change Industries Status
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function changeStatus(CommonBulkStatusChangeRequest $request) {
        try {
            $this->model->whereIn('id', $request->ids)->update(['status' => $request->status]);
            return response()->json(setResponse([], ['message' => __('admin.industry.status-updated')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage())))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Import Industries
     *
     * @return \Illuminate\Http\Response
     */
    public function importIndustries(ImportCSVFileRequest $request) {
        try {
            $isImported = $this->repository->importIndustries($request);
            if($isImported) {
                return response()->json(setResponse([], ['message' => __('admin.industry.import-success')]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setErrorResponse(__('admin.industry.import-error')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage())))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

}
