<?php
namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Requests\Document\AddDocumentRequest;

class DocumentController extends Controller {
    /**
     *
     * @param Request $request
     * @return type
     */
    public function store(AddDocumentRequest $request) {
        try {
            $path = array_key_exists($request->module, config('constants.module')) ? config('constants.module')[$request->module] : "taxbotic";
            $fileInfo['data'] = uploadImage($request->file('image'), $path);
            return $fileInfo;
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

}
