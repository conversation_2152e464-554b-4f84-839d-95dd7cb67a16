<?php
/**
     * @OA\Post(
     *     path="/api/products",
     *     tags={"Mobile - Products"},
     *     summary="Products Listing",
     *     description="Products Listing",
     *     operationId="index",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                  @OA\Property(
     *                     property="types",
     *                     description="Product Types",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                  @OA\Property(
     *                     property="industries",
     *                     description="Product Industries",
     *                     type="array",
     *                     @OA\Items(),
     *                 ),
     *                 @OA\Property(
     *                     property="sort_by",
     *                     description="Sort By",
     *                     type="string",
     *                     enum={"asc","desc","latest","oldest"}
     *                 ),
     *                 example={"types": {1, 2}, "industries": {1, 2}, "sort_by": "asc"}
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */

/**
     * @OA\Get(
     *     path="/api/products/{id}",
     *     tags={"Mobile - Products"},
     *     summary="Product Details",
     *     description="Product Details",
     *     operationId="show",
     *     @OA\Parameter(
     *         description="Id of product to fetch",
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Success!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"User": {}}
     *     },
     * )
     */