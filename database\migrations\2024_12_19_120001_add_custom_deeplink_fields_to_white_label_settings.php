<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCustomDeeplinkFieldsToWhiteLabelSettings extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('white_label_settings', function (Blueprint $table) {
            $table->string('custom_deeplink_domain')->nullable()->after('fallback_link');
            $table->boolean('custom_deeplink_enabled')->default(false)->after('custom_deeplink_domain');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('white_label_settings', function (Blueprint $table) {
            $table->dropColumn(['custom_deeplink_domain', 'custom_deeplink_enabled']);
        });
    }
}
