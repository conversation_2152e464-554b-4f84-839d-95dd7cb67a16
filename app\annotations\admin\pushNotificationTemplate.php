<?php
/**
     * @OA\Post(
     *     path="/admin/pushNotificationTemplate",
     *     tags={"Admin - Push Notification Templates"},
     *     summary="Add Push Notification Template",
     *     description="Add Push Notification Template",
     *     operationId="store",
     *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="User Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="User Status",
     *                     type="string",
     *                      enum={"Active", "Inactive"}
     *                 ),
     *                 example={"title":"",
     *                 "message":"",
     *                 "user_type":"Admin/Operator/ContentProvider/EndUser",
     *                 "template_key":""
     *               }
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Template Created successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */ 

/**
     * @OA\Put(
     *     path="/admin/pushNotificationTemplate/{id}",
     *     tags={"Admin - Push Notification Templates"},
     *     summary="Update Push Notification Template",
     *     description="Update Push Notification Template",
     *     operationId="update",
     *     @OA\Parameter(
     *         description="Id of Template to update",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
    *     @OA\RequestBody(
     *         description="Input data format",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     description="User Name",
     *                     type="string"
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     description="group Status",
     *                     type="string",
     *                      enum={"Active", "Inactive"}
     *                 ),
     *                 example={"title":"",
     *                 "message":"",
     *               }
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Template Updated successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=401,description="Unauthorize Access!"),
     *     @OA\Response(response=404,description="Not Found."),
     *     @OA\Response(response=500,description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
     */ 

/**
     * @OA\Get(
     *     path="/admin/pushNotificationTemplate/{id}",
     *     tags={"Admin - Push Notification Templates"},
     *     summary="Show Push Notification Template Details",
     *     description="Show Push Notification Template Details",
     *     operationId="show",
     *     @OA\Parameter(
     *         description="Id of Template to fetch",
     *         in="path",
     *         name="id",
     *         required=true,
     *         @OA\Schema(
     *             format="int64",
     *             type="integer"
     *         )
     *     ),
     *     @OA\Response(response=200, description="OK"),
     *     @OA\Response(response=201, description="Successfully!"),
     *     @OA\Response(response=422, description="Missing Or Invalid Parameters."),
     *     @OA\Response(response=400, description="Bad Request!"),
     *     @OA\Response(response=401, description="Unauthorize Access!"),
     *     @OA\Response(response=403, description="Forbidden Access!"),
     *     @OA\Response(response=404, description="Not Found."),
     *     @OA\Response(response=500, description="Something went wrong!"),
     *     security={
     *         {"Admin": {}}
     *     },
     * )
    */