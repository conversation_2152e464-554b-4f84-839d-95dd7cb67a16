<?php

namespace App\Http\Controllers\Api\V1;

use DB;
use App\Models\User;
use App\Models\MasterUser;
use App\Models\QuizOffline;
use Illuminate\Http\Response;
use App\Models\TrainingCourse;
use App\Http\Controllers\Controller;
use App\Models\TrainingCourseProgress;
use App\Models\TrainingCourseSubModuleQuiz;
use App\Models\TrainingCourseModuleProgress;
use App\Models\TrainingCourseSubmoduleDetails;
use App\Models\TrainingCourseSubmoduleProgress;
use App\Http\Requests\V1\StoreQuizResultRequest;
use App\Http\Requests\V1\StoreQuizAttemptRequest;
use App\Models\TrainingCourseSubModuleQuizResults;
use App\Http\Requests\V1\PreviousQuizResultRequest;
use App\Models\TrainingCourseSubModuleQuizQuestion;
use App\Models\TrainingCourseSubModuleQuizUsersData;
use App\Http\Resources\V1\TrainingCourseQuizResource;
use App\Models\TrainingCourseSubModuleQuizQuestionOption;
use App\Http\Resources\V1\TrainingCourseSubModuleQuizResultResource;

class TrainingCourseSubModuleQuizController extends Controller
{

    /**
     * Create a new controller instance.
     *
     * @return void
     */

    public function __construct()
    {

    }

    /**
     * Check Training Course Submodule Quiz Status
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function checkQuizStatus($id)
    {
        try {
            $submoduleDetails = TrainingCourseSubmoduleDetails::where('id', $id)->first();
            $quiz = TrainingCourseSubModuleQuiz::whereSubmoduleId($id)->first();
            if ($quiz) {
                // User time spent and progress
                $submoduleProgress = (new TrainingCourseSubmoduleProgress)->getProgress(['user_id' => auth()->user()->id, 'submodule_id' => $id]);
                $userTimeSpent = (new TrainingCourseSubmoduleProgress)->getUserTimeSpent(['user_id' => auth()->user()->id, 'submodule_id' => $id]);

                $lastAttempt = TrainingCourseSubModuleQuizResults::where(['submodule_id' => $id, 'user_id' => auth()->id()])->latest()->first();

                $message = '';

                if ($lastAttempt) {

                    if ($quiz->max_attempts_before_fail != null) {
                        if ($lastAttempt->is_pass != 1) {
                            if ($lastAttempt->attempts >= $quiz->max_attempts_before_fail) {
                                $lastAttempt->maximum_attempts = 1;
                            } else {
                                $lastAttempt->maximum_attempts = 0;
                            }
                        }

                    } else {
                        $lastAttempt->maximum_attempts = 0;
                    }

                    if ($lastAttempt->passed_by_operator == 1) {
                        $message = __('user.TrainingCourseSubmodule.quiz-result.passed-operator');
                    }
                    $lastAttempt->max_attempts_before_fail = $quiz->max_attempts_before_fail;
                    $lastAttempt->submodule_progress = $submoduleProgress;
                    $lastAttempt->user_time_spent = $userTimeSpent;
                    $lastAttempt->disable_result_pdf = $submoduleDetails->disable_result_pdf;

                    $autoResetQuizDuration = 0;
                    $maxAttemptsQuiz = false;
                    $userLastattempt = TrainingCourseSubModuleQuizResults::where(['submodule_id' => $id, 'user_id' => auth()->id()])->orderBy('id', 'DESC')->first();
                    if (!empty($userLastattempt)) {
                        if ($userLastattempt->is_pass != 1) {
                            $count = TrainingCourseSubModuleQuizResults::where(['submodule_id' => $id, 'user_id' => auth()->id()])->count();
                            $maxAttempts = $quiz->max_attempts_before_fail;
                            if ($maxAttempts == 0) {
                                $autoResetQuizDuration = 0;
                                $maxAttemptsQuiz = false;
                            } else if ($count < $maxAttempts) {
                                $autoResetQuizDuration = 0;
                                $maxAttemptsQuiz = false;
                            } else {
                                $autoResetQuizDuration = ($quiz->auto_reset_duration_on_fail > 0 ? $quiz->auto_reset_duration_on_fail : 0);
                                $maxAttemptsQuiz = true;
                            }
                        }
                    } else {
                        $autoResetQuizDuration = 0;
                        $maxAttemptsQuiz = false;
                    }
                    $lastAttempt->auto_reset_duration_on_fail = ($autoResetQuizDuration) ? $autoResetQuizDuration : 0;
                    $lastAttempt->is_maximum_attempts = $maxAttemptsQuiz;

                    return (new TrainingCourseSubModuleQuizResultResource($lastAttempt))->additional(['extra_meta' => ['message' => $message]]);
                }

                $userId = auth()->user()->id;
                $masterUserId = auth()->user()->user_relation->master_user_id;

                $offlineQuizDetail = QuizOffline::where('master_user_id',$masterUserId)
                                    ->where('user_id',$userId)
                                    ->where('submodule_id',$id)
                                    ->first();
                $prev_quiz = 0;
                $questionAnswerArray = [];
                $questionAnswerId = [];
                $offlineTotalQuestion = 0;
                $offlineTotalQuestionAnswered = 0;
                $offlineDuration = 0;
                $offlineTimeSpent = 0;
                $offlineStartQuizTimeStamp = null;
                if(!empty($offlineQuizDetail)){
                    if(($offlineQuizDetail->total_question == $offlineQuizDetail->total_answered_question)){
                        $prev_quiz = 1;
                        $questionAnswerArray = json_decode($offlineQuizDetail->questionAnswerArray);
                        $questionAnswerId = json_decode($offlineQuizDetail->questionAnswerID);
                        $offlineTotalQuestion = $offlineQuizDetail->total_question;
                        $offlineTotalQuestionAnswered = $offlineQuizDetail->total_answered_question;
                        $offlineDuration = $offlineQuizDetail->duration;
                        $offlineTimeSpent = $offlineQuizDetail->time_spent;
                        $offlineStartQuizTimeStamp = $offlineQuizDetail->quiz_start_timestamp;
                    }
                }

                $prevResponseArr = [
                    'submodule_progress'=> $submoduleProgress ?? 0,
                    'user_time_spent'=> $userTimeSpent,
                    'prev_quiz' => $prev_quiz
                ];

                if ($prev_quiz == 1) {
                    $prevResponseArr = array_merge($prevResponseArr, [
                        'prev_quiz_data' => [
                            'questionAnswerArray' => $questionAnswerArray,
                            'questionAnswerID' => $questionAnswerId,
                            'total_question' => (int)$offlineTotalQuestion,
                            'total_answered_question' => (int)$offlineTotalQuestionAnswered,
                            'duration' => (int)$offlineDuration,
                            'time_spent' => (int)$offlineTimeSpent,
                            'quiz_start_timestamp' => $offlineStartQuizTimeStamp
                        ]
                    ]);
                }
                
                $message = __('user.TrainingCourseSubmodule.quiz-result.previous-not-found');
                return response()->json(setResponse($prevResponseArr, ["message" => $message]))->setStatusCode(Response::HTTP_OK);
            }
            return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Training Course Submodule Quiz
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function getQuiz($id)
    {
        try {
            $submoduleDetails = TrainingCourseSubmoduleDetails::where('id', $id)
                                                            ->where('submodule_type_id',config('constants.submodule_types.question_bank_quiz'))
                                                            ->first();
            if($submoduleDetails){
                $checkTrainingCourse = checkTrainingCourse($submoduleDetails);
                if($checkTrainingCourse == 1){
                    return response()->json(setErrorResponse(__('user.TrainingCourse.unAuthorized')))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
                }
                if($checkTrainingCourse == 2){
                    return response()->json(setErrorResponse(__('user.TrainingCourseModule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
                if($submoduleDetails->status != 'Active'){
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }

                $quiz = TrainingCourseSubModuleQuiz::whereSubmoduleId($id)->first();
                if ($quiz) {
                    $finalCategories = [];
                    $previousAttempts = TrainingCourseSubModuleQuizResults::where(['submodule_id' => $id, 'user_id' => auth()->id()])->get();
                    foreach ($previousAttempts as $attempt) {
                        $finalCategories = array_merge($finalCategories, $attempt->quizData()->where('is_correct', 1)->pluck('category_id')->toArray());
                    }

                    if ($submoduleDetails->is_retake_all_questions == 0) {
                        $categories = $quiz->categories()->whereNotIn('id', $finalCategories)->get();
                    } else {
                        $categories = $quiz->categories()->get();
                    }

                    $questions = [];
                    foreach ($categories as $category) {
                        if ($category->questions && $category->questions()->count() > 0) {
                            $question = $category->questions->random(1)->first();
                            $url_parts_question = parse_url($question->question_image);
                            $questions[] = [
                                'id' => $question->id,
                                'question' => $question->question,
                                'question_image' => isset($url_parts_question['host']) ? $question->question_image : $question->question_image_url,
                                'options' => $question->options()->select('id', 'answer')->get()->toArray(),
                            ];
                        }
                    }
                    $quiz->questions = $questions;

                    if ($quiz->max_attempts_before_fail != null) {
                        $userAttempts = (new TrainingCourseSubmoduleQuizResults)->getUserAttempts(['user_id' => auth()->user()->id, 'submodule_id' => $id]);
                        $quiz->is_pass = $userAttempts->is_pass;
                        
                        if ($userAttempts->is_pass != 1) {
                            if ($userAttempts->attempts >= $quiz->max_attempts_before_fail) {
                                $quiz->maximum_attempts = 1;
                            } else {
                                $quiz->maximum_attempts = 0;
                            }
                        }

                    } else {
                        $quiz->maximum_attempts = 0;
                        $quiz->is_pass = 0;
                    }
                    // User time spent and progress
                    $submoduleProgress = (new TrainingCourseSubmoduleProgress)->getProgress(['user_id' => auth()->user()->id, 'submodule_id' => $id]);
                    $userTimeSpent = (new TrainingCourseSubmoduleProgress)->getUserTimeSpent(['user_id' => auth()->user()->id, 'submodule_id' => $id]);

                    $quiz->submodule_progress = $submoduleProgress;
                    $quiz->user_time_spent = $userTimeSpent;
                    $quiz->enable_time_spend = $submoduleDetails->enable_time_spend;
                    $quiz->disable_result_pdf = $submoduleDetails->disable_result_pdf;

                    $autoResetQuizDuration = 0;
                    $maxAttemptsQuiz = false;
                    $lastattempt = TrainingCourseSubModuleQuizResults::where(['submodule_id' => $id, 'user_id' => auth()->id()])->orderBy('id', 'DESC')->first();
                    if (!empty($lastattempt)) {
                        if ($lastattempt->is_pass != 1) {
                            $count = TrainingCourseSubModuleQuizResults::where(['submodule_id' => $id, 'user_id' => auth()->id()])->count();
                            $maxAttempts = $quiz->max_attempts_before_fail;
                            if ($maxAttempts == 0) {
                                $autoResetQuizDuration = 0;
                                $maxAttemptsQuiz = false;
                            } else if ($count < $maxAttempts) {
                                $autoResetQuizDuration = 0;
                                $maxAttemptsQuiz = false;
                            } else {
                                $autoResetQuizDuration = ($quiz->auto_reset_duration_on_fail > 0 ? $quiz->auto_reset_duration_on_fail : 0);
                                $maxAttemptsQuiz = true;
                            }
                        }
                    } else {
                        $autoResetQuizDuration = 0;
                        $maxAttemptsQuiz = false;
                    }
                    $quiz->auto_reset_duration_on_fail = ($autoResetQuizDuration) ? $autoResetQuizDuration : 0;
                    $quiz->is_maximum_attempts = $maxAttemptsQuiz;
                    return (new TrainingCourseQuizResource($quiz));
                }else{
                    return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
                }
            }else{
                return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Store Training Course Submodule Quiz Results
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function storeAttemptQuiz(StoreQuizAttemptRequest $request)
    {
        $newUser = QuizOffline::updateOrCreate([
            'master_user_id'   => $request['master_user_id'],
            'user_id'   => $request['user_id'],
            'submodule_id'   => $request['submodule_id'],
        ],[
            'questionAnswerArray'     => json_encode($request['questionAnswerArray']),
            'questionAnswerID'     => json_encode($request['questionAnswerID']),
            'total_question'     => $request['total_question'],
            'total_answered_question'     => $request['total_answered_question'],
            'duration'     => $request['duration'],
            'time_spent'   => $request['time_spent'],
            'quiz_start_timestamp'   => $request['quiz_start_timestamp'] ?? null,
            'created_at'    => date('Y-m-d H:i:s'),
            'updated_at'    => date('Y-m-d H:i:s')
        ]);
        return response()->json(setResponse([], ["message" => 'Ok']))->setStatusCode(Response::HTTP_OK);
    }

    /**
     * Store Training Course Submodule Quiz Results
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function storeQuizResult(StoreQuizResultRequest $request)
    {
        try {
            //\Cache::flush();
            $operatorId = auth()->user()->user_relation->master_user_id;
            $quizResult = TrainingCourseSubModuleQuizResults::where(['submodule_id' => $request->submodule_id, 'user_id' => auth()->user()->id])->orderBy('attempts', 'DESC')->first();
            if (!is_null($quizResult) && $quizResult->passed_by_operator == 1) {
                return response()->json(setResponse([], ['status' => 1, 'message' => __('user.TrainingCourseSubmodule.quiz-result.passed-operator')]))->setStatusCode(Response::HTTP_OK);
            } else {
                return DB::transaction(function () use ($request, $operatorId) {
                    $data = $request->all();
                    $user = auth()->user();
                    $quiz = TrainingCourseSubModuleQuiz::where(['submodule_id' => $data['submodule_id']])->first();

                    //add training course progress
                    TrainingCourse::addDefaultCourseProgress($quiz->subModule->training_course_id);

                    $data['training_course_id'] = $quiz->subModule->training_course_id;
                    $data['module_id'] = $quiz->subModule->module_id;
                    $data['user_id'] = $user->id;
                    $data['master_user_id'] = $operatorId;

                    $previousAttempt = TrainingCourseSubModuleQuizResults::where(['training_course_id' => $data['training_course_id'], 'submodule_id' => $data['submodule_id'], 'user_id' => $user->id])->select('attempts')->orderBy('attempts', 'desc')->first();
                    $data['attempts'] = is_null($previousAttempt) ? 1 : $previousAttempt->attempts + 1;
                    $result = TrainingCourseSubModuleQuizResults::create($data);

                    $correctAnswers = 0;
                    $quizData = [];
                    if (count($data['data']) > 0) {
                        $questions=TrainingCourseSubModuleQuizQuestion::select('id','category_id')->whereIn('id',array_column($data['data'], 'question_id'))->pluck('category_id','id')->toArray();
                        $answers=TrainingCourseSubModuleQuizQuestionOption::whereIn('id',array_column($data['data'], 'answer_id'))->pluck('is_correct_answer','id')->toArray();
                        foreach ($data['data'] as $value) {
                            if (isset($value['answer_id'])) {
                                $correctAnswers += (isset($answers[$value['answer_id']]) ? $answers[$value['answer_id']] : 0);
                            }
                            $quizData[] = [
                                'quiz_result_id' => $result->id,
                                'category_id' => $questions[$value['question_id']],
                                'question_id' => $value['question_id'],
                                'answer_id' => isset($value['answer_id']) ? $value['answer_id'] : null,
                                'is_correct' => isset($value['answer_id']) ? $answers[$value['answer_id']] : 0,
                            ];
                        }
                        TrainingCourseSubModuleQuizUsersData::insert($quizData);
                    }

                    $allAttempts = TrainingCourseSubModuleQuizResults::where(['training_course_id' => $data['training_course_id'], 'submodule_id' => $data['submodule_id'], 'user_id' => $user->id])->pluck('correct_answers')->toArray();
                    $totalAttempts = count($allAttempts);
                    $previousCorrectAnswers = array_sum($allAttempts);
                    $passingCriteria = $quiz->passing_criteria;

                    $submoduleDetails = TrainingCourseSubmoduleDetails::where('id', $data['submodule_id'])->first();

                    // For question wise passing criteria
                    $totalCorrectAnswers = ($submoduleDetails->is_retake_all_questions == 1) ? $correctAnswers : ($previousCorrectAnswers + $correctAnswers);
                    $totalQuestions = $quiz->categories->count();
                    // echo $totalCorrectAnswers;exit;
                    $quizPassCount = (int) round(($totalQuestions * $passingCriteria) / 100);
                    $passFailResult = round(($totalCorrectAnswers/$totalQuestions)*100);
                    $isPass = ($passFailResult >= $passingCriteria ? 1 : 0);
                    $result->update(['correct_answers' => $correctAnswers, 'is_pass' => $isPass, 'pdf' => '']);

                    // Progress Calculation
                    $submoduleData = TrainingCourseSubmoduleDetails::select('enable_time_spend', 'condition', 'time_spent', 'completion_percentage', 'disable_result_pdf')->find($data['submodule_id']);

                    if ($submoduleData->enable_time_spend == 1) {
                        // Check submodule progress
                        $isExist = TrainingCourseSubmoduleProgress::status()->where(['user_id' => $data['user_id'], 'training_course_id' => $data['training_course_id'], 'module_id' => $data['module_id'], 'submodule_id' => $data['submodule_id'], 'submodule_type_id' => 12])->first();
                        $data['time_spent'] = (!is_null($isExist) ? ($isExist->time_spent + $data['time_spent']) : $data['time_spent']);

                        $data['completion_percentage'] = ($result->is_pass == 1 ? $submoduleData->completion_percentage : 0);

                        // Quiz Progress
                        if ($submoduleData->condition == 'and') {
                            if ($data['time_spent'] >= $submoduleData->time_spent && $data['completion_percentage'] == $submoduleData->completion_percentage) {
                                $progress = 100;
                            } elseif ($data['completion_percentage'] == 100 && $data['time_spent'] >= $submoduleData->time_spent) {
                                $progress = 100;
                            } else {
                                $timeSpent = (int) ((100 * $data['time_spent']) / $submoduleData->time_spent);
                                $progress = (($timeSpent + $data['completion_percentage']) / 2);
                            }
                        } else {
                            $timeSpent = (int) ((100 * $data['time_spent']) / $submoduleData->time_spent);
                            $progress = max($data['completion_percentage'], $timeSpent);
                        }
                        $submoduleProgress = $progress;
                    } else {
                        $submoduleProgress = ($totalCorrectAnswers >= $quizPassCount ? 100 : (($totalCorrectAnswers / $totalQuestions) * 100));
                    }
		            $submoduleProgress = round($submoduleProgress);

                    // Submodule Update progress
                    $progressData = ['user_id' => $data['user_id'], 'training_course_id' => $data['training_course_id'], 'module_id' => $data['module_id'], 'submodule_id' => $data['submodule_id'], 'submodule_type_id' => 12, 'time_spent' => $data['time_spent'], 'submodule_progress' => $submoduleProgress];
                    $overRideResult = TrainingCourseSubmoduleProgress::select('is_result_override')->where([
                        'user_id' => $data['user_id'],
                        'training_course_id' => $data['training_course_id'],
                        'module_id' => $data['module_id'],
                        'submodule_id' => $data['submodule_id']
                    ])->first();
                    if($overRideResult->is_result_override == 'Default'){
                        (new TrainingCourseSubmoduleProgress)->updateData($progressData);
                        // Module progress
                        (new TrainingCourseModuleProgress)->calculateModuleProgress($data);
                        // Training course progress
                        (new TrainingCourseProgress)->calculateTrainingCourseProgress($data);
                        // Update New flag
                        (new TrainingCourseModuleProgress)->updateIsNew($progressData);
                        (new TrainingCourseProgress)->updateIsNew($progressData);
                        }

                    $result->submodule_progress = $submoduleProgress;
                    $result->user_time_spent = (new TrainingCourseSubmoduleProgress)->getUserTimeSpent(['user_id' => $data['user_id'], 'submodule_id' => $data['submodule_id']]);
                    $result->disable_result_pdf = $submoduleData->disable_result_pdf;
                    
                    $lateSubmission = 0;
                    if(isset(request()->quiz_start_timestamp) && isset(request()->submission_timestamp)){
                        $quizDuration = ($quiz->quiz_duration > 0 ? ($quiz->quiz_duration * 60) : 0);
                        if($quizDuration != 0){
                            $submitTimeDiff = offlineQuizSubmitTimeDiff(date('Y-m-d H:i:s',request()->quiz_start_timestamp),date('Y-m-d H:i:s',request()->submission_timestamp));
                            if($submitTimeDiff > $quizDuration){
                                $lateSubmission = 1;
                            }
                        }
                    }

                    // $result->update(['late_submission'=>$lateSubmission]);

                    dispatch(new \App\Jobs\QuizResultPdfSendMailJob($result->id, $user->id, $totalAttempts, $user->user_relation,'API',$lateSubmission));

                    $smartawards_operator_id = config('constants.smart_award_operator_id');
                    if ($user->user_relation->master_user_id == $smartawards_operator_id && $result->master_user_id == $smartawards_operator_id) {
                        $operatorDetail = MasterUser::where('id', $user->user_relation->master_user_id)->first();
                        $apiKey = $operatorDetail->external_api_key;
                        $resultData = [
                            "email_address" => $user->email,
                            "pass" => ($result->is_pass == 0) ? false : true,
                            "result" => $result->correct_answers,
                            "total_questions" => $result->total_questions,
                            "training_course_id" => $data['training_course_id'],
                            "attempt_id" => $result->attempts,
                            "date_last_updated" => date_format($result->updated_at, 'Y-m-d H:i:s'),
                            "percentage" => $result->submodule_progress
                        ];
                        $this->submitResult($resultData, $apiKey);
                    }
                    
                    /** Delete offline quiz submit record START  */
                    $offlineQuizDetail = QuizOffline::where('user_id',$user->id)
                                ->where('master_user_id',$operatorId)
                                ->where('submodule_id',$data['submodule_id'])
                                ->first();
                    if(!empty($offlineQuizDetail)){
                        $offlineQuizDetail->delete();
                    }
                    /** Delete offline quiz submit record END  */

                    $autoResetQuizDuration = 0;
                    $maxAttemptsQuiz = false;
                    $lastattempt = TrainingCourseSubModuleQuizResults::where(['submodule_id' => $data['submodule_id'], 'user_id' => auth()->id()])->orderBy('id', 'DESC')->first();
                    if (!empty($lastattempt)) {
                        if ($lastattempt->is_pass != 1) {
                            $count = TrainingCourseSubModuleQuizResults::where(['submodule_id' => $data['submodule_id'], 'user_id' => auth()->id()])->count();
                            $maxAttempts = $quiz->max_attempts_before_fail;
                            if ($maxAttempts == 0) {
                                $autoResetQuizDuration = 0;
                                $maxAttemptsQuiz = false;
                            } else if ($count < $maxAttempts) {
                                $autoResetQuizDuration = 0;
                                $maxAttemptsQuiz = false;
                            } else {
                                $autoResetQuizDuration = ($quiz->auto_reset_duration_on_fail > 0 ? $quiz->auto_reset_duration_on_fail : 0);
                                $maxAttemptsQuiz = true;
                            }
                        }
                    } else {
                        $autoResetQuizDuration = 0;
                        $maxAttemptsQuiz = false;
                    }
                    $result->auto_reset_duration_on_fail = ($autoResetQuizDuration) ? $autoResetQuizDuration : 0;
                    $result->is_maximum_attempts = $maxAttemptsQuiz;
                    return (new TrainingCourseSubModuleQuizResultResource($result));
                });
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /** Submit Result */
    public function submitResult($resultData, $apiKey)
    {
        $url = env('SMART_AWARDS_RESULT_URL');
        $headers = array(
            'API-KEY:' . $apiKey,
            'Content-Type:application/json',
        );
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($resultData));
        $result = curl_exec($ch);
        curl_close($ch);
    }

    /**
     * Get Previous Quiz Result
     *
     * @return \Illuminate\Http\Response
     */
    public function previousQuizResult(PreviousQuizResultRequest $request)
    {
        try {
            $user = auth()->user();
            if ($request->send_email) {
                dispatch(new \App\Jobs\QuizPreviousResultSendMailJob($request->submodule_id, $user->id, $request->send_to));
                return response()->json(setResponse([], ['message' => __('user.TrainingCourseSubmodule.quiz-result.send-email')]))->setStatusCode(Response::HTTP_OK);
            }
            $quiz = TrainingCourseSubModuleQuizResults::with('subModule')->where([
                'submodule_id' => $request->submodule_id, 'user_id' => $user->id,
            ])->orderBy('id', 'DESC')->get();
            return TrainingCourseSubModuleQuizResultResource::collection($quiz);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Get Specific Quiz Result
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function getQuizResult($id)
    {
        try {
            $quiz = TrainingCourseSubModuleQuizResults::find($id);
            if ($quiz) {
                $message = '';
                if ($quiz->passed_by_operator == 1) {
                    $message = __('user.TrainingCourseSubmodule.quiz-result.passed-operator');
                }
                // User time spent and progress
                $submoduleProgress = (new TrainingCourseSubmoduleProgress)->getProgress(['user_id' => auth()->user()->id, 'submodule_id' => $quiz->submodule_id]);
                $userTimeSpent = (new TrainingCourseSubmoduleProgress)->getUserTimeSpent(['user_id' => auth()->user()->id, 'submodule_id' => $quiz->submodule_id]);

                $quiz->submodule_progress = $submoduleProgress;
                $quiz->user_time_spent = $userTimeSpent;
                return (new TrainingCourseSubModuleQuizResultResource($quiz))->additional(['extra_meta' => ['message' => $message]]);
            }
            return response()->json(setErrorResponse(__('user.TrainingCourseSubmodule.quiz-result.notFound')))->setStatusCode(Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
