<?php

namespace App\Models;

use DBTableNames;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class LocationMatrixData extends Model
{
    use SoftDeletes;

    protected $table = 'location_matrix_data';

    protected $fillable = [
        'question_id', 'master_user_id', 'region', 'territory', 
        'exchange', 'additional_data'
    ];

    protected $casts = [
        'additional_data' => 'array'
    ];

    // Relations
    public function question()
    {
        return $this->belongsTo(TrainingCourseSubModulePracticalAssessmentQuestion::class, 'question_id', 'id');
    }

    public function masterUser()
    {
        return $this->belongsTo(MasterUser::class, 'master_user_id', 'id');
    }

    // Scopes
    public function scopeByQuestion($query, $questionId)
    {
        return $query->where('question_id', $questionId);
    }

    public function scopeByRegion($query, $region)
    {
        return $query->where('region', $region);
    }

    public function scopeByTerritory($query, $territory)
    {
        return $query->where('territory', $territory);
    }

    public function scopeByMasterUser($query, $masterUserId)
    {
        return $query->where('master_user_id', $masterUserId);
    }

    public function scopeSearch($query, $searchTerm)
    {
        return $query->where(function ($q) use ($searchTerm) {
            $q->where('region', 'like', '%' . $searchTerm . '%')
              ->orWhere('territory', 'like', '%' . $searchTerm . '%')
              ->orWhere('exchange', 'like', '%' . $searchTerm . '%');
        });
    }

    // Helper methods
    public function getFilterValue1Attribute()
    {
        return $this->region;
    }

    public function getFilterValue2Attribute()
    {
        return $this->territory;
    }

    public function getSelectionValueAttribute()
    {
        return $this->exchange;
    }
}
