<?php

namespace App\Http\Controllers\Api\V1;

use Illuminate\Http\Request;
use App\Models\Notifications;
use Illuminate\Http\Response;
use App\Models\NotificationUser;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Requests\V1\NotificationRequest;
use App\Http\Resources\V1\NotificationResource;
use App\Http\Resources\V1\NotificationListResource;


class NotificationsController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {

    }

    /**
     * Get Notification Settings List
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {
        try {
            $notificationIds=NotificationUser::where('user_id',auth()->user()->id)->pluck('notification_id')->toArray();
            $notification_setting=DB::table('notification_settings')->whereNotIn('id',$notificationIds)->where('type','user')->pluck('id')->toArray();
            if(!empty($notification_setting))
            {
                foreach($notification_setting as $value){
                    $record=new NotificationUser();
                    $record->notification_id=$value;
                    $record->user_id=auth()->user()->id;
                    $record->is_on=1;
                    $record->save();
                }
            }
            $featureId=DB::table('features')->where('permission_key','live_chat')->value('id');
            $featureSetting=DB::table('feature_settings')->where('master_user_id',auth()->user()->user_relation->master_user_id)->where('feature_id',$featureId)->value('is_feature_on');
            if($featureSetting){
                $header_bundle_id = strtolower($request->header('bundle-id'));
                $bundle_ids = array_change_key_case(config('constants.bundle_ids'), CASE_LOWER);
                if (in_array($header_bundle_id, array_keys($bundle_ids))) {
                    $settings = NotificationUser::user()->where('notification_id','!=',14)->get();
                }else{
                $settings = NotificationUser::user()->get();
                }
            }else{
                $settings = NotificationUser::user()->where('notification_id','!=',14)->get();
            }
            return NotificationResource::collection($settings);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(NotificationRequest $request) {
        try {
            $user = auth()->user();
            $notification = NotificationUser::where(['id' => $request->id, 'user_id' => $user->id])->first();
            $notification->update(['is_on' => $request->is_on]);
            $settings = NotificationUser::user()->get();
            return NotificationResource::collection($settings);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }


    /**
     * Get Notification List
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function notificationList() {
        try {
            $newNotificationList = Notifications::whereUserId(auth()->user()->id)->whereMasterUserId(auth()->user()->user_relation->master_user_id)->where('created_at','LIKE', date('Y-m-d')."%")->orderBy('created_at', 'DESC')->get();
            $oldNotificationList = Notifications::whereUserId(auth()->user()->id)->whereMasterUserId(auth()->user()->user_relation->master_user_id)->whereDate('created_at','<', date('Y-m-d'))->orderBy('created_at', 'DESC')->get();
            $list['today'] = NotificationListResource::collection($newNotificationList);
            $list['previous'] = NotificationListResource::collection($oldNotificationList);
            if(!$newNotificationList && !$oldNotificationList){
                return response()->json(setResponse([], ['message' => __('user.NotificationController.notificationListNotFound')]))->setStatusCode(Response::HTTP_OK);
            }else{
                return response()->json(setResponse($list, ['message' => __('user.NotificationController.notificationList')]))->setStatusCode(Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function markAsRead(Request $request) {
        try {
            if(isset($request->notification_id)){
                Notifications::whereUserId(auth()->user()->id)->whereMasterUserId(auth()->user()->user_relation->master_user_id)->whereId($request->notification_id)->update(['is_read' => 0]);
            }else{
                Notifications::whereUserId(auth()->user()->id)->whereMasterUserId(auth()->user()->user_relation->master_user_id)->update(['is_read' => 0]);
            }
            return response()->json(setResponse([], ['message' => __('user.NotificationController.markAsRead')]))->setStatusCode(Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function deleteNotifications(Request $request) {
        try {
            if(isset($request->id)){
                Notifications::destroy($request->id);
                $data['user_id'] = auth()->user()->id;
                $data['master_user_id'] = auth()->user()->user_relation->master_user_id;
                $response['total_unread_count'] = Notifications::totalUnreadCount($data);
                return response()->json(setResponse($response, ['message' => __('user.NotificationController.destroyed')]))->setStatusCode(Response::HTTP_OK);
            }else{
                return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $e) {
            return response()->json(setErrorResponse(__($e->getMessage()),null,true))->setStatusCode(Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
